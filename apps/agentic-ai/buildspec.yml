version: 0.2

env:
  variables:
    DOCKER_BUILDKIT: 1
    # Add cache variables if needed
    BUILDX_CACHE_ENABLED: "true"

phases:
  pre_build:
    commands:
      - aws --version
      - echo $AWS_DEFAULT_REGION
      - $(aws ecr get-login --region $AWS_DEFAULT_REGION --no-include-email)
      - REPOSITORY_URI=$ECR_TARGET
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${COMMIT_HASH:=latest}
      - docker pull $REPOSITORY_URI:latest || echo "No existing image found for caching"

  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build --cache-from $REPOSITORY_URI:latest --build-arg BUILDKIT_INLINE_CACHE=1 -t $REPOSITORY_URI:latest apps/agentic-ai
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
  post_build:
    commands:
      - echo Build completed on `date`
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - printf '[{"name":"%s","imageUri":"%s"}]' $TASK_DEF_NAME $REPOSITORY_URI:$IMAGE_TAG > imagedefinations.json

artifacts:
  files: imagedefinations.json
