import { Modu<PERSON> } from '@nestjs/common';
import { TokenCounterService } from './token-counter.service';
import { TokenCounterController } from './token-counter.controller';
import { TokenCounterModel, TokenCounterSchema } from './schemas/token-counter.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { ProjectModel } from '../project/schemas/project.schema';
import { ProjectSchema } from '../project/schemas/project.schema';
import { User, UserSchema } from '../core/user/schemas/user.model';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: TokenCounterModel.name, schema: TokenCounterSchema }]),
    MongooseModule.forFeature([{ name: ProjectModel.name, schema: ProjectSchema }]),
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
  ],
  controllers: [TokenCounterController],
  providers: [TokenCounterService],
  exports: [TokenCounterService],
})
export class TokenCounterModule {}
