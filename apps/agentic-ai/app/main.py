from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from app.api.v1.api import api_router
from app.db.mongodb import db
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
import asyncio
from contextlib import asynccontextmanager
import concurrent.futures

# Configure timeout settings
TIMEOUT = 3600  # 60 minutes in seconds

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Set the timeout for asyncio tasks
    asyncio.get_event_loop().set_default_executor(
        concurrent.futures.ThreadPoolExecutor(max_workers=4)
    )
    yield

class TimeoutMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            return await asyncio.wait_for(call_next(request), timeout=TIMEOUT)
        except asyncio.TimeoutError:
            return Response(
                content={"detail": "Request timeout"},
                status_code=504
            )

app = FastAPI(
    title="Industry LM API",
    description="API for Industry Language Model",
    version="1.0.0",
    lifespan=lifespan,
)

# Add timeout middleware
app.add_middleware(TimeoutMiddleware)

app.include_router(api_router, prefix="/api/v1")


@app.get("/health")
async def health_check():
    try:
        await db.command("ping")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    return {"message": "OK"}
