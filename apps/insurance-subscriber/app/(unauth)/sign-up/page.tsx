"use client";
import { API } from "@/apis/api";
import { TPlan, TTierGroup } from "@/apis/plan-&-tier.api";
import { TTenant } from "@/apis/tenant.api";
import { ThemeToggle } from "@/components/theme-toggle";
import Orb from "@/components/ui/orb";
import { useAuth } from "@/contexts/auth-context";
import { useIsMobile } from "@/hooks/use-is-mobile";
import { AuroraText } from "@workspace/ui/components/aurora-text";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@workspace/ui/components/carousel";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Marquee } from "@workspace/ui/components/marquee";
import { cn } from "@workspace/ui/lib/utils";
import {
  ArrowLeft,
  ArrowRight,
  CheckIcon,
  CircleDot,
  Rabbit,
  X,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { ReactNode, Suspense, useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

// Wrapper component that uses searchParams
function SignUpPageContent() {
  const searchParams = useSearchParams();
  const tierGroupId = searchParams.get("tier_group");
  const planId = searchParams.get("plan");
  const domain = searchParams.get("domain");
  const variant = searchParams.get("variant");
  const [tierGroup, setTierGroup] = useState<TTierGroup | null>(null);
  const { signIn } = useAuth();
  const [tenant, setTenant] = useState<TTenant | null>(null);

  const [continueToPlan, setContinueToPlan] = useState(false);

  const PlatformIcons = {
    LinkedIn: (h: number, w: number) => (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="#0077B5"
        width={w}
        height={h}
      >
        <path d="M20 2H4C2.89 2 2 2.9 2 4v16c0 1.1.89 2 2 2h16c1.11 0 2-.9 2-2V4c0-1.1-.89-2-2-2zM8.27 18H5.56V9h2.71v9zm-1.34-10.21a1.55 1.55 0 11-.01-3.1 1.55 1.55 0 01.01 3.1zm11.58 10.21h-2.71v-4.29c0-1.08-.02-2.47-1.51-2.47s-1.74 1.18-1.74 2.4V18h-2.71V9h2.61v1.23h.04c.36-.68 1.23-1.4 2.52-1.4 2.7 0 3.2 1.77 3.2 4.06V18z M9.75 15.02V8.98l5.5 3.02-5.5 3.02z" />
      </svg>
    ),
    Twitter: (h: number, w: number) => <X className="h-8 w-8" size={w} />,
    Instagram: (h: number, w: number) => (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="#E4405F"
        width={w}
        height={h}
      >
        <path d="M7.75 2h8.5A5.75 5.75 0 0122 7.75v8.5A5.75 5.75 0 0116.25 22h-8.5A5.75 5.75 0 012 16.25v-8.5A5.75 5.75 0 017.75 2zm8.5 2h-8.5A3.75 3.75 0 004 7.75v8.5A3.75 3.75 0 007.75 20h8.5A3.75 3.75 0 0020 16.25v-8.5A3.75 3.75 0 0016.25 4zm-4.25 5.25a3.75 3.75 0 110 7.5 3.75 3.75 0 010-7.5zm0 2a1.75 1.75 0 100 3.5 1.75 1.75 0 000-3.5zm4.5-3.5a1 1 0 110 2 1 1 0 010-2z" />
      </svg>
    ),
    YouTube: (h: number, w: number) => (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="#FF0000"
        width={w}
        height={h}
      >
        <path d="M21.8 8.01s-.2-1.42-.8-2.04a3.36 3.36 0 00-2.42-.83C16.92 5 12 5 12 5s-4.92 0-6.56.14a3.36 3.36 0 00-2.42.83C2.4 6.59 2.2 8.01 2.2 8.01S2 9.86 2 11.7v.6c0 1.84.2 3.69.2 3.69s.2 1.42.8 2.04a3.36 3.36 0 002.42.83c1.64.14 6.56.14 6.56.14s4.92 0 6.56-.14a3.36 3.36 0 002.42-.83c.6-.62.8-2.04.8-2.04s.2-1.84.2-3.69v-.6c0-1.84-.2-3.69-.2-3.69zM9.75 15.02V8.98l5.5 3.02-5.5 3.02z" />
      </svg>
    ),
    Discord: (h: number, w: number) => (
      <svg
        width={w}
        height={h}
        viewBox="0 -28.5 256 256"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="xMidYMid"
      >
        <g>
          <path
            d="M216.856339,16.5966031 C200.285002,8.84328665 182.566144,3.2084988 164.041564,0 C161.766523,4.11318106 159.108624,9.64549908 157.276099,14.0464379 C137.583995,11.0849896 118.072967,11.0849896 98.7430163,14.0464379 C96.9108417,9.64549908 94.1925838,4.11318106 91.8971895,0 C73.3526068,3.2084988 55.6133949,8.86399117 39.0420583,16.6376612 C5.61752293,67.146514 -3.4433191,116.400813 1.08711069,164.955721 C23.2560196,181.510915 44.7403634,191.567697 65.8621325,198.148576 C71.0772151,190.971126 75.7283628,183.341335 79.7352139,175.300261 C72.104019,172.400575 64.7949724,168.822202 57.8887866,164.667963 C59.7209612,163.310589 61.5131304,161.891452 63.2445898,160.431257 C105.36741,180.133187 151.134928,180.133187 192.754523,160.431257 C194.506336,161.891452 196.298154,163.310589 198.110326,164.667963 C191.183787,168.842556 183.854737,172.420929 176.223542,175.320965 C180.230393,183.341335 184.861538,190.991831 190.096624,198.16893 C211.238746,191.588051 232.743023,181.531619 254.911949,164.955721 C260.227747,108.668201 245.831087,59.8662432 216.856339,16.5966031 Z M85.4738752,135.09489 C72.8290281,135.09489 62.4592217,123.290155 62.4592217,108.914901 C62.4592217,94.5396472 72.607595,82.7145587 85.4738752,82.7145587 C98.3405064,82.7145587 108.709962,94.5189427 108.488529,108.914901 C108.508531,123.290155 98.3405064,135.09489 85.4738752,135.09489 Z M170.525237,135.09489 C157.88039,135.09489 147.510584,123.290155 147.510584,108.914901 C147.510584,94.5396472 157.658606,82.7145587 170.525237,82.7145587 C183.391518,82.7145587 193.761324,94.5189427 193.539891,108.914901 C193.539891,123.290155 183.391518,135.09489 170.525237,135.09489 Z"
            fill="#5865F2"
            fill-rule="nonzero"
          ></path>
        </g>
      </svg>
    ),
    Facebook: (h: number, w: number) => (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="#1877F2"
        width={w}
        height={h}
      >
        <path d="M22 12.07C22 6.48 17.52 2 12 2S2 6.48 2 12.07c0 5 3.66 9.13 8.44 9.88v-6.98h-2.54v-2.9h2.54v-2.2c0-2.51 1.49-3.9 3.77-3.9 1.09 0 2.23.2 2.23.2v2.43h-1.26c-1.24 0-1.63.77-1.63 1.56v1.9h2.78l-.44 2.9h-2.34v6.98c4.78-.75 8.44-4.88 8.44-9.88z" />
      </svg>
    ),
    Other: (h: number, w: number) => <></>,
  };

  const getTierGroup = async () => {
    const { data, errors } = await API.PLAN_TIER_API.GetTierGroup({
      InviteCode: tierGroupId!,
    });
    if (data) {
      setTierGroup(data);
    }
    if (errors) {
      console.log(errors);
    }
  };

  const getTenant = async (subdomain: string) => {
    const { data, errors } = await API.TENANT.GetTenantByDomain(subdomain);
    if (data) {
      setTenant(data);
    }
    if (errors) {
      console.log(errors);
    }
  };

  useEffect(() => {
    if (tierGroupId) {
      getTierGroup();
    }
    if (planId) {
      getTenant(domain!);
    }
  }, [tierGroupId, domain, planId]);

  const buttonStyles = {
    default: cn(
      "h-12 bg-zinc-900 dark:bg-zinc-100",
      "hover:bg-zinc-50 dark:hover:bg-zinc-800",
      "text-zinc-100 dark:text-zinc-900 hover:text-zinc-900 dark:hover:text-zinc-100",
      "border border-zinc-200 dark:border-zinc-800",
      "hover:border-zinc-700 dark:hover:border-zinc-300",
      "shadow-sm hover:shadow-md",
      "text-sm font-medium"
    ),
    highlight: cn(
      "h-12 bg-zinc-900 dark:bg-zinc-100",
      "hover:bg-zinc-800 dark:hover:bg-zinc-300",
      "text-zinc-100 dark:text-zinc-900",
      "shadow-[0_1px_15px_rgba(0,0,0,0.1)]",
      "hover:shadow-[0_1px_20px_rgba(0,0,0,0.15)]",
      "font-semibold text-base"
    ),
  };

  const images = tenant?.Pictures?.length
    ? tenant?.Pictures
    : [
        "https://6512psv07j.ufs.sh/f/etAgn58tC72WVYHZ78EIKl5BN3twmjURQXgrJCGiDf68MFod",
        "https://6512psv07j.ufs.sh/f/etAgn58tC72Wu3ViWRZDyrqU1g0KHdCTiczGOAnlMWeShjR7",
        "https://6512psv07j.ufs.sh/f/etAgn58tC72WMxEbkDA40SWRJBZ9ODz6klTxwFHsgKGadc78",
      ];

  const [userData, setUserData] = useState({
    Name: "",
    Email: "",
    Password: "",
    PhoneNumber: "",
    Gender: "Male",
    DateOfBirth: "",
    Address: "",
    ProfilePicture: "",
    ZoneInfo: "",
    Locale: "",
    GivenName: "",
    FamilyName: "",
    MiddleName: "",
    Website: "",
  });

  const handleSubmit = async (
    selectedPlanId?: string,
    fieldValues?: any,
    selectedVariant?: string
  ) => {
    const values = fieldValues ?? userData;
    // For generic sign-up, selectedPlan will be null, so PlanID: null will be sent.
    // Ensure your API.AUTH.SignUp can handle { FullName, Email, Password, PhoneNumber, PlanID: null | string }
    const { data, errors } = await API.AUTH.SignUp({
      Name: values.Name,
      Email: values.Email,
      Password: values.Password,
      PhoneNumber: values.PhoneNumber,
      DateOfBirth: values.DateOfBirth,
      Address: values.Address,
      ProfilePicture: values.ProfilePicture,
      ZoneInfo: values.ZoneInfo,
      Locale: values.Locale,
      GivenName: values.GivenName,
      FamilyName: values.FamilyName,
      MiddleName: values.MiddleName,
      Website: values.Website,
      PlanID: selectedPlanId || planId || "",
      AppType: "Industry",
      Variant: selectedVariant || variant || "",
    });
    if (errors) {
      errors.forEach((error) => toast.error(error.message));
      return;
    }
    toast.success(data?.message || "Sign up successful!");
    // selectedPlan is likely null here if it's a generic signup, so no need to setSelectedPlan(null)
    setTimeout(() => {
      signIn(values.Email, values.Password);
    }, 1000); // Add a delay before sign-in to allow backend processing
  };

  const SignUp = () => {
    const [fieldValues, setFieldValues] = useState(userData);
    return (
      <div className="mx-auto w-full flex flex-col space-y-6 max-w-lg">
        <div className="w-full">
          <div
            className={cn(
              "flex gap-2 flex-col items-center",
              tenant?.Personalization?.HeaderPlacing
            )}
          >
            <div>
              {tenant?.Personalization?.ShowTenantLogo &&
                (tenant?.Logo?.length ? (
                  <img
                    src={tenant?.Logo}
                    alt={tenant?.Name}
                    className="object-contain h-full max-w-[220px] max-h-[60px]"
                  />
                ) : (
                  <Orb
                    hoverIntensity={0}
                    rotateOnHover={false}
                    hue={20}
                    forceHoverState={true}
                  />
                ))}
            </div>
            {tenant?.Personalization?.ShowTenantName && (
              <AuroraText className="text-2xl font-bold">
                {tenant?.Name}
              </AuroraText>
            )}
            {tenant?.Personalization?.ShowWebsiteURL && (
              <Badge
                variant="outline"
                className="cursor-pointer mt-2 "
                onClick={() => window.open(tenant?.WebsiteURL, "_blank")}
              >
                {new URL(tenant?.WebsiteURL || "").hostname}
              </Badge>
            )}
            {tenant?.Personalization?.ShowSocialCards && (
              <div className="flex gap-2 items-center">
                {tenant?.SocialCards?.map((card, i) => (
                  <Link href={card?.URL} key={i}>
                    {
                      PlatformIcons?.[
                        card?.Platform as keyof typeof PlatformIcons
                      ]?.(28, 28) as ReactNode
                    }
                  </Link>
                ))}
              </div>
            )}
          </div>
          <div className="flex flex-col gap-1 mt-10">
            <h1 className="text-xl md:text-2xl font-bold tracking-tight text-foreground">
              Create an Account
            </h1>
            <p className="text-muted-foreground">
              {tenant?.Personalization?.ShowTagline && tenant?.Tagline}
            </p>
          </div>
        </div>
        <form
          className="max-w-full w-full space-y-6"
          onSubmit={(e) => {
            e.preventDefault();
            setUserData(fieldValues);
            if (planId) {
              handleSubmit(planId, fieldValues);
            }
            if (tierGroupId) {
              setContinueToPlan(true);
            }
          }}
        >
          <div className="flex gap-2">
            <div className="flex-1 space-y-2">
              <Label htmlFor="name" className="text-foreground">
                First Name
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="John"
                value={fieldValues.Name}
                onChange={(e) =>
                  setFieldValues({ ...fieldValues, Name: e.target.value })
                }
                required
                className="mt-1 bg-input text-foreground border-border placeholder:text-muted-foreground"
              />
            </div>
            <div className="flex-1 space-y-2">
              <Label htmlFor="name" className="text-foreground">
                Last Name
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="Doe"
                value={fieldValues.FamilyName}
                onChange={(e) =>
                  setFieldValues({ ...fieldValues, FamilyName: e.target.value })
                }
                className="mt-1 bg-input text-foreground border-border placeholder:text-muted-foreground"
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="email" className="text-foreground">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={fieldValues.Email}
              onChange={(e) =>
                setFieldValues({
                  ...fieldValues,
                  Email: e.target.value.toLowerCase().trim().replace(/\s/g, ""),
                })
              }
              required
              className="mt-1 bg-input text-foreground border-border placeholder:text-muted-foreground"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone" className="text-foreground">
              Phone Number (Optional)
            </Label>
            <Input
              id="phone"
              type="tel"
              placeholder="+1234567890"
              value={fieldValues.PhoneNumber}
              onChange={(e) =>
                setFieldValues({
                  ...fieldValues,
                  PhoneNumber: e.target.value,
                })
              }
              className="mt-1 bg-input text-foreground border-border placeholder:text-muted-foreground"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password" className="text-foreground">
              Password
            </Label>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              value={fieldValues.Password}
              onChange={(e) =>
                setFieldValues({
                  ...fieldValues,
                  Password: e.target.value,
                })
              }
              required
              className="mt-1 bg-input text-foreground border-border placeholder:text-muted-foreground"
            />
          </div>
          <Button type="submit" className="w-full h-11 text-base">
            <ArrowRight className="w-4 h-4" />
            {planId ? "Complete Sign Up" : "Continue to Choose Plan"}
          </Button>
        </form>
        <p className="text-sm text-muted-foreground">
          Already have an account?{" "}
          <Link
            href="/sign-in"
            className="font-medium text-primary hover:underline"
          >
            Sign In
          </Link>
        </p>
      </div>
    );
  };

  const TierGroup = () => {
    const allPlans = tierGroup?.Plans || [];
    const [selectedPlan, setSelectedPlan] = useState<TPlan | null>(null);
    const uniqueVariants = useCallback(() => {
      const Durations = {
        Monthly: 30,
        Yearly: 365,
        Daily: 1,
        Weekly: 7,
      };
      // After sorting by duration then all other variants
      const SortOrder = ["Daily", "Weekly", "Monthly", "Yearly"];
      const vars = [
        ...new Set(
          allPlans
            ?.map((plan) =>
              plan.PricingStructures?.map(
                (pricingStructure) => pricingStructure.Type
              )
            )
            .flat()
        ),
      ].sort(
        (a, b) =>
          SortOrder.indexOf(a) - SortOrder.indexOf(b) ||
          Durations[a as keyof typeof Durations] -
            Durations[b as keyof typeof Durations]
      );
      return vars;
    }, [allPlans]);
    const [variant, setVariant] = useState(uniqueVariants()[0]);
    const [selectedVariant, setSelectedVariant] = useState<string | null>(null);
    const plans = useCallback(
      () =>
        allPlans.filter((plan) =>
          plan.PricingStructures?.some(
            (pricingStructure) => pricingStructure.Type === variant
          )
        ),
      [allPlans, variant]
    );

    const [current, setCurrent] = useState(0);
    const [count, setCount] = useState(0);
    const [api, setApi] = useState<CarouselApi>();

    const Plan = ({ plan, variant }: { plan: TPlan; variant: string }) => {
      return (
        <div
          key={plan.Name}
          className={cn(
            "relative group backdrop-blur-sm",
            "rounded-3xl transition-all duration-300",
            "flex flex-col",
            "bg-white/30 dark:bg-zinc-800/50",
            "border",
            "hover:translate-y-0 hover:shadow-lg",
            "h-full",
            plan.PlanID === selectedPlan?.PlanID &&
              selectedVariant === variant &&
              "border-primary shadow-lg bg-blue-50 dark:bg-primary/10 shadow-blue-500/50 dark:shadow-blue-500/50"
          )}
        >
          <div className="p-6 flex-1">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-md font-semibold text-zinc-900 dark:text-zinc-100">
                {plan.Name}
              </h3>
              <div
                className={cn(
                  "p-1 px-2 rounded-sm",
                  "bg-zinc-100/20 text-white"
                )}
              >
                {plan.Tier}
              </div>
            </div>

            <div className="mb-4">
              <div className="flex items-baseline gap-2">
                <span className="text-4xl font-bold text-zinc-900 dark:text-zinc-100">
                  {new Intl.NumberFormat("en-IN", {
                    style: "currency",
                    currency: "INR",
                  }).format(
                    (plan.PricingStructures.find((ps) => ps.Type === variant)
                      ?.Price as number) / 100
                  )}
                </span>
                <span className="text-md text-zinc-500 dark:text-zinc-400">
                  / {variant}
                </span>
              </div>
              <p className="mt-2 text-sm text-zinc-600 dark:text-zinc-400">
                {plan.Description}
              </p>
            </div>

            <div className="space-y-4 max-h-[300px] overflow-y-auto scrollbar-thin">
              {plan.Features.map((feature) => (
                <div key={feature.Title} className="flex gap-4">
                  <div
                    className={cn(
                      "mt-1 p-0.5 rounded-full transition-colors duration-200",
                      "text-emerald-600 dark:text-emerald-400"
                    )}
                  >
                    <CheckIcon className="w-4 h-4" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                      {feature.Title}
                    </div>
                    <div className="text-xs text-zinc-500 dark:text-zinc-400">
                      {feature.HelpText}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="p-4 pt-0 mt-auto">
            <Button
              className={cn(
                "w-full relative transition-all duration-300",
                buttonStyles.default,
                selectedPlan?.PlanID === plan.PlanID &&
                  selectedVariant === variant
                  ? "!bg-transparent !text-primary !border-1 !border-primary"
                  : ""
              )}
              onClick={() => {
                setSelectedPlan(plan);
                setSelectedVariant(variant || "");
              }}
            >
              <span className="relative z-10 flex items-center justify-center gap-2">
                {selectedPlan?.PlanID === plan.PlanID &&
                selectedVariant === variant
                  ? "Plan Selected"
                  : "Select Plan"}
                {selectedPlan?.PlanID === plan.PlanID &&
                selectedVariant === variant ? (
                  <CheckIcon className="w-4 h-4" />
                ) : (
                  <CircleDot className="w-4 h-4" />
                )}
              </span>
            </Button>
          </div>
        </div>
      );
    };

    useEffect(() => {
      if (!api) {
        return;
      }

      setCount(api.scrollSnapList().length);
      setCurrent(api.selectedScrollSnap() + 1);

      api.on("select", () => {
        setCurrent(api.selectedScrollSnap() + 1);
      });
    }, [api]);
    return (
      <div className="flex min-h-screen flex-col items-center bg-muted w-full relative justify-center">
        <div className="absolute left-0 top-1/2 -translate-y-1/2 h-screen w-full opacity-20 pointer-events-none">
          <Orb
            hoverIntensity={0}
            rotateOnHover={false}
            hue={20}
            forceHoverState={true}
          />
        </div>
        <main className="flex-1 flex flex-col items-center gap-8 px-4 mt-6">
          <section className="container px-4">
            <div className="flex flex-col items-center text-center space-y-1">
              <h1 className="text-xl md:text-2xl font-bold tracking-tight max-w-3xl font-bold text-primary">
                {tierGroup?.Name}
              </h1>
              <p className="text-lg max-w-xl">{tierGroup?.Description}</p>
            </div>
          </section>
          <div className="inline-flex items-center p-1.5 bg-white dark:bg-zinc-800/50 rounded-full border border-zinc-200 dark:border-zinc-700 shadow-sm">
            {uniqueVariants()?.map((period) => (
              <button
                key={period}
                onClick={() => setVariant(period)}
                className={cn(
                  "px-8 py-2 text-sm font-medium rounded-full transition-all duration-300",
                  period === variant
                    ? "bg-zinc-900 dark:bg-zinc-100 text-white dark:text-zinc-900 shadow-lg"
                    : "text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100"
                )}
              >
                {period}
              </button>
            ))}
          </div>

          <section className="w-full flex items-center justify-center gap-1 lg:gap-6">
            {plans()?.length > 0 && (
              <Button
                variant="outline"
                onClick={() => api?.scrollPrev()}
                disabled={current === 1}
                className="gap-2 hidden lg:flex"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            <Carousel
              className="max-w-xs md:max-w-2xl lg:max-w-4xl xl:max-w-7xl"
              setApi={setApi}
            >
              <CarouselContent>
                {plans()?.map((plan) => (
                  <CarouselItem
                    key={plan.PlanID}
                    className="basis-1/1 max-w-sm md:basis-1/2 md:max-w-md lg:basis-1/3 min-w-sm"
                  >
                    <Plan plan={plan} variant={variant || ""} />
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>

            {plans()?.length > 0 && (
              <Button
                variant="outline"
                onClick={() => api?.scrollNext()}
                disabled={current === count}
                className="gap-2 hidden lg:flex"
              >
                <ArrowRight className="h-4 w-4" />
              </Button>
            )}

            {plans()?.length === 0 && (
              <div className="flex flex-col items-center justify-center mt-10">
                <Rabbit strokeWidth={1} size={100} />
                <p className="text-muted-foreground text-2xl">
                  No plans available
                </p>
              </div>
            )}
          </section>
          <span className="text-center text-muted-foreground lg:hidden">
            {current} / {plans()?.length}
          </span>
          <div className="flex flex-col gap-4 md:flex-row">
            <Button
              size="lg"
              className={cn("relative transition-all duration-300")}
              variant="outline"
              onClick={() => setContinueToPlan(false)}
            >
              <ArrowLeft className="h-4 w-4" />
              Edit Sign Up
            </Button>
            <Button
              size="lg"
              className={cn("min-w-60 relative transition-all duration-300")}
              disabled={selectedPlan === null}
              onClick={() =>
                handleSubmit(
                  selectedPlan?.PlanID,
                  userData,
                  selectedVariant ?? undefined
                )
              }
            >
              Complete Sign Up
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </main>
      </div>
    );
  };

  const isMobile = useIsMobile();

  return (
    <div className="flex min-h-screen flex-col w-full">
      <div className="absolute top-4 right-4 z-50">
        <ThemeToggle />
      </div>

      {!continueToPlan && (
        <main className="flex w-full min-h-screen">
          <div
            className={`flex-1 relative flex w-full flex-col items-center justify-center overflow-hidden h-screen ${isMobile ? "hidden" : ""}`}
          >
            <div
              className="flex flex-row items-center gap-4"
              style={{
                transform:
                  "translateX(-50px) translateY(0px) translateZ(-100px) rotateX(20deg) rotateY(-10deg) rotateZ(20deg)",
              }}
            >
              <Marquee vertical className="[--duration:60s]">
                {images.reverse().map((image) => (
                  <figure
                    key={image}
                    className="rounded-lg overflow-hidden max-h-[400px]"
                  >
                    <Image
                      src={image}
                      alt=""
                      className="w-full h-full object-cover"
                      unoptimized
                      width={100}
                      height={100}
                    />
                  </figure>
                ))}
              </Marquee>
              <Marquee vertical className="[--duration:100s]">
                {images.map((image) => (
                  <figure
                    key={image}
                    className="rounded-lg overflow-hidden max-h-[400px]"
                  >
                    <Image
                      src={image}
                      alt=""
                      className="w-full h-full object-cover"
                      width={100}
                      height={100}
                      unoptimized
                    />
                  </figure>
                ))}
              </Marquee>
            </div>
            <div className="pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r from-zinc-500/20 dark:from-zinc-800/60"></div>
            <div className="pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l from-zinc-500/20 dark:from-zinc-800"></div>
          </div>
          <div className="max-w-2xl bg-background/50 backdrop-blur-md w-full p-6 sm:p-8 space-y-6 shadow-xl sm:rounded-lg flex-1 flex flex-col justify-center">
            <SignUp />
          </div>
        </main>
      )}
      {!planId && continueToPlan && <TierGroup />}
    </div>
  );
}

// Main component with Suspense boundary
export default function SignUpPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
        </div>
      }
    >
      <SignUpPageContent />
    </Suspense>
  );
}
