from dataclasses import dataclass, field
from agents.common.config import Config


@dataclass(kw_only=True)
class Config(Config):
    """Configuration for the embedding agent"""

    embedding_model: str = field(
        default="text-embedding-3-large",
        metadata={"help": "The model to use for the embedding"},
    )

    embedding_dimensions: int = field(
        default=1536,
        metadata={"help": "The number of dimensions to use for the embedding"},
    )
