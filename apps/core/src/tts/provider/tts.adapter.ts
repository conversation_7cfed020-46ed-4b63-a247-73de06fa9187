import { ConfigService } from '@nestjs/config';
import { IGenerateCostEstimationParams, ISynthesizeSpeechParams } from './_.blueprint';
import ElevenLabsTTSAdapter from './elevenlabs.tts';
import { GoogleCloudTTSAdapter, ISynthesizeSpeechGoogleParams } from './google.tts';

export interface IBaseParams {
  Provider: 'Google' | 'ElevenLabs';
}

class TTSAdapter {
  private Providers = {
    Google: new GoogleCloudTTSAdapter(this.ConfigService),
    ElevenLabs: new ElevenLabsTTSAdapter(this.ConfigService),
  };
  constructor(private readonly ConfigService: ConfigService) {}

  private ProvidersKeys = Object.keys(this.Providers);

  async ListVoices(params: IBaseParams) {
    if (!this.ProvidersKeys.includes(params.Provider)) {
      throw new Error('Provider not supported');
    }
    return this.Providers[params.Provider].ListVoices(params);
  }

  async SynthesizeSpeech(params: ISynthesizeSpeechParams & IBaseParams & ISynthesizeSpeechGoogleParams) {
    if (!this.ProvidersKeys.includes(params.Provider)) {
      throw new Error('Provider not supported');
    }
    return this.Providers[params.Provider].SynthesizeSpeech(params);
  }

  async GenerateCostEstimation(params: IGenerateCostEstimationParams & IBaseParams) {
    if (!this.ProvidersKeys.includes(params.Provider)) {
      throw new Error('Provider not supported');
    }
    return this.Providers[params.Provider].GenerateCostEstimation(params);
  }
}

export default TTSAdapter;
