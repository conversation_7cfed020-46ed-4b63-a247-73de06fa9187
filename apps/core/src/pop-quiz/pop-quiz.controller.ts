import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { TenantID, User } from '../../common/decorators/core.decorators';
import { AuthAdminGuard, AuthSubscriberGuard } from '../../common/guards/auth.guard';
import { TUser } from '../core/user/entities/user.entity';
import {
  CreatePopQuizRequestDto,
  CreateQuestionBulkRequestDto,
  CreateQuestionRequestDto,
  GenerateSelfQuizRequestDto,
  ListPopQuizRequestDto,
  ListQuestionsRequestDto,
  UpdatePopQuizRequestDto,
  UpdateQuestionRequestDto,
} from './dto/pop-quiz.controller.dto';
import { CreatePopQuizResponseDto, GenerateSelfQuizDto } from './dto/pop-quiz.service.dto';
import { PopQuizService } from './pop-quiz.service';
import { GenerateQuestionsBodyDto } from './dto/pop-quiz.controller.dto';
import { QuestionService } from './qustion.service';
import { ResponseService } from './response.service';

@UseGuards(AuthAdminGuard)
@Controller('pop-quiz/quiz')
export class PopQuizController {
  constructor(
    private readonly PopQuizService: PopQuizService,
    private readonly ResponseService: ResponseService
  ) {}

  @Post()
  async CreatePopQuiz(@Body() Params: CreatePopQuizRequestDto, @TenantID() TenantID: string) {
    const popQuiz = await this.PopQuizService.CreatePopQuiz({
      ...Params,
      TenantID,
    });
    return {
      message: 'Pop Quiz created successfully',
      ack: popQuiz,
    };
  }

  @Post('generate')
  async GeneratePopQuizRandomQuestions(@Body() Params: CreatePopQuizRequestDto, @TenantID() TenantID: string) {
    const questions = await this.PopQuizService.GeneratePopQuiz({
      ...Params,
      TenantID,
    });
    return {
      message: 'Pop Quiz generated successfully',
      ack: questions,
    };
  }

  @Patch(':PopQuizID')
  async UpdatePopQuiz(
    @Body() Params: UpdatePopQuizRequestDto,
    @Param('PopQuizID') PopQuizID: string,
    @TenantID() TenantID: string
  ) {
    const popQuiz = await this.PopQuizService.UpdatePopQuiz({
      ...Params,
      PopQuizID,
      TenantID,
    });
    return {
      message: 'Pop Quiz updated successfully',
      ack: popQuiz,
    };
  }

  @Get()
  async ListPopQuiz(@Query() Params: ListPopQuizRequestDto, @TenantID() TenantID: string) {
    const popQuiz = await this.PopQuizService.ListPopQuiz({
      ...Params,
      TenantID,
    });
    return popQuiz;
  }

  @Get(':PopQuizID')
  async GetPopQuiz(@Param('PopQuizID') PopQuizID: string, @TenantID() TenantID: string) {
    const popQuiz = await this.PopQuizService.GetPopQuiz({
      PopQuizID,
      TenantID,
    });
    return popQuiz;
  }

  @Get(':PopQuizID/responses')
  async GetResponsesForPopQuiz(@Param('PopQuizID') PopQuizID: string, @TenantID() TenantID: string) {
    const responses = await this.ResponseService.GetResponsesForPopQuiz({
      PopQuizID,
      TenantID,
    });
    return responses;
  }
}

@UseGuards(AuthAdminGuard)
@Controller('pop-quiz/question')
export class QuestionController {
  constructor(private readonly QuestionService: QuestionService) {}

  @Post()
  async CreateQuestion(@Body() Params: CreateQuestionRequestDto, @TenantID() TenantID: string) {
    const question = await this.QuestionService.CreateQuestion({
      ...Params,
      TenantID,
    });
    return {
      message: 'Question created successfully',
      ack: question,
    };
  }

  @Post('bulk')
  async CreateQuestionBulk(@Body() Params: CreateQuestionBulkRequestDto, @TenantID() TenantID: string) {
    const questions = await this.QuestionService.CreateQuestionBulk(
      Params.Questions.map((question) => ({
        ...question,
        TenantID,
      }))
    );
    return {
      message: 'Questions created successfully',
      ack: questions,
    };
  }

  @Patch(':QuestionID')
  async UpdateQuestion(
    @Body() Params: UpdateQuestionRequestDto,
    @Param('QuestionID') QuestionID: string,
    @TenantID() TenantID: string
  ) {
    const question = await this.QuestionService.UpdateQuestion({
      ...Params,
      QuestionID,
      TenantID,
    });
    return {
      message: 'Question updated successfully',
      ack: question,
    };
  }

  @Delete(':QuestionID/:ProjectID')
  async DeleteQuestion(
    @Param('QuestionID') QuestionID: string,
    @TenantID() TenantID: string,
    @Param('ProjectID') ProjectID: string
  ) {
    const question = await this.QuestionService.DeleteQuestion({
      QuestionID,
      TenantID,
      ProjectID,
    });
    return {
      message: 'Question deleted successfully',
      ack: question,
    };
  }

  @Get(':QuestionID')
  async GetQuestion(@Param('QuestionID') QuestionID: string, @TenantID() TenantID: string) {
    const question = await this.QuestionService.GetQuestion({
      QuestionID,
      TenantID,
    });
    return question;
  }

  @Get()
  async ListQuestions(
    @Query() Params: ListQuestionsRequestDto,
    @TenantID() TenantID: string,
    @Query('ProjectID') ProjectID: string
  ) {
    const questions = await this.QuestionService.ListQuestions({
      ...Params,
      TenantID,
      ProjectID,
    });
    return questions;
  }

  @Post('/generate')
  async GenerateQuizes(@Body() Body: GenerateQuestionsBodyDto, @TenantID() TenantID: string, @User() User: TUser) {
    const response = await this.QuestionService.GenerateQuestions(Body, TenantID, User.UserID, User.AppType);
    return {
      message: 'Questions generated successfully',
      ack: response,
    };
  }
}

@UseGuards(AuthSubscriberGuard)
@Controller('pop-quiz/response')
export class ResponseController {
  constructor(private readonly responseService: ResponseService) {}

  @Get('pre-flight')
  async PreFlightGetQuiz(@TenantID() TenantID: string, @User() User: TUser) {
    const popQuiz = await this.responseService.PreFlightPopQuiz({
      TenantID,
      UserID: User.UserID,
    });
    return popQuiz;
  }

  @Get('list-quizes')
  async ListMyQuizes(@TenantID() TenantID: string, @User() User: TUser, @Query('ProjectID') ProjectID?: string) {
    const popQuiz = await this.responseService.ListMyQuizes({
      TenantID,
      UserID: User.UserID,
      ProjectID,
    });
    return popQuiz;
  }

  @Post('generate-self-quiz')
  async GenerateSelfQuiz(
    @Body() Params: GenerateSelfQuizRequestDto,
    @TenantID() TenantID: string,
    @User() User: TUser
  ) {
    const response = await this.responseService.GenerateSelfQuiz(
      {
        ...Params,
        TenantID,
        UserID: User.UserID,
      },
      User.AppType
    );
    return response;
  }

  @Post()
  async CreateResponse(@Body() Params: CreatePopQuizResponseDto, @TenantID() TenantID: string, @User() User: TUser) {
    const response = await this.responseService.CreateResponse({
      ...Params,
      TenantID,
      UserID: User.UserID,
    });
    return {
      message: 'Quiz Response created successfully',
      ack: response,
    };
  }

  @Get(':ResponseID')
  async GetResponse(@Param('ResponseID') ResponseID: string, @TenantID() TenantID: string, @User() User: TUser) {
    const response = await this.responseService.GetResponse({
      ResponseID,
      UserID: User.UserID,
      TenantID,
    });
    return response;
  }

  @Get()
  async GetMyResponses(@TenantID() TenantID: string, @User() User: TUser, @Query('ProjectID') ProjectID?: string) {
    const responses = await this.responseService.GetPopQuizResponsesForUser({
      TenantID,
      UserID: User.UserID,
      ProjectID,
    });
    return responses;
  }
}
