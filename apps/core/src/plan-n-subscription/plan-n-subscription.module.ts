import { Module } from '@nestjs/common';
import { PlanNSubscriptionService } from './plan-n-subscription.service';
import {
  PlanNSubscriptionController,
  PlanNSubscriptionSubscriberController,
  PlanNSubscriptionTierGroupController,
} from './plan-n-subscription.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { PlanModel, PlanSchema } from './schemas/plan.schema';
import { SubscriptionModel, SubscriptionSchema } from './schemas/subscription.schema';
import { TTSModule } from '../tts/tts.module';
import { ContentAlbumModule } from '../content-album/content-album.module';
import { TierGroupModel, TierGroupSchema } from './schemas/tier-group.schema';
import { UserModule } from '../core/user/user.module';
import { PaymentModule } from '../payment/payment.module';
import { CreditUsageModel, CreditUsageSchema } from './schemas/credit-usage.schema';
import { TenantModule } from '../tenant/tenant.module';

@Module({
  imports: [
    TenantModule,
    PaymentModule,
    UserModule,
    ContentAlbumModule,
    TTSModule,
    MongooseModule.forFeature([
      { name: PlanModel.name, schema: PlanSchema },
      { name: SubscriptionModel.name, schema: SubscriptionSchema },
      { name: TierGroupModel.name, schema: TierGroupSchema },
      { name: CreditUsageModel.name, schema: CreditUsageSchema },
    ]),
  ],
  controllers: [
    PlanNSubscriptionController,
    PlanNSubscriptionSubscriberController,
    PlanNSubscriptionTierGroupController,
  ],
  providers: [PlanNSubscriptionService],
  exports: [PlanNSubscriptionService],
})
export class PlanNSubscriptionModule {}
