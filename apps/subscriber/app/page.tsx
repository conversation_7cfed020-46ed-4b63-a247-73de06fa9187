"use client";
import AccessMode from "@/components/auth/mode-access";
import ChatInput from "@/components/chat/chat-input";
import { useAuth } from "@/contexts/auth-context";
import { useModeContext } from "@/contexts/mode-context";
import { useProjectContext } from "@/contexts/project-context";
import { useThreadContext } from "@/contexts/thread-context";
import { Button } from "@workspace/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Separator } from "@workspace/ui/components/separator";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  AlertCircle,
  BookOpen,
  CircleHelp,
  Clock,
  History,
  Loader2,
  MessageCircle,
  RefreshCw,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

export default function Page() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const {
    projects,
    isLoading: projectsLoading,
    error: projectsError,
    selectedProject,
    setSelectedProject,
    refetchProjects,
  } = useProjectContext();
  const { refetchThreads, threads } = useThreadContext();
  const { mode, setMode } = useModeContext();

  const [startConversation, setStartConversation] = useState(false);
  const [message, setMessage] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleStartConversation = async (message: string) => {
    if (!selectedProject || !message.trim() || isSubmitting) return;
    setIsSubmitting(true);
    router.push(`/thread/new-thread?message=${message}`);
  };

  const isLoading = authLoading || projectsLoading;

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-svh">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
      </div>
    );
  }

  const GreetingMessages = () => [
    "Sup nerds! Ready for another round of all-nighters?",
    "Hey! Back to the grind – grab coffee, we got this!",
    "Yo, class is back – time to pretend we know what's happening!",
    "Squad's back together! Let's ace this semester (or at least pass it)!",
    "What's up! Same classes, different excuses – let's roll!",
    "Hey fam! Another day of looking interested while daydreaming!",
    "Guess who's back? Still broke, still sleepy, still awesome!",
    "Hey! Bringing snacks to class counts as being prepared, right?",
    "Yo! If we're gonna fail, let's fail together in style!",
    "Hey there! Time to dust off those notebooks you swore you'd use last time!",
    "Squad goals: actually staying awake in morning lectures!",
    "Wassup! Let's make some memories between all those deadlines!",
    "Back at it! Coffee in one hand, phone in the other – classic student life!",
    "Hey! Just smile and nod when the prof asks questions!",
    "Yo! Another semester of saying 'I'll start studying earlier this time'!",
  ];

  return (
    <div className="flex flex-col items-center justify-center h-full">
      <div className="flex flex-col items-center  gap-4 w-full">
        <h1 className="text-2xl font-bold">
          Hi, <span className="text-primary">{user?.Name}</span>
        </h1>
        <h2 className="text-lg font-semibold text-center text-secondary-foreground mb-4">
          {
            GreetingMessages()[
              Math.floor(Math.random() * GreetingMessages().length)
            ]
          }
        </h2>

        <p className="text-muted-foreground text-md max-w-lg text-center text-sm">
          Select project to start a conversation. Project let's you chat with
          your tutor and ask anything related to your project.
        </p>

        <Select
          value={selectedProject?.id || ""}
          onValueChange={(value) => {
            const project = projects.find((p) => p.id === value) || null;
            setSelectedProject(project);
          }}
          disabled={projectsLoading}
        >
          <SelectTrigger className="w-sm ">
            {projectsLoading ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
                <span>Loading projects...</span>
              </div>
            ) : (
              <SelectValue placeholder="Select a Project" />
            )}
          </SelectTrigger>
          <SelectContent>
            {projectsLoading ? (
              <div className="p-4 space-y-2">
                <Skeleton className="h-5 w-full" />
                <Skeleton className="h-5 w-full" />
                <Skeleton className="h-5 w-full" />
              </div>
            ) : projects.length === 0 ? (
              <div className="p-2 text-center text-muted-foreground">
                No projects available
              </div>
            ) : (
              projects.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.name}
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>

        {isAuthenticated &&
          user &&
          (!startConversation ? (
            <div className="flex flex-col gap-4 w-full items-center justify-center max-w-lg mt-4">
              <div className="flex gap-2 mb-2 flex-col md:flex-row">
                <AccessMode isPopQuiz>
                  <div
                    className="flex flex-col items-center gap-2 border rounded-md p-4 hover:bg-primary/10 cursor-pointer hover:border-gray-400"
                    onClick={() => setMode("quiz")}
                  >
                    <div className="flex items-center gap-2">
                      <CircleHelp />
                      <h2 className="text-lg font-semibold">Pop Quiz</h2>
                    </div>
                    <p className="text-center text-muted-foreground text-xs">
                      Take a pop quiz to test your knowledge
                    </p>
                  </div>
                </AccessMode>
                <AccessMode isStory>
                  <div
                    className="flex flex-col items-center gap-2 border rounded-md p-4 hover:bg-primary/10 cursor-pointer hover:border-gray-400"
                    onClick={() => setMode("story")}
                  >
                    <div className="flex items-center gap-2">
                      <BookOpen />
                      <h2 className="text-lg font-semibold">Story Mode</h2>
                    </div>
                    <p className="text-center text-muted-foreground text-xs">
                      Explore video lectures and audio content
                    </p>
                  </div>
                </AccessMode>
              </div>

              <AccessMode isChat>
                <ChatInput
                  isDisabled={isSubmitting}
                  onSubmit={(message) => handleStartConversation(message)}
                />
                <div className="flex flex-col items-center justify-center gap-2 mt-2">
                  <h3 className="text-sm font-semibold flex items-center gap-2 text-gray-400">
                    <History size={18} />
                    Open Recent
                  </h3>
                  <div className="flex flex-col gap-[0.1rem]">
                    {threads
                      ?.sort((a, b) => {
                        return (
                          new Date(b.updatedAt || b.createdAt).getTime() -
                          new Date(a.updatedAt || a.createdAt).getTime()
                        );
                      })
                      .slice(0, 4)
                      .map((thread) => (
                        <div
                          key={thread.ThreadID}
                          className="flex items-center gap-2 rounded-lg px-2 py-1 hover:bg-primary/10 cursor-pointer"
                          onClick={() => {
                            router.push(`/thread/${thread.ThreadID}`);
                          }}
                        >
                          <MessageCircle size={16} />
                          <span className="text-sm text-gray-400 hover:text-white">
                            {thread.title}
                          </span>
                          <span className="text-xs  gap-1 flex items-center text-muted-foreground">
                            <Clock size={12} />
                            {new Date(
                              thread.updatedAt || thread.createdAt
                            ).toLocaleTimeString()}
                          </span>
                        </div>
                      ))}
                  </div>
                </div>
              </AccessMode>
            </div>
          ) : (
            <div className="border rounded-2xl w-[700px] p-4 space-y-4">
              {projectsError && (
                <div className="bg-destructive/10 p-3 rounded-md flex items-center gap-2 text-destructive">
                  <AlertCircle size={16} />
                  <span className="flex-1">{projectsError}</span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={refetchProjects}
                    className="h-8 gap-1"
                    disabled={projectsLoading}
                  >
                    <RefreshCw
                      size={14}
                      className={projectsLoading ? "animate-spin" : ""}
                    />
                    Retry
                  </Button>
                </div>
              )}

              <Select
                value={selectedProject?.id || ""}
                onValueChange={(value) => {
                  const project = projects.find((p) => p.id === value) || null;
                  setSelectedProject(project);
                }}
                disabled={projectsLoading || isSubmitting}
              >
                <SelectTrigger className="w-full">
                  {projectsLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
                      <span>Loading projects...</span>
                    </div>
                  ) : (
                    <SelectValue placeholder="Select a Project" />
                  )}
                </SelectTrigger>
                <SelectContent>
                  {projectsLoading ? (
                    <div className="p-4 space-y-2">
                      <Skeleton className="h-5 w-full" />
                      <Skeleton className="h-5 w-full" />
                      <Skeleton className="h-5 w-full" />
                    </div>
                  ) : projects.length === 0 ? (
                    <div className="p-2 text-center text-muted-foreground">
                      No projects available
                    </div>
                  ) : (
                    projects.map((project) => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              <Separator />
              <div className="flex gap-4 flex-col">
                <Textarea
                  placeholder="Enter your message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  disabled={isSubmitting}
                  className="min-h-[100px]"
                />
                <Button
                  onClick={() => handleStartConversation(message)}
                  disabled={
                    !selectedProject ||
                    !message.trim() ||
                    isLoading ||
                    isSubmitting
                  }
                  className="gap-2"
                >
                  {isSubmitting && (
                    <Loader2 size={16} className="animate-spin" />
                  )}
                  {isSubmitting
                    ? "Starting Conversation..."
                    : "Start New Conversation"}
                </Button>
              </div>
            </div>
          ))}
      </div>
    </div>
  );
}
