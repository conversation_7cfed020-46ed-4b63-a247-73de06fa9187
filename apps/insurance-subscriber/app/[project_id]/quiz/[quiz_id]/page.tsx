"use client";
import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { API } from "@/apis/api";
import {
  TPopQuiz,
  TResponse,
  TCreateResponseAnswer,
  TCitation,
} from "@/apis/pop-quiz.api";
import { useAuth } from "@/contexts/auth-context";
import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@workspace/ui/components/card";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@workspace/ui/components/carousel";
import { Progress } from "@workspace/ui/components/progress";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import {
  ArrowLeft,
  ArrowRight,
  Check,
  CircleX,
  Save,
  X,
  Home,
  ChevronDown,
  ChevronUp,
  BookO<PERSON>,
} from "lucide-react";
import { toast } from "sonner";
import { useProjectContext } from "@/contexts/project-context";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@workspace/ui/components/collapsible";
import { useQuizStore } from "@/components/pop-quiz/pop-quiz-list";

const QuizPage = () => {
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const projectId = params.project_id as string;
  const quizId = params.quiz_id as string;
  const { refetchProjects } = useProjectContext();
  const { triggerRefresh } = useQuizStore();

  const [isLoading, setIsLoading] = useState(true);
  const [quiz, setQuiz] = useState<TPopQuiz | null>(null);
  const [response, setResponse] = useState<TResponse | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const [answers, setAnswers] = useState<
    (TCreateResponseAnswer & { IsCorrect?: boolean })[]
  >([]);
  const [expandedExplanations, setExpandedExplanations] = useState<{
    [key: string]: boolean;
  }>({});

  // Character mapping for options
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

  // Check if the quiz is completed
  const isCompleted = response !== null;

  useEffect(() => {
    if (!api) return;

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  // Fetch quiz and response data
  useEffect(() => {
    if (projectId && quizId) {
      fetchQuizAndResponse();
    }
  }, [projectId, quizId]);

  const fetchQuizAndResponse = async () => {
    setIsLoading(true);
    try {
      // Get quiz details
      const quizListResponse = await API.POP_QUIZ.GetPopQuizList({
        ProjectID: projectId,
      });

      if (quizListResponse.errors) {
        toast.error("Failed to fetch quiz details");
        setIsLoading(false);
        return;
      }

      const foundQuiz = quizListResponse.data?.find(
        (q) => q.PopQuizID === quizId,
      );
      if (!foundQuiz) {
        toast.error("Quiz not found");
        router.push(`/${projectId}/quiz`);
        return;
      }

      setQuiz(foundQuiz);

      // Check if user has already completed this quiz
      const responseResult = await API.POP_QUIZ.GetMyResponses({
        ProjectID: projectId,
        PopQuizID: quizId,
      });
      if (responseResult.data) {
        const userResponse = responseResult.data.find(
          (r) => r.PopQuizID === quizId,
        );
        if (userResponse) {
          setResponse(userResponse);
          if (userResponse.Questions) {
            setAnswers(
              userResponse.Questions.map((q) => ({
                QuestionID: q.QuestionID,
                OptionIDs: q.OptionIDs,
                IsCorrect: q.IsCorrect,
              })),
            );
          }
        }
      }
    } catch (error) {
      console.error("Error fetching quiz data:", error);
      toast.error("Failed to load quiz");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!quiz || answers.length !== quiz.QuestionCount) {
      toast.error("Please answer all questions before submitting");
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await API.POP_QUIZ.CreateResponse({
        PopQuizID: quizId,
        Answers: answers,
      });

      if (result.errors) {
        result.errors.forEach((error) => {
          toast.error(error.message || "Failed to submit answers");
        });
        return;
      }

      toast.success("Quiz submitted successfully!");
      setResponse(result.data?.ack || null);
      if (result.data?.ack?.Questions) {
        setAnswers(result.data.ack.Questions);
      }
      // Reset carousel to first question
      api?.scrollTo(0);

      // Trigger a refresh in the quiz store to update the lists
      triggerRefresh();
    } catch (error) {
      console.error("Error submitting quiz:", error);
      toast.error("Failed to submit quiz");
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleExplanation = (questionId: string) => {
    setExpandedExplanations((prev) => ({
      ...prev,
      [questionId]: !prev[questionId],
    }));
  };

  const formatExplanationWithCitations = (
    explanation: string,
    citations: TCitation[] = [],
  ) => {
    if (!explanation) return "No explanation available";

    // Replace citation IDs with actual citation data
    let formattedText = explanation;

    // Create a map of citation IDs to their details for efficient lookup
    const citationMap = new Map();
    citations.forEach((citation) => {
      if (citation.ID) {
        citationMap.set(citation.ID, citation);
      }
    });

    // Replace each [ID] with source and page info
    const regex = /\[([\w-]+)\]/g;
    formattedText = formattedText.replace(regex, (match, id) => {
      const citation = citationMap.get(id);
      if (citation) {
        return `[Source: ${citation.SourceTitle}, Page ${citation.PageNumber}]`;
      }
      return match; // Keep original if citation not found
    });

    return formattedText;
  };

  const navigateToQuizList = () => {
    router.push(`/${projectId}/quiz`);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto max-w-4xl py-8 space-y-6">
        <Skeleton className="h-8 w-1/3" />
        <Skeleton className="h-4 w-2/3" />
        <Skeleton className="h-4 w-1/2 mt-2" />
        <Skeleton className="h-80 w-full mt-6" />
        <div className="flex justify-between mt-4">
          <Skeleton className="h-10 w-28" />
          <Skeleton className="h-10 w-28" />
        </div>
      </div>
    );
  }

  if (!quiz) {
    return (
      <div className="container mx-auto max-w-4xl py-8">
        <Card>
          <CardContent className="pt-6 flex flex-col items-center text-center">
            <CircleX className="h-12 w-12 text-muted-foreground mb-2" />
            <CardTitle className="text-xl">Quiz Not Found</CardTitle>
            <CardDescription className="max-w-md mt-2">
              This quiz doesn't exist or you don't have access to it.
            </CardDescription>
            <Button className="mt-4 gap-2" onClick={navigateToQuizList}>
              <Home className="h-4 w-4" />
              Return to Quiz Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-4xl py-8">
      {/* Quiz Header */}
      <div className="mb-6 space-y-2">
        <h1 className="text-2xl font-bold">{quiz.Title}</h1>
        {quiz.Description && (
          <p className="text-muted-foreground">{quiz.Description}</p>
        )}

        <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
          <span>{quiz.QuestionCount} Questions</span>
          {isCompleted && response?.Result && (
            <div className="flex items-center gap-2">
              <span className="font-medium">
                Your Score: {response.Result.Score}%
              </span>
              <span className="font-medium bg-green-50/20 text-green-500 px-2 py-1 rounded">
                Correct: {response.Result.CorrectAnswers}
              </span>
              <span className="font-medium bg-red-50/20 text-red-500 px-2 py-1 rounded">
                Incorrect: {response.Result.IncorrectAnswers}
              </span>
            </div>
          )}
        </div>

        {!isCompleted && (
          <Progress
            value={(answers.length / quiz.QuestionCount) * 100}
            className="mt-2"
          />
        )}
      </div>

      {/* Quiz Content */}
      <div className="flex flex-col items-center space-y-6">
        <div className="text-sm text-muted-foreground">
          Question {current} of {count}
        </div>

        <Carousel className="w-full" setApi={setApi}>
          <CarouselContent>
            {quiz.QuestionDetails.map((question) => {
              const findAnswer = answers.find(
                (answer) => answer.QuestionID === question.QuestionID,
              );

              const findQuestion = response?.QuestionDetails?.find(
                (q) => q.QuestionID === question.QuestionID,
              );

              const correctOption = findQuestion?.Options.find(
                (option) => option.IsCorrect,
              )?.OptionID;

              const isExplanationExpanded =
                expandedExplanations[question.QuestionID] || false;

              return (
                <CarouselItem key={question.QuestionID} className="basis-full">
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="text-lg">
                        {question.QuestionText}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {question.Options?.map((option, index) => (
                          <div
                            key={option.OptionID}
                            className={cn(
                              "border p-3 rounded-md flex gap-2 items-center",
                              findAnswer?.OptionIDs.includes(option.OptionID)
                                ? "bg-accent"
                                : "",
                              isCompleted
                                ? "pointer-events-none"
                                : "cursor-pointer hover:bg-accent/50",
                              isCompleted && correctOption === option.OptionID
                                ? "border-green-500 bg-green-50/10"
                                : "",
                              isCompleted &&
                                findAnswer?.OptionIDs.includes(
                                  option.OptionID,
                                ) &&
                                correctOption !== option.OptionID
                                ? "border-red-500 bg-red-50/10"
                                : "",
                            )}
                            onClick={() => {
                              if (isCompleted) return;

                              const findIndex = answers.findIndex(
                                (answer) =>
                                  answer.QuestionID === question.QuestionID,
                              );

                              if (findIndex >= 0) {
                                const newAnswers = [...answers];
                                newAnswers[findIndex] = {
                                  QuestionID: question.QuestionID,
                                  OptionIDs: [option.OptionID],
                                };
                                setAnswers(newAnswers);
                              } else {
                                setAnswers([
                                  ...answers,
                                  {
                                    QuestionID: question.QuestionID,
                                    OptionIDs: [option.OptionID],
                                  },
                                ]);
                              }
                            }}
                          >
                            <span
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center text-sm",
                                findAnswer?.OptionIDs.includes(option.OptionID)
                                  ? "bg-primary text-primary-foreground"
                                  : "bg-muted",
                              )}
                            >
                              {chars[index]}
                            </span>
                            <span className="flex-1">{option.Title}</span>
                            {isCompleted && (
                              <span
                                className={cn(
                                  "ml-auto",
                                  correctOption === option.OptionID
                                    ? "text-green-600"
                                    : "text-red-600",
                                )}
                              >
                                {correctOption === option.OptionID ? (
                                  <Check className="h-5 w-5" />
                                ) : (
                                  findAnswer?.OptionIDs.includes(
                                    option.OptionID,
                                  ) && <X className="h-5 w-5" />
                                )}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </CardContent>

                    {/* Expandable Explanation Section */}
                    {isCompleted && findQuestion && (
                      <CardFooter className="pt-3 pb-4 px-6 border-t flex flex-col items-start">
                        <Collapsible
                          className="w-full space-y-2"
                          open={isExplanationExpanded}
                          onOpenChange={() =>
                            toggleExplanation(question.QuestionID)
                          }
                        >
                          <div className="flex items-center space-x-1 w-full">
                            <CollapsibleTrigger className="flex items-center justify-between hover:bg-muted/50 hover:text-primary transition-colors rounded-md p-2 text-sm font-medium w-full">
                              <div className="flex items-center gap-2">
                                <BookOpen className="h-4 w-4" />
                                <span>Explanation</span>
                              </div>
                              {isExplanationExpanded ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )}
                            </CollapsibleTrigger>
                          </div>
                          <CollapsibleContent className="space-y-2">
                            <div className="rounded-md border bg-card p-4 text-sm bg-muted/30">
                              <p className="whitespace-pre-line">
                                {formatExplanationWithCitations(
                                  findQuestion.Answer?.Explnation,
                                  findQuestion.Answer?.Citations,
                                )}
                              </p>
                            </div>
                          </CollapsibleContent>
                        </Collapsible>
                      </CardFooter>
                    )}
                  </Card>
                </CarouselItem>
              );
            })}
          </CarouselContent>
        </Carousel>

        {/* Navigation Controls */}
        <div className="flex justify-between w-full mt-4">
          <Button
            variant="outline"
            onClick={() => api?.scrollPrev()}
            disabled={current === 1}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>

          <div className="flex gap-2">
            {!isCompleted && answers.length === quiz.QuestionCount && (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="gap-2"
              >
                {isSubmitting ? (
                  <>Submitting...</>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    Submit Quiz
                  </>
                )}
              </Button>
            )}

            <Button
              variant="outline"
              onClick={() => api?.scrollNext()}
              disabled={current === count}
              className="gap-2"
            >
              Next
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Button
          variant="outline"
          className="mt-4 gap-2"
          onClick={navigateToQuizList}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Quizzes
        </Button>
      </div>
    </div>
  );
};

export default QuizPage;
