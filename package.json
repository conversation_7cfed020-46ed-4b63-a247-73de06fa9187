{"name": "shadcn-ui-monorepo", "version": "0.0.1", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "prettier": "^3.5.1", "tailwindcss": "^4.0.8", "turbo": "^2.4.2", "typescript": "5.7.3"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=20"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-switch": "^1.1.3", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.0.1"}}