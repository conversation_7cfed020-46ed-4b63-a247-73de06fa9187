from dotenv import load_dotenv
import json
from langgraph.graph import StateGraph, START, END
from agents.embedding_agent.py_mu_pdf.parser import parse_pdf as parse_pdf_py_mu_pdf
from agents.embedding_agent.py_mu_pdf.state import (
    InputState,
    Chunk,
    OutputState,
    SectionsOutput,
    State,
    SummarizedChunksOutput,
    SummarizedChunks,
    EnhancedChunks,
    Section,
    PageMetadata,
    PageNumbers,
    Subsection,
)
from agents.common.utils import get_llm, get_vector_store
from agents.embedding_agent.py_mu_pdf.prompts import (
    DERIEVE_SECTIONS_PROMPT,
    SUMMARIZE_CHUNKS_PROMPT,
)
from langchain_core.prompts import PromptTemplate
from langchain_experimental.text_splitter import SemanticChunker
from langchain_openai.embeddings import OpenAIEmbeddings
from langchain_core.documents import Document
from uuid import uuid4

load_dotenv()


def parse_pdf(state: State) -> State:
    pages = parse_pdf_py_mu_pdf(state.source_content)
    state.pages = pages
    return state


def derieve_sections(state: State) -> State:
    llm = get_llm()
    prompt = PromptTemplate.from_template(DERIEVE_SECTIONS_PROMPT)
    prompt = prompt.format(
        language=state.language,
        file_type=state.source_type,
        source=state.pages,
    )
    structured_llm = llm.with_structured_output(SectionsOutput)
    sections = structured_llm.invoke(prompt)
    state.sections = [
        Section(
            section_id=str(uuid4()),
            section_name=section["section_name"],
            section_description=section["section_description"],
            subsections=[
                Subsection(
                    subsection_name=subsection["subsection_name"],
                    page_number=subsection["page_number"],
                )
                for subsection in section["subsections"]
            ],
            page_numbers=PageNumbers(
                start_page_number=section["page_numbers"]["start_page_number"],
                end_page_number=section["page_numbers"]["end_page_number"],
            ),
        )
        for section in sections["sections"]
    ]
    return state


def chunk_pages(state: State) -> State:
    text_splitter = SemanticChunker(
        OpenAIEmbeddings(model="text-embedding-3-large"),
        min_chunk_size=200,
        breakpoint_threshold_type="gradient",
    )
    documents = [
        Document(
            page_content=page["page_content"],
            metadata=page["metadata"],
        )
        for page in state.pages
    ]
    chunks = text_splitter.split_documents(documents)
    chunked_pages = [
        Chunk(
            chunk_id=str(uuid4()),
            chunk_number=index + 1,
            chunk_content=chunk.page_content,
            metadata=PageMetadata(
                page_number=chunk.metadata["page_number"],
                source_name=state.source_name,
            ),
        )
        for index, chunk in enumerate(chunks)
    ]
    state.chunks = chunked_pages
    return state


def enhance_chunks(state: State) -> State:
    enhanced_chunks = state.chunks
    for chunk_index, chunk in enumerate(state.chunks):
        sections_for_chunk = []
        for section in state.sections:
            if (
                section.page_numbers.start_page_number
                <= chunk.metadata.page_number
                <= section.page_numbers.end_page_number
            ):
                subsections_for_chunk = []
                for subsection in section.subsections:
                    if subsection.page_number == chunk.metadata.page_number:
                        subsections_for_chunk.append(subsection)
                sections_for_chunk.append(
                    Section(
                        section_id=section.section_id,
                        section_name=section.section_name,
                        section_description=section.section_description,
                        subsections=subsections_for_chunk,
                        page_numbers=section.page_numbers,
                    )
                )

        enhanced_chunks[chunk_index] = EnhancedChunks(
            chunk_id=chunk.chunk_id,
            chunk_number=chunk.chunk_number,
            chunk_content=chunk.chunk_content,
            metadata=chunk.metadata,
            sections=sections_for_chunk,
        )
    state.enhanced_chunks = enhanced_chunks
    return state


def summarize_chunks(state: State) -> State:
    llm = get_llm()
    prompt_template = PromptTemplate.from_template(SUMMARIZE_CHUNKS_PROMPT)
    summarized_chunks = state.enhanced_chunks
    for chunk_index, chunk in enumerate(state.enhanced_chunks):
        prompt = prompt_template.format(
            chunk_content=chunk.chunk_content,
            language=state.language,
        )
        structured_llm = llm.with_structured_output(SummarizedChunksOutput)
        summary = structured_llm.invoke(prompt)
        summarized_chunks[chunk_index] = SummarizedChunks(
            chunk_id=chunk.chunk_id,
            chunk_number=chunk.chunk_number,
            chunk_content=chunk.chunk_content,
            summarized_content=summary["summary"],
            metadata=chunk.metadata,
            sections=chunk.sections,
        )
    state.summarized_chunks = summarized_chunks
    return state


def embed_chunks(state: State) -> State:
    vector_store = get_vector_store(namespace=state.project_id)
    documents = [
        Document(
                page_content=f"SECTIONS: {[section.section_name for section in chunk.sections]}, CONTENT: {chunk.chunk_content}",
            metadata={
                "sections": [
                    json.dumps(
                        {
                            "section_id": section.section_id,
                            "section_name": section.section_name,
                            "page_numbers": {
                                "start_page_number": section.page_numbers.start_page_number,
                                "end_page_number": section.page_numbers.end_page_number,
                            },
                        }
                    )
                    for section in chunk.sections
                ],
                "source_id": state.source_id,
                "source": json.dumps(
                    {
                        "source_id": state.source_id,
                        "source_name": state.source_name,
                        "source_type": state.source_type,
                        "page_number_offset": state.page_number_offset,
                    }
                ),
                "chunk": json.dumps(
                    {
                        "chunk_id": chunk.chunk_id,
                        "chunk_number": chunk.chunk_number,
                        "chunk_page_number": chunk.metadata.page_number,
                    }
                ),
                "summary": chunk.summarized_content,
                "tenant_id": state.tenant_id,
                "project_id": state.project_id,
            },
            id=chunk.chunk_id,
        )
        for chunk in state.summarized_chunks
    ]
    vector_store.add_documents(
        documents=documents,
        ids=[chunk.chunk_id for chunk in state.summarized_chunks],
    )
    return state


# Create the graph builder with the shared state
builder = StateGraph(State, input=InputState, output=OutputState)
builder.set_entry_point("parse_pdf")

# Add the processing node
builder.add_node("parse_pdf", parse_pdf)
builder.add_node("derieve_sections", derieve_sections)
builder.add_node("chunk_pages", chunk_pages)
builder.add_node("enhance_chunks", enhance_chunks)
builder.add_node("summarize_chunks", summarize_chunks)
builder.add_node("embed_chunks", embed_chunks)

# Connect the nodes
builder.add_edge(START, "parse_pdf")
builder.add_edge("parse_pdf", "derieve_sections")
builder.add_edge("derieve_sections", "chunk_pages")
builder.add_edge("chunk_pages", "enhance_chunks")
builder.add_edge("enhance_chunks", "summarize_chunks")
builder.add_edge("summarize_chunks", "embed_chunks")
builder.add_edge("embed_chunks", END)

# Compile the graph
graph = builder.compile()

if __name__ == "__main__":
    with open("agents/sample_data/Tabular File.pdf", "rb") as file:
        file_content = file.read()
        state: State = graph.invoke(
            {
                "source_content": file_content,
                "source_id": "123",
                "source_name": "Tabular File.pdf",
                "source_type": "application/pdf",
                "language": "eng",
                "tenant_id": "123",
                "project_id": "456",
                "user_id": "789",
                "page_number_offset": 0,
            }
        )

        # print the state in a pretty format without source_content
        filtered_state = {k: v for k, v in state.items() if k != "source_content"}
        print(type(state))
