class Base64Utils {
  /**
   * Calculate maximum base64 length can be generated from the given file size
   * @param maxFileSize - Maximum file size in bytes
   * @returns Maximum base64 length can be generated from the given file size
   */
  static MaxBase64Length(maxFileSize: number) {
    return 4 * Math.ceil(maxFileSize / 3);
  }

  /**
   * Calculate base64 size in bytes, KB and MB
   * @param base64 - Base64 string
   * @returns Base64 size in bytes, KB and MB
   */
  static Base64SizeType(base64: string) {
    const size = base64.length * (3 / 4) - (base64.split('=').length - 1); // In bytes
    const fileType = base64?.split(';')[0]?.split(':')[1]?.split('/')[1];
    return {
      sizeInBytes: size, // In bytes
      sizeInKB: new Intl.NumberFormat('en-US', {
        maximumFractionDigits: 2,
      }).format(size / 1000),
      sizeInMB: new Intl.NumberFormat('en-US', {
        maximumFractionDigits: 2,
      }).format(size / 1000000),
      fileType,
    };
  }
  static IsBase64(value: string) {
    return typeof value === 'string' && value?.includes(`data:`);
  }
}

export default Base64Utils;
