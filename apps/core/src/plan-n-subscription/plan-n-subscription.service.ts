import { <PERSON><PERSON>, CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { sum } from 'radash';
import { User } from '../core/user/schemas/user.model';
import { OrderStatus } from '../payment/entities/payment.entity';
import { PaymentService } from '../payment/payment.service';
import { ProjectModel } from '../project/schemas/project.schema';
import { TokenCounterModel } from '../token-counter/schemas/token-counter.schema';
import { CreateCreditUsageDto, ListCreditUsageDto, UpdateCreditUsageDto } from './dto/create-usage.service.dto';
import { CreatePlanDto, ListPlansDto, UpdatePlanDto } from './dto/plan.service.dto';
import { CreateSubscriptionDto, ListSubscriptions, UpdateSubscriptionDto } from './dto/subscription.service.dto';
import { CreateTierGroupDto, ListTierGroupsDto, UpdateTierGroupDto } from './dto/tier-group.service.dto';
import { Renewal, RenewalStatus, SubscriptionStatus } from './entities/plan-n-subscription.entity';
import { CreditUsageModel } from './schemas/credit-usage.schema';
import { PlanModel } from './schemas/plan.schema';
import { SubscriptionModel } from './schemas/subscription.schema';
import { TierGroupModel } from './schemas/tier-group.schema';

@Injectable()
export class PlanNSubscriptionService {
  constructor(
    @InjectModel(PlanModel.name) private readonly PlanModel: Model<PlanModel>,
    @InjectModel(SubscriptionModel.name) private readonly SubscriptionModel: Model<SubscriptionModel>,
    @InjectModel(TierGroupModel.name) private readonly TierGroupModel: Model<TierGroupModel>,
    @InjectModel(CreditUsageModel.name) private readonly CreditUsageModel: Model<CreditUsageModel>,
    private readonly PaymentService: PaymentService,
    @Inject(CACHE_MANAGER) private CacheService: Cache
  ) {}

  async CreatePlan(Params: CreatePlanDto) {
    const plan = new this.PlanModel(Params);
    return await plan.save();
  }

  async UpdatePlan(Params: UpdatePlanDto) {
    return await this.PlanModel.findOneAndUpdate(
      {
        PlanID: Params.PlanID,
        TenantID: Params.TenantID,
      },
      Params,
      { new: true }
    ).lean();
  }

  async ListPlans(Params: ListPlansDto) {
    return (await this.PlanModel.find({
      TenantID: Params.TenantID,
      ...(Params?.PlanIDs?.length && { PlanID: { $in: Params.PlanIDs } }),
      ...(Params?.ProjectIDs?.length && { ProjectIDs: { $in: Params.ProjectIDs } }),
      Archived: { $ne: true },
    })
      .populate('Projects')
      .lean()) as unknown as (PlanModel & { Projects: ProjectModel[] })[];
  }

  async GetPlanByID(Params: { PlanID: string }) {
    return await this.PlanModel.findOne({
      PlanID: Params.PlanID,
    })
      .populate('Projects')
      .lean();
  }

  async DeletePlan(Params: { PlanID: string; TenantID: string }) {
    return await this.PlanModel.findOneAndDelete({
      PlanID: Params.PlanID,
      TenantID: Params.TenantID,
    }).lean();
  }

  async CreateSubscription(Params: CreateSubscriptionDto) {
    const subscription = new this.SubscriptionModel(Params);
    return await subscription.save();
  }

  async GetSubscription(Params: { SubscriptionID: string }) {
    return (await this.SubscriptionModel.findOne({
      _id: Params.SubscriptionID,
    })
      .populate({
        path: 'Plan',
      })
      .populate({
        path: 'User',
        select: '-PasswordHash',
      })
      .populate({
        path: 'Credits',
      })
      .lean()) as unknown as SubscriptionModel & {
      Plan: PlanModel;
      User: User;
      _id: string;
      Credits: CreditUsageModel[];
    };
  }

  async RevalidateSubscription(Params: { SubscriptionID: string }) {
    const subscription = await this.GetSubscription({ SubscriptionID: Params.SubscriptionID });
    if (!subscription) {
      throw new Error('Subscription not found');
    }

    // check expiry date
    if (subscription.EndDate !== null && new Date(subscription.EndDate) < new Date()) {
      subscription.Status = SubscriptionStatus.Inactive;
    }

    return (await this.SubscriptionModel.findOneAndUpdate(
      {
        _id: Params.SubscriptionID,
      },
      {
        $set: {
          Status: subscription.Status,
        },
      },
      { new: true }
    )
      .populate({
        path: 'Plan',
      })
      .populate({
        path: 'User',
        select: '-PasswordHash',
      })
      .lean()) as unknown as SubscriptionModel & { Plan: PlanModel; User: User; _id: string };
  }

  async AddRenewalLog(Params: { SubscriptionID: string; Renewal: Renewal }) {
    // delete all existing renewal logs with order id
    await this.SubscriptionModel.updateOne(
      {
        _id: Params.SubscriptionID,
      },
      {
        $pull: {
          RenewalLogs: { OrderID: Params.Renewal.OrderID },
        },
      }
    );
    const sub = await this.SubscriptionModel.findOneAndUpdate(
      {
        _id: Params.SubscriptionID,
      },
      {
        $push: {
          RenewalLogs: Params.Renewal,
        },
        $set: {
          EndDate: Params.Renewal.ToDate,
        },
      },
      { new: true }
    ).lean();
    await this.CalibrateCreditUsageSubscription({ SubscriptionID: Params.SubscriptionID });
    return sub;
  }

  async DeleteRenewalLog(Params: { SubscriptionID: string; RenewalID: string }) {
    return await this.SubscriptionModel.findOneAndUpdate(
      {
        _id: Params.SubscriptionID,
      },
      {
        $pull: {
          RenewalLogs: { _id: Params.RenewalID },
        },
      },
      { new: true }
    ).lean();
  }

  async UpdateSubscription(Params: UpdateSubscriptionDto) {
    return await this.SubscriptionModel.findOneAndUpdate(
      {
        _id: Params.SubscriptionID,
        TenantID: Params.TenantID,
      },
      Params,
      { new: true }
    ).lean();
  }

  @OnEvent('payment.order.captured')
  async SyncOrder(Params: { OrderID: string }) {
    const order = await this.PaymentService.GetOrderDetails(Params.OrderID);

    if (!order) {
      throw new Error('Order not found');
    }

    const subscription = await this.SubscriptionModel.findOneAndUpdate(
      {
        'RenewalLogs.OrderID': Params.OrderID,
      },
      {
        $set: {
          'RenewalLogs.$[renewal].Status':
            order.Status === OrderStatus.enum.Success ? RenewalStatus.Paid : RenewalStatus.Failed,

          Status: order.Status === OrderStatus.enum.Success ? SubscriptionStatus.Active : SubscriptionStatus.Pending,
        },
      },
      {
        arrayFilters: [
          {
            'renewal.OrderID': Params.OrderID,
          },
        ],
      }
    ).lean();

    if (subscription) {
      await this.CalibrateCreditUsageSubscription({ SubscriptionID: subscription._id.toString() });
    }
  }

  async ListSubscriptions(Params: ListSubscriptions) {
    return (await this.SubscriptionModel.find({
      TenantID: Params.TenantID,
      ...(Params?.PlanIDs?.length && { PlanID: { $in: Params.PlanIDs } }),
      ...(Params?.UserIDs?.length && { UserID: { $in: Params.UserIDs } }),
      ...(Params?.ProjectIDs?.length && { ProjectIDs: { $in: Params.ProjectIDs } }),
    })
      .populate({
        path: 'Plan',
      })
      .populate({
        path: 'User',
        select: '-PasswordHash',
      })
      .populate({
        path: 'Credits',
      })
      .lean()) as unknown as (SubscriptionModel & { Plan: PlanModel; User: User; _id: string })[];
  }

  async ListProjectForSubscribers(Params: ListSubscriptions) {
    const subscriptions = await this.ListSubscriptions(Params);
    const planIDs = subscriptions.map((subscription) => subscription.Plan.PlanID);
    const projects = (await this.ListPlans({ TenantID: Params.TenantID, PlanIDs: planIDs }))
      .map((plan) => plan.Projects)
      .flat();
    return projects;
  }

  async DeleteSubscription(Params: { SubscriptionID: string; TenantID: string }) {
    return await this.SubscriptionModel.findOneAndDelete({
      SubscriptionID: Params.SubscriptionID,
      TenantID: Params.TenantID,
    }).lean();
  }

  async CancelSubscription(Params: { SubscriptionID: string; TenantID: string; Reason: string }) {
    return await this.SubscriptionModel.findOneAndUpdate(
      {
        _id: Params.SubscriptionID,
        TenantID: Params.TenantID,
      },
      {
        $set: {
          Status: SubscriptionStatus.Cancelled,
          Reason: Params.Reason,
        },
      },
      { new: true }
    ).lean();
  }

  async CreateTierGroup(Params: CreateTierGroupDto) {
    const tierGroup = new this.TierGroupModel(Params);
    return await tierGroup.save();
  }

  async UpdateTierGroup(Params: UpdateTierGroupDto) {
    return await this.TierGroupModel.findOneAndUpdate(
      {
        _id: Params.TierGroupID,
        TenantID: Params.TenantID,
      },
      Params,
      { new: true }
    ).lean();
  }

  async ListTierGroups(Params: ListTierGroupsDto) {
    const query = {
      ...(Params?.TenantID && { TenantID: Params.TenantID }),
      ...(Params?.TierGroupIDs?.length && { TierGroupID: { $in: Params.TierGroupIDs } }),
      // ...(Params?.PlanIDs?.length && !Params.ListAsPublic && { PlanIDs: { $in: Params.PlanIDs } }),
      ...(Params?.PlanIDs?.length && { PlanIDs: { $in: Params.PlanIDs } }),
      ...(Params?.ListAsPublic && { ListAsPublic: Params.ListAsPublic }),
    };
    return (await this.TierGroupModel.find(query)
      .populate({
        path: 'Plans',
      })
      .lean()) as unknown as (TierGroupModel & { Plans: PlanModel[] })[];
  }

  async GetTierGroupByID(Params: { TierGroupID: string; TenantID: string }) {
    return (await this.TierGroupModel.findOne({
      _id: Params.TierGroupID,
      TenantID: Params.TenantID,
    })
      .populate({
        path: 'Plans',
      })
      .lean()) as unknown as TierGroupModel & { Plans: PlanModel[] };
  }

  async GetTierGroupByInviteCode(Params: { InviteCode: string }) {
    return await this.TierGroupModel.findOne({
      InviteCode: Params.InviteCode,
    })
      .populate({
        path: 'Plans',
      })
      .lean();
  }

  async DeleteTierGroup(Params: { TierGroupID: string; TenantID: string }) {
    return await this.TierGroupModel.findOneAndDelete({
      _id: Params.TierGroupID,
      TenantID: Params.TenantID,
    }).lean();
  }

  // Cron that run every day and check for end date of subscription & update the status
  async CalibrateCreditUsageSubscription(Params: { SubscriptionID: string }) {
    const subscription = await this.RevalidateSubscription({ SubscriptionID: Params.SubscriptionID });
    if (subscription.Status !== 'Active') {
      return null;
    }
    const getPlan = await this.GetPlanByID({ PlanID: subscription.PlanID });

    const creditCap = getPlan.PricingStructures.find((struct) => struct.Type === subscription.Variant)?.CreditUsage;

    // Unlimited plan does not have credit usage
    if (creditCap?.CapQuota > 0) {
      // Create credit usage after order is captured
      await this.CreateCreditUsage({
        UserID: subscription.UserID,
        Type: 'Subscription',
        SubscriptionID: subscription._id.toString(),
        Credit: creditCap?.CapQuota ?? 0,
        RemainingCredit: creditCap?.CapQuota ?? 0,
        ExpiryDate: subscription.EndDate,
        CapType: creditCap?.CapType,
        LastSyncDate: new Date(),
        RenewalID: subscription.RenewalLogs.at(-1)?.OrderID,
      });
    }
  }

  async CreateCreditUsage(Params: CreateCreditUsageDto) {
    const creditUsage = new this.CreditUsageModel(Params);
    return await creditUsage.save();
  }

  async GetCreditUsageBySubscriptionID(Params: { SubscriptionID: string }) {
    return await this.CreditUsageModel.find({
      SubscriptionID: Params.SubscriptionID,
    }).lean();
  }

  async UpdateCreditUsage(Params: UpdateCreditUsageDto) {
    return await this.CreditUsageModel.findOneAndUpdate(
      {
        _id: Params.CreditUsageID,
      },
      Params,
      { new: true }
    ).lean();
  }

  @OnEvent('token-counter.created')
  async UpdateRemainingCredit(Params: TokenCounterModel) {
    const { UserID, ProjectID } = Params;

    // Circuit breaker
    const creditByUser = await this.CreditUsageModel.findOne({
      UserID,
    }).countDocuments();

    if (creditByUser === 0) {
      return { message: 'No credit usage found' };
    }

    // get active subscription
    const subscriptions = (await this.SubscriptionModel.find({
      UserID: Params.UserID,
      Status: SubscriptionStatus.Active,
    })
      .populate({
        path: 'Plan',
        match: {
          ProjectIDs: { $in: [ProjectID] },
        },
      })
      .lean()) as unknown as (SubscriptionModel & { _id: string; Plan: PlanModel })[];

    if (!subscriptions.length) {
      return { message: 'No active subscription found' };
    }

    const subscriptionIDs = subscriptions
      .filter((s) => s.Plan?.ProjectIDs?.includes(ProjectID))
      .map((s) => s._id.toString());

    const creditUsage = await this.CreditUsageModel.find({
      UserID,
      SubscriptionID: {
        $in: subscriptionIDs,
      },
    }).lean();

    if (!creditUsage.length) {
      return { message: 'No credit usage found' };
    }

    // Always give priority to Type Subscription With Expiry & Remaining Credit
    const SubBasedCredits = creditUsage.filter((credUsage) => credUsage.Type === 'Subscription');
    let CreditUsageSubscription = SubBasedCredits.find(
      (credUsage) =>
        new Date().getTime() < new Date(credUsage.ExpiryDate).getTime() &&
        credUsage.RemainingCredit >= (credUsage.CapType === 'Token' ? Params.TokenCount.TotalTokens : 1)
    );

    if (!CreditUsageSubscription) {
      const NonSubBasedCredits = creditUsage.filter((credUsage) => credUsage.Type !== 'Subscription');
      // If no credit usage found then find the first credit usage
      CreditUsageSubscription = NonSubBasedCredits.find(
        (credUsage) => credUsage.RemainingCredit >= (credUsage.CapType === 'Token' ? Params.TokenCount.TotalTokens : 1)
      );
    }

    if (!CreditUsageSubscription) {
      return { message: 'No valid credit usage found' };
    }

    const RemainingCredit = CreditUsageSubscription?.CapType === 'Token' ? -Params.TokenCount.TotalTokens : -1;
    await this.CreditUsageModel.updateOne(
      {
        _id: CreditUsageSubscription._id,
      },
      {
        $inc: {
          RemainingCredit,
        },
        $set: {
          LastSyncDate: new Date(),
        },
      }
    ).lean();

    const RemainingSum =
      sum(
        creditUsage,
        (c) => (c.ExpiryDate === null || new Date().getTime() < new Date(c.ExpiryDate).getTime()) && c.RemainingCredit
      ) + RemainingCredit;

    // Update Cache
    await this.CacheService.set(`${UserID}:${CreditUsageSubscription.SubscriptionID}`, RemainingSum);
  }

  async ListCreditUsage(Params: ListCreditUsageDto) {
    return (await this.CreditUsageModel.find({
      TenantID: Params.TenantID,
      UserID: Params.UserID,
    })
      .populate({
        path: 'User',
        select: '-PasswordHash',
      })
      .lean()) as unknown as (CreditUsageModel & { User: User })[];
  }

  async IsCreditLimitAvailable(Params: { UserID: string; SubscriptionID: string }) {
    const { UserID, SubscriptionID } = Params;

    const creditLimit = await this.CacheService.get<number>(`${UserID}:${SubscriptionID}`);

    if (creditLimit && creditLimit > 0) {
      return { ack: true };
    }

    const creditUsage = await this.GetCreditUsageBySubscriptionID({
      SubscriptionID,
    });

    if (creditUsage.length === 0) {
      return { ack: true };
    }

    const RemainingSum = sum(
      creditUsage,
      (c) => (c.ExpiryDate === null || new Date().getTime() < new Date(c.ExpiryDate).getTime()) && c.RemainingCredit
    );

    if (RemainingSum < 1) {
      return { error: 'Credit limit exceeded' };
    }

    await this.CacheService.set(`${UserID}:${SubscriptionID}`, RemainingSum);

    return { ack: true };
  }
}
