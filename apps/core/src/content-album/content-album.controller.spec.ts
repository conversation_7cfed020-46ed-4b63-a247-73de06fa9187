import { Test, TestingModule } from '@nestjs/testing';
import { ContentAlbumController } from './content-album.controller';
import { ContentAlbumService } from './content-album.service';

describe('ContentAlbumController', () => {
  let controller: ContentAlbumController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ContentAlbumController],
      providers: [ContentAlbumService],
    }).compile();

    controller = module.get<ContentAlbumController>(ContentAlbumController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
