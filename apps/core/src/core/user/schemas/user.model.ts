import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import GenerateID from '../../../../common/lib/generate-id';
import { AppTypeEnum, TUserType } from '../entities/user.entity';

@Schema({
  _id: null,
})
class OAuth {
  @Prop({
    type: Object,
  })
  Google: {
    Connected: boolean;
    UserID: string;
  };
}

@Schema({
  _id: null,
})
class Resource {
  @Prop({
    type: String,
    required: true,
  })
  ResourceID: string;

  @Prop({
    type: Object,
    default: {},
  })
  Properties: Record<string, any>;
}

@Schema({
  _id: null,
})
class Access {
  @Prop({
    type: [Resource],
    default: [],
  })
  Resources: Resource[];
}
@Schema({ collection: 'users', timestamps: true, versionKey: false })
export class User {
  @Prop({ required: true, unique: true })
  Email: string;

  @Prop({})
  Name: string;

  @Prop({})
  PhoneNumber: string;

  @Prop({
    type: String,
    enum: ['Male', 'Female', 'Other'],
  })
  Gender: 'Male' | 'Female' | 'Other';

  @Prop({
    type: Date,
  })
  DateOfBirth: Date;

  @Prop({})
  Address: string;

  @Prop({})
  ProfilePicture: string;

  @Prop({})
  ZoneInfo: string;

  @Prop({})
  Locale: string;

  @Prop({})
  GivenName: string;

  @Prop({})
  FamilyName: string;

  @Prop({})
  MiddleName: string;

  @Prop({})
  Website: string;

  @Prop({ required: true, default: () => GenerateID.Generate16Hex('user_') })
  UserID: string;

  @Prop({
    type: String,
    required: true,
  })
  UserType: TUserType;

  @Prop({
    type: String,
    enum: AppTypeEnum,
  })
  AppType: AppTypeEnum;

  @Prop({
    type: String,
    required: true,
  })
  PasswordHash: string;

  @Prop({
    type: Access,
    default: {
      Resources: [],
    },
  })
  Access: Access;

  @Prop({
    type: String,
    required: true,
  })
  TenantID: string;

  @Prop({
    type: OAuth,
  })
  OAuth: OAuth;
}

export const UserSchema = SchemaFactory.createForClass(User);
