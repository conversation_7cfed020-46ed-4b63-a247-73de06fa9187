"use client";

import { AuthProvider } from "@/contexts/auth-context";
import { ModeProvider } from "@/contexts/mode-context";
import { PaywallProvider } from "@/contexts/paywall-context";
import { PopQuizProvider } from "@/contexts/pop-quiz-context";
import { ProjectProvider } from "@/contexts/project-context";
import { ThreadProvider } from "@/contexts/thread-context";
import { TTSProvider } from "@/contexts/tts-context";
import { AutoReadProvider } from "@/contexts/auto-read-context";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import * as React from "react";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="dark"
      enableSystem
      disableTransitionOnChange
      enableColorScheme
      storageKey="zuma-theme-preference"
    >
      <AuthProvider>
        <PaywallProvider>
          <ProjectProvider>
            <ModeProvider>
              <ThreadProvider>
                <TTSProvider>
                  <AutoReadProvider>
                    <PopQuizProvider>{children}</PopQuizProvider>
                  </AutoReadProvider>
                </TTSProvider>
              </ThreadProvider>
            </ModeProvider>
          </ProjectProvider>
        </PaywallProvider>
      </AuthProvider>
    </NextThemesProvider>
  );
}
