import { z } from 'zod';
export type RequiredAll<T> = {
  [P in keyof Required<T>]: Pick<T, P> extends Required<Pick<T, P>> ? T[P] : T[P] | undefined;
};

export interface QueryPagination {
  limit: number;
  page: number;
}

export type Prettify<T> = { [K in keyof T]: T[K] };

export type Modify<T, R> = Omit<T, keyof R> & R;

export type ArrayType<T> = T extends (infer U)[] ? U : T;

export type SelectPartial<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export const FileTypes = z.enum(['JSON', 'CSV', 'XLSX']);

export type TFileTypes = z.infer<typeof FileTypes>;

export type ToQuery<T> = {
  [P in keyof T]: T[P] extends Array<infer U> ? U : T[P];
};

export type AddKeyValue<T extends Record<string, unknown>, K extends string, V> = T & { [P in K]: V };

export type Expand<T, R> = T & R;

export type OR<T, R> = T | R;
export type AND<T, R> = T & R;
