import PromisePool, { PromisePoolError } from '@supercharge/promise-pool/dist';
import { S3 } from 'aws-sdk';

export const ImageType = ['image/jpg', 'image/png', 'image/jpeg', 'image/tiff', 'image/tif'];
export type TImageType = (typeof ImageType)[number];
export const BinaryType = ['application/pdf'];
export const TextType = ['text/plain', 'text/csv'];
export const SupportedMediaTypes = [...ImageType, ...BinaryType, ...TextType]; // Union of all media types
export const MediaAcronym = SupportedMediaTypes?.map((mediaType) => mediaType?.split('/')?.slice(-1)?.[0]);
export type TUploadFileParams = {
  Bucket: string;
  ContentType: TImageType;
  Key: string;
  Body: string | Uint8Array | Buffer | Blob;
};

export default class s3Service {
  private _s3Client: S3;
  private CONCURRENT_UPLOAD_LIMIT = 5;

  constructor(s3Client?: S3) {
    this._s3Client = s3Client;
  }

  async uploadImage(params: TUploadFileParams, options: { public?: boolean }): Promise<S3.ManagedUpload.SendData> {
    return await new Promise((resolve, reject) => {
      // if (!ImageType.includes(params.ContentType)) {
      //   return reject('Invalid media type, only jpg and png are allowed');
      // }
      this._s3Client.upload(params, {}, async (err, data) => {
        if (err) {
          return reject(err);
        }
        if (options.public) {
          await this.addACLToObject({ Bucket: params.Bucket, Key: params.Key, ACL: 'public-read' });
          return resolve(data);
        }
      });
    });
  }

  async uploadFile(params: TUploadFileParams, options: { public?: boolean }): Promise<S3.ManagedUpload.SendData> {
    return await new Promise((resolve, reject) => {
      // if (!TextType.includes(params.ContentType)) {
      //   return reject('Invalid media type, only text files are allowed');
      // }
      this._s3Client.upload(params, {}, async (err, data) => {
        if (err) {
          return reject(err);
        }
        if (options.public) {
          await this.addACLToObject({ Bucket: params.Bucket, Key: params.Key, ACL: 'public-read' });
          return resolve(data);
        }
      });
    });
  }

  async uploadFiles(
    params: TUploadFileParams[],
    options: { public?: boolean }
  ): Promise<{ results: S3.ManagedUpload.SendData[]; errors: PromisePoolError<TUploadFileParams>[] }> {
    return await PromisePool.withConcurrency(this.CONCURRENT_UPLOAD_LIMIT)
      .for(params)
      .process(async (object) => {
        return await new Promise((resolve, reject) => {
          // console.log("-->",SupportedMediaTypes.includes(object.ContentType.toString()));
          // if (!SupportedMediaTypes.includes(object.ContentType)) {
          //   return reject(`Invalid media type, only ${MediaAcronym.toString()} are supported.`);
          // }
          this._s3Client.upload(object, {}, async (err, data) => {
            if (err) {
              return reject(err);
            }
            if (options.public)
              await this.addACLToObject({ Bucket: object.Bucket, Key: object.Key, ACL: 'public-read' });
            return resolve(data);
          });
        });
      });
  }

  async addACLToObject(params: { Bucket: string; Key: string; ACL: string }) {
    return await new Promise((resolve, reject) => {
      this._s3Client.putObjectAcl(params, (err, data) => {
        if (err) {
          return reject(err);
        }
        return resolve(data);
      });
    });
  }

  async deleteObject(params: { Bucket: string; Key: string }) {
    return await new Promise((resolve, reject) => {
      this._s3Client.deleteObject(params, (err, data) => {
        if (err) {
          return reject(err);
        }
        return resolve(data);
      });
    });
  }
}
