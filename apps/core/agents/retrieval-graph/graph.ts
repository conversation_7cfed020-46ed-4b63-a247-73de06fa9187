import { RunnableConfig } from '@langchain/core/runnables';
import { AgentStateAnnotation, InputStateAnnotation } from './state';
import { AgentConfigurationAnnotation, ensureAgentConfiguration } from './configuration';
import { formatDocs, loadChatModel } from '../shared/utils';
import { z } from 'zod';
import { END, LangGraphRunnableConfig, START, StateGraph } from '@langchain/langgraph';
import { graph as researcherGraph } from './researcher-graph/graph';
import * as readline from 'readline';
import { makeRetriever } from '../shared/retrieval';
import { BaseMessage, MessageContentText } from '@langchain/core/messages';

/**
 * Safely extracts text from various message content formats
 */
function extractTextFromContent(content: any): string {
  if (typeof content === 'string') {
    return content;
  }

  if (Array.isArray(content)) {
    // Try to find a text content object
    for (const item of content) {
      if (item && typeof item === 'object' && item.type === 'text' && typeof item.text === 'string') {
        return item.text;
      }
    }
  }

  // Fallback: convert to string or return empty
  return String(content || '');
}

/**
 * First step: Verify if the query is related to the syllabus by checking for relevant documents
 */
async function verifySyllabusQuery(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  // Extract the user message content
  console.log('State: ', state.messages[state.messages.length - 1]);
  const lastMessage = state.messages[state.messages.length - 1];

  // Extract text from message content
  const userQuery = extractTextFromContent(lastMessage.content);

  if (!userQuery) {
    return {
      syllabusVerified: false,
      verificationDocuments: [],
      router: { type: 'more-info', logic: 'Could not extract query text from message.' },
    };
  }

  const retriever = await makeRetriever(config);

  const configWithFilters = {
    ...config,
    configurable: {
      ...(config?.configurable || {}),
      searchKwargs: {
        ...(config?.configurable?.searchKwargs || {}),
        filter: {
          ...(config?.configurable?.searchKwargs?.filter || {}),
          tenantId: state.tenantId,
          projectId: state.projectId,
        },
      },
    },
  };

  const documents = await retriever.similaritySearchWithScore(userQuery, 4, {
    tenantId: state.tenantId,
    projectId: state.projectId,
  });
  const filteredDocuments = documents.filter((doc) => {
    console.log('Score: ', doc[1]);
    return (
      doc[0].metadata.tenantId === state.tenantId && doc[0].metadata.projectId === state.projectId && doc[1] >= 0.3
    );
  });
  console.log(
    `Verification query: ${userQuery} Retrieved ${documents.length} documents and filtered to ${filteredDocuments.length}`
  );

  return {
    syllabusVerified: filteredDocuments.length > 0,
    verificationDocuments: filteredDocuments.map((doc) => doc[0]),
  };
}

/**
 * Second step: Analyze and route the query based on its type and whether it's verified in the syllabus
 */
async function analyzeAndRouteQuery(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const configuration = ensureAgentConfiguration(config);
  const model = await loadChatModel(configuration.queryModel);

  // Add syllabus verification context to the prompt
  const syllabus_context = state.syllabusVerified
    ? "The system has verified that this query relates to topics in the student's syllabus."
    : "The system has verified that this query does NOT relate to topics in the student's syllabus.";

  console.log('Syllabus Verified: ', state.syllabusVerified);
  if (!state.syllabusVerified) {
    return {
      router: { type: 'wrong-syllabus', logic: syllabus_context },
    };
  }
  console.log('Syllabus Context: ', syllabus_context);

  const messages = [
    {
      role: 'system',
      content: `${configuration.routerSystemPrompt}\n\n${syllabus_context}`,
    },
    ...state.messages,
  ];

  const Router = z
    .object({
      type: z.enum(['more-info', 'syllabus', 'wrong-syllabus', 'exam-prep', 'general']),
      logic: z.string(),
    })
    .describe('Classify user query');

  const response = await model.withStructuredOutput(Router).invoke(messages);

  // If syllabusVerified is true, ensure type is syllabus or exam-prep
  let type = response.type;
  let logic = response.logic;

  if (state.syllabusVerified && (type === 'wrong-syllabus' || type === 'general')) {
    type = 'syllabus';
    logic += ' The system has verified this topic is in the syllabus, so treating as a syllabus question.';
  } else if (!state.syllabusVerified && (type === 'syllabus' || type === 'exam-prep')) {
    type = 'wrong-syllabus';
    logic += ' The system has verified this topic is NOT in the syllabus.';
  }

  return {
    router: { type, logic },
  };
}

function routeQuery(
  state: typeof AgentStateAnnotation.State
): 'createResearchPlan' | 'askForMoreInfo' | 'respondToGeneralQuery' | 'respondToWrongSyllabusQuery' {
  const type = state.router.type;
  if (type === 'syllabus' || type === 'exam-prep') {
    return 'createResearchPlan';
  } else if (type === 'more-info') {
    return 'askForMoreInfo';
  } else if (type === 'general') {
    return 'respondToGeneralQuery';
  } else if (type === 'wrong-syllabus') {
    return 'respondToWrongSyllabusQuery';
  }
  throw new Error(`Unknown router type ${type}`);
}

async function askForMoreInfo(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const configuration = ensureAgentConfiguration(config);
  const model = await loadChatModel(configuration.queryModel);
  const systemPrompt = configuration.moreInfoSystemPrompt.replace('{logic}', state.router.logic);
  const messages = [{ role: 'system', content: systemPrompt }, ...state.messages];
  const response = await model.invoke(messages);
  return { messages: [response] };
}

async function respondToGeneralQuery(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const configuration = ensureAgentConfiguration(config);
  const model = await loadChatModel(configuration.queryModel);
  const systemPrompt = configuration.generalQueriesSystemPrompt.replace('{logic}', state.router.logic);
  const messages = [{ role: 'system', content: systemPrompt }, ...state.messages];
  const response = await model.invoke(messages);
  return { messages: [response] };
}

async function createResearchPlan(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const Plan = z
    .object({
      steps: z.array(z.string()),
    })
    .describe('Generate research plan.');

  const configuration = ensureAgentConfiguration(config);
  const model = (await loadChatModel(configuration.queryModel)).withStructuredOutput(Plan);

  // Include verification documents if available for better context
  let context = '';
  if (state.verificationDocuments && state.verificationDocuments.length > 0) {
    context = formatDocs(state.verificationDocuments);
  }

  // Add context to the prompt if available
  const systemPromptWithContext = context
    ? `${configuration.researchPlanSystemPrmompt}\n\nHere is some initial context from the syllabus:\n${context}`
    : configuration.researchPlanSystemPrmompt;

  const messages = [{ role: 'system', content: systemPromptWithContext }, ...state.messages];
  const response = await model.invoke(messages);
  return { steps: response.steps, documents: 'delete' };
}

async function conductResearch(
  state: typeof AgentStateAnnotation.State,
  config: LangGraphRunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const result = await researcherGraph.invoke(
    {
      question: state.steps[0],
      tenantId: state.tenantId,
      projectId: state.projectId,
    },
    { ...config }
  );

  return {
    documents: result.documents,
    steps: state.steps.slice(1),
  };
}

function checkFinished(state: typeof AgentStateAnnotation.State): 'conductResearch' | 'respond' {
  return state.steps && state.steps.length > 0 ? 'conductResearch' : 'respond';
}

async function respond(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const configuration = ensureAgentConfiguration(config);
  const model = await loadChatModel(configuration.responseModel);

  // Use verification documents if no research documents available
  let documents = state.documents;
  if ((!documents || documents.length === 0) && state.verificationDocuments && state.verificationDocuments.length > 0) {
    documents = state.verificationDocuments;
  }

  const context = formatDocs(documents);

  const systemPrompt = configuration.responseSystemPrompt.replace('{context}', context);

  const finalPrompt =
    state.tenantId && state.projectId
      ? `You are answering questions for a student in tenant ID: ${state.tenantId}, project ID: ${state.projectId}.\n${systemPrompt}`
      : systemPrompt;

  const messages = [{ role: 'system', content: finalPrompt }, ...state.messages];
  const response = await model.invoke(messages);
  return { messages: [response] };
}

async function respondToWrongSyllabusQuery(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  console.log('Responding to wrong syllabus query');
  const configuration = ensureAgentConfiguration(config);
  const model = await loadChatModel(configuration.responseModel);
  const systemPrompt = configuration.wrongSyllabusResponseSystemPrompt.replace('{logic}', state.router.logic);
  const messages = [{ role: 'system', content: systemPrompt }, ...state.messages];
  const response = await model.invoke(messages);
  return { messages: [response] };
}

// Define the graph with the new flow
const builder = new StateGraph(
  {
    stateSchema: AgentStateAnnotation,
    input: InputStateAnnotation,
  },
  AgentConfigurationAnnotation
)
  .addNode('verifySyllabusQuery', verifySyllabusQuery)
  .addNode('analyzeAndRouteQuery', analyzeAndRouteQuery)
  .addNode('askForMoreInfo', askForMoreInfo)
  .addNode('respondToGeneralQuery', respondToGeneralQuery)
  .addNode('respondToWrongSyllabusQuery', respondToWrongSyllabusQuery)
  .addNode('createResearchPlan', createResearchPlan)
  .addNode('conductResearch', conductResearch, { subgraphs: [researcherGraph] })
  .addNode('respond', respond)
  .addEdge(START, 'verifySyllabusQuery')
  .addEdge('verifySyllabusQuery', 'analyzeAndRouteQuery')
  .addConditionalEdges('analyzeAndRouteQuery', routeQuery, [
    'askForMoreInfo',
    'respondToGeneralQuery',
    'createResearchPlan',
    'respondToWrongSyllabusQuery',
  ])
  .addEdge('createResearchPlan', 'conductResearch')
  .addConditionalEdges('conductResearch', checkFinished, ['conductResearch', 'respond'])
  .addEdge('askForMoreInfo', END)
  .addEdge('respondToGeneralQuery', END)
  .addEdge('respondToWrongSyllabusQuery', END)
  .addEdge('respond', END);

export const graph = builder.compile().withConfig({ runName: 'RetrievalGraph' });

// Add specialized streaming method
export async function streamDirectFromModel(
  messages: any[],
  tenantId: string,
  projectId: string,
  onTokenCallback: (token: string, node: string) => void
) {
  // First determine which path we'll take through the graph
  const result = await graph.invoke({
    messages,
    tenantId,
    projectId,
  });

  // Based on the router result, directly stream from the appropriate model
  const configuration = ensureAgentConfiguration({ configurable: { tenantId, projectId } });
  const type = result.router.type;
  const model = await loadChatModel(configuration.responseModel);

  let systemPrompt = '';

  // Select the appropriate system prompt based on router type
  if (type === 'more-info') {
    systemPrompt = configuration.moreInfoSystemPrompt.replace('{logic}', result.router.logic);
  } else if (type === 'general') {
    systemPrompt = configuration.generalQueriesSystemPrompt.replace('{logic}', result.router.logic);
  } else if (type === 'wrong-syllabus') {
    systemPrompt = configuration.wrongSyllabusResponseSystemPrompt.replace('{logic}', result.router.logic);
  } else if (type === 'syllabus' || type === 'exam-prep') {
    // For syllabus or exam-prep, we need documents
    const context = formatDocs(result.documents || result.verificationDocuments);
    systemPrompt = configuration.responseSystemPrompt.replace('{context}', context);
  }

  // Create message array with system prompt
  const promptMessages = [{ role: 'system', content: systemPrompt }, ...messages];

  // Stream directly from the model
  const stream = await model.stream(promptMessages);
  const nodeName =
    type === 'more-info'
      ? 'askForMoreInfo'
      : type === 'general'
        ? 'respondToGeneralQuery'
        : type === 'wrong-syllabus'
          ? 'respondToWrongSyllabusQuery'
          : 'respond';

  let fullResponse = '';
  for await (const chunk of stream) {
    const content = chunk.content || '';
    fullResponse += content;
    onTokenCallback(fullResponse, nodeName);
  }

  return {
    messages: [{ role: 'assistant', content: fullResponse }],
    router: result.router,
    syllabusVerified: result.syllabusVerified,
  };
}

const readlineInterface = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function askQuestion(query: string): Promise<string> {
  return new Promise((resolve) => readlineInterface.question(query, resolve));
}

async function main() {
  try {
    const tenantId = await askQuestion('Enter Tenant ID: ');
    const projectId = await askQuestion('Enter Project ID: ');

    console.log(`Starting chat for Tenant: ${tenantId}, Project: ${projectId}`);
    console.log('--------------------------------');

    while (true) {
      const prompt = await askQuestion('Human Message: ');
      const response = await graph.invoke({
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        tenantId,
        projectId,
      });

      console.log('AI Message: ', response.messages[response.messages.length - 1].content);
      console.log('AI Logic: ', response.router.logic);
      console.log('Syllabus Verified: ', response.syllabusVerified);
      console.log('--------------------------------');

      if (prompt.toLowerCase() === 'exit' || prompt.toLowerCase() === 'quit') {
        console.log('Exiting chat...');
        break;
      }
    }
  } finally {
    readlineInterface.close();
  }
}

// Start the program
// main();
