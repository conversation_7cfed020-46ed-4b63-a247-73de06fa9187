@import "tailwindcss";
@plugin "tailwind-scrollbar";
@plugin "@tailwindcss/typography";

@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../**/*.{ts,tsx}";

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.75rem;

  --background: oklch(0.985 0 0);
  /* very light neutral */
  --foreground: oklch(0.18 0.01 260);
  /* dark cool gray */

  --card: oklch(1 0 0);
  --card-foreground: var(--foreground);

  --popover: var(--card);
  --popover-foreground: var(--foreground);

  --primary: oklch(0.7 0.12 270);
  /* gentle indigo */
  --primary-foreground: oklch(0.99 0 0);
  /* pure white */

  --secondary: oklch(0.95 0.005 260);
  /* soft gray */
  --secondary-foreground: var(--foreground);

  --muted: oklch(0.95 0.002 260);
  --muted-foreground: oklch(0.45 0.01 260);

  --accent: oklch(0.94 0.01 265);
  --accent-foreground: var(--foreground);

  --destructive: oklch(0.6 0.22 25);
  /* soft red */
  --border: oklch(0.9 0.01 260);
  --input: var(--border);
  --ring: var(--primary);

  --chart-1: oklch(0.6 0.2 40);
  --chart-2: oklch(0.6 0.1 200);
  --chart-3: oklch(0.5 0.08 260);
  --chart-4: oklch(0.85 0.17 90);
  --chart-5: oklch(0.8 0.15 70);

  --sidebar: oklch(0.98 0 0);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--primary);
}

.dark {
  --background: oklch(0.14 0.01 260);
  --foreground: oklch(0.98 0 0);

  --card: oklch(0.18 0.01 260);
  --card-foreground: var(--foreground);

  --popover: var(--card);
  --popover-foreground: var(--foreground);

  --primary: oklch(0.7 0.12 270);
  --primary-foreground: oklch(0.98 0 0);

  --secondary: oklch(0.3 0.005 260);
  --secondary-foreground: oklch(0.98 0 0);

  --muted: oklch(0.25 0.005 260);
  --muted-foreground: oklch(0.6 0.01 260);

  --accent: var(--muted);
  --accent-foreground: var(--foreground);

  --destructive: oklch(0.6 0.22 25);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: var(--primary);

  --chart-1: oklch(0.6 0.22 270);
  --chart-2: oklch(0.65 0.17 150);
  --chart-3: oklch(0.75 0.18 70);
  --chart-4: oklch(0.6 0.24 310);
  --chart-5: oklch(0.6 0.22 20);

  --sidebar: var(--card);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--primary);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);


  --animate-marquee: marquee var(--duration) infinite linear;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
  
  @keyframes marquee {
    from {
      transform: translateX(0);
    }

    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }

    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }

  --animate-aurora: aurora 8s ease-in-out infinite alternate;

  @keyframes aurora {
    0% {
      background-position: 0% 50%;
      transform: rotate(-5deg) scale(0.9);
    }

    25% {
      background-position: 50% 100%;
      transform: rotate(5deg) scale(1.1);
    }

    50% {
      background-position: 100% 50%;
      transform: rotate(-3deg) scale(0.95);
    }

    75% {
      background-position: 50% 0%;
      transform: rotate(3deg) scale(1.05);
    }

    100% {
      background-position: 0% 50%;
      transform: rotate(-5deg) scale(0.9);
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

    button:not([disabled]),
    [role="button"]:not([disabled]) {
      cursor: pointer;
    }
}
