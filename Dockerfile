# Build Stage
FROM public.ecr.aws/docker/library/node:18-alpine AS build-stage

WORKDIR /app

# Install pnpm and NestJS CLI
RUN npm install -g pnpm @nestjs/cli


# Copy package files first to leverage Docker cache
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml .npmrc ./
COPY apps/core/package.json ./apps/core/
COPY packages/common-nest/package.json ./packages/common-nest/
COPY packages/utils/package.json ./packages/utils/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Install TypeScript
RUN npm install -g typescript

# Ensure node_modules/.bin is in PATH for TypeScript and other tools
ENV PATH="/app/node_modules/.bin:${PATH}"

# Copy source code
COPY . .

# Build packages
RUN pnpm run build --filter=@package/common-nest --filter=academic-lm-core

# Development Stage
FROM public.ecr.aws/docker/library/node:18-alpine AS development
WORKDIR /app

COPY --from=build-stage /app /app
ENV NODE_ENV=dev
EXPOSE 3001
CMD ["npm", "run", "dev"]

# Production Stage
FROM public.ecr.aws/docker/library/node:18-alpine AS production
WORKDIR /app

# Copy only necessary files from build stage
COPY --from=build-stage /app/node_modules ./node_modules
COPY --from=build-stage /app/apps/core ./apps/core
COPY --from=build-stage /app/packages/common-nest/dist ./packages/common-nest/dist
COPY --from=build-stage /app/packages/common-nest/node_modules ./packages/common-nest/node_modules




ENV NODE_ENV=production
WORKDIR /app/apps/core
CMD ["npm", "run", "start"]
