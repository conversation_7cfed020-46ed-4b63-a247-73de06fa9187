"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Link from "next/link";
import { TProjectDetails, TSource } from "@/types/project";
import { API } from "@/apis/api";
import {
  ChevronLeft,
  AlertCircle,
  FileText,
  Loader2,
  PlusCircle,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@workspace/ui/components/breadcrumb";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Button } from "@workspace/ui/components/button";
import { toast } from "sonner";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@workspace/ui/components/accordion";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  QUIZ,
  TQuestion,
  TPopQuizListResponse,
  TGenerateQuestionsResponse,
  TPopQuiz,
} from "@/apis/quiz.api";

export default function ChapterQuizPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.proj_id as string;
  const sourceId = params.source_id as string;
  const sectionId = params.section_id as string;

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [project, setProject] = useState<TProjectDetails | null>(null);
  const [source, setSource] = useState<TSource | null>(null);
  const [chapter, setChapter] = useState<any | null>(null);
  const [questions, setQuestions] = useState<TQuestion[]>([]);
  const [quizzes, setQuizzes] = useState<TPopQuizListResponse[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);
  const [loadingQuizzes, setLoadingQuizzes] = useState(false);
  const [generatingQuestions, setGeneratingQuestions] = useState(false);
  const [showCreateQuizDialog, setShowCreateQuizDialog] = useState(false);
  const [newQuiz, setNewQuiz] = useState({
    title: "",
    description: "",
    questionCount: 5,
  });
  const [creatingQuiz, setCreatingQuiz] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const projectResponse = await API.PROJECT.GetProjectDetails({
          ProjectID: projectId,
        });

        if (projectResponse.data) {
          setProject(projectResponse.data);
          const foundSource = projectResponse.data.Sources?.find(
            (s) => s.SourceID === sourceId
          );

          if (foundSource) {
            setSource(foundSource);
            const foundSection = foundSource.Sections?.find(
              (s) => s.SectionID === sectionId
            );

            if (foundSection) {
              setChapter(foundSection);

              // Only fetch questions and quizzes after we have the source data
              await Promise.all([
                fetchQuestionsWithSource(foundSource),
                fetchQuizzesWithSource(foundSource),
              ]);
            } else {
              setError("Chapter not found");
            }
          } else {
            setError("Source not found");
          }
        }
      } catch (error) {
        console.error("Failed to fetch data:", error);
        setError("An error occurred while fetching data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId, sourceId, sectionId]);

  // Separate function to fetch questions with source data provided directly
  const fetchQuestionsWithSource = async (sourceData: TSource) => {
    setLoadingQuestions(true);
    try {
      const response = await QUIZ.ListQuestions({
        ProjectID: projectId,
        SourceIDs: [sourceId],
        SectionID: sectionId,
      });

      if (response.data?.ack) {
        console.log(
          "Questions fetched successfully:",
          response.data.ack.length
        );
        setQuestions(response.data.ack);
      } else {
        console.log("No questions found or empty response");
        setQuestions([]);
      }
    } catch (error) {
      console.error("Failed to fetch questions:", error);
      toast.error("Failed to fetch questions");
      setQuestions([]);
    } finally {
      setLoadingQuestions(false);
    }
  };

  // Use this wrapper for the normal fetch questions function
  const fetchQuestions = async () => {
    if (!source) return;
    await fetchQuestionsWithSource(source);
  };

  // Separate function to fetch quizzes with source data provided directly
  const fetchQuizzesWithSource = async (sourceData: TSource) => {
    setLoadingQuizzes(true);
    try {
      console.log("Fetching quizzes for project:", projectId);
      const response = await QUIZ.ListQuizes({
        ProjectID: projectId,
        TenantID: sourceData.TenantID,
      });

      if (response.data?.ack) {
        console.log("Quizzes fetched successfully:", response.data.ack.length);
        // Filter quizzes for the current chapter
        const chapterQuizzes = response.data.ack.filter(
          (quiz: TPopQuizListResponse) => {
            return quiz.QuestionDetails.some(
              (q: TQuestion) => q.Scopes?.ChapterID === sectionId
            );
          }
        );
        console.log("Filtered quizzes for chapter:", chapterQuizzes.length);
        setQuizzes(chapterQuizzes);
      } else {
        console.log("No quizzes found or empty response");
        setQuizzes([]);
      }
    } catch (error) {
      console.error("Failed to fetch quizzes:", error);
      toast.error("Failed to fetch quizzes");
      setQuizzes([]);
    } finally {
      setLoadingQuizzes(false);
    }
  };

  // Use this wrapper for the normal fetch quizzes function
  const fetchQuizzes = async () => {
    if (!source) return;
    await fetchQuizzesWithSource(source);
  };

  const handleGenerateQuestions = async () => {
    if (!source) return;

    setGeneratingQuestions(true);
    try {
      toast.info("Generating questions... This may take a minute or more.");
      const response = await QUIZ.GenerateQuestions({
        ProjectID: projectId,
        SourceID: sourceId,
        ID: sectionId,
        NumberOfQuestions: 20,
      });

      if (response.data?.ack) {
        toast.success("Questions generated successfully");
        await fetchQuestionsWithSource(source);
      }
    } catch (error) {
      console.error("Failed to generate questions:", error);
      toast.error("Failed to generate questions");
    } finally {
      setGeneratingQuestions(false);
    }
  };

  const handleCreateQuiz = async () => {
    if (!source || !questions.length) return;

    if (!newQuiz.title.trim()) {
      toast.error("Title is required");
      return;
    }

    const maxQuestionCount = questions.length;
    if (newQuiz.questionCount > maxQuestionCount) {
      toast.error(`Question count cannot be more than ${maxQuestionCount}`);
      return;
    }

    setCreatingQuiz(true);
    try {
      const response = await QUIZ.GenerateQuizes({
        Title: newQuiz.title,
        Description: newQuiz.description,
        Status: "Active",
        Questions: [],
        QuestionCount: newQuiz.questionCount,
        Scopes: {
          SectionID: sectionId,
        },
        ProjectID: projectId,
        Subscribers: [],
        FloatingSubscribers: true,
      });

      if (response.data?.ack) {
        toast.success("Quiz created successfully");
        setShowCreateQuizDialog(false);
        setNewQuiz({
          title: "",
          description: "",
          questionCount: 5,
        });
        await fetchQuizzesWithSource(source);
      }
    } catch (error) {
      console.error("Failed to create quiz:", error);
      toast.error("Failed to create quiz");
    } finally {
      setCreatingQuiz(false);
    }
  };

  if (loading) {
    return (
      <div className="w-full h-full p-6 md:p-10 space-y-6">
        <div className="border rounded-lg bg-muted/30 p-6">
          <Skeleton className="h-8 w-1/3 mb-4" />
          <Skeleton className="h-4 w-2/3 mb-2" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-1/4" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !project || !source || !chapter) {
    return (
      <div className="w-full h-full py-6 space-y-6">
        <div className="border rounded-lg bg-muted/30 p-6">
          <h2 className="text-2xl font-bold mb-4">Error</h2>
          <p className="text-muted-foreground">
            {error || "Resource not found"}
          </p>
          <Link
            href={`/projects/${projectId}/sources/${sourceId}`}
            className="mt-4 inline-flex items-center text-primary hover:underline"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to Source
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full p-6 md:p-10 space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/projects">Projects</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/projects/${projectId}`}>
              {project.Name}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/projects/${projectId}/sources/${sourceId}`}>
              {source.SourceName}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>
              Chapter {chapter.ChapterNumber}: {chapter.ChapterName} - Quiz
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Chapter Meta */}
      <div className="border rounded-lg bg-muted/30 p-6 mb-6">
        <h2 className="text-2xl font-bold mb-1 flex items-center gap-2">
          <FileText className="w-6 h-6 text-muted-foreground" />
          Chapter {chapter.ChapterNumber}: {chapter.ChapterName}
        </h2>
        <div className="flex flex-wrap gap-4 text-xs text-muted-foreground mb-2">
          <span>
            <b>Project:</b> {project.Name}
          </span>
          <span>
            <b>Source:</b> {source.SourceName}
          </span>
          <span>
            <b>Chapter ID:</b> {chapter.ChapterID}
          </span>
          <span>
            <b>Pages:</b> {chapter.PageNumber?.From ?? "N/A"} -{" "}
            {chapter.PageNumber?.To ?? "N/A"}
          </span>
        </div>
        {chapter.Summary && (
          <div className="mt-2">
            <p className="text-sm text-muted-foreground">{chapter.Summary}</p>
          </div>
        )}
      </div>

      {/* Questions Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold">Questions</h3>
          <Button
            onClick={handleGenerateQuestions}
            disabled={
              generatingQuestions || loadingQuestions || questions.length > 0
            }
          >
            {generatingQuestions ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : questions.length > 0 ? (
              "Questions Already Generated"
            ) : (
              <>
                <PlusCircle className="mr-2 h-4 w-4" />
                Generate Questions
              </>
            )}
          </Button>
        </div>

        {loadingQuestions ? (
          <div className="space-y-2">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        ) : questions.length === 0 ? (
          <div className="border rounded-lg p-6 text-center text-muted-foreground">
            No questions available for this chapter. Click "Generate Questions"
            to create some.
          </div>
        ) : (
          <Accordion type="multiple" className="w-full">
            {questions.map((question) => (
              <AccordionItem
                key={question.QuestionID}
                value={question.QuestionID}
              >
                <AccordionTrigger className="hover:bg-muted/50 px-4">
                  <div className="flex items-start justify-between w-full">
                    <div className="text-left">
                      <div className="font-medium mb-1">{question.Title}</div>
                      <div className="text-sm text-muted-foreground">
                        {question.QuestionText}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {question.Type === "SINGLE_CHOICE"
                          ? "Single Choice"
                          : question.Type}
                      </Badge>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pt-2">
                  <Card>
                    <CardContent className="p-4 space-y-4">
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium">Options:</h4>
                        <div className="grid grid-cols-1 gap-2">
                          {question.Options.map((option) => (
                            <div
                              key={option.OptionID}
                              className={`p-3 rounded-md border ${
                                option.IsCorrect
                                  ? "border-green-500 bg-green-50"
                                  : "border-gray-200"
                              }`}
                            >
                              <div className="flex items-start gap-2">
                                <div className="flex-1">
                                  <p className="text-sm">{option.Title}</p>
                                </div>
                                {option.IsCorrect && (
                                  <Badge className="bg-green-500">
                                    Correct
                                  </Badge>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="border-t pt-3">
                        <h4 className="text-sm font-medium mb-2">
                          Answer Explanation:
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {question.Answer.Explnation}
                        </p>

                        {question.Answer.Citations &&
                          question.Answer.Citations.length > 0 && (
                            <div className="mt-3">
                              <h4 className="text-sm font-medium mb-1">
                                Citations:
                              </h4>
                              <ul className="list-disc ml-5 text-sm text-muted-foreground">
                                {question.Answer.Citations.map(
                                  (citation, idx) => (
                                    <li key={idx}>
                                      {citation.SourceTitle}, Page{" "}
                                      {citation.PageNumber}
                                    </li>
                                  )
                                )}
                              </ul>
                            </div>
                          )}
                      </div>

                      <div className="flex flex-wrap gap-2 mt-2">
                        {question.Tags.map((tag, idx) => (
                          <Badge
                            key={idx}
                            variant="secondary"
                            className="text-xs"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </div>

      {/* Pop Quizzes Section */}
      <div className="space-y-4 mt-8">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold">Pop Quizzes</h3>
          <Dialog
            open={showCreateQuizDialog}
            onOpenChange={setShowCreateQuizDialog}
          >
            <DialogTrigger asChild>
              <Button disabled={questions.length === 0}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Create New Quiz
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Quiz</DialogTitle>
                <DialogDescription>
                  Create a new quiz from the available questions for this
                  chapter.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Quiz Title</Label>
                  <Input
                    id="title"
                    value={newQuiz.title}
                    onChange={(e) =>
                      setNewQuiz({ ...newQuiz, title: e.target.value })
                    }
                    placeholder="Enter quiz title"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={newQuiz.description}
                    onChange={(e) =>
                      setNewQuiz({ ...newQuiz, description: e.target.value })
                    }
                    placeholder="Enter quiz description"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="questionCount">
                    Number of Questions (max: {questions.length})
                  </Label>
                  <Input
                    id="questionCount"
                    type="number"
                    min={1}
                    max={questions.length}
                    value={newQuiz.questionCount}
                    onChange={(e) =>
                      setNewQuiz({
                        ...newQuiz,
                        questionCount: Math.min(
                          Math.max(1, parseInt(e.target.value) || 1),
                          questions.length
                        ),
                      })
                    }
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowCreateQuizDialog(false)}
                  disabled={creatingQuiz}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateQuiz}
                  disabled={creatingQuiz || !newQuiz.title.trim()}
                >
                  {creatingQuiz ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Quiz"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {loadingQuizzes ? (
          <div className="space-y-2">
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        ) : quizzes.length === 0 ? (
          <div className="border rounded-lg p-6 text-center text-muted-foreground">
            No quizzes available for this chapter. Click "Create New Quiz" to
            create one.
          </div>
        ) : (
          <div className="space-y-6">
            {quizzes.map((quiz) => (
              <Card key={quiz.PopQuizID} className="w-full">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle>{quiz.Title}</CardTitle>
                      {quiz.Description && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {quiz.Description}
                        </p>
                      )}
                    </div>
                    <Badge
                      variant={
                        quiz.Status === "Active" ? "default" : "secondary"
                      }
                    >
                      {quiz.Status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-4 text-xs text-muted-foreground mb-4">
                    <span>
                      <b>Created:</b>{" "}
                      {formatDistanceToNow(new Date(quiz.createdAt), {
                        addSuffix: true,
                      })}
                    </span>
                    <span>
                      <b>Questions:</b> {quiz.QuestionCount}
                    </span>
                    <span>
                      <b>Quiz ID:</b> {quiz.PopQuizID}
                    </span>
                  </div>

                  <Tabs defaultValue="questions" className="w-full">
                    <TabsList className="mb-4">
                      <TabsTrigger value="questions">Questions</TabsTrigger>
                      <TabsTrigger value="responses">Responses</TabsTrigger>
                    </TabsList>

                    <TabsContent value="questions" className="space-y-4">
                      {quiz.QuestionDetails?.length > 0 ? (
                        <Accordion type="single" collapsible>
                          {quiz.QuestionDetails.map((question, idx) => (
                            <AccordionItem
                              key={question.QuestionID}
                              value={question.QuestionID}
                            >
                              <AccordionTrigger className="hover:bg-muted/50 px-4">
                                <div className="text-left">
                                  <div className="font-medium">
                                    Q{idx + 1}: {question.Title}
                                  </div>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent className="px-4 pt-2">
                                <div className="space-y-4">
                                  <div>
                                    <p className="text-sm mb-3">
                                      {question.QuestionText}
                                    </p>
                                    <div className="space-y-2">
                                      {question.Options.map((option) => (
                                        <div
                                          key={option.OptionID}
                                          className={`p-3 rounded-md border ${
                                            option.IsCorrect
                                              ? "border-green-500 bg-green-50"
                                              : "border-gray-200"
                                          }`}
                                        >
                                          <div className="flex items-start gap-2">
                                            <div className="flex-1">
                                              <p className="text-sm">
                                                {option.Title}
                                              </p>
                                            </div>
                                            {option.IsCorrect && (
                                              <Badge className="bg-green-500">
                                                Correct
                                              </Badge>
                                            )}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>

                                  <div className="border-t pt-3">
                                    <h4 className="text-sm font-medium mb-2">
                                      Answer Explanation:
                                    </h4>
                                    <p className="text-sm text-muted-foreground">
                                      {question.Answer.Explnation}
                                    </p>

                                    {question.Answer.Citations &&
                                      question.Answer.Citations.length > 0 && (
                                        <div className="mt-3">
                                          <h4 className="text-sm font-medium mb-1">
                                            Citations:
                                          </h4>
                                          <ul className="list-disc ml-5 text-sm text-muted-foreground">
                                            {question.Answer.Citations.map(
                                              (citation, idx) => (
                                                <li key={idx}>
                                                  {citation.SourceTitle}, Page{" "}
                                                  {citation.PageNumber}
                                                </li>
                                              )
                                            )}
                                          </ul>
                                        </div>
                                      )}
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          ))}
                        </Accordion>
                      ) : (
                        <div className="border rounded-lg p-6 text-center text-muted-foreground">
                          No questions available for this quiz.
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="responses">
                      <div className="border rounded-lg p-6 text-center text-muted-foreground">
                        No responses available for this quiz yet.
                      </div>
                      {/* Table for responses would go here when the API is available */}
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
