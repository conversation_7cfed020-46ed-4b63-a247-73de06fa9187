import { PartialType } from '@nestjs/mapped-types';
import { PricingType, SubscriptionStatus } from '../entities/plan-n-subscription.entity';
import { IsValidValueArray } from '../../../common/decorators/sanitization.decorators';

export class CreateSubscriptionDto {
  UserID: string;
  TenantID: string;
  PlanID: string;
  Price: number;
  Status: SubscriptionStatus;
  StartDate: Date;
  EndDate: Date;
  AutoRenew: boolean;
  Variant: PricingType;
  VariantDuration?: number;
}

export class UpdateSubscriptionDto extends PartialType(CreateSubscriptionDto) {
  SubscriptionID: string;
  TenantID: string;
}

export class ListSubscriptions {
  @IsValidValueArray()
  PlanIDs?: string[];

  @IsValidValueArray()
  ProjectIDs?: string[];

  @IsValidValueArray()
  UserIDs?: string[];

  TenantID: string;
}
