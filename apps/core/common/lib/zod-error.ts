import { ZodError, z } from 'zod';
import { ZodValidationError } from '@nextcampus/common';
import { isArray } from 'radash';

const handleValidation = <Schema extends z.ZodTypeAny, Data>(schema: Schema, data: Data, api?: boolean) => {
  let validation;
  try {
    if (isArray(data)) validation = schema.array().parse(data) as z.infer<Schema>[];

    if (!isArray(data)) validation = schema.parse(data) as z.infer<Schema>;
    return validation;
  } catch (err: any) {
    // if (api) throw new ZodValidationError(err as ZodError);

    console.log('::: Validation Error :::', err);
    return {
      errors: (err as ZodError).issues.map((err) => {
        return { message: err.message, field: err?.path?.join('.') };
      }),
    };
  }
};

export default handleValidation;
