import { Module } from '@nestjs/common';
import { LectureService } from './lecture.service';
import { LectureController } from './lecture.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Chunk } from '../embed/schemas/chunk.schema';
import { ChunkSchema } from '../embed/schemas/chunk.schema';
import { SourceModel, SourceSchema } from '../embed/schemas/source.schema';
import { Lecture, LectureSchema } from './schemas/lecture.schema';
import { TTSModule } from '../tts/tts.module';
import { TokenCounterModule } from '../token-counter/token-counter.module';
import { IndChunkModel, IndChunkSchema } from '../embed/schemas/ind-chunk.schema';
import { IndSourceModel, IndSourceSchema } from '../embed/schemas/ind-source.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Chunk.name, schema: ChunkSchema },
      { name: SourceModel.name, schema: SourceSchema },
      { name: Lecture.name, schema: LectureSchema },
      { name: IndChunkModel.name, schema: IndChunkSchema },
      { name: IndSourceModel.name, schema: IndSourceSchema },
    ]),
    TTSModule,
    TokenCounterModule,
  ],
  controllers: [LectureController],
  providers: [LectureService],
  exports: [LectureService],
})
export class LectureModule {}
