import { TryCatch } from "@/lib/try-catch";
import { Access, PartialUserAttrs, TUser, TUserType } from "@/types/user";
import { KY, TAckResponse, TErrorResponse } from "./_.index";
import { shake } from "@/lib/shake";
import { z } from "zod";

export const ResourcesTypes = z.enum([
  "Projects",
  "ContentAlbum",
  "Plans",
  "Subscribers",
  "Subscriptions",
  "Users",
  "Billing",
  "Settings",
]);

export type TResourceType = z.infer<typeof ResourcesTypes>;

export interface CreateUserParams extends Partial<PartialUserAttrs> {
  Email: string;
  Password: string;
  UserType: TUserType;
}

const CreateUser = async (params: CreateUserParams) => {
  return await TryCatch<TAckResponse<TUser>, TErrorResponse>(
    KY.post("user", { json: params }).json()
  );
};

export interface UpdateUserParams extends PartialUserAttrs {
  UserID: string;
}

const UpdateUser = async (params: UpdateUserParams) => {
  return await TryCatch<TAckResponse<TUser>, TErrorResponse>(
    KY.patch(`user/${params.UserID}`, { json: shake(params) }).json()
  );
};

export interface ListUsersParams {
  UserType: TUserType;
}
const ListUsers = async (params: ListUsersParams) => {
  return await TryCatch<TUser[], TErrorResponse>(
    KY.get(`user?UserType=${params.UserType}`).json()
  );
};

const GetUser = async (params: { UserID: string }) => {
  return await TryCatch<TUser, TErrorResponse>(
    KY.get(`user/${params.UserID}`).json()
  );
};

const UpdateUserAccess = async (params: { UserID: string; Access: Access }) => {
  return await TryCatch<TAckResponse<TUser>, TErrorResponse>(
    KY.patch(`user/${params.UserID}/access`, { json: params.Access }).json()
  );
};

const ResetUserPassword = async (params: {
  UserID: string;
  Password: string;
}) => {
  return await TryCatch<TAckResponse<TUser>, TErrorResponse>(
    KY.patch(`user/${params.UserID}/password`, {
      json: {
        NewPassword: params.Password,
      },
    }).json()
  );
};

export const USER = {
  CreateUser,
  UpdateUser,
  ListUsers,
  GetUser,
  UpdateUserAccess,
  ResetUserPassword,
};
