import { TryCatch } from "@/lib/try-catch";
import { KY, TAckResponse, TErrorResponse } from "./_.index";

export type TGenerateQuestionsBody = {
  ProjectID: string;
  SourceID: string;
  ID: string;
  NumberOfQuestions: number;
};

export type TQuestion = {
  Title: string;
  QuestionText: string;
  Type: "SINGLE_CHOICE";
  Tags: string[];
  Options: {
    Title: string;
    IsCorrect: boolean;
    OptionID: string;
  }[];
  Difficulty: "easy" | "medium" | "hard";
  Answer: {
    Explnation: string;
    Citations: {
      ID: string;
      SourceTitle: string;
      SourceID: string;
      PageNumber: number;
      DocumentPageNumber: number;
    }[];
  };
  QuestionID: string;
  SourceID: string;
  TenantID: string;
  ProjectID: string;
  _id: string;
  createdAt: string;
  updatedAt: string;
  Scopes: {
    ChapterID: string;
  };
};

export type TGenerateQuestionsResponse = {
  message: string;
  ack: TQuestion[];
};

export type TGenerateQuizesBody = {
  Title: string;
  Description: string;
  Status: "Draft" | "Active"; // keep Active
  Questions: []; // keep empty
  QuestionCount: number;
  Scopes: {
    ChapterID: string;
  };
  ProjectID: string;
  Subscribers: []; // keep empty
  FloatingSubscribers: boolean; // true
};

export type TPopQuiz = {
  Title: string;
  Description: string;
  Status: "Draft" | "Active";
  Questions: string[];
  QuestionCount: number;
  ProjectID: string;
  TenantID: string;
  Scopes: {
    ChapterID: string;
  };
  Subscribers: [];
  FloatingSubscribers: boolean;
  _id: string;
  PopQuizID: string;
  createdAt: string;
  updatedAt: string;
};

export type TPopQuizListResponse = TPopQuiz & {
  QuestionDetails: TQuestion[];
};

export type TGenerateQuizesResponse = {
  message: string;
  ack: TPopQuiz;
};

export type TQuizResponse = {
  _id: string;
  PopQuizID: string;
  UserID: string;
  ResponseID: string;
  Questions: {
    QuestionID: string;
    IsCorrect: boolean;
    OptionIDs: string[];
  }[];
  Result: {
    CorrectAnswers: number;
    IncorrectAnswers: number;
    TotalAnswers: number;
    Score: number;
  };
  TenantID: string;
  createdAt: string;
  updatedAt: string;
  User: {
    _id: string;
    Email: string;
    UserID: string;
    FamilyName: string;
    Gender: "Male" | "Female" | "Other";
    Name: string;
  };
};

const GenerateQuestions = async (body: TGenerateQuestionsBody) => {
  return await TryCatch<
    TAckResponse<TGenerateQuestionsResponse>,
    TErrorResponse
  >(KY.post("pop-quiz/question/generate", { json: body }).json());
};

const ListQuestions = async (params: {
  ProjectID: string;
  SourceIDs?: string[];
  Tags?: string[];
  ChapterID?: string;
}) => {
  const searchParams = new URLSearchParams();
  if (params.SourceIDs) {
    params.SourceIDs.forEach((sourceId) => {
      searchParams.append(`SourceIDs`, sourceId);
    });
  }
  if (params.Tags) {
    params.Tags.forEach((tag) => {
      searchParams.append(`Tags`, tag);
    });
  }
  if (params.ChapterID) {
    searchParams.append(`ChapterID`, params.ChapterID);
  }
  searchParams.append(`ProjectID`, params.ProjectID);
  console.log(
    "ListQuestions request params:",
    Object.fromEntries(searchParams)
  );

  const result = await TryCatch<TAckResponse<TQuestion[]>, TErrorResponse>(
    KY.get(`pop-quiz/question`, {
      searchParams: searchParams,
    }).json()
  );
  console.log("ListQuestions processed response:", result);
  return result;
};

const GenerateQuizes = async (body: TGenerateQuizesBody) => {
  return await TryCatch<TAckResponse<TGenerateQuizesResponse>, TErrorResponse>(
    KY.post("pop-quiz/quiz/generate", { json: body }).json()
  );
};

const ListQuizes = async (params: {
  ProjectID: string;
  TenantID: string;
  PopQuizIDs?: string[];
}) => {
  const searchParams = new URLSearchParams();
  if (params.PopQuizIDs) {
    params.PopQuizIDs.forEach((popQuizId) => {
      searchParams.append(`PopQuizIDs`, popQuizId);
    });
  }
  searchParams.append(`ProjectID`, params.ProjectID);
  searchParams.append(`TenantID`, params.TenantID);
  console.log("ListQuizes request params:", Object.fromEntries(searchParams));

  const result = await TryCatch<
    TAckResponse<TPopQuizListResponse[]>,
    TErrorResponse
  >(
    KY.get(`pop-quiz/quiz`, {
      searchParams: searchParams,
    }).json()
  );
  console.log("ListQuizes processed response:", result);
  return result;
};

const GetResponsesForQuiz = async (PopQuizID: string) => {
  return await TryCatch<TAckResponse<TQuizResponse[]>, TErrorResponse>(
    KY.get(`pop-quiz/quiz/${PopQuizID}/responses`).json()
  );
};

export const QUIZ = {
  GenerateQuestions,
  ListQuestions,
  GenerateQuizes,
  ListQuizes,
  GetResponsesForQuiz,
};
