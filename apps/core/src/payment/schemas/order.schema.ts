import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import {
  OrderStatus,
  PaymentGatewayProviders,
  SettlementStatus,
  TOrderStatus,
  TPaymentGatewayProviders,
  TSettlementStatus,
} from '../entities/payment.entity';
import GenerateID from '../../../common/lib/generate-id';

@Schema({
  timestamps: true,
  versionKey: false,
  collection: 'payment_orders',
})
export class OrderModel {
  @Prop({ type: Number, required: true })
  Amount: number;

  @Prop({ type: String, default: () => GenerateID.Generate32Hex('order_'), unique: true })
  OrderID: string;

  @Prop({ type: Date, default: () => new Date() })
  Timestamp: Date;

  @Prop({ type: String, enum: OrderStatus.options, default: OrderStatus.Enum.Pending })
  Status: TOrderStatus;

  @Prop({
    type: {
      Status: { type: String, enum: SettlementStatus.options, default: SettlementStatus.Enum.NA },
      Timestamp: { type: Date },
      ETA: { type: Date },
    },
  })
  Settlement: {
    Status: TSettlementStatus;
    Timestamp: Date;
    ETA: Date;
  };

  @Prop({ type: String, enum: PaymentGatewayProviders.options, default: PaymentGatewayProviders.Enum.RAZORPAY })
  Gateway: TPaymentGatewayProviders;

  @Prop({ type: String })
  IdempotentKey?: string;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  GatewaySignatures: Record<string, any>;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  VerificationSignatures: Record<string, any>;

  @Prop({ type: Boolean, default: false })
  OrderVerified: boolean;

  @Prop({ type: Date })
  OrderVerifiedAt: Date;

  @Prop({ type: String })
  Surcharge?: number;

  @Prop({ type: String })
  SurchargePercentage?: number;

  @Prop({ type: String })
  MerchantBearingCharge?: number;

  @Prop({ type: String })
  RedirectURL?: string;

  @Prop({ type: String })
  ErrorMessage?: string;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  Extras: Record<string, any>;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  ScopeParameters: Record<string, any>;

  @Prop({ type: String })
  Scope: string;

  @Prop({ type: String })
  TenantID: string;
}

export const OrderSchema = SchemaFactory.createForClass(OrderModel);
