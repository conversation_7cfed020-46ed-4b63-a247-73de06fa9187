import { usePaywallContext } from "@/contexts/paywall-context";

export default function AccessMode(props: {
  isStory?: boolean;
  isChat?: boolean;
  isPopQuiz?: boolean;
  children: React.ReactNode;
}) {
  const { isStory, isChat, isPopQuiz, children } = props;
  const { access } = usePaywallContext();

  return [
    access.chat.access && isChat,
    access.story.access && isStory,
    access.popQuiz.access && isPopQuiz,
  ].includes(true)
    ? children
    : null;
}
