import { API } from "@/apis/api";
import { TCreateResponseAnswer, TResponse } from "@/apis/pop-quiz.api";
import { usePopQuizContext } from "@/contexts/pop-quiz-context";
import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
} from "@workspace/ui/components/card";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@workspace/ui/components/carousel";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@workspace/ui/components/drawer";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@workspace/ui/components/hover-card";
import { Progress } from "@workspace/ui/components/progress";
import { cn } from "@workspace/ui/lib/utils";
import { ArrowLeft, ArrowRight, Check, CircleX, Save, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function Quiz() {
  const { popQuiz, setPopQuiz, refetch } = usePopQuizContext();

  const [completedQuestionCount, setCompletedQuestionCount] =
    useState<number>(0);
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const [answers, setAnswers] = useState<
    (TCreateResponseAnswer & { IsCorrect?: boolean })[]
  >([]);
  const [response, setResponse] = useState<TResponse | null>(null);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  if (!popQuiz) return null;

  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

  const handleSubmit = async () => {
    const { data, errors } = await API.POP_QUIZ.CreateResponse({
      PopQuizID: popQuiz.PopQuizID,
      Answers: answers,
    });
    if (errors) {
      errors.forEach((error) => {
        toast.error(error.message);
      });
      // preflight again
      setPopQuiz(null);
      refetch();
      return;
    }
    toast.success("Response created successfully!");
    setResponse(data?.ack || null);
    setAnswers(data?.ack?.Questions || []);

    // reset carousel
    api?.scrollTo(0);
  };

  return (
    <Drawer open={!!popQuiz} onOpenChange={() => {}}>
      <DrawerContent className="min-h-[100vh]">
        <DrawerHeader className="border-b">
          <DrawerTitle>{popQuiz.Title}</DrawerTitle>
          <DrawerDescription className="flex justify-between items-center gap-2">
            <div className="flex items-center gap-2">
              <span>{popQuiz.QuestionCount} Questions</span>
              {response && (
                <div className="flex  items-center gap-2 text-md">
                  <span>
                    <span className="font-semibold text-foreground">
                      You Score
                    </span>
                    <span className="font-semibold text-foreground">
                      {" "}
                      {response?.Result?.Score}%
                    </span>
                  </span>
                  <span className="flex items-center gap-2 text-sm">
                    <span className="font-semibold bg-green-50/20 text-green-500 px-2 py-1 rounded">
                      Correct {response?.Result?.CorrectAnswers}
                    </span>
                    <span className="font-semibold bg-red-50/20 text-red-500 px-2 py-1 rounded">
                      Incorrect {response?.Result?.IncorrectAnswers}
                    </span>
                  </span>
                </div>
              )}
            </div>
            {!response && (
              <div className="flex items-center gap-2 text-xl">
                <span>
                  <span className="font-semibold text-foreground">
                    {answers.length}
                  </span>
                  <span className="mx-1 text-muted-foreground">/</span>
                  <span className="font-semibold text-foreground">
                    {popQuiz.QuestionCount}
                  </span>
                </span>
              </div>
            )}
          </DrawerDescription>
          {!response && (
            <Progress
              value={(answers.length / popQuiz.QuestionCount) * 100}
              className="mt-2"
            />
          )}
        </DrawerHeader>

        <div className="flex flex-col justify-center items-center gap-2 max-w-md mx-auto">
          <div className="flex items-center gap-2 mt-2">
            <p>
              {current} of {count}
            </p>
          </div>
          <Carousel className="w-full mt-2" setApi={setApi}>
            <CarouselContent>
              {popQuiz.QuestionDetails.map((question) => {
                const findAnswer = answers.find(
                  (answer) => answer.QuestionID === question.QuestionID,
                );

                const isUserSubmittedCorrect = findAnswer?.IsCorrect;

                const findQuestion = response?.QuestionDetails.find(
                  (quest) => quest.QuestionID === question.QuestionID,
                );

                const correctOption = findQuestion?.Options.find(
                  (option) => option.IsCorrect,
                )?.OptionID;
                return (
                  <CarouselItem
                    key={question.QuestionID}
                    className="basis-1/1 overflow-y-auto"
                  >
                    <div>
                      <Card className="max-w-md mx-auto">
                        <CardContent className="p-2 flex flex-col gap-2">
                          <CardHeader>
                            <h2 className="text-sm font-semibold text-muted-foreground">
                              {question.Title}
                            </h2>
                            <h3 className="text-md">{question.QuestionText}</h3>
                            {response && (
                              <HoverCard>
                                <HoverCardTrigger asChild>
                                  <Button variant="outline" size="sm">
                                    Explanation
                                  </Button>
                                </HoverCardTrigger>
                                <HoverCardContent className="w-80">
                                  <div className="flex justify-between space-x-4">
                                    <p>
                                      {findQuestion?.Answer?.Explnation.replace(
                                        /\[.*?\]/g,
                                        "",
                                      )}
                                    </p>
                                  </div>
                                </HoverCardContent>
                              </HoverCard>
                            )}
                          </CardHeader>
                          <CardDescription className="flex flex-col gap-2 text-sm p-2 overflow-y-auto max-h-[300px]">
                            {question.Options?.map((option, index) => {
                              return (
                                <div
                                  key={option.OptionID}
                                  className={cn(
                                    "border-1 p-2 rounded-md flex gap-2 cursor-pointer hover:bg-accent group items-center",
                                    findAnswer?.OptionIDs.includes(
                                      option.OptionID,
                                    )
                                      ? "bg-accent"
                                      : "",
                                    response && "pointer-events-none",
                                  )}
                                  onClick={() => {
                                    if (response) {
                                      return;
                                    }
                                    const findAnswer = answers.find(
                                      (answer) =>
                                        answer.QuestionID ===
                                        question.QuestionID,
                                    );
                                    if (findAnswer) {
                                      const findIndex = answers.findIndex(
                                        (answer) =>
                                          answer.QuestionID ===
                                          question.QuestionID,
                                      );
                                      const newAnswers = [...answers];
                                      newAnswers[findIndex] = {
                                        QuestionID: question.QuestionID,
                                        OptionIDs: [option.OptionID],
                                      };
                                      setAnswers([...newAnswers]);
                                    } else {
                                      setAnswers([
                                        ...answers,
                                        {
                                          QuestionID: question.QuestionID,
                                          OptionIDs: [option.OptionID],
                                        },
                                      ]);
                                    }
                                  }}
                                >
                                  <span
                                    className={cn(
                                      "text-lg rounded-sm bg-accent text-accent-foreground p-2 group-hover:bg-primary group-hover:text-primary-foreground flex-wrap",
                                      findAnswer?.OptionIDs.includes(
                                        option.OptionID,
                                      )
                                        ? "bg-primary text-primary-foreground"
                                        : "",
                                    )}
                                  >
                                    {chars[index]}
                                  </span>
                                  <p className="text-md">{option.Title}</p>
                                  {response && (
                                    <p
                                      className={cn(
                                        "text-sm p-1 rounded-sm ml-auto",
                                        correctOption === option.OptionID
                                          ? "bg-green-200 text-green-700"
                                          : "bg-red-200 text-red-700",
                                      )}
                                    >
                                      {correctOption === option.OptionID ? (
                                        <Check
                                          className="h-5 w-5"
                                          fontVariant="bold"
                                        />
                                      ) : (
                                        <X
                                          className="h-5 w-5"
                                          fontVariant="bold"
                                        />
                                      )}
                                    </p>
                                  )}
                                </div>
                              );
                            })}
                          </CardDescription>
                        </CardContent>
                      </Card>
                    </div>
                  </CarouselItem>
                );
              })}
            </CarouselContent>
          </Carousel>

          <div className="flex justify-between items-center w-full mt-2">
            <Button
              variant="outline"
              onClick={() => api?.scrollPrev()}
              disabled={current === 1}
              className="gap-2 cursor-pointer"
            >
              <ArrowLeft className="h-4 w-4" />
              Previous Question
            </Button>
            <Button
              variant="outline"
              onClick={() => api?.scrollNext()}
              disabled={current === count}
              className="cursor-pointer gap-2"
            >
              <ArrowRight className="h-4 w-4" />
              Next Question
            </Button>
            {answers.length === popQuiz.QuestionCount && !response && (
              <Button
                onClick={handleSubmit}
                className="cursor-pointer gap-2 bg-primary"
              >
                <Save className="h-4 w-4" />
                Submit
              </Button>
            )}
            {response && (
              <Button
                onClick={() => {
                  setPopQuiz(null);
                  setResponse(null);
                  setAnswers([]);
                  refetch();
                }}
                className="cursor-pointer gap-2 bg-primary"
              >
                <Save className="h-4 w-4" />
                Close
              </Button>
            )}
          </div>
        </div>
        <Button
          onClick={() => {
            setPopQuiz(null);
            setResponse(null);
            setAnswers([]);
            refetch();
          }}
          className="cursor-pointer mt-2 w-md mx-auto"
        >
          <CircleX className="h-4 w-4" />
          Quit Quiz
        </Button>
      </DrawerContent>
    </Drawer>
  );
}
