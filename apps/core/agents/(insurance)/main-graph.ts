import { RunnableConfig } from '@langchain/core/runnables';
import { Annotation, END, MessagesAnnotation, START, StateGraph } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';
import { researcherGraph } from './researcher-graph';
import { reduceDocs } from './shared';
import { Document } from '@langchain/core/documents';
import { PROMPTS } from './prompts';
import { LLMInvocation, LLMOutput } from '../shared/utils';
import { calculateLLMInvocations } from '../shared/utils';

const OutputSchema = z.object({
  response: z.string(),
  citations: z.array(
    z.object({
      id: z.string(),
      source_title: z.string(),
      source_id: z.string().nullable(),
      page_number: z.number(),
      document_page_number: z.number(),
      line_from: z.number().nullable(),
      line_to: z.number().nullable(),
    })
  ),
});

export const InputStateAnnotation = Annotation.Root({
  ...MessagesAnnotation.spec,
  tenantId: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
  projectId: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
  Sources: Annotation<
    {
      SourceID: string;
      SourceName: string;
      Sections: {
        SectionID: string;
        SectionName: string;
        Description: string;
        SubSections: {
          SubSectionName: string;
          PageNumber: number;
        }[];
        PageNumber: {
          From: number;
          To: number;
        }[];
      }[];
      SourceSummary: {
        Title: string;
        Summary: string;
        MainTopics: string[];
        SectionHighlights: {
          SectionName: string;
          Highlight: string;
        }[];
      };
    }[]
  >(),
});

const AgentStateAnnotation = Annotation.Root({
  ...InputStateAnnotation.spec,
  router: Annotation<{
    type: 'more-info' | 'general-conversation' | 'industry-related';
    logic: string;
  }>(),
  initialDocuments: Annotation<Document[]>({
    default: () => [],
    reducer: reduceDocs,
  }),
  initialQueries: Annotation<string[]>(),
  refinedQueries: Annotation<string[]>(),
  refinedDocuments: Annotation<Document[]>({
    default: () => [],
    reducer: reduceDocs,
  }),
  TokenCount: Annotation<{
    PromptTokens: number;
    CompletionTokens: number;
    TotalTokens: number;
    LLMInvocations: LLMInvocation[];
  }>({
    default: () => ({
      PromptTokens: 0,
      CompletionTokens: 0,
      TotalTokens: 0,
      LLMInvocations: [],
    }),
    reducer: (acc, curr) => ({
      PromptTokens: acc.PromptTokens + curr.PromptTokens,
      CompletionTokens: acc.CompletionTokens + curr.CompletionTokens,
      TotalTokens: acc.TotalTokens + curr.TotalTokens,
      LLMInvocations: [...acc.LLMInvocations, ...curr.LLMInvocations],
    }),
  }),
});

async function analyzeAndRouteQuery(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'analyze_and_route_query'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const messages = [
    {
      role: 'system',
      content: PROMPTS.ANALYZE_AND_ROUTE_QUERY,
    },
    ...state.messages,
  ];

  const responseSchema = z
    .object({
      type: z.enum(['more-info', 'general-conversation', 'industry-related']),
      logic: z.string(),
    })
    .describe('Classify users query');

  const response = await model.withStructuredOutput(responseSchema).invoke(messages);

  return {
    router: {
      type: response.type,
      logic: response.logic,
    },
    TokenCount,
  };
}

async function askForMoreInfo(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.2,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'ask_for_more_info'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const systemPrompt = PROMPTS.MORE_INFO_PROMPT.replace('{logic}', state.router.logic).replace(
    '{sourcesSummary}',
    JSON.stringify(state.Sources?.map((s) => ({ SourceSummary: s.SourceSummary.Summary })))
  );
  const messages = [
    {
      role: 'system',
      content: systemPrompt,
    },
    ...state.messages,
  ];

  const response = await model.withStructuredOutput(OutputSchema).invoke(messages);

  return {
    messages: [JSON.stringify(response)],
    TokenCount,
  };
}

async function respondToGeneralConversation(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.7,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'respond_to_general_conversation'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const systemPrompt = PROMPTS.GENERAL_CONVERSATION_PROMPT.replace('{logic}', state.router.logic).replace(
    '{sourcesSummary}',
    JSON.stringify(state.Sources?.map((s) => ({ SourceSummary: s.SourceSummary.Summary })))
  );
  const messages = [
    {
      role: 'system',
      content: systemPrompt,
    },
    ...state.messages,
  ];

  const response = await model.withStructuredOutput(OutputSchema).invoke(messages);

  return {
    messages: [JSON.stringify(response)],
    TokenCount,
  };
}

async function conductResearch(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const researcherGraphConfig = {
    ...config.configurable,
    tenantId: state.tenantId,
    projectId: state.projectId,
    messages: state.messages,
    sources: state.Sources,
  };
  const researcherGraphResult = await researcherGraph.invoke(researcherGraphConfig);
  return {
    initialDocuments: researcherGraphResult.initialDocuments,
    initialQueries: researcherGraphResult.initialQueries,
    refinedDocuments: researcherGraphResult.refinedDocuments,
    refinedQueries: researcherGraphResult.refinedQueries,
    TokenCount: researcherGraphResult.TokenCount,
  };
}

async function analyzeAndConstructAnswer(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'analyze_and_construct_answer'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const uniqueIds = new Set();
  const documents = [...state.initialDocuments, ...state.refinedDocuments]
    .map((d) => {
      if (uniqueIds.has(d.id)) {
        return null;
      }
      uniqueIds.add(d.id);

      const chunk = JSON.parse(d.metadata?.['chunk'] ?? '{}');
      const source = JSON.parse(d.metadata?.['source'] ?? '{}');
      const sections = d.metadata['sections']?.map((s: any) => JSON.parse(s)) ?? [];

      console.log(source);

      return {
        content: d.pageContent,
        metadata: {
          chunkPageNumber: chunk.chunk_page_number || 'Unknown',
          sourceTitle: source.source_name || 'Unknown',
          sourceId: source.source_id || null,
          summarizedText: d.metadata['summary'],
          chunkSections: sections.map((s) => s.section_name),
          chunkId: chunk.chunk_id,
        },
      };
    })
    .filter((d) => d !== null);

  const { sourceIds, sectionIds } = documents.reduce(
    (acc, d) => {
      const source = JSON.parse(d.metadata?.['source'] ?? '{}');
      const sections = d.metadata['sections']?.map((s: any) => JSON.parse(s)) ?? [];
      if (source.source_id) {
        acc.sourceIds.add(source.source_id);
      }
      if (sections.length > 0) {
        acc.sectionIds.add(sections.map((s) => s.section_id).join(','));
      }
      return acc;
    },
    { sourceIds: new Set(), sectionIds: new Set() }
  );

  const sources = state.Sources?.filter((s) => sourceIds.has(s.SourceID)).map((s) => ({
    SourceID: s.SourceID,
    SourceName: s.SourceName,
    Sections: s.Sections.map((c) => ({
      SectionID: c.SectionID,
      SectionName: c.SectionName,
      Description: c.Description,
      SubSections: c.SubSections,
      PageNumber: c.PageNumber,
    })),
    SourceSummary: {
      Title: s.SourceSummary.Title,
      Summary: s.SourceSummary.Summary,
      MainTopics: s.SourceSummary.MainTopics,
      SectionHighlights: s.SourceSummary.SectionHighlights,
    },
  }));

  const systemPrompt = PROMPTS.ANALYZE_AND_CONSTRUCT_ANSWER.replace('{documents}', JSON.stringify(documents)).replace(
    '{sections}',
    JSON.stringify(sources)
  );

  const messages = [
    {
      role: 'system',
      content: systemPrompt,
    },
    ...state.messages,
  ];

  const response = await model.withStructuredOutput(OutputSchema).invoke(messages);

  return {
    messages: [JSON.stringify(response)],
    TokenCount,
  };
}

function queryRouter(
  state: typeof AgentStateAnnotation.State
): 'askForMoreInfo' | 'respondToGeneralConversation' | 'conductResearch' {
  switch (state.router.type) {
    case 'more-info':
      return 'askForMoreInfo';
    case 'general-conversation':
      return 'respondToGeneralConversation';
    case 'industry-related':
      return 'conductResearch';
    default:
      throw new Error(`Unknown router type: ${state.router.type}`);
  }
}

const builder = new StateGraph({
  stateSchema: AgentStateAnnotation,
  input: InputStateAnnotation,
})
  .addNode('analyzeAndRouteQuery', analyzeAndRouteQuery)
  .addNode('askForMoreInfo', askForMoreInfo)
  .addNode('respondToGeneralConversation', respondToGeneralConversation)
  .addNode('conductResearch', conductResearch, { subgraphs: [researcherGraph] })
  .addNode('analyzeAndConstructAnswer', analyzeAndConstructAnswer)
  .addEdge(START, 'analyzeAndRouteQuery')
  .addConditionalEdges('analyzeAndRouteQuery', queryRouter, [
    'askForMoreInfo',
    'respondToGeneralConversation',
    'conductResearch',
  ])
  .addEdge('conductResearch', 'analyzeAndConstructAnswer')
  .addEdge('askForMoreInfo', END)
  .addEdge('respondToGeneralConversation', END)
  .addEdge('analyzeAndConstructAnswer', END);

export const graph = builder.compile();

export async function streamInsuranceGraph(input: {
  messages: { role: string; content: string }[];
  Sources: {
    SourceID: string;
    SourceName: string;
    Sections: {
      SectionID: string;
      SectionName: string;
      Description: string;
      SubSections: {
        SubSectionName: string;
        PageNumber: number;
      }[];
      PageNumber: {
        From: number;
        To: number;
      }[];
    }[];
    SourceSummary: {
      Title: string;
      Summary: string;
      MainTopics: string[];
      SectionHighlights: {
        SectionName: string;
        Highlight: string;
      }[];
    };
  }[];
  tenantId: string;
  projectId: string;
}) {
  let TokenCount = {
    PromptTokens: 0,
    CompletionTokens: 0,
    TotalTokens: 0,
    LLMInvocations: [],
  };
  const stream = await graph.stream(
    {
      tenantId: input.tenantId,
      projectId: input.projectId,
      messages: input.messages,
      Sources: input.Sources,
    },
    {
      streamMode: 'messages',
      callbacks: [
        {
          handleLLMEnd(output: LLMOutput) {
            const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
              output,
              'message_stream'
            );
            const tokenCount = {
              PromptTokens,
              CompletionTokens,
              TotalTokens,
              LLMInvocations,
            };
            TokenCount = {
              PromptTokens: TokenCount.PromptTokens + tokenCount.PromptTokens,
              CompletionTokens: TokenCount.CompletionTokens + tokenCount.CompletionTokens,
              TotalTokens: TokenCount.TotalTokens + tokenCount.TotalTokens,
              LLMInvocations: [...TokenCount.LLMInvocations, ...tokenCount.LLMInvocations],
            };
          },
        },
      ],
    }
  );

  return new ReadableStream({
    async start(controller) {
      for await (const data of stream) {
        if (data[1].langgraph_node === 'analyzeAndRouteQuery') controller.enqueue('$$%%##analyzeAndRouteQuery##%%$$');
        if (data[1].langgraph_node === 'conductResearch') controller.enqueue('$$%%##conductResearch##%%$$');
        if (
          data[1].langgraph_node === 'askForMoreInfo' ||
          data[1].langgraph_node === 'respondToGeneralConversation' ||
          data[1].langgraph_node === 'analyzeAndConstructAnswer'
        ) {
          if (data[1].langgraph_node === 'respondToGeneralConversation')
            controller.enqueue('$$%%##respondToGeneralConversation##%%$$');
          controller.enqueue(data[0].content);
        }
      }
      controller.enqueue('$$%%##TokenCount##%%$$');
      controller.enqueue(JSON.stringify(TokenCount));
      controller.close();
    },
  });
}
