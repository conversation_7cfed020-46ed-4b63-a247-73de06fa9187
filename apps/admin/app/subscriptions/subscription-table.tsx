"use client";

import { useEffect, useState } from "react";
import { TSubscription, SubscriptionStatus } from "@/types/plan-subscription";
import { API } from "@/apis/api";
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Badge } from "@workspace/ui/components/badge";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Activity, PauseCircle, XCircle } from "lucide-react";
import Link from "next/link";
import { Input } from "@workspace/ui/components/input";
import { Search } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";

const statusColorMap = {
  [SubscriptionStatus.Active]: "bg-green-500",
  [SubscriptionStatus.Inactive]: "bg-gray-500",
  [SubscriptionStatus.Cancelled]: "bg-red-500",
  [SubscriptionStatus.Paused]: "bg-yellow-500",
  [SubscriptionStatus.Pending]: "bg-blue-500",
};

export default function SubscriptionTable() {
  const [subscriptions, setSubscriptions] = useState<TSubscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<SubscriptionStatus | "all">(
    "all"
  );

  useEffect(() => {
    fetchSubscriptions();
  }, []);

  const fetchSubscriptions = async () => {
    setLoading(true);
    try {
      const { data, errors } = await API.SUBSCRIPTION.ListSubscriptions({
        UserIDs: [],
      });
      if (data) {
        setSubscriptions(data);
      }
      if (errors) {
        toast.error(errors[0]?.message || "Failed to fetch subscriptions");
      }
    } catch (error) {
      toast.error("An error occurred while fetching subscriptions");
    } finally {
      setLoading(false);
    }
  };

  const filteredSubscriptions = subscriptions.filter((subscription) => {
    const matchesSearch =
      searchQuery === "" ||
      subscription?.User?.Name?.toLowerCase().includes(
        searchQuery.toLowerCase()
      ) ||
      subscription?.User?.Email?.toLowerCase().includes(
        searchQuery.toLowerCase()
      );

    const matchesStatus =
      statusFilter === "all" || subscription.Status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusCounts = () => {
    return subscriptions.reduce(
      (acc, sub) => {
        acc[sub.Status] = (acc[sub.Status] || 0) + 1;
        return acc;
      },
      {} as Record<SubscriptionStatus, number>
    );
  };

  const statusCounts = getStatusCounts();

  return (
    <div className="space-y-6">
      {/* Status Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">
              Active Subscriptions
            </CardTitle>
            <Activity className="w-4 h-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statusCounts[SubscriptionStatus.Active] || 0}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">
              Paused Subscriptions
            </CardTitle>
            <PauseCircle className="w-4 h-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statusCounts[SubscriptionStatus.Paused] || 0}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">
              Cancelled Subscriptions
            </CardTitle>
            <XCircle className="w-4 h-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statusCounts[SubscriptionStatus.Cancelled] || 0}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative w-full sm:w-72">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by name or email..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select
          value={statusFilter}
          onValueChange={(value) =>
            setStatusFilter(value as SubscriptionStatus | "all")
          }
        >
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value={SubscriptionStatus.Active}>Active</SelectItem>
            <SelectItem value={SubscriptionStatus.Paused}>Paused</SelectItem>
            <SelectItem value={SubscriptionStatus.Cancelled}>
              Cancelled
            </SelectItem>
            <SelectItem value={SubscriptionStatus.Pending}>Pending</SelectItem>
            <SelectItem value={SubscriptionStatus.Inactive}>
              Inactive
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Subscriptions Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Plan</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Start Date</TableHead>
              <TableHead>End Date</TableHead>
              <TableHead className="text-right">Price</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-10">
                  <div className="flex items-center justify-center">
                    <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredSubscriptions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-10">
                  {searchQuery || statusFilter !== "all"
                    ? "No subscriptions found matching the filters"
                    : "No subscriptions found"}
                </TableCell>
              </TableRow>
            ) : (
              filteredSubscriptions.map((subscription) => (
                <TableRow key={`${subscription.UserID}-${subscription.PlanID}`}>
                  <TableCell>
                    <Link
                      href={`/subscribers/${subscription.UserID}`}
                      className="font-medium hover:underline"
                    >
                      {subscription?.User?.Name}
                    </Link>
                    <div className="text-sm text-muted-foreground">
                      {subscription?.User?.Email}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {subscription?.Plan?.Name}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {subscription?.Plan?.Tier}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={`${statusColorMap[subscription.Status]} bg-opacity-10 border-0`}
                    >
                      <div
                        className={`h-2 w-2 rounded-full ${
                          statusColorMap[subscription.Status]
                        } mr-2`}
                      />
                      {subscription.Status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {subscription?.StartDate
                      ? format(new Date(subscription.StartDate), "PP")
                      : "No start date"}
                  </TableCell>
                  <TableCell>
                    {subscription?.EndDate
                      ? format(new Date(subscription.EndDate), "PP")
                      : "No end date"}
                  </TableCell>
                  <TableCell className="text-right">
                    {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: subscription.Plan.Currency,
                    }).format(subscription.Price / 100)}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
