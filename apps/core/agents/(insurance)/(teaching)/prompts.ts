import { AgeGroup, TeachingStyle } from '../../../src/lecture/dto/create-lecture.dto';

export const PROMPTS = {
  CREATE_TEACHING_PLAN: (
    SourceSummary: {
      Title: string;
      Summary: string;
      MainTopics: string[];
      SectionHighlights: { SectionName: string; Highlight: string }[];
    },
    CurrentSectionSummary: {
      SectionID: string;
      SectionName: string;
      Description?: string;
      SubSections?: { SubSectionName: string; PageNumber: number; DocumentPageNumber: number }[];
      PageNumber: { From: number; To: number };
      DocumentPageNumber: { From: number; To: number };
    },
    topicsAndSubtopics: { sections: string[] },
    previousSections: {
      SectionID: string;
      SectionName: string;
      Description?: string;
      SubSections?: { SubSectionName: string; PageNumber: number; DocumentPageNumber: number }[];
      PageNumber: { From: number; To: number };
      DocumentPageNumber: { From: number; To: number };
    }[],
    nextSections: {
      SectionID: string;
      SectionName: string;
      Description?: string;
      SubSections?: { SubSectionName: string; PageNumber: number; DocumentPageNumber: number }[];
      PageNumber: { From: number; To: number };
      DocumentPageNumber: { From: number; To: number };
    }[],
    TeachingStyle: string,
    CreativityLevel: number,
    AgeGroup: string
  ) => `
  You are an expert instructional designer tasked with creating a teaching plan for an audio lecture. Your goal is to design a plan that effectively covers the material in a human and understanding way.
  As an experienced instructional designer creating a teaching plan for a lecture on section "${CurrentSectionSummary.SectionName}" ${CurrentSectionSummary.SectionID ? `(Section ${CurrentSectionSummary.SectionID})` : ''}.

  Source Content can be from any industry or subject matter, like insurance, finance, law, etc and source document can be anything related to that and can be anything like book, article, report, pdf,policy, etc. Current Industry is will be provided in the context information.

  ## Context Information
  - Source title: "${SourceSummary.Title}"
  - Section summary: "${CurrentSectionSummary.Description}"
  - Main sections in this section: ${JSON.stringify(SourceSummary.MainTopics)}
  - Specific sections identified: ${topicsAndSubtopics.sections.join(', ')}
  ${previousSections.length > 0 ? `- Previous section(s): ${previousSections.map((c) => `"${c.SectionName}" covering ${JSON.stringify(c.SubSections)}"`).join('; ')}` : ''}
  ${nextSections.length > 0 ? `- Upcoming section(s): ${nextSections.map((c) => `"${c.SectionName}" covering ${JSON.stringify(c.SubSections)}"`).join('; ')}` : ''}
  - Current Industry: Insurance

  ## Teaching Parameters
  - Teaching style (Scientific/Storytelling/Socratic/Academic/Conversational/General): ${TeachingStyle}
  - Creativity level (0-1 scale): ${CreativityLevel}
  - Age group of Teacher (20s/30s/40s/50s/60s): ${AgeGroup}

  ## Your Task
  Create a detailed teaching plan that outlines how to present this section as an audio lecture. The plan should:

  1. Set clear learning objectives based on the section content
  2. Organize the content in a logical sequence, grouping related sections
  3. Include teaching strategies appropriate for the specified teaching style and age group
  4. Plan for references to previous sections when building upon earlier concepts
  5. Mention connections to future sections when introducing foundational concepts
  6. Include specific page references for important sections (available in the content)
  7. Structure the lecture with an engaging introduction, clear body sections, and a meaningful conclusion
  8. Plan how to explain complex sections using analogies or examples (with creativity appropriate to the specified level)

  Format your teaching plan as a detailed outline with clear sections. This plan will be used to generate the actual lecture script, so be specific about what content should be covered and how.
  `,
  GENERATE_LECTURE_SCRIPT: (
    SourceSummary: {
      Title: string;
      Summary: string;
      MainTopics: string[];
      SectionHighlights: { SectionName: string; Highlight: string }[];
    },
    CurrentSectionSummary: {
      SectionID: string;
      SectionName: string;
      Description?: string;
      SubSections?: { SubSectionName: string; PageNumber: number; DocumentPageNumber: number }[];
      PageNumber: { From: number; To: number };
      DocumentPageNumber: { From: number; To: number };
    },
    topicsAndSubtopics: { sections: string[] },
    previousSections: {
      SectionID: string;
      SectionName: string;
      Description?: string;
      SubSections?: { SubSectionName: string; PageNumber: number; DocumentPageNumber: number }[];
      PageNumber: { From: number; To: number };
      DocumentPageNumber: { From: number; To: number };
    }[],
    nextSections: {
      SectionID: string;
      SectionName: string;
      Description?: string;
      SubSections?: { SubSectionName: string; PageNumber: number; DocumentPageNumber: number }[];
      PageNumber: { From: number; To: number };
      DocumentPageNumber: { From: number; To: number };
    }[],
    TeachingPlan: string,
    CreativityLevel: number,
    InputAgeGroup: string,
    InputTeachingStyle: string,
    Chunks: {
      ChunkID: string;
      ChunkNumber: number;
      PageNumber: number;
      DocumentPageNumber: number;
      Summary: string;
      Content: string;
      Sections: { SectionID: string; SectionName: string; SubSections?: { SubSectionName: string }[] }[];
    }[]
  ) => {
    const styleGuide = {
      [TeachingStyle.STORYTELLING]:
        'Use narrative techniques with characters and plotlines to explain concepts. Create relatable scenarios and metaphors.',
      [TeachingStyle.TECHNICAL]:
        'Focus on precise explanations with technical terminology. Provide detailed breakdowns of processes and concepts.',
      [TeachingStyle.SCIENTIFIC]:
        'Emphasize the scientific method and evidence-based explanations. Include mentions of research and empirical findings.',
      [TeachingStyle.HISTORICAL]:
        'Take a chronological approach, explaining how concepts developed over time. Reference historical contexts and influential figures.',
      [TeachingStyle.CONVERSATIONAL]:
        'Use a casual, friendly tone with frequent questions and personal asides. Speak directly to the student as if in conversation.',
      [TeachingStyle.SOCRATIC]:
        'Ask guiding questions throughout, encouraging critical thinking. Build concepts by leading students through a logical thought process.',
      [TeachingStyle.INTERACTIVE]:
        'Include occasional activities, thought experiments, or moments of reflection. Prompt the listener to pause and consider concepts.',
    };

    const ageGroupGuide = {
      [AgeGroup.ELEMENTARY]:
        'Use simple language with concrete examples. Avoid abstract concepts. Maintain enthusiasm and frequent attention-grabbing elements.',
      [AgeGroup.MIDDLE_SCHOOL]:
        'Balance concrete examples with some abstract concepts. Use relatable examples from everyday life. Keep an energetic, encouraging tone.',
      [AgeGroup.HIGH_SCHOOL]:
        'Introduce more complex terminology with clear explanations. Connect concepts to real-world applications. Maintain a respectful but engaging tone.',
      [AgeGroup.UNDERGRADUATE]:
        'Use discipline-appropriate terminology. Discuss theoretical frameworks alongside practical applications. Maintain a professional but approachable tone.',
      [AgeGroup.GRADUATE]:
        'Employ advanced terminology and complex analysis. Reference current research and scholarly debates. Maintain a collegial, scholarly tone.',
      [AgeGroup.ADULT]:
        'Focus on practical applications and professional relevance. Acknowledge life experience and prior knowledge. Maintain a respectful, peer-like tone.',
    };
    return `
  You are an experienced teacher creating an engaging and informative audio lecture script for section "${CurrentSectionSummary.SectionName}" of the source document "${SourceSummary.Title}". Your goal is to create a script that is engaging, informative, and relevant to the section's content. You will be provided with the following information:

  Source Content can be from any industry or subject matter, like insurance, finance, law, etc and source document can be anything related to that and can be anything like book, article, report, pdf,policy, etc. Current Industry is will be provided in the context information. So take that into account while creating the script and industry specific terminology and concepts and overall context of the script.

  ## Context Information

  ## 1. Overall Context:
  - Source Title: "${SourceSummary.Title}"
  - Source Summary: "${SourceSummary.Summary}"
  - Source Current Industry: Insurance

  ## 2. Current Section Context (Section for which lecture is being created):
    - Section Summary: "${CurrentSectionSummary.Description}"
  - Main sections in this section: ${JSON.stringify(SourceSummary.MainTopics)}
  - Specific sections identified: ${topicsAndSubtopics.sections.join(', ')}
  - Section number: ${CurrentSectionSummary.SectionID}
  - Section name: "${CurrentSectionSummary.SectionName}"

  ## 3. Previous and Next Seciions:
${previousSections.length > 0 ? `- Previous sections: ${previousSections.map((c) => `"${c.SectionName}" covering ${JSON.stringify(c.SubSections)}"`).join('; ')}` : ''}
  ${nextSections.length > 0 ? `- Upcoming sections: ${nextSections.map((c) => `"${c.SectionName}" covering ${JSON.stringify(c.SubSections)}"`).join('; ')}` : ''}

  ## 4. Teaching Plan:
  ${TeachingPlan}

  ## 5. Admin Defined Style Guidelines
  - Teaching style (Scientific/Storytelling/Socratic/Academic/Conversational/General): ${InputTeachingStyle}
  - Style characteristics: ${styleGuide[InputTeachingStyle]}
  - Target age group (Elementary/Middle School/High School/Undergraduate/Graduate/Adult): ${InputAgeGroup}
  - Age group adaptations: ${ageGroupGuide[InputAgeGroup]}
  - Creativity level (0-1 scale): ${CreativityLevel}

  ## 6. Chapter Chunks:
  Each chunk MUST be covered in output and source text page number refrenced.
  ${Chunks.map((c) => `  - ${c.Sections.map((s) => `[Section: ${s.SectionName}]`).join(', ')} Page ${c.PageNumber}: ${c.Summary}`).join('\n')}
  Subsections: ${Chunks.map((c) => `  - ${c.Sections.map((s) => s.SubSections?.map((ss) => `[Subsection: ${ss.SubSectionName}]`).join(', ')).join(', ')}`).join('\n')}

 ## Instructions:

THE MAIN AIM OF THE LECTURE SCRIPT IS TO COVER ALL THE TOPICS AND CONCEPTS IN THE SECTION AND EXPLAIN THEM TO INDUSTRY USERS SO THAT THEY CAN UNDERSTAND THE SECTION. DO NOT OMIT ANY CONCEPT FROM THE CHUNKS. USE ALL PROVIDED TEXT. IT SHOULD BE LIKE VERY HELPFUL AND LECTURES TO UNDERSTAND AND REMEBER THE CONTENT. SO THAT IT SHOULD OUTPERFORMS THE LECTURES IN SCHOOL OR COACHING IT SHOULD BE VERY HELPFUL AND USERS SHOULD GET INVOVLED IN THE LECTURE AND SHOULD ENJOY IT. AND SAME TIME ALL CONCEPTS SHOULD STRICTLY FROM THE PROVIDED CHUNKS NO ADDITIOANAL CONCEPTS OR ANYTHING ELSE.

FOCUS ON THE KEY CONCEPTS IN THE SECTION, EMPAHASIZE THEM SUCH THAT USERS SHOULD REMEBER THEM AND ALSO UNDERSTAND, USE BEST PEDAGOGY CONCETPS TO DESIGN THE SCRIPT. IT SHOULD HELP USERS TO EXCEL IN THEIR STUDY AND LEARNING. YOU SHOULD ALSO CONSIDER SUBJECT SPECIFIC TEACHING METHODS AND STYLES.

Create a detailed script covering all subsections and duration can be anything which is suitable for the section it can be as long as it should be. General guidline is audio lecture should be at least 15 mins long and at max 30 mins, choose any length which suits the content and the audience. SCRIPT SHOULD BE MINIMUM 2000 WORDS LONG.

Your goal is to create a natural, conversational audio lecture script that:

1.  **Teaches Comprehensively:** Cover ALL key topics and concepts within the section. Do NOT omit any concepts from the chunks.

2.  **Leverages Section Context:**

*   **If First Section:** Briefly introduce the section using the Section Summary, highlighting the main themes and the purpose of the first section to build the initial foundation. Add a line explaining what is this section is for so the student will have idea on what this will be about.

    *   **Connects to Previous Sections:** When building on concepts from previous sections, briefly summarize the relevant information and explicitly cite the previous section (e.g., "As we learned in Section X, "). Look across the sections that are provided and explain in short if any information is related with any concepts from other sections, then please follow that by explaining the context by one line and page number from that specific section.

    *   **Foreshadows Future Sections:** When introducing foundational concepts that will be explored in more detail later, mention this connection and cite the upcoming section (e.g., "We'll delve deeper into this in Section Z, page AAA..."). Look across the sections that are provided and explain in short if any information is related with any concepts from other sections, then please follow that by explaining the context by one line and page number from that specific section.

4.  **Applies Appropriate Style:** Use a tone and language suitable for the defined teaching style and target age group.

5.  **Remains Conversational:** Create a human-like script that sounds natural and engaging. Consider this to be spoken as a lecture script.

6.  **Follows Structure:** Provide a clear introduction, well-organized body sections, and a thoughtful conclusion. Each structure MUST have all the information included in it
    * Start lecture with short intro about section
    * Then in body include all the topic based on the chunks provided
    * end the lecture with all the chunks that were teached, provide detailed conclusion or summary of the section.

7.  **Strictly Uses Provided Information:** Do NOT include any information or concepts that are not present in the provided data.

8.  **Act as a Real Human Teacher/Domain Expert:** Focus on simplifying complex information into a way that make users learn and should focus on all the text chunk.

9.  **Remember Always reference the chunk refrence whenever used."""
10. MAKE SURE USE ALL TOPICS AND SUBTOPICS PROVIDED IN THE CHUNKS. AND USE TOPICS AS A SECTION HEADINGS IN THE SCRIPT. As topic provided in the chunks are actual heading in the section.

## Output:

Create a full lecture script here using above parameter by considering yourself as a real life Teacher/Domain Expert and use all parameters to create audio lecture text.
Give direct output of the lecture script and do not include any other text or comments in the output and NO PREAMBLE like "Here is the lecture script" or anything like that. DO NOT MENTION ANY TEXT FOR SCENE DESCRIPTIONS LIKE [OPENING SCENE] OR [CLOSING SCENE] OR ANYTHING LIKE THAT. SCRIPT SHOULD BE MINIMUM 2000 WORDS LONG. AND ALSO DO NOT MENTION AN PREAMBLE OR ANY UNWANTED METADATA like Target Age Group, Teaching Style, Creativity Level, etc.


GIVE PROPER LINE BREAKS AND PARAGRAPH BREAKS AND HEADINGS AND SUBHEADINGS AND ANY OTHER FORMATTING THAT IS APPROPRIATE FOR THE SCRIPT USING RICH MARKDOWN FORMAT.
  `;
  },

  FORMAT_WITH_SSML: (LectureScript: string, InputTeachingStyle: string, InputAgeGroup: string) => {
    const ssmlStyleGuide = {
      [TeachingStyle.STORYTELLING]:
        'Use prosody for character voices. Add emphasis on key plot points. Include dramatic pauses and varied voice inflections.',
      [TeachingStyle.TECHNICAL]:
        'Maintain even pacing with strategic pauses before complex terms. Add emphasis on key technical terms. Use slightly slower rate for difficult concepts.',
      [TeachingStyle.SCIENTIFIC]:
        'Maintain formal pronunciation. Add emphasis on scientific terms and research findings. Use structured pauses between major concepts.',
      [TeachingStyle.HISTORICAL]:
        'Use a contemplative pace with reflective pauses. Emphasize dates and historical figures. Slight volume increases for significant events.',
      [TeachingStyle.CONVERSATIONAL]:
        'Include natural pauses and fillers. Vary pitch and rate as in casual conversation. Add emphasis that sounds spontaneous rather than rehearsed.',
      [TeachingStyle.SOCRATIC]:
        'Use rising intonation for questions. Include thoughtful pauses after questions. Emphasize key points in summarized conclusions.',
      [TeachingStyle.INTERACTIVE]:
        'Add longer pauses for reflection moments. Use increased volume and emphasis for activities. Change pace between instruction and activity segments.',
    };

    // Age group SSML adaptations
    const ssmlAgeGuide = {
      [AgeGroup.ELEMENTARY]:
        'Use higher pitch, energetic tone. Include sound effects where appropriate. Exaggerate emphasis and excitement. Use slower rate overall.',
      [AgeGroup.MIDDLE_SCHOOL]:
        'Moderately energetic tone. Brief sound effects for important points. Clear enunciation with slightly slower rate for new terms.',
      [AgeGroup.HIGH_SCHOOL]:
        'Balanced, natural tone. Strategic emphasis on key concepts. Appropriate pauses for note-taking. Normal conversational rate.',
      [AgeGroup.UNDERGRADUATE]:
        'Professional tone with academic emphasis. Structured pauses between concepts. Slight rate increases for reviews of familiar concepts.',
      [AgeGroup.GRADUATE]:
        'Scholarly, measured tone. Minimal sound effects. Strategic emphasis on nuanced points. Efficient pacing with breaks between complex sections.',
      [AgeGroup.ADULT]:
        'Professional, peer-like tone. No sound effects unless topic-appropriate. Strategic emphasis on practical applications. Efficient overall pacing.',
    };

    return `
  You are an expert in Speech Synthesis Markup Language (SSML) tasked with enhancing a lecture script for audio generation. Your goal is to format the script with appropriate SSML tags to create an engaging and effective audio learning experience tailored for the target audience.

## 1. Input Lecture Script:
${LectureScript}

## 2. Target Audience & Style:
* Teaching Style: ${InputTeachingStyle} (Guidelines: ${ssmlStyleGuide[InputTeachingStyle]})
* Target Age Group: ${InputAgeGroup} (Adaptations: ${ssmlAgeGuide[InputAgeGroup]})

## 3. SSML Requirements
  1. Convert the lecture script into properly formatted SSML tags
  2. Use appropriate tags for:
     - Emphasis (<emphasis>) on key terms and concepts
     - Pauses (<break>) between sections and for dramatic effect
     - Prosody (<prosody>) to adjust rate, pitch, and volume at appropriate moments
     - Paragraph breaks (<p>) and sentence breaks (<s>) for proper structure
     - Phonetic pronunciation (<phoneme>) for any technical or challenging terms
     - Any other SSML tags that are required to make the script more engaging and effective
  3. Enclose the entire script in <speak> tags
  4. Ensure all XML is properly nested and formatted
  5. Maintain the conversational, natural flow of the original script

  ## 4. Output
  Produce a complete SSML-tagged version of the lecture script that would sound natural and engaging when processed by a text-to-speech system.
  The final output should be valid compressed SSML STRING enclosed in <speak> tags with appropriate use of SSML elements throughout.

  Instructions:
  - Do not include any other text or comments in the output.
  - Only output the SSML-tagged version of the lecture script.
  - Do not add or change any additional information and do not alter the original script content, just give SSML optimized version of the script.
  - Use referencesto the page number and sections like introduction, body, etc in the script where it given in the script.
  - Do not add even a word to original script.
    Do not even omit any word from original script.
  KEEP THE ORIGINAL SCRIPT AS IT IS WORD BY WORD and JUST CONVERT IT TO SSML.
  OUTPUT SHOULD BE VALID VALID COMPRESSED SSML STRING.
  `;
  },

  GENERATE_MODULE_BASED_LECTURE_SCRIPTS: (
    SourceSummary: {
      Title: string;
      Summary: string;
      MainTopics: string[];
      SectionHighlights: { SectionName: string; Highlight: string }[];
    },
    CurrentSectionSummary: {
      SectionID: string;
      SectionName: string;
      Description?: string;
      SubSections?: { SubSectionName: string; PageNumber: number; DocumentPageNumber: number }[];
      PageNumber: { From: number; To: number };
      DocumentPageNumber: { From: number; To: number };
    },
    topicsAndSubtopics: { sections: string[] },
    previousSections: {
      SectionID: string;
      SectionName: string;
      Description?: string;
      SubSections?: { SubSectionName: string; PageNumber: number; DocumentPageNumber: number }[];
      PageNumber: { From: number; To: number };
      DocumentPageNumber: { From: number; To: number };
    }[],
    nextSections: {
      SectionID: string;
      SectionName: string;
      Description?: string;
      SubSections?: { SubSectionName: string; PageNumber: number; DocumentPageNumber: number }[];
      PageNumber: { From: number; To: number };
      DocumentPageNumber: { From: number; To: number };
    }[],
    TeachingPlan: string,
    CreativityLevel: number,
    InputAgeGroup: string,
    InputTeachingStyle: string,
    Chunks: {
      ChunkID: string;
      ChunkNumber: number;
      PageNumber: number;
      DocumentPageNumber: number;
      Summary: string;
      Content: string;
      Sections: { SectionID: string; SectionName: string; SubSections?: { SubSectionName: string }[] }[];
    }[]
  ) => {
    const styleGuide = {
      [TeachingStyle.STORYTELLING]:
        'Use narrative techniques with characters and plotlines to explain concepts. Create relatable scenarios and metaphors.',
      [TeachingStyle.TECHNICAL]:
        'Focus on precise explanations with technical terminology. Provide detailed breakdowns of processes and concepts.',
      [TeachingStyle.SCIENTIFIC]:
        'Emphasize the scientific method and evidence-based explanations. Include mentions of research and empirical findings.',
      [TeachingStyle.HISTORICAL]:
        'Take a chronological approach, explaining how concepts developed over time. Reference historical contexts and influential figures.',
      [TeachingStyle.CONVERSATIONAL]:
        'Use a casual, friendly tone with frequent questions and personal asides. Speak directly to the student as if in conversation.',
      [TeachingStyle.SOCRATIC]:
        'Ask guiding questions throughout, encouraging critical thinking. Build concepts by leading students through a logical thought process.',
      [TeachingStyle.INTERACTIVE]:
        'Include occasional activities, thought experiments, or moments of reflection. Prompt the listener to pause and consider concepts.',
    };

    const ageGroupGuide = {
      [AgeGroup.ELEMENTARY]:
        'Use simple language with concrete examples. Avoid abstract concepts. Maintain enthusiasm and frequent attention-grabbing elements.',
      [AgeGroup.MIDDLE_SCHOOL]:
        'Balance concrete examples with some abstract concepts. Use relatable examples from everyday life. Keep an energetic, encouraging tone.',
      [AgeGroup.HIGH_SCHOOL]:
        'Introduce more complex terminology with clear explanations. Connect concepts to real-world applications. Maintain a respectful but engaging tone.',
      [AgeGroup.UNDERGRADUATE]:
        'Use discipline-appropriate terminology. Discuss theoretical frameworks alongside practical applications. Maintain a professional but approachable tone.',
      [AgeGroup.GRADUATE]:
        'Employ advanced terminology and complex analysis. Reference current research and scholarly debates. Maintain a collegial, scholarly tone.',
      [AgeGroup.ADULT]:
        'Focus on practical applications and professional relevance. Acknowledge life experience and prior knowledge. Maintain a respectful, peer-like tone.',
    };

    return `
You are an experienced teacher tasked with creating an engaging, **module-based lecture script series** for the section "${CurrentSectionSummary.SectionName}" of the document "${SourceSummary.Title}". Your goal is to:

✅ Identify **5-10 distinct modules or mini-lectures** that together cover the entire section's content.
✅ Each module should cover **1-2 key topics or subtopics** and should be **5-7 minutes long** (minimum ~700-1000 words per script, but adapt based on content).
✅ The modules together should form a **complete, continuous audio lecture course** for the section.
✅ Each module script should:
  - **Introduce the module** (and link it to previous modules if relevant)
  - **Cover its topics fully, using the chunks provided**
  - **End with a short summary and a teaser/hook for the next module**
✅ The final output should be **a list of modules** in order, with each module script provided separately.

## Context Information

1️⃣ **Overall Context:**
- Source Title: "${SourceSummary.Title}"
- Source Summary: "${SourceSummary.Summary}"
- Current Industry: Insurance

2️⃣ **Current Section Context:**
- Section Summary: "${CurrentSectionSummary.Description}"
- Main topics: ${JSON.stringify(SourceSummary.MainTopics)}
- Topics/subtopics identified: ${topicsAndSubtopics.sections.join(', ')}
- Section ID: ${CurrentSectionSummary.SectionID}
- Section name: "${CurrentSectionSummary.SectionName}"

3️⃣ **Previous and Next Sections:**
${previousSections.length > 0 ? `- Previous sections: ${previousSections.map((c) => `"${c.SectionName}" covering ${JSON.stringify(c.SubSections)}`).join('; ')}` : ''}
${nextSections.length > 0 ? `- Upcoming sections: ${nextSections.map((c) => `"${c.SectionName}" covering ${JSON.stringify(c.SubSections)}`).join('; ')}` : ''}

4️⃣ **Teaching Plan:**
${TeachingPlan}

5️⃣ **Style Guidelines:**
- Teaching style: ${InputTeachingStyle}
- Style characteristics: ${styleGuide[InputTeachingStyle]}
- Target age group: ${InputAgeGroup}
- Age group adaptations: ${ageGroupGuide[InputAgeGroup]}
- Creativity level: ${CreativityLevel}

6️⃣ **Chunks:**
Each chunk MUST be included in the relevant module scripts and referenced by page number.
${Chunks.map((c) => `- [Page ${c.PageNumber}] ${c.Summary}`).join('\n')}

7️⃣ **Instructions:**
- Thoroughly analyze the **CurrentSectionSummary** and **Chunks** to determine the **5-10 best modules** (natural content groupings).
- Each module script should:
  ✅ **Focus on a core topic/subtopic** (no random grouping)
  ✅ Be standalone and complete for that topic
  ✅ Transition smoothly to the next module
- **NO concepts should be omitted.**
- Scripts must remain **conversational, human-like, and engaging**.
- Use industry-specific language (insurance) and terminology.
- Include references to previous and upcoming sections if relevant.
- Scripts should be structured as:
  1. **Introduction** (context + link to previous modules)
  2. **Main content** (detailed, thorough explanation from the chunks)
  3. **Conclusion** (recap + hook for next module)

The final output should be a JSON-like structure with this format:

[
  {
    "content_title": "Title of Module 1",
    "description": "Description of Module 1 (3-4 lines)",
    "transcript": "Script of Module 1 (5-7 min)"
  },
  {
    "content_title": "Title of Module 2",
    "description": "Description of Module 2 (3-4 lines)",
    "transcript": "Script of Module 2 (5-7 min)"
  },
  // ... more modules
]

**Your goal:** To create an entire lecture series that covers the section in a modular, pedagogically sound way, providing maximum clarity and engagement.

`;
  },

  FORMAT_MODULE_BASED_LECTURE_SCRIPTS: (
    Lectures: {
      content_title: string;
      description: string;
      transcript: string;
    }[]
  ) => {
    return `
    You are an expert in formatting lecture scripts. Your task is to take the following module-based lecture scripts and format them into a each script as rich HTML lecture script.

    ## Instructions:
    These are generated by the part of your system that generates module-based lecture scripts from given section of source documents.
    Currently transcript is simple text and you need to format it into a rich HTML string to show it in transcript section of the lecture.
    You can use text styling like bold, italic, underline, etc to make the script more engaging and readable.
    You can use headings and subheadings to structure the script, lists to list the points, line breaks to make the script more readable. Or what ever is required to make the script more engaging and readable.

    ## MANTADORY INSTRUCTIONS:
    YOUR TASK IS TO ONLY FORMAT THE TRANSCRIPT INTO RICH HTML STRING AND DO NOT ALTER OR CHANGE THE CONTENT OF THE TRANSCRIPT. USE EXACT SAME TRANSCRIPT AS IT IS. AND JUST FORMAT IT INTO RICH HTML STRING.
    DO NOT ADD ANYTHING ELSE TO THE TRANSCRIPT.
    DO NOT OMMIT ANYTHING FROM THE TRANSCRIPT.

    ## Input Structure:
    [
      {
        "content_id": "1",
        "content_title": "Title of Module 1",
        "description": "Description of Module 1",
        "transcript": "Script of Module 1"
      },
      {
        "content_id": "2",
        "content_title": "Title of Module 2",
        "description": "Description of Module 2",
        "transcript": "Script of Module 2"
      },
      // ... more modules
    ]

    ## Input:
    ${JSON.stringify(Lectures, null, 2)}

    ## Output:
    [
      {
        "content_id": "1", // THIS SHOULD BE EXACTLY SAME AS content_id in input (MANDATORY)
        "formatted_transcript": "Rich HTML string of Module 1"
      },
      {
        "content_id": "2", // THIS SHOULD BE EXACTLY SAME AS content_id in input (MANDATORY)
        "formatted_transcript": "Rich HTML string of Module 2"
      },
      // ... more modules
    ]
    `;
  },
};
