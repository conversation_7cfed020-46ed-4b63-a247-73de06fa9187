import { Test, TestingModule } from '@nestjs/testing';
import { PopQuizController } from './pop-quiz.controller';
import { PopQuizService } from './pop-quiz.service';

describe('PopQuizController', () => {
  let controller: PopQuizController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PopQuizController],
      providers: [PopQuizService],
    }).compile();

    controller = module.get<PopQuizController>(PopQuizController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
