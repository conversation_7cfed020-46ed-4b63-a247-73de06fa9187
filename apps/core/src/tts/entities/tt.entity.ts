import { z } from 'zod';

export const TTSProviders = z.enum(['Google', 'ElevenLabs']);
export type TTSProviders = z.infer<typeof TTSProviders>;

export const Status = z.enum(['Queued', 'Processing', 'Completed', 'Failed']);
export type Status = z.infer<typeof Status>;

export const AudioFormat = z.enum(['MP3', 'WAV', 'AAC']);
export type AudioFormat = z.infer<typeof AudioFormat>;

const GoogleTTSParams = z.object({
  Pitch: z.number(),
  SpeechRate: z.number(),
  VolumeGainDecibels: z.number(),
  SSML: z.boolean(),
  Voice: z.string(),
  LanguageCode: z.string(),
  Encoding: AudioFormat,
});
const AzureTTSParams = z.object({});
const AmazonTTSParams = z.object({});
const ElevenLabsTTSParams = z.object({
  FoundationModel: z.enum(['eleven_multilingual_v2', 'eleven_flash_v2_5']),
});

export type GoogleTTSParams = z.infer<typeof GoogleTTSParams>;
export type AzureTTSParams = z.infer<typeof AzureTTSParams>;
export type AmazonTTSParams = z.infer<typeof AmazonTTSParams>;
export type ElevenLabsTTSParams = z.infer<typeof ElevenLabsTTSParams>;

export type TTSParams = GoogleTTSParams | AzureTTSParams | AmazonTTSParams | ElevenLabsTTSParams;

export type CreditUsage = {
  Cost: number;
  Currency: string;
  Credits: number | null;
  PerCharacter: number;
};

export type AudioConfig = {
  SpeechRate: number;
  Pitch: number;
  Gender: 'MALE' | 'FEMALE';
  VoiceModel: string;
  Language: string;
  SampleRateHertz: number;
  VolumeGainDecibels: number; // -100 to 100
};

export type Event = {
  StartedAt: Date;
  FinishedAt: Date;
  FailedReason?: string;
};

export class TTS {
  Name: string;
  Summary: string;
  SpeechText: string;
  CharacterCount: number;
  AudioConfig: AudioConfig;
  Provider: TTSProviders;
  TTSParams: TTSParams;
  Status: Status;
  Scopes: Record<string, string>; // Queryable fields
  Duration: number;
  S3: {
    Location: string;
    Key: string;
    AudioFormat: AudioFormat;
    FileSize: number; // Size In KB
    Expiration: Date;
    AccessUrl: string;
  };
  CDN: {
    URL: string;
  };
  TenantID: string;
  CreditUsage: CreditUsage;
  Event: Event;
}
