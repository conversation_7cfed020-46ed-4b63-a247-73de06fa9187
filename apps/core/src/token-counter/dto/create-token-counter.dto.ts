import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON>O<PERSON>, <PERSON>Optional, IsString } from 'class-validator';
import { LLMInvocation } from '../../../agents/shared/utils';
import { AppTypeEnum } from '../../core/user/entities/user.entity';
export class GraphTokenCount {
  PromptTokens: number;
  CompletionTokens: number;
  TotalTokens: number;
  LLMInvocations: LLMInvocation[];
}

export class CreateTokenCounterDto {
  @IsString()
  @IsNotEmpty()
  ProjectID: string;

  @IsString()
  @IsNotEmpty()
  TenantID: string;

  @IsString()
  @IsNotEmpty()
  UserID: string;

  @IsString()
  @IsNotEmpty()
  ResourceType: 'message' | 'lecture' | 'pop_quiz' | 'embedding';

  @IsArray()
  @IsString({ each: true })
  ResourceIDs: string[];

  @IsObject()
  TokenCount: GraphTokenCount;

  @IsString()
  @IsNotEmpty()
  AppType: AppTypeEnum;
}
