from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class SubSection(BaseModel):
    SubSectionName: str


class Section(BaseModel):
    SectionID: str
    SectionName: str
    SubSections: List[SubSection] = Field(default_factory=list)


class Chunk(BaseModel):
    ChunkID: str
    SourceID: str
    Sections: List[Section] = Field(default_factory=list)
    ChunkNumber: int
    PageNumber: int
    DocumentPageNumber: int
    Summary: Optional[str] = None
    Content: str
    TenantID: str
    ProjectID: str
    createdAt: datetime = Field(default_factory=datetime.utcnow)
    updatedAt: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        collection = "ind_chunks"
        json_schema_extra = {
            "example": {
                "ChunkID": "chunk_123",
                "SourceID": "src_123",
                "Sections": [],
                "ChunkNumber": 1,
                "PageNumber": 1,
                "DocumentPageNumber": 1,
                "Content": "Sample content",
                "TenantID": "tenant_123",
                "ProjectID": "proj_123",
            }
        }
