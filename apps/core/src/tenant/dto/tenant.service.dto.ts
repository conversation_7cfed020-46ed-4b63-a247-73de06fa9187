import { PartialType } from '@nestjs/mapped-types';
import { Personalization, SocialCard } from '../entities/tenant.entity';

export class CreateTenantDto {
  Name: string;
  TenantID?: string;
  Logo?: string;
  WebsiteURL?: string;
  Tagline?: string;
  AdminUserID: string;
  WorkSpaceDomain: string;
  SocialCards?: SocialCard[];
  Personalization?: Personalization;
  Pictures?: string[];
}
export class UpdateTenantDto extends PartialType(CreateTenantDto) {}
