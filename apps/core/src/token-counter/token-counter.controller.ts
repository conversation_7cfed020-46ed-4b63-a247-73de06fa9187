import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { TokenCounterService } from './token-counter.service';
import { TenantID, User } from '../../common/decorators/core.decorators';
import { TUser } from '../core/user/entities/user.entity';
import { AuthAdminGuard } from '../../common/guards/auth.guard';

@UseGuards(AuthAdminGuard)
@Controller('token-counter')
export class TokenCounterController {
  constructor(private readonly tokenCounterService: TokenCounterService) {}

  @Get('tenant')
  async getTenantTokenUsage(@TenantID() tenantID: string, @User() user: TUser) {
    return this.tokenCounterService.getProjectsOverviewForTenant(tenantID, user.AppType);
  }

  @Get('tenant/project/:projectId')
  async getProjectTokenUsage(@Param('projectId') projectId: string, @TenantID() tenantID: string, @User() user: TUser) {
    return this.tokenCounterService.getUsersOverviewForProject(tenantID, user.AppType, projectId);
  }

  @Get('tenant/project/:projectId/user/:userId')
  async getUserTokenUsage(
    @Param('projectId') projectId: string,
    @Param('userId') userId: string,
    @TenantID() tenantID: string,
    @User() user: TUser
  ) {
    return this.tokenCounterService.getUserStatsForProject(tenantID, user.AppType, projectId, userId);
  }
}
