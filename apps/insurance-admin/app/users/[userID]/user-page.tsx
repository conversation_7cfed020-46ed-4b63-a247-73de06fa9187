"use client";
import { API } from "@/apis/api";
import { ResourcesTypes } from "@/apis/user.api";
import { SubscriptionStatus } from "@/types/plan-subscription";
import { TProject } from "@/types/project";
import { Resource, TUser } from "@/types/user";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Label } from "@workspace/ui/components/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { Switch } from "@workspace/ui/components/switch";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { format } from "date-fns";
import {
  Building2,
  Cake,
  CalendarIcon,
  ChevronLeftIcon,
  Info,
  Mail,
  MapPin,
  Phone,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const statusColorMap = {
  [SubscriptionStatus.Active]: "bg-green-500",
  [SubscriptionStatus.Inactive]: "bg-gray-500",
  [SubscriptionStatus.Cancelled]: "bg-red-500",
  [SubscriptionStatus.Paused]: "bg-yellow-500",
  [SubscriptionStatus.Pending]: "bg-blue-500",
};

export default function UserPage() {
  const [user, setUser] = useState<TUser | null>(null);
  const [projects, setProjects] = useState<TProject[]>([]);

  const userID = useParams<{ userID: string }>().userID;

  const fetchUser = async () => {
    const { data, errors } = await API.USER.GetUser({ UserID: userID || "" });
    if (data) {
      setUser(data);
    }
    if (errors) {
      toast.error(errors[0]?.message || "Something went wrong");
    }
  };

  const fetchProjects = async () => {
    const { data, errors } = await API.PROJECT.ListProjects();
    if (data) {
      setProjects(data);
    }
    if (errors) {
      errors.forEach((err) => toast.error(err.message));
    }
  };

  useEffect(() => {
    fetchUser();
    fetchProjects();
  }, [userID]);

  if (!user) return null;

  const router = useRouter();

  const AccessManagement = () => {
    const Resources = [
      {
        ResourceID: ResourcesTypes.Enum.Projects,
        Name: "Projects",
        HelpText:
          "Access to project with source upload, delete, Along with Pop Quiz",
      },
      {
        ResourceID: ResourcesTypes.Enum.ContentAlbum,
        Name: "Content Album",
        HelpText: "Create, Edit, Delete, View content albums",
      },
      {
        ResourceID: ResourcesTypes.Enum.Subscribers,
        Name: "Subscribers",
        HelpText: "Create, Edit, Delete, View subscribers users",
      },
      {
        ResourceID: ResourcesTypes.Enum.Subscriptions,
        Name: "Subscriptions",
        HelpText: "Create, Edit, Delete, View subscriptions",
      },
      {
        ResourceID: ResourcesTypes.Enum.Plans,
        Name: "Plans & Tiers",
        HelpText: "Create, Edit, Delete, View plans",
      },
      {
        ResourceID: ResourcesTypes.Enum.Users,
        Name: "Users",
        HelpText: "Create, Edit, Delete, View users",
      },
      {
        ResourceID: ResourcesTypes.Enum.Billing,
        Name: "Billing",
        HelpText:
          "View and manage all the billing and usage across all organizations",
      },
      {
        ResourceID: ResourcesTypes.Enum.Settings,
        Name: "Settings",
        HelpText: "Manage settings",
      },
    ];

    const [resourcesAccess, setResourcesAccess] = useState<Resource[]>(
      user.Access?.Resources || []
    );

    const handleSaveAccess = async () => {
      if (!user.UserID) {
        toast.error("User ID not found");
        return;
      }
      const { data, errors } = await API.USER.UpdateUserAccess({
        UserID: user.UserID,
        Access: {
          Resources: resourcesAccess.filter((r) => r.ResourceID !== ""),
        },
      });
      if (data) {
        toast.success(data.message);
      }
      if (errors) {
        toast.error(errors[0]?.message || "Something went wrong");
      }
      fetchUser();
    };

    return (
      <Card>
        <CardHeader>
          <CardTitle>Access Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-8 gap-y-4">
              {Resources.map((resource) => (
                <div className="flex flex-col gap-2">
                  <div
                    key={resource.ResourceID}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <span>{resource.Name}</span>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent className="bg-muted text-sm p-2">
                          <p>{resource.HelpText}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Switch
                      checked={
                        !!resourcesAccess?.find(
                          (r) => r.ResourceID === resource.ResourceID
                        )?.ResourceID
                      }
                      onCheckedChange={(checked) => {
                        const findResource = resourcesAccess?.find(
                          (r) => r.ResourceID === resource.ResourceID
                        );

                        if (findResource) {
                          setResourcesAccess(
                            resourcesAccess?.map((r) => {
                              if (r.ResourceID === resource.ResourceID) {
                                return {
                                  ...r,
                                  ResourceID: checked
                                    ? resource.ResourceID
                                    : "",
                                };
                              }
                              return r;
                            })
                          );
                        }

                        if (!findResource) {
                          setResourcesAccess([
                            ...resourcesAccess,
                            {
                              ResourceID: resource.ResourceID,
                              Properties: {},
                            },
                          ]);
                        }
                      }}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    {resource.ResourceID === ResourcesTypes.Enum.Projects &&
                      resourcesAccess?.find(
                        (r) => r.ResourceID === resource.ResourceID
                      )?.ResourceID && (
                        <div className="w-full">
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button variant="outline">
                                {resourcesAccess.find(
                                  (r) => r.ResourceID === resource.ResourceID
                                )?.Properties?.ProjectIDs?.length
                                  ? `${
                                      resourcesAccess.find(
                                        (r) =>
                                          r.ResourceID === resource.ResourceID
                                      )?.Properties?.ProjectIDs?.length
                                    } project(s) selected`
                                  : "Select Projects"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent>
                              <div className="space-y-2 max-h-60 overflow-y-auto scrollbar-thin">
                                {projects.map((project) => {
                                  const findResource = resourcesAccess.find(
                                    (r) => r.ResourceID === resource.ResourceID
                                  );
                                  const selectedProjects: string[] =
                                    findResource?.Properties?.ProjectIDs || [];

                                  const isSelected = selectedProjects.includes(
                                    project.ProjectID
                                  );

                                  return (
                                    <div
                                      key={project.ProjectID}
                                      className="flex items-center space-x-2"
                                    >
                                      <Checkbox
                                        id={project.ProjectID}
                                        checked={isSelected}
                                        onCheckedChange={(checked) => {
                                          const updated = resourcesAccess.map(
                                            (r) => {
                                              if (
                                                r.ResourceID ===
                                                resource.ResourceID
                                              ) {
                                                const currentIDs =
                                                  r.Properties?.ProjectIDs ||
                                                  [];

                                                return {
                                                  ...r,
                                                  Properties: {
                                                    ...r.Properties,
                                                    ProjectIDs: checked
                                                      ? [
                                                          ...currentIDs,
                                                          project.ProjectID,
                                                        ]
                                                      : currentIDs.filter(
                                                          (id: string) =>
                                                            id !==
                                                            project.ProjectID
                                                        ),
                                                  },
                                                };
                                              }
                                              return r;
                                            }
                                          );

                                          setResourcesAccess(updated);
                                        }}
                                      />
                                      <Label htmlFor={project.ProjectID}>
                                        {project.Name}
                                      </Label>
                                    </div>
                                  );
                                })}
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                      )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button onClick={handleSaveAccess}>Save Access</Button>
        </CardFooter>
      </Card>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={() => router.back()}>
          <ChevronLeftIcon />
        </Button>
        <h1 className="text-sm">Go Back To Users</h1>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={user.ProfilePicture} />
                <AvatarFallback>
                  {user.Name?.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-2xl">{user.Name}</CardTitle>
                <CardDescription>{user.Email}</CardDescription>
              </div>
            </div>
            <Badge
              variant={user.UserType === "Admin" ? "default" : "secondary"}
            >
              {user.UserType}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{user.Email}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span>{user.PhoneNumber || "Not provided"}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                <span>
                  {user?.DateOfBirth
                    ? format(new Date(user.DateOfBirth), "PPP")
                    : "Not provided"}
                </span>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{user.Address || "Not provided"}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Cake className="h-4 w-4 text-muted-foreground" />
                <span>
                  {user?.DateOfBirth
                    ? format(new Date(user.DateOfBirth), "PPP")
                    : "Not provided"}
                </span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Building2 className="h-4 w-4 text-muted-foreground" />
                <span>User ID: {user.UserID}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <AccessManagement />
    </div>
  );
}
