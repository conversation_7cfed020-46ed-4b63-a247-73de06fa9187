export enum TeachingStyle {
  SCIENTIFIC = "Scientific",
  HISTORICAL = "Historical",
  TECHNICAL = "Technical",
  STORYTELLING = "Storytelling",
  SOCRATIC = "Socratic",
  ACADEMIC = "Academic",
  CONVERSATIONAL = "Conversational",
  GENERAL = "General",
  INTERACTIVE = "Interactive",
}

export enum AgeGroup {
  ELEMENTARY = "Elementary",
  MIDDLE_SCHOOL = "Middle School",
  HIGH_SCHOOL = "High School",
  UNDERGRADUATE = "Undergraduate",
  GRADUATE = "Graduate",
  ADULT = "Adult",
}

export type TLecture = {
  _id: string;
  TenantID: string;
  ProjectID: string;
  SourceID: string;
  ChapterID: string;
  LectureTitle: string;
  LectureDescription: string;
  LectureSummary: string;
  TeachingStyle: TeachingStyle;
  AgeGroup: AgeGroup;
  CreativityLevel: string;
  LectureScript: string;
  SSMLScript: string;
  TeachingPlan: string;
  LectureID: string;
  createdAt: string;
  updatedAt: string;
};

export type TTSProcess = {
  Name: string;
  Summary: string;
  AudioConfig: {
    SpeechRate: number;
    Pitch: number;
    Gender: string;
    VoiceModel: string;
    Language: string;
    SampleRateHertz: number;
    VolumeGainDecibels: number;
  };
  SpeechText: string;
  CharacterCount: number;
  Duration: number;
  Provider: string;
  TTSParams: {
    SpeechRate: number;
    Pitch: number;
    Gender: string;
    VoiceModel: string;
    Language: string;
    SampleRateHertz: number;
    VolumeGainDecibels: number;
    Encoding: string;
  };
  Status: string;
  Scopes: {
    ProjectID: string;
  };
  TenantID: string;
  CreditUsage: {
    Cost: number;
    Currency: string;
    Credits: null | number;
    PerCharacter: number;
  };
  _id: string;
  createdAt: string;
  updatedAt: string;
};
