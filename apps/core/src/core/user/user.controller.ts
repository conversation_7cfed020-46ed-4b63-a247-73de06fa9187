import { Body, Controller, Post, Get, Param, Query, UseGuards, Patch } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UserService } from './user.service';
import { TUser, TUserType } from './entities/user.entity';
import { User } from '../../../common/decorators/core.decorators';
import { AuthAdminGuard, AuthGuard } from '../../../common/guards/auth.guard';
import { UpdateUserDto } from './dto/update-user.dto';
import { UpdateUserAccessDto } from './dto/update-user-acess.dto';

@UseGuards(AuthAdminGuard)
@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  async Create(@Body() params: CreateUserDto, @User() user: TUser) {
    const createdUser = await this.userService.Create({
      ...params,
      TenantID: user.TenantID,
      UserType: params.UserType,
      AppType: user.AppType,
    });
    return {
      message: 'User created successfully',
      ack: createdUser,
    };
  }

  @Patch(':UserID')
  async Update(@Body() params: UpdateUserDto, @User() user: TUser, @Param('UserID') UserID: string) {
    const updatedUser = await this.userService.Update(UserID, user.TenantID, params);
    return {
      message: 'User updated successfully',
      ack: updatedUser,
    };
  }

  @Get()
  async ListUser(@User() user: TUser, @Query('UserType') UserType: TUserType, @User() authUser: TUser) {
    const users = await this.userService.ListUsers({ TenantID: user.TenantID, UserType, AppType: authUser.AppType });
    return users;
  }

  @Get(':UserID')
  async GetUser(@Param('UserID') UserID: string, @User() authUser: TUser) {
    const user = await this.userService.GetUserById(UserID, authUser.TenantID);
    return user;
  }

  @Patch(':UserID/access')
  async UpdateUserAccess(
    @Body() params: Omit<UpdateUserAccessDto, 'UserID' | 'TenantID'>,
    @User() user: TUser,
    @Param('UserID') UserID: string
  ) {
    const updatedUser = await this.userService.UpdateUserAccess({
      ...params,
      UserID,
      TenantID: user.TenantID,
    });
    return {
      message: 'User access updated successfully',
      ack: updatedUser,
    };
  }

  @Patch(':UserID/password')
  async UpdateUserPassword(
    @Body() params: { NewPassword: string },
    @User() user: TUser,
    @Param('UserID') UserID: string
  ) {
    const updatedUser = await this.userService.Update(UserID, user.TenantID, { Password: params.NewPassword });
    return {
      message: 'User password updated successfully',
      ack: updatedUser,
    };
  }
}
