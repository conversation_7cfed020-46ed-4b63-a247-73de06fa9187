import { TryCatch } from "@/lib/try-catch";
import { KY, TAckResponse, TErrorResponse } from "./_.index";

export const SocialPlatform = [
  "Facebook",
  "Twitter",
  "LinkedIn",
  "Instagram",
  "YouTube",
  "Discord",
  "WhatsApp",
];

export interface TTenant {
  Name: string;
  Logo?: string;
  WebsiteURL?: string;
  Tagline?: string;
  TenantID: string;
  AdminUserID: string;
  WorkSpaceDomain: string;
  SocialCards?: { Platform: string; URL: string }[];
  Personalization?: {
    ShowSocialCards?: boolean;
    ShowTagline?: boolean;
    ShowMarquee?: boolean;
    ShowWebsiteURL?: boolean;
    ShowTenantName?: boolean;
    ShowTenantLogo?: boolean;
    MarqueeDirection?: "vertical" | "horizontal";
    HeaderPlacing?:
      | "justify-between"
      | "justify-end"
      | "justify-center"
      | "justify-start";
  };
  Pictures?: string[];
}

export const GetTenant = async () => {
  return await TryCatch<TTenant, TErrorResponse>(KY.get(`tenant`).json());
};

export const UpdateTenant = async (tenant: Partial<TTenant>) => {
  return await TryCatch<TAckResponse<TTenant>, TErrorResponse>(
    KY.patch(`tenant`, {
      json: tenant,
    }).json()
  );
};

export const GetTenantByDomain = async (workspaceDomain: string) => {
  return await TryCatch<TTenant, TErrorResponse>(
    KY.get(`tenant/workspace-domain/${workspaceDomain}`).json()
  );
};

export const TENANT = {
  GetTenant,
  UpdateTenant,
  GetTenantByDomain,
};
