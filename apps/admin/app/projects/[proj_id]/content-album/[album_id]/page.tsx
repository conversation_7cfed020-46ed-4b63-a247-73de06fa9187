"use client";
import { API } from "@/apis/api";
import { Content, ContentGroup, TContentAlbum } from "@/apis/content-album";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@radix-ui/react-accordion";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@workspace/ui/components/alert-dialog";
import { Button } from "@workspace/ui/components/button";
import AdvanceEditor from "@workspace/ui/components/editor";
import { Input } from "@workspace/ui/components/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { Slider } from "@workspace/ui/components/slider";

import {
  Sheet,
  SheetContent,
  SheetDescription,
  She<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/sheet";
import {
  <PERSON><PERSON>,
  TabsContent,
  Tabs<PERSON>ist,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { cn } from "@workspace/ui/lib/utils";
import {
  ArrowDownToDot,
  ArrowLeft,
  ArrowUpFromDot,
  AudioLines,
  Boxes,
  Clapperboard,
  Clock8,
  CloudAlert,
  FilePen,
  FileType,
  Fullscreen,
  Loader2,
  Pause,
  Pencil,
  Play,
  Plus,
  Save,
  ScrollText,
  Squirrel,
  Tags,
  Trash,
  Upload,
  Video,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

export default function ContentAlbum() {
  const { album_id, proj_id } = useParams();
  const [album, setAlbum] = useState<TContentAlbum | null>(null);
  const [uploadContent, setUploadContent] = useState(false);
  const [contentGroupID, setContentGroupID] = useState<string | null>(null);
  const [selectedContent, setSelectedContent] = useState<Content | null>(null);
  const [editContent, setEditContent] = useState(false);
  const [previewContent, setPreviewContent] = useState(false);
  const router = useRouter();

  const getContentAlbum = async () => {
    const { data, errors } = await API.CONTENT_ALBUM.GetContentAlbum({
      ContentAlbumID: album_id as string,
    });

    if (errors) {
      errors.forEach((error) => toast.error(error.message));
    }

    if (data) {
      setAlbum(data);
    }
  };

  useEffect(() => {
    if (album_id && proj_id) getContentAlbum();
  }, [album_id, proj_id]);

  const ManageContentGroup = (props: {
    contentGroup?: TContentAlbum["ContentGroups"][number];
  }) => {
    const { contentGroup } = props;
    const [contentGroupState, setContentGroupState] = useState(
      contentGroup ?? {
        Title: "",
        Description: "",
        SortOrder: album?.ContentGroups?.length ?? 0,
      }
    );
    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant="outline"
            size={contentGroup ? "icon" : "default"}
            className={cn(
              !contentGroup && "w-full",
              !contentGroup && "border-dashed border-primary text-primary"
            )}
          >
            {contentGroup ? <Pencil /> : <Plus />}
            {!contentGroup && "Add Module"}
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {contentGroup ? "Edit Module" : "Add Module"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              Add a new module to this content album
            </AlertDialogDescription>
          </AlertDialogHeader>
          <form>
            <div className="flex flex-col gap-3">
              <Input
                placeholder="Module Title"
                value={contentGroupState.Title}
                onChange={(e) =>
                  setContentGroupState({
                    ...contentGroupState,
                    Title: e.target.value,
                  })
                }
              />
              <Textarea
                placeholder="Module Description"
                value={contentGroupState.Description}
                onChange={(e) =>
                  setContentGroupState({
                    ...contentGroupState,
                    Description: e.target.value,
                  })
                }
              />
            </div>
          </form>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                const { data, errors } = contentGroup
                  ? await API.CONTENT_ALBUM.UpdateContentAlbum({
                      ContentAlbumID: album_id as string,
                      ContentGroups: album?.ContentGroups?.map((group) => {
                        if (
                          group.ContentGroupID === contentGroup.ContentGroupID
                        ) {
                          return contentGroupState;
                        }
                        return group;
                      }),
                    })
                  : await API.CONTENT_ALBUM.UpdateContentAlbum({
                      ContentAlbumID: album_id as string,
                      ContentGroups: [
                        ...(album?.ContentGroups ?? []),
                        {
                          Title: contentGroupState.Title,
                          Description: contentGroupState.Description,
                          SortOrder: album?.ContentGroups?.length ?? 0,
                        },
                      ],
                    });

                if (errors) {
                  errors.forEach((error) => toast.error(error.message));
                }

                if (data) {
                  setContentGroupState({
                    Title: "",
                    Description: "",
                    SortOrder: album?.ContentGroups?.length ?? 0,
                  });
                  toast.success("Module added successfully");
                }

                if (album) {
                  getContentAlbum();
                }
              }}
            >
              Submit
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  const UploadContent = () => {
    const [contentState, setContentState] = useState<{
      Meta: {
        Title: string;
        Description: string;
        Transcription: string;
      };
      ContentGroupID: string;
      ContentFile: File | null;
    }>({
      Meta: {
        Title: "",
        Description: "",
        Transcription: "",
      },
      ContentGroupID: contentGroupID ?? "",
      ContentFile: null,
    });

    const UploadNewContent = async () => {
      toast.loading("Uploading content...");
      const sortOrder =
        (album?.Contents.filter(
          (content) => content.ContentGroupID === contentState.ContentGroupID
        ).length ?? 0) + 1;
      const { data, errors } = await API.CONTENT_ALBUM.UploadContent({
        ContentAlbumID: album_id as string,
        Title: contentState.Meta.Title,
        Description: contentState.Meta.Description,
        Transcript: contentState.Meta.Transcription,
        ContentGroupID: contentState.ContentGroupID,
        Content: contentState.ContentFile as File,
        SortOrder: sortOrder,
      });

      if (errors) {
        errors.forEach((error) => toast.error(error.message));
      }

      if (data) {
        toast.success("Content uploaded successfully");
        setUploadContent(false);
        getContentAlbum();
      }
      toast.dismiss();
    };

    return (
      <Sheet open={uploadContent} onOpenChange={setUploadContent}>
        <SheetContent className="min-w-[600px]">
          <SheetHeader>
            <SheetTitle>Add Content</SheetTitle>
            <SheetDescription>
              Upload a new content to this content album
            </SheetDescription>
          </SheetHeader>
          <div className="p-2">
            <form className="flex flex-col gap-3">
              <Tabs defaultValue="metadata" className="gap-4">
                <TabsList>
                  <TabsTrigger value="metadata">
                    <Tags />
                    Metadata
                  </TabsTrigger>
                  <TabsTrigger value="transcript">
                    <ScrollText />
                    Transcript
                  </TabsTrigger>
                  <TabsTrigger value="content">
                    <Clapperboard />
                    Content
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="metadata" className="flex flex-col gap-3">
                  <Input
                    placeholder="Content Title"
                    value={contentState.Meta.Title}
                    onChange={(e) =>
                      setContentState({
                        ...contentState,
                        Meta: {
                          ...contentState.Meta,
                          Title: e.target.value,
                        },
                      })
                    }
                  />
                  <Textarea
                    placeholder="Description"
                    value={contentState.Meta.Description}
                    onChange={(e) =>
                      setContentState({
                        ...contentState,
                        Meta: {
                          ...contentState.Meta,
                          Description: e.target.value,
                        },
                      })
                    }
                    className="max-h-[50vh] min-h-[200px]"
                  />
                </TabsContent>
                <TabsContent value="transcript">
                  <div className="border rounded-lg">
                    <AdvanceEditor
                      content={contentState.Meta.Transcription}
                      onChange={(e) =>
                        setContentState({
                          ...contentState,
                          Meta: {
                            ...contentState.Meta,
                            Transcription: e,
                          },
                        })
                      }
                      editorClassName="max-h-[50vh] min-h-[220px] overflow-y-auto scrollbar-thin"
                    />
                  </div>
                </TabsContent>
                <TabsContent value="content">
                  <div
                    className={`relative border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-primary/50 transition-colors ${
                      contentState.ContentFile
                        ? "bg-muted/30 border-primary"
                        : "bg-muted/10"
                    }`}
                    onClick={() =>
                      document.getElementById("content-file-upload")?.click()
                    }
                  >
                    <input
                      id="content-file-upload"
                      type="file"
                      className="hidden"
                      onChange={(e) => {
                        setContentState({
                          ...contentState,
                          ContentFile: e.target.files?.[0] as File,
                        });
                      }}
                    />

                    {contentState.ContentFile ? (
                      <div className="flex flex-col items-center gap-2 py-2">
                        <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                          <Upload className="w-6 h-6 text-primary" />
                        </div>
                        <span className="text-sm font-medium">
                          {contentState.ContentFile.name}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {(
                            contentState.ContentFile.size /
                            (1024 * 1024)
                          ).toFixed(2)}{" "}
                          MB
                        </span>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center gap-2 py-4">
                        <div className="w-12 h-12 rounded-full bg-muted/30 flex items-center justify-center">
                          <Upload className="w-6 h-6 text-muted-foreground" />
                        </div>
                        <span className="text-sm font-medium">
                          Drag & drop your file here
                        </span>
                        <span className="text-xs text-muted-foreground">
                          or click to browse
                        </span>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </form>
          </div>
          <SheetFooter>
            <Button type="submit" onClick={UploadNewContent}>
              <Upload />
              Upload
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    );
  };

  const EditContent = () => {
    const [contentState, setContentState] = useState<{
      Meta: {
        Title: string;
        Description: string;
        Transcription: string;
      };
      ContentGroupID: string;
    }>({
      Meta: {
        Title: selectedContent?.Meta.Title ?? "",
        Description: selectedContent?.Meta.Description ?? "",
        Transcription: selectedContent?.Meta.Transcript ?? "",
      },
      ContentGroupID: selectedContent?.ContentGroupID ?? "",
    });
    const [uploading, setUploading] = useState(false);

    const ReUploadContent = async (file: File) => {
      toast.loading("Uploading Content...");
      setUploading(true);
      const { data, errors } = await API.CONTENT_ALBUM.UpdateContentMedia({
        ContentAlbumID: album_id as string,
        ContentID: selectedContent?.ContentID ?? "",
        Content: file,
      });

      if (errors) {
        errors.forEach((error) => toast.error(error.message));
        toast.dismiss();
      }

      if (data) {
        toast.success("Content uploaded successfully");
        getContentAlbum();
        setSelectedContent(null);
        setUploadContent(false);
        setEditContent(false);
        toast.dismiss();
      }
      setUploading(false);
    };

    const UpdateContentData = async () => {
      const { data, errors } = await API.CONTENT_ALBUM.UpdateContentData({
        ContentAlbumID: album_id as string,
        ContentID: selectedContent?.ContentID ?? "",
        Meta: {
          Title: contentState.Meta.Title,
          Description: contentState.Meta.Description,
          Transcript: contentState.Meta.Transcription,
        },
        ContentGroupID: contentState.ContentGroupID ?? "",
        Scopes: {},
      });

      if (errors) {
        errors.forEach((error) => toast.error(error.message));
      }

      if (data) {
        toast.success("Content updated successfully");
        getContentAlbum();
        setSelectedContent(null);
        setEditContent(false);
      }
    };

    const [file, setFile] = useState<File | null>(null);
    return (
      <Sheet open={editContent} onOpenChange={setEditContent}>
        <SheetContent className="min-w-[600px]">
          <SheetHeader>
            <SheetTitle>Edit Content</SheetTitle>
            <SheetDescription>Edit the content details</SheetDescription>
          </SheetHeader>
          <form
            onSubmit={async (e) => {
              e.preventDefault();
            }}
          >
            <div className="px-4">
              <Tabs defaultValue="metadata" className="gap-4">
                <TabsList>
                  <TabsTrigger value="metadata">
                    <Tags />
                    Metadata
                  </TabsTrigger>
                  <TabsTrigger value="transcript">
                    <ScrollText />
                    Transcript
                  </TabsTrigger>
                  <TabsTrigger value="content">
                    <Clapperboard />
                    Content
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="metadata" className="flex flex-col gap-2">
                  <Input
                    placeholder="Title"
                    value={contentState.Meta.Title}
                    onChange={(e) =>
                      setContentState({
                        ...contentState,
                        Meta: {
                          ...contentState.Meta,
                          Title: e.target.value,
                        },
                      })
                    }
                  />
                  <Textarea
                    placeholder="Description"
                    value={contentState.Meta.Description}
                    onChange={(e) =>
                      setContentState({
                        ...contentState,
                        Meta: {
                          ...contentState.Meta,
                          Description: e.target.value,
                        },
                      })
                    }
                    className="max-h-[50vh] min-h-[200px]"
                  />
                </TabsContent>
                <TabsContent value="transcript">
                  <div className="border rounded-lg">
                    <AdvanceEditor
                      content={contentState.Meta.Transcription}
                      onChange={(e) =>
                        setContentState({
                          ...contentState,
                          Meta: {
                            ...contentState.Meta,
                            Transcription: e,
                          },
                        })
                      }
                      editorClassName="max-h-[50vh] min-h-[220px] overflow-y-auto scrollbar-thin"
                    />
                  </div>
                </TabsContent>
                <TabsContent value="content">
                  <div className="mt-4 flex flex-col gap-2">
                    <h1 className="text-base font-semibold">
                      Change Uploaded Content
                    </h1>
                    <div
                      className={`relative border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-primary/50 transition-colors ${file ? "bg-muted/30 border-primary" : "bg-muted/10"}`}
                      onClick={() =>
                        document.getElementById("content-file-upload")?.click()
                      }
                    >
                      <input
                        id="content-file-upload"
                        type="file"
                        className="hidden"
                        onChange={(e) => {
                          setFile(e.target.files?.[0] as File);
                          ReUploadContent(e.target.files?.[0] as File);
                        }}
                      />

                      {file ? (
                        <div className="flex flex-col items-center gap-2 py-2">
                          <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                            <Upload className="w-6 h-6 text-primary" />
                          </div>
                          <span className="text-sm font-medium">
                            {file.name}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {(file.size / (1024 * 1024)).toFixed(2)} MB
                          </span>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center gap-2 py-4">
                          <div className="w-12 h-12 rounded-full bg-muted/30 flex items-center justify-center">
                            <Upload className="w-6 h-6 text-muted-foreground" />
                          </div>
                          <span className="text-sm font-medium">
                            Drag & drop your file here
                          </span>
                          <span className="text-xs text-muted-foreground">
                            or click to browse
                          </span>
                        </div>
                      )}
                    </div>
                    {uploading && (
                      <div className="flex gap-2 mt-2">
                        <Loader2 className="animate-spin" />
                        <p>Uploading Content...</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </form>
          <SheetFooter>
            <Button onClick={UpdateContentData} disabled={uploading}>
              <Pencil className="w-4 h-4 mr-2" />
              Save Changes
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    );
  };

  const PreviewContent = () => {
    const Player = () => {
      const ref = useRef<HTMLVideoElement>(null);
      useEffect(() => {
        if (ref.current) {
          ref.current.play();
        }
      }, [ref]);
      return (
        <div className="bg-accent w-full rounded-lg overflow-hidden lg:max-h-[500px] lg:min-h-[300px] max-h-[400px] min-h-[200px]">
          {selectedContent && selectedContent?.S3?.Type.includes("video") && (
            <video
              ref={ref}
              src={selectedContent?.CDN?.URL}
              controls
              autoPlay
              className="w-full h-full object-contain"
              controlsList="nodownload"
            />
          )}
          {selectedContent && selectedContent?.S3?.Type.includes("audio") && (
            <div className="">
              <AudioPlayer selectedAudioBook={selectedContent} />
            </div>
          )}
          {!selectedContent?.CDN?.URL && (
            <div className="flex flex-col items-center justify-center h-full min-h-[200px] lg:min-h-[300px]">
              <CloudAlert size={100} strokeWidth={1} />
              <h1 className="text-xl font-semibold mt-4">
                No Content Uploaded
              </h1>
            </div>
          )}
        </div>
      );
    };

    function AudioPlayer({
      selectedAudioBook,
    }: {
      selectedAudioBook: Content | null;
    }) {
      const audioRef = useRef<HTMLAudioElement>(null);
      const [isPlaying, setIsPlaying] = useState(false);
      const [currentTime, setCurrentTime] = useState(0);
      const [duration, setDuration] = useState(0);

      const handlePlayPause = () => {
        if (!audioRef.current) return;
        if (isPlaying) {
          audioRef.current.pause();
        } else {
          audioRef.current.play();
        }
        setIsPlaying(!isPlaying);
      };

      const handleTimeUpdate = () => {
        if (!audioRef.current) return;
        setCurrentTime(audioRef.current.currentTime);
      };

      const handleLoadedMetadata = () => {
        if (!audioRef.current) return;
        setDuration(audioRef.current.duration);
      };

      const formatTime = (time: number) => {
        const minutes = Math.floor(time / 60)
          .toString()
          .padStart(2, "0");
        const seconds = Math.floor(time % 60)
          .toString()
          .padStart(2, "0");
        return `${minutes}:${seconds}`;
      };

      const handleSliderChange = (value: number[]) => {
        if (!audioRef.current || !value.length) return;
        const time = value[0];
        audioRef.current.currentTime = time ?? 0;
        setCurrentTime(time ?? 0);
      };

      useEffect(() => {
        if (selectedAudioBook?.S3?.AccessUrl && audioRef.current) {
          audioRef.current.load(); // reload the new URL
          audioRef.current
            .play()
            .then(() => {
              setIsPlaying(true);
            })
            .catch((error) => {
              console.error("Autoplay failed:", error);
              setIsPlaying(false);
            });
          setCurrentTime(0);
          setDuration(0);
        }
        if (selectedAudioBook?.CDN?.URL) {
          handlePlayPause();
        }
      }, [selectedAudioBook?.CDN?.URL]);

      // set playing false when audio is done
      useEffect(() => {
        if (!audioRef.current) return;
        audioRef.current.addEventListener("ended", () => {
          setIsPlaying(false);
        });
      }, [audioRef]);

      return (
        <div className="flex flex-col w-full p-2 space-y-2">
          {/* <div
            style={{ width: "100%", height: "300px", position: "relative" }}
            className="pointer-events-none"
          >
            <Orb
              hoverIntensity={0.5}
              rotateOnHover={false}
              hue={0}
              forceHoverState={isPlaying}
            />
          </div> */}
          <div className="flex items-center">
            <div className="flex-1 ml-2">
              <p className="text-sm font-medium">
                {selectedAudioBook?.Meta?.Title ?? "No Audio Book Selected"}
              </p>
              <p className="text-xs text-muted-foreground">
                {formatTime(currentTime)} / {formatTime(duration)}
              </p>
            </div>

            <div className="flex items-center gap-2">
              <button onClick={handlePlayPause} className="cursor-pointer">
                {isPlaying ? (
                  <Pause className="h-8 w-8" />
                ) : (
                  <Play className="h-8 w-8" />
                )}
              </button>
            </div>
          </div>

          {/* Progress Bar */}
          <Slider
            value={[currentTime]}
            max={duration || 1}
            step={0.1}
            onValueChange={handleSliderChange}
            className="w-full mt-4"
          />

          {/* Hidden Audio Element */}
          <audio
            ref={audioRef}
            src={selectedAudioBook?.CDN?.URL ?? ""}
            preload="metadata"
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
          />
        </div>
      );
    }

    const Transcript = () => {
      return (
        <>
          {selectedContent && (
            <div className="flex flex-col gap-2 overflow-y-auto scrollbar-thin">
              <div className="flex flex-col">
                <h1 className="lg:text-xl text-md font-semibold">
                  {selectedContent?.Meta.Title}
                </h1>
                <p className="lg:text-sm text-xs text-muted-foreground">
                  {selectedContent?.Meta.Description ?? "..."}
                </p>
              </div>
              <Accordion
                collapsible
                type="single"
                className="bg-accent px-4 py-2 rounded-lg"
                defaultValue="transcript"
              >
                <AccordionItem value="transcript">
                  <AccordionTrigger className="hover:no-underline cursor-pointer">
                    <div className="flex items-center gap-2 hover:no-underline">
                      <ScrollText size={16} />
                      Transcript
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <AdvanceEditor
                      viewOnly
                      content={selectedContent?.Meta?.Transcript}
                    />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          )}
        </>
      );
    };

    return (
      <Sheet open={previewContent} onOpenChange={setPreviewContent}>
        <SheetContent className="min-w-[600px] px-4">
          <SheetHeader>
            <SheetTitle>Preview Content</SheetTitle>
            <SheetDescription></SheetDescription>
          </SheetHeader>
          <Player />
          {!selectedContent && (
            <div className="flex flex-col gap-2">
              <h1 className="text-lg font-semibold">{album?.Name}</h1>
              <p className="text-muted-foreground">
                {album?.Description ?? "..."}
              </p>
            </div>
          )}
          <Transcript />
        </SheetContent>
      </Sheet>
    );
  };

  const handleReorderContentGroup = async (ContentGroups: ContentGroup[]) => {
    const { data, errors } = await API.CONTENT_ALBUM.UpdateContentAlbum({
      ContentAlbumID: album_id as string,
      ContentGroups,
    });

    if (errors) {
      errors.forEach((error) => toast.error(error.message));
    }

    if (data) {
      toast.success("Content group reordered successfully");
    }
  };

  return (
    <div className="w-full h-full flex flex-col gap-4 px-4 py-6">
      <Button
        variant="outline"
        onClick={() => {
          router.back();
        }}
        className="self-start"
      >
        <ArrowLeft size={20} />
        Back
      </Button>
      {album && (
        <>
          <div
            className="rounded-lg border flex gap-2"
            key={album.ContentAlbumID}
          >
            <div className="flex items-center gap-2 p-2">
              <img
                src={album.CoverImage}
                alt={album.Name}
                width={"100%"}
                height={"100%"}
                className="w-full h-full object-cover rounded-lg max-h-40 overflow-hidden"
              />
            </div>
            <div className="flex flex-col gap-2 py-2">
              <div className="">
                <h2 className="text-lg font-semibold">{album.Name}</h2>
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {album.Description ?? "..."}
                </p>
              </div>
              <div className="flex items-center gap-4 text-md">
                <span className="flex items-center gap-1">
                  <Clock8 size={20} />
                  <span className="text-muted-foreground">
                    {Math.floor((album.ContentDuration ?? 0) / 60 / 60) > 0
                      ? Math.floor((album.ContentDuration ?? 0) / 60 / 60) +
                        " hours"
                      : Math.floor((album.ContentDuration ?? 0) / 60) +
                        " minutes"}
                  </span>
                </span>
                <span className="flex items-center gap-1">
                  <Clapperboard size={20} />
                  <span className="text-muted-foreground">
                    {album.ContentCount ?? 0} contents
                  </span>
                </span>
                <span className="flex items-center gap-1">
                  <Boxes size={20} />
                  <span className="text-muted-foreground">
                    {album.ContentGroups.length} modules
                  </span>
                </span>
              </div>
            </div>
          </div>
          <div className="flex justify-between gap-2 w-full">
            <ManageContentGroup />
          </div>
          <div className="flex flex-col rounded-lg">
            <Accordion
              type="single"
              // defaultValue={album?.ContentGroups?.[0]?.ContentGroupID}
              className="w-full"
              collapsible
            >
              {album?.ContentGroups?.sort(
                // sort by sortOrder
                (a, b) => (a.SortOrder ?? 0) - (b.SortOrder ?? 0)
              )?.map((group) => {
                const contents = album?.Contents?.filter(
                  (content) => content.ContentGroupID === group.ContentGroupID
                )?.sort(
                  // sort by sortOrder
                  (a, b) => (a.SortOrder ?? 0) - (b.SortOrder ?? 0)
                );

                const groupDuration = contents?.reduce(
                  (total, content) => total + (content.Duration ?? 0),
                  0
                );
                const currentIndex = album?.ContentGroups?.findIndex(
                  (g) => g.ContentGroupID === group.ContentGroupID
                );
                return (
                  <AccordionItem
                    key={group.ContentGroupID}
                    value={group.ContentGroupID!}
                    className="mt-2 p-2 rounded-lg border-1"
                  >
                    <AccordionTrigger className="hover:no-underline cursor-pointer w-full">
                      <div className="flex items-center justify-between w-full p-2">
                        <div className="flex flex-col">
                          <h1 className="text-start text-lg font-semibold">
                            {group.Title}
                          </h1>
                          <p className="text-start text-xs text-muted-foreground max-w-xl line-clamp-2">
                            {group.Description ?? "..."}
                          </p>
                          <div className="flex items-center gap-4 mt-2">
                            <div className="flex items-center gap-1">
                              <Clock8 size={16} />
                              <span className="text-xs text-muted-foreground">
                                {Math.floor((groupDuration ?? 0) / 60 / 60) > 0
                                  ? Math.floor((groupDuration ?? 0) / 60 / 60) +
                                    " hours"
                                  : Math.floor((groupDuration ?? 0) / 60) > 0
                                    ? Math.floor((groupDuration ?? 0) / 60) +
                                      " minutes"
                                    : Math.floor(groupDuration ?? 0) +
                                      " seconds"}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clapperboard size={16} />
                              <span className="text-xs text-muted-foreground">
                                {contents?.length ?? 0} contents
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-4 text-muted-foreground">
                            <Tooltip>
                              <TooltipTrigger>
                                <ArrowUpFromDot
                                  size={20}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    const currentIndex =
                                      album?.ContentGroups?.findIndex(
                                        (g) =>
                                          g.ContentGroupID ===
                                          group.ContentGroupID
                                      );

                                    if (
                                      currentIndex !== undefined &&
                                      currentIndex > 0
                                    ) {
                                      const newContentGroups = [
                                        ...album?.ContentGroups,
                                      ];
                                      const [movedGroup] =
                                        newContentGroups.splice(
                                          currentIndex,
                                          1
                                        );
                                      newContentGroups.splice(
                                        currentIndex - 1,
                                        0,
                                        movedGroup as any
                                      );
                                      const newOrder = newContentGroups.map(
                                        (group, index) => ({
                                          ...group,
                                          SortOrder: index + 1,
                                        })
                                      );
                                      handleReorderContentGroup(newOrder);
                                      setAlbum({
                                        ...album,
                                        ContentGroups: newOrder,
                                      });
                                    }
                                  }}
                                  className={cn(
                                    "cursor-pointer",
                                    currentIndex === 0 &&
                                      "opacity-50 cursor-not-allowed"
                                  )}
                                />
                              </TooltipTrigger>
                              <TooltipContent>Move up</TooltipContent>
                            </Tooltip>
                            <Tooltip>
                              <TooltipTrigger>
                                <ArrowDownToDot
                                  size={20}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    const currentIndex =
                                      album?.ContentGroups?.findIndex(
                                        (g) =>
                                          g.ContentGroupID ===
                                          group.ContentGroupID
                                      );

                                    if (
                                      currentIndex !== undefined &&
                                      currentIndex <
                                        album?.ContentGroups?.length - 1
                                    ) {
                                      const newContentGroups = [
                                        ...album?.ContentGroups,
                                      ];
                                      const [movedGroup] =
                                        newContentGroups.splice(
                                          currentIndex,
                                          1
                                        );
                                      newContentGroups.splice(
                                        currentIndex + 1,
                                        0,
                                        movedGroup as any
                                      );
                                      const newOrder = newContentGroups.map(
                                        (group, index) => ({
                                          ...group,
                                          SortOrder: index + 1,
                                        })
                                      );
                                      handleReorderContentGroup(newOrder);
                                      setAlbum({
                                        ...album,
                                        ContentGroups: newOrder,
                                      });
                                    }
                                  }}
                                  className={cn(
                                    "cursor-pointer",
                                    currentIndex ===
                                      album?.ContentGroups?.length - 1 &&
                                      "opacity-50 cursor-not-allowed"
                                  )}
                                />
                              </TooltipTrigger>
                              <TooltipContent>Move down</TooltipContent>
                            </Tooltip>
                          </div>
                          <ManageContentGroup contentGroup={group} />
                          <Button
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              setUploadContent(true);
                              setContentGroupID(group.ContentGroupID ?? null);
                            }}
                          >
                            <Plus />
                            Add Content
                          </Button>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Trash
                                className="cursor-pointer text-red-500"
                                size={20}
                                onClick={(e) => e.stopPropagation()}
                              />
                            </PopoverTrigger>
                            <PopoverContent className="flex flex-col gap-2">
                              <p>Are you sure you want to delete this group?</p>
                              <Button
                                variant="destructive"
                                disabled={contents?.length > 0}
                                onClick={async (e) => {
                                  e.stopPropagation();
                                  // check if group has contents
                                  if (contents?.length > 0) {
                                    toast.error("Group has contents");
                                    return;
                                  }
                                  await API.CONTENT_ALBUM.UpdateContentAlbum({
                                    ContentAlbumID: album?.ContentAlbumID ?? "",
                                    ContentGroups: album?.ContentGroups?.filter(
                                      (g) =>
                                        g.ContentGroupID !==
                                        group.ContentGroupID
                                    ),
                                  });
                                  toast.success("Group deleted");
                                  setAlbum({
                                    ...album,
                                    ContentGroups: album?.ContentGroups?.filter(
                                      (g) =>
                                        g.ContentGroupID !==
                                        group.ContentGroupID
                                    ),
                                  });
                                }}
                              >
                                Delete Group
                              </Button>
                            </PopoverContent>
                          </Popover>
                        </div>
                      </div>
                    </AccordionTrigger>

                    <AccordionContent>
                      <div className="flex flex-col gap-2 px-2 py-4">
                        {contents?.map((content) => {
                          return (
                            <div
                              key={content.ContentID}
                              className={cn(
                                "flex gap-2 cursor-pointer justify-between items-center py-2 px-2 rounded-lg hover:bg-secondary border border-secondary"
                              )}
                              onClick={() => {
                                setSelectedContent(content);
                                setEditContent(true);
                              }}
                            >
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-muted-foreground bg-primary/20 px-2 py-1 rounded-full font-bold">
                                  {content.SortOrder}
                                </span>
                                <div className="flex flex-col">
                                  <h1 className="text-sm font-semibold line-clamp-2 max-w-xl">
                                    {content.Meta.Title}
                                  </h1>
                                  {/* <p className="text-xs text-muted-foreground line-clamp-2">
                                    {content.Meta.Description ?? "..."}
                                  </p> */}
                                  <div className="flex items-center gap-2 mt-1 flex-wrap">
                                    {content?.S3?.AccessUrl && (
                                      <div className="flex items-center gap-2 bg-accent px-2 py-1 rounded-lg text-xs">
                                        <Save size={15} />
                                        <span>
                                          {content?.S3?.FileSize / 1024 > 1
                                            ? (
                                                content?.S3?.FileSize / 1024
                                              ).toFixed(2)
                                            : content?.S3?.FileSize.toFixed(
                                                2
                                              )}{" "}
                                          {content?.S3?.FileSize > 1024
                                            ? "MB"
                                            : "KB"}
                                        </span>
                                      </div>
                                    )}
                                    {content?.S3?.FileName && (
                                      <div className="flex items-center gap-2 bg-accent px-2 py-1 rounded-lg text-xs">
                                        <FilePen size={15} />
                                        <span>{content?.S3?.FileName}</span>
                                      </div>
                                    )}

                                    {content?.S3?.Type && (
                                      <div className="flex items-center gap-2 bg-accent px-2 py-1 rounded-lg text-xs">
                                        <FileType size={15} />
                                        <span>{content?.S3?.Type}</span>
                                      </div>
                                    )}

                                    {!content?.S3?.AccessUrl && (
                                      <div className="flex items-center gap-2 bg-red-500/20 px-2 py-1 rounded-lg text-xs">
                                        <CloudAlert size={15} />
                                        <span>No Content</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                              <div className="flex gap-3 items-center">
                                <Button
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setPreviewContent(true);
                                    setSelectedContent(content);
                                  }}
                                  size="sm"
                                  className="text-xs cursor-pointer"
                                >
                                  <Fullscreen />
                                  Preview
                                </Button>
                                <div className="flex items-center gap-2">
                                  <Clock8 size={16} />
                                  <span className="text-xs text-muted-foreground">
                                    {Math.floor(
                                      (content.Duration ?? 0) / 60 / 60
                                    ) > 0
                                      ? Math.floor(
                                          (content.Duration ?? 0) / 60 / 60
                                        ) + " hours"
                                      : Math.floor(
                                            (content.Duration ?? 0) / 60
                                          ) > 0
                                        ? Math.floor(
                                            (content.Duration ?? 0) / 60
                                          ) + " minutes"
                                        : Math.floor(content.Duration ?? 0) +
                                          " seconds"}
                                  </span>
                                </div>
                                <div className="flex items-center gap-2 bg-accent px-2 py-1 rounded-lg text-xs">
                                  {content?.S3?.Type.includes("video") ? (
                                    <Video size={16} />
                                  ) : (
                                    <AudioLines size={16} />
                                  )}
                                  <span className="text-xs text-muted-foreground">
                                    {content?.S3?.Type.includes("video")
                                      ? "Video"
                                      : content?.S3?.Type.includes("audio")
                                        ? "Audio"
                                        : "NA"}
                                  </span>
                                </div>
                                <div className="flex items-center gap-4 text-muted-foreground">
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <ArrowUpFromDot
                                        size={20}
                                        onClick={async (e) => {
                                          e.stopPropagation();
                                          const currentItemSortOrder =
                                            content.SortOrder ?? 1;

                                          const contentBefore = contents?.find(
                                            (c) =>
                                              c.SortOrder ===
                                              currentItemSortOrder - 1
                                          );

                                          if (contentBefore) {
                                            const updatedContentBefore =
                                              await API.CONTENT_ALBUM.UpdateContentData(
                                                {
                                                  ...contentBefore,
                                                  ContentAlbumID:
                                                    album_id as string,
                                                  ContentID:
                                                    contentBefore.ContentID,
                                                  SortOrder:
                                                    currentItemSortOrder,
                                                  Scopes: {},
                                                }
                                              );
                                          }

                                          const updatedContent =
                                            await API.CONTENT_ALBUM.UpdateContentData(
                                              {
                                                ...content,
                                                ContentAlbumID:
                                                  album_id as string,
                                                ContentID: content.ContentID,
                                                SortOrder:
                                                  currentItemSortOrder - 1,
                                                Scopes: {},
                                              }
                                            );

                                          getContentAlbum();
                                        }}
                                        className={cn(
                                          "cursor-pointer",
                                          content.SortOrder === 1 &&
                                            "opacity-50 cursor-not-allowed"
                                        )}
                                      />
                                    </TooltipTrigger>
                                    <TooltipContent>Move up</TooltipContent>
                                  </Tooltip>
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <ArrowDownToDot
                                        size={20}
                                        onClick={async (e) => {
                                          e.stopPropagation();
                                          const currentSortOrder =
                                            content.SortOrder ?? 1;

                                          const contentAfter = contents?.find(
                                            (c) =>
                                              c.SortOrder ===
                                              currentSortOrder + 1
                                          );

                                          if (contentAfter) {
                                            await API.CONTENT_ALBUM.UpdateContentData(
                                              {
                                                ...contentAfter,
                                                ContentAlbumID:
                                                  album_id as string,
                                                ContentID:
                                                  contentAfter.ContentID,
                                                SortOrder: currentSortOrder,
                                                Scopes: {},
                                              }
                                            );
                                          }

                                          await API.CONTENT_ALBUM.UpdateContentData(
                                            {
                                              ...content,
                                              ContentAlbumID:
                                                album_id as string,
                                              ContentID: content.ContentID,
                                              SortOrder: currentSortOrder + 1,
                                              Scopes: {},
                                            }
                                          );

                                          getContentAlbum();
                                        }}
                                        className={cn(
                                          "cursor-pointer",
                                          content.SortOrder ===
                                            album?.Contents?.length &&
                                            "opacity-50 cursor-not-allowed"
                                        )}
                                      />
                                    </TooltipTrigger>
                                    <TooltipContent>Move down</TooltipContent>
                                  </Tooltip>
                                </div>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Trash
                                      className="cursor-pointer text-red-500"
                                      size={20}
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                  </PopoverTrigger>
                                  <PopoverContent className="flex flex-col gap-2">
                                    <p>
                                      Are you sure you want to delete this
                                      content?
                                    </p>
                                    <Button
                                      variant="destructive"
                                      onClick={async (e) => {
                                        e.stopPropagation();
                                        const { data } =
                                          await API.CONTENT_ALBUM.DeleteContent(
                                            {
                                              ContentID: content.ContentID,
                                              ContentAlbumID:
                                                album.ContentAlbumID,
                                            }
                                          );
                                        if (data) {
                                          toast.success(
                                            "Content deleted successfully"
                                          );
                                          getContentAlbum();
                                        }
                                      }}
                                    >
                                      Delete Content
                                    </Button>
                                  </PopoverContent>
                                </Popover>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      {contents?.length === 0 && (
                        <div className="flex flex-col items-center justify-center h-full">
                          <Squirrel size={32} strokeWidth={1} />
                          <h1 className="text-sm font-semibold">
                            No contents found
                          </h1>
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
        </>
      )}
      <UploadContent />
      <EditContent />
      <PreviewContent />
    </div>
  );
}
