import { Injectable, NotFoundException, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateChatDto } from './dto/create-chat.dto';
import { UpdateChatDto } from './dto/update-chat.dto';
import { graph as MainGraph, streamAcademicGraph } from '../../agents/(new)/main-graph';
import { ChatResponseDto } from './dto/chat-response.dto';
import { ChatHistoryDto, ChatMessage as ChatMessageDto } from './dto/chat-history.dto';
import { BaseMessage } from '@langchain/core/messages';
import { ThreadService } from './thread.service';
import { ChatMessage } from './schemas/chat-message.schema';
import { ConfigService } from '@nestjs/config';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { SourceModel } from '../embed/schemas/source.schema';
import { ChatOpenAI } from '@langchain/openai';
import { ThreadDto } from './dto/thread.dto';
import { streamInsuranceGraph } from '../../agents/(insurance)/main-graph';
import { TokenCounterService } from '../token-counter/token-counter.service';
import { AppTypeEnum } from '../core/user/entities/user.entity';
import { IndSourceModel } from '../embed/schemas/ind-source.schema';

@Injectable()
export class ChatService {
  private s3Client: S3Client;

  constructor(
    private readonly threadService: ThreadService,
    @InjectModel(ChatMessage.name) private chatMessageModel: Model<ChatMessage>,
    @InjectModel(SourceModel.name) private sourceModel: Model<SourceModel>,
    @InjectModel(IndSourceModel.name) private indSourceModel: Model<IndSourceModel>,
    private readonly configService: ConfigService,
    private readonly tokenCounterService: TokenCounterService
  ) {
    this.s3Client = new S3Client({
      region: this.configService.get('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      },
    });
  }

  /**
   * Convert LangChain messages to our ChatMessage format
   */
  private convertToStorableMessage(
    message: BaseMessage | any,
    UserID: string,
    TenantID: string,
    ThreadID: string,
    ProjectID: string
  ): Partial<ChatMessage> {
    return {
      role: message.role || message.type || 'system',
      content: message.content?.toString() || '',
      timestamp: new Date(),
      UserID,
      TenantID,
      ThreadID,
      ProjectID,
    };
  }

  /**
   * Convert our ChatMessage format to LangChain message format
   */
  private convertToLangChainMessage(message: ChatMessage | ChatMessageDto): any {
    return {
      role: message.role,
      content: message.content,
    };
  }

  /**
   * Convert ChatMessage document to DTO
   */
  private toMessageDto(message: ChatMessage): ChatMessageDto {
    return {
      role: message.role,
      content: message.content,
      timestamp: message.timestamp,
      citations: message.citations,
      output_language: message.output_language,
    };
  }

  async create(UserID: string, TenantID: string, createChatDto: CreateChatDto): Promise<ChatResponseDto> {
    const { message, ProjectID, ThreadID: requestedThreadID } = createChatDto;

    if (!message) {
      throw new BadRequestException('Message is required');
    }

    if (!ProjectID) {
      throw new BadRequestException('Project ID is required');
    }

    // Validate user has access to this project in this tenant
    await this.threadService.validateProjectAccess(UserID, TenantID, ProjectID);

    // Either use the provided ThreadID or create a new thread if none exists
    let ThreadID = requestedThreadID;
    let thread;

    if (!ThreadID) {
      // Create a new thread
      thread = await this.threadService.createThread(UserID, TenantID, {
        ProjectID,
        title: `${new Date().toLocaleString('en-GB')}`,
      });
      ThreadID = thread.ThreadID;
    } else {
      // Validate thread and get its details
      thread = await this.threadService.validateAndGetThread(ThreadID, UserID);

      // Verify thread belongs to the requested project and tenant
      await this.threadService.validateThreadTenantAndProject(ThreadID, TenantID, ProjectID);
    }

    // Get existing conversation messages
    const conversationHistory = await this.chatMessageModel.find({ ThreadID, UserID }).sort({ timestamp: 1 }).exec();

    // Convert stored messages to LangChain format for the graph
    const langChainMessages = conversationHistory.map((msg) => this.convertToLangChainMessage(msg));

    const sources = await this.sourceModel.find({ TenantID, ProjectID }).lean();
    const Sources = sources.map((s) => ({
      SourceID: s.SourceID,
      SourceName: s.SourceName,
      Chapters: s.Chapters.map((c) => ({
        ChapterID: c.ChapterID,
        ChapterNumber: c.ChapterNumber,
        ChapterName: c.ChapterName,
        Summary: c.Summary,
        Topics: c.Topics,
        PageNumber: {
          From: c.PageNumber?.From,
          To: c.PageNumber?.To,
        },
      })),
      BookSummary: {
        Title: s.BookSummary.Title,
        Summary: s.BookSummary.Summary,
        MainTopics: s.BookSummary.MainTopics,
        ChapterHighlights: s.BookSummary.ChapterHighlights,
      },
      Language: s.Language,
    }));

    // Invoke the retrieval graph
    const response = await MainGraph.invoke({
      messages: [
        ...langChainMessages,
        {
          role: 'user',
          content: message,
        },
      ],
      tenantId: TenantID,
      projectId: ProjectID,
      Sources: Sources,
      Language: 'us',
    });

    // Get the latest AI message
    const aiMessage = (response as any).messages[(response as any).messages.length - 1];
    const aiMessageJSON = JSON.parse(aiMessage.content);
    const aiMessageContent = aiMessageJSON.response;
    const sourceIds = aiMessageJSON.citations.reduce((acc, chunk) => {
      if (chunk.source_id) {
        acc.add(chunk.source_id);
      }
      return acc;
    }, new Set<string>());

    // Get presigned URLs for the sources
    const bucketName = this.configService.get('S3_NXTCAMPUS_PLATFORM');
    for (const sourceId of sourceIds) {
      // Construct the S3 key using the same format as in embed.service.ts
      const key = `academic-lm/${TenantID}/${ProjectID}/${sourceId}`;
      try {
        // Generate presigned URL
        const command = new GetObjectCommand({
          Bucket: bucketName,
          Key: key,
        });
        const presignedUrl = await getSignedUrl(this.s3Client, command, { expiresIn: 86400 }); // 24 hours expiry
        // Add the pdf_url to the citation
        aiMessageJSON.citations.forEach((citation) => {
          if (citation.source_id === sourceId) {
            citation.pdf_url = presignedUrl;
          }
        });
      } catch (error) {
        console.error(`Failed to generate presigned URL for source ID ${sourceId}:`, error);
      }
    }

    const aiMessageCitations = aiMessageJSON.citations;

    // Create and save user message
    const userMessage = new this.chatMessageModel({
      ...this.convertToStorableMessage({ role: 'user', content: message }, UserID, TenantID, ThreadID, ProjectID),
    });
    await userMessage.save();

    // Create and save assistant message
    const assistantMessage = new this.chatMessageModel({
      ...this.convertToStorableMessage(
        { role: 'assistant', content: aiMessageContent },
        UserID,
        TenantID,
        ThreadID,
        ProjectID
      ),
      citations: aiMessageCitations,
    });
    await assistantMessage.save();

    // Update thread activity
    await this.threadService.updateThreadActivity(ThreadID);

    // Return response to the client
    return {
      message: aiMessageContent,
      citations: aiMessageCitations.length > 0 ? aiMessageCitations : [{}],
      isComplete: true,
      ThreadID: ThreadID, // Include the ThreadID in response
    };
  }

  async streamResponse(
    UserID: string,
    TenantID: string,
    createChatDto: CreateChatDto,
    streamProcessor: (chunk: ChatResponseDto) => void,
    AppType: AppTypeEnum
  ): Promise<void> {
    const { message, ProjectID, ThreadID: requestedThreadID } = createChatDto;

    if (!ProjectID || !TenantID || !UserID || !message) {
      streamProcessor({
        message: 'Error: Project ID, Tenant ID, User ID, and Message are required',
        isComplete: true,
        chunkIndex: -1,
        isResponseEnded: false,
        isResponseStarted: false,
        citations: [],
        output_language: 'us',
        status: 'error',
      });
      return;
    }

    try {
      await this.threadService.validateProjectAccess(UserID, TenantID, ProjectID);

      let ThreadID = requestedThreadID;
      let thread: ThreadDto;

      if (!ThreadID) {
        // Create a new thread
        thread = await this.threadService.createThread(UserID, TenantID, {
          ProjectID,
          title: `Untitled Thread`,
        });
        ThreadID = thread.ThreadID;

        // Send thread ID in the initial chunk
        streamProcessor({
          message: '',
          ThreadID,
          isComplete: false,
          chunkIndex: 0,
          isResponseStarted: false,
          isResponseEnded: false,
          citations: [],
          status: 'processing',
          output_language: 'us',
        });
      } else {
        // Validate thread and get its details
        thread = await this.threadService.validateAndGetThread(ThreadID, UserID);

        // Verify thread belongs to the requested project and tenant
        await this.threadService.validateThreadTenantAndProject(ThreadID, TenantID, ProjectID);
      }

      streamProcessor({
        message: '',
        ThreadID,
        isComplete: false,
        chunkIndex: 0,
        isResponseStarted: false,
        isResponseEnded: false,
        citations: [],
        status: 'processing',
        output_language: 'us',
      });

      // Get existing conversation messages
      const conversationHistory = await this.chatMessageModel.find({ ThreadID, UserID }).sort({ timestamp: 1 }).exec();

      // Convert stored messages to LangChain format for the graph
      const langChainMessages = conversationHistory.map((msg) => this.convertToLangChainMessage(msg));

      // Create and save user message
      const userMessage = new this.chatMessageModel({
        ...this.convertToStorableMessage({ role: 'user', content: message }, UserID, TenantID, ThreadID, ProjectID),
      });
      await userMessage.save();

      let Sources;
      if (AppType === AppTypeEnum.Industry) {
        const sources = await this.indSourceModel
          .find({ TenantID, ProjectID })
          .select('SourceID SourceName Sections SourceSummary')
          .lean();

        Sources = sources.map((s) => ({
          SourceID: s.SourceID,
          SourceName: s.SourceName,
          Sections: s.Sections.map((c) => ({
            SectionID: c.SectionID,
            SectionName: c.SectionName,
            Description: c.Description,
            SubSections: c.SubSections.map((s) => ({
              SubSectionName: s.SubSectionName,
              PageNumber: s.PageNumber,
            })),
            PageNumber: c.PageNumber,
          })),
          SourceSummary: s.SourceSummary,
        }));
      } else {
        const sources = await this.sourceModel
          .find({ TenantID, ProjectID })
          .select('SourceID SourceName Chapters BookSummary Language')
          .lean();

        Sources = sources.map((s) => ({
          SourceID: s.SourceID,
          SourceName: s.SourceName,
          Chapters: s.Chapters.map((c) => ({
            ChapterID: c.ChapterID,
            ChapterNumber: c.ChapterNumber,
            ChapterName: c.ChapterName,
            Summary: c.Summary,
            Topics: c.Topics,
            PageNumber: {
              From: c.PageNumber?.From,
              To: c.PageNumber?.To,
            },
          })),
          BookSummary: {
            Title: s.BookSummary.Title,
            Summary: s.BookSummary.Summary,
            MainTopics: s.BookSummary.MainTopics,
            ChapterHighlights: s.BookSummary.ChapterHighlights,
          },
          Language: s.Language,
        }));
      }

      // Prepare inputs for the graph
      const inputs = {
        messages: [
          ...langChainMessages,
          {
            role: 'user',
            content: message,
          },
        ],
        Sources: Sources,
        tenantId: TenantID,
        projectId: ProjectID,
        Language: Array.from(new Set(Sources?.map((s) => s.Language))).join(', '),
      };

      const stream =
        AppType === AppTypeEnum.Industry ? await streamInsuranceGraph(inputs) : await streamAcademicGraph(inputs);

      let fullAssistantResponse = '';
      let chunkIndex = 0;
      let isResponseStarted = false;
      let isResponseEnded = false;
      let isTokenCount = false;
      let currentNode: 'analyzeAndRouteQuery' | 'conductResearch' | 'respondToGeneralConversation' | 'anyOther' =
        'anyOther';
      let graphTokenCount;

      let response = '';
      for await (const chunk of stream) {
        if (chunk === '$$%%##TokenCount##%%$$') {
          isTokenCount = true;
          continue;
        }
        if (isTokenCount) {
          graphTokenCount = JSON.parse(chunk);
          isTokenCount = false;
          continue;
        }
        if (chunk === '$$%%##analyzeAndRouteQuery##%%$$') {
          currentNode = 'analyzeAndRouteQuery';
          streamProcessor({
            message: '',
            isComplete: false,
            chunkIndex: 0,
            isResponseStarted: false,
            isResponseEnded: false,
            citations: [],
            status: 'analyzing',
            ThreadID: ThreadID,
            output_language: 'us',
          });
          continue;
        } else if (chunk === '$$%%##conductResearch##%%$$') {
          currentNode = 'conductResearch';
          streamProcessor({
            message: '',
            isComplete: false,
            chunkIndex: 0,
            isResponseStarted: false,
            isResponseEnded: false,
            citations: [],
            status: 'researching',
            ThreadID: ThreadID,
            output_language: 'us',
          });
          continue;
        } else if (chunk === '$$%%##respondToGeneralConversation##%%$$') {
          currentNode = 'respondToGeneralConversation';
          continue;
        }

        fullAssistantResponse += chunk;

        if (!isResponseStarted && fullAssistantResponse.includes('<response>')) {
          isResponseStarted = true;
        }

        if (isResponseStarted && !isResponseEnded) {
          // Send the processed chunk to the client
          response += chunk;
          streamProcessor({
            message: response.replace('<response>', '').replace('</response>', ''),
            isComplete: false,
            chunkIndex: chunkIndex++,
            ThreadID,
            isResponseStarted,
            isResponseEnded,
            citations: [],
            status: 'replying',
            output_language: 'us',
          });
        }

        if (isResponseStarted && fullAssistantResponse.includes('</response>')) {
          isResponseEnded = true;
        }
      }

      const fullResponseJSON = JSON.parse(fullAssistantResponse);
      streamProcessor({
        message: fullResponseJSON.response.replace('<response>', '').replace('</response>', ''),
        isComplete: false,
        chunkIndex: chunkIndex++,
        ThreadID,
        isResponseStarted,
        isResponseEnded,
        citations: fullResponseJSON.citations,
        status: 'completed',
        output_language: fullResponseJSON.output_language,
      });

      // pdf_url
      const sourceIds = fullResponseJSON.citations.reduce((acc, chunk) => {
        if (chunk.source_id) {
          acc.add(chunk.source_id);
        }
        return acc;
      }, new Set<string>());

      // Get presigned URLs for the sources
      const bucketName = this.configService.get('S3_NXTCAMPUS_PLATFORM');
      for (const sourceId of sourceIds) {
        // Construct the S3 key using the same format as in embed.service.ts
        const key = `${AppType === AppTypeEnum.Academic ? 'academic-lm' : 'industry-lm'}/${TenantID}/${ProjectID}/${sourceId}`;
        try {
          // Generate presigned URL
          const command = new GetObjectCommand({
            Bucket: bucketName,
            Key: key,
          });
          const presignedUrl = await getSignedUrl(this.s3Client, command, { expiresIn: 86400 }); // 24 hours expiry
          // Add the pdf_url to the citation
          fullResponseJSON.citations.forEach((citation) => {
            if (citation.source_id === sourceId) {
              citation.pdf_url = presignedUrl;
            }
          });
        } catch (error) {
          console.error(`Failed to generate presigned URL for source ID ${sourceId}:`, error);
        }
      }

      streamProcessor({
        message: fullResponseJSON.response.replace('<response>', '').replace('</response>', ''),
        isComplete: true,
        chunkIndex: chunkIndex++,
        ThreadID,
        isResponseStarted,
        isResponseEnded,
        citations: fullResponseJSON.citations,
        status: 'completed',
        output_language: fullResponseJSON.output_language,
      });

      // Save the complete assistant message to the database
      if (fullResponseJSON) {
        const assistantMessage = new this.chatMessageModel({
          ...this.convertToStorableMessage(
            {
              role: 'assistant',
              content: fullResponseJSON.response.replace('<response>', '').replace('</response>', ''),
            },
            UserID,
            TenantID,
            ThreadID,
            ProjectID
          ),
          citations: fullResponseJSON.citations || [],
          output_language: fullResponseJSON.output_language || 'us',
        });
        const savedAssistantMessage = await assistantMessage.save();
        this.tokenCounterService.create({
          TenantID,
          ProjectID,
          ResourceIDs: [savedAssistantMessage.MessageId],
          ResourceType: 'message',
          TokenCount: graphTokenCount,
          UserID,
          AppType: AppType,
        });
      }

      // Update thread activity
      if (thread.title === 'Untitled Thread' && currentNode !== 'respondToGeneralConversation') {
        const model = new ChatOpenAI({
          model: 'gpt-4.1-mini',
          temperature: 0,
        });

        const threadTitle = await model.invoke(
          'You are an AI-powered chatbot. Your task is to summarize the following user query and generate a thread title under 25 (max 30) characters for this chat thread. Output should be only Thread Title without any Preamble. User Query is: ' +
            message
        );

        await this.threadService.updateThreadActivity(ThreadID, threadTitle.content.toString());
      } else {
        await this.threadService.updateThreadActivity(ThreadID);
      }
    } catch (error) {
      console.error('Error in stream processing:', error);

      // Send error message
      streamProcessor({
        message: `Error: ${error.message || 'An unexpected error occurred'}`,
        isComplete: true,
        chunkIndex: -1,
        ThreadID: requestedThreadID,
        isResponseStarted: false,
        isResponseEnded: false,
        citations: [],
        status: 'error',
        output_language: 'us',
      });
    }
  }

  /**
   * Get chat history for a specific thread
   */
  async getChatHistory(ThreadID: string, UserID: string): Promise<ChatHistoryDto> {
    // Validate thread and get its details
    const thread = await this.threadService.validateAndGetThread(ThreadID, UserID);

    // Get messages for this thread
    const messages = await this.chatMessageModel.find({ ThreadID, UserID }).sort({ timestamp: 1 }).exec();

    return {
      messages: messages.map((msg) => this.toMessageDto(msg)),
      ThreadID,
      tenantId: thread.tenantId,
      ProjectID: thread.ProjectID,
      userId: thread.userId,
      title: thread.title,
    };
  }

  /**
   * Clear chat history for a specific thread
   */
  async clearChatHistory(ThreadID: string, UserID: string): Promise<{ success: boolean }> {
    // Validate thread and get its details
    await this.threadService.validateAndGetThread(ThreadID, UserID);

    // Delete all messages for this thread
    const result = await this.chatMessageModel.deleteMany({ ThreadID, UserID }).exec();

    return { success: result.deletedCount > 0 };
  }

  /**
   * Delete a thread and its conversation history
   */
  async deleteThread(ThreadID: string, UserID: string): Promise<{ success: boolean }> {
    // Validate thread and get its details
    await this.threadService.validateAndGetThread(ThreadID, UserID);

    // Delete the thread
    const success = await this.threadService.deleteThread(ThreadID);

    // Delete all messages for this thread
    if (success) {
      await this.chatMessageModel.deleteMany({ ThreadID, UserID }).exec();
    }

    return { success };
  }

  findAll() {
    return `This action returns all chat`;
  }

  findOne(id: number) {
    return `This action returns a #${id} chat`;
  }

  update(id: number, updateChatDto: UpdateChatDto) {
    return `This action updates a #${id} chat`;
  }

  remove(id: number) {
    return `This action removes a #${id} chat`;
  }
}
