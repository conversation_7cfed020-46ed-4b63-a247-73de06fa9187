"use client";

import { API } from "@/apis/api";
import { ChatResponseDto } from "@/apis/chat.api";
import { TAudioBook, TAudioBookMeta } from "@/apis/subscription.api";
import ChatInput from "@/components/chat/chat-input";
import { ChatMessage } from "@/components/thread/chat-message";
import { useProjectContext } from "@/contexts/project-context";
import { useThreadContext } from "@/contexts/thread-context";
import { useTTS } from "@/contexts/tts-context";
import { Button } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { RefreshCw } from "lucide-react";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

interface Message {
  id: string;
  content: string;
  timestamp: string;
  isUser: boolean;
  status?:
    | "sending"
    | "sent"
    | "error"
    | "analyzing"
    | "researching"
    | "processing"
    | "completed"
    | "replying"
    | undefined;
  isStreaming?: boolean;
  isExistingMessage?: boolean;
  citations: {
    id: string;
    source_title: string;
    pdf_url: string | null;
    page: number;
    line_from: number | null;
    line_to: number | null;
    document_page_number: number;
  }[];
}

export default function ThreadPage() {
  const { threadId } = useParams();
  const { selectedProject } = useProjectContext();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { autoRead } = useTTS();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { refetchThreads } = useThreadContext();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    const message = searchParams.get("message");
    if (message) {
      setNewMessage(message);
      const params = new URLSearchParams(searchParams);
      params.delete("message");
      router.push(`${pathname}?${params.toString()}`);
    } else {
      handleSendStreamMessage(newMessage);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  // Fetch thread messages
  useEffect(() => {
    if (!threadId || threadId === "new-thread") return;

    const fetchMessages = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const { data, errors } = await API.CHAT.GetChatHistory(
          threadId as string
        );

        if (errors) {
          setError(errors[0]?.message || "Failed to load conversation");
          toast.error("Failed to load conversation");
          setIsLoading(false);
          return;
        }

        if (data) {
          // Convert API messages to our format
          const formattedMessages = data.messages.map((msg, index) => ({
            id: `msg-${threadId}-${index}`,
            content: msg.content,
            timestamp: new Date(msg.timestamp).toISOString(),
            isUser: msg.role === "user",
            isExistingMessage: true,
            citations: msg.citations.map((citation) => ({
              id: citation.id,
              source_title: citation.source_title,
              pdf_url: citation.pdf_url,
              page: citation.page_number,
              line_from: citation.line_from,
              line_to: citation.line_to,
              document_page_number: citation.document_page_number,
            })),
          }));

          console.log(formattedMessages);

          setMessages(formattedMessages);
        }

        setIsLoading(false);
      } catch (err) {
        console.error("Failed to load messages:", err);
        setError("Failed to load messages");
        toast.error("An error occurred while loading the conversation");
        setIsLoading(false);
      }
    };

    fetchMessages();
  }, [threadId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendStreamMessage = async (newMessage: string) => {
    if (!newMessage.trim() || !threadId || !selectedProject) return;

    const userMessageId = `user-${threadId}-${Date.now()}`;
    const aiResponseId = `ai-${threadId}-${Date.now()}`;

    const userMessage: Message = {
      id: userMessageId,
      content: newMessage,
      timestamp: new Date().toISOString(),
      isUser: true,
      status: "sent",
      isExistingMessage: false,
      citations: [],
    };

    setMessages((prev) => [...prev, userMessage]);

    const aiMessage: Message = {
      id: aiResponseId,
      content: "typing-indicator",
      timestamp: new Date().toISOString(),
      isUser: false,
      status: "sending",
      isExistingMessage: false,
      citations: [],
      isStreaming: true,
    };

    setMessages((prev) => [...prev, aiMessage]);

    let newThreadId = "";

    try {
      const onChunk = (chunk: ChatResponseDto) => {
        newThreadId = chunk.ThreadID || "";
        setMessages((prevMessages) => {
          return prevMessages.map((msg) => {
            if (msg.id === userMessageId) {
              return {
                ...msg,
                status: "sent",
              };
            }
            if (msg.id === aiResponseId) {
              return {
                ...msg,
                content: chunk.message,
                citations:
                  chunk.citations?.map((citation) => ({
                    id: citation.id,
                    source_title: citation.source_title,
                    pdf_url: citation.pdf_url,
                    page: citation.page_number,
                    line_from: citation.line_from,
                    line_to: citation.line_to,
                    document_page_number: citation.document_page_number,
                  })) || [],
                status: chunk.status,
                isStreaming: !chunk.isComplete,
              };
            }
            return msg;
          });
        });
      };

      const onComplete = () => {
        if (newThreadId !== threadId) router.push(`/thread/${newThreadId}`);
      };

      const onError = (error: Error) => {
        throw new Error(error?.message || "Failed to send message");
      };

      API.CHAT.StreamMessage(
        {
          message: newMessage.trim(),
          ProjectID: selectedProject?.id || "",
          ...(threadId !== "new-thread" && { ThreadID: threadId as string }),
        },
        onChunk,
        onComplete,
        onError
      );

      setNewMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  const handleRetry = () => {
    window.location.reload();
  };

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Messages area */}
      <div className="flex-1 overflow-y-auto p-4">
        {isLoading ? (
          // Loading skeletons
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div
                key={i}
                className={`flex gap-3 ${i % 2 === 0 ? "" : "justify-end"}`}
              >
                <Skeleton className="h-10 w-10 rounded-full flex-shrink-0" />
                <div className={`${i % 2 === 0 ? "" : "text-right"} space-y-2`}>
                  <Skeleton className="h-4 w-20" />
                  <Skeleton
                    className={`h-24 ${i % 2 === 0 ? "w-[280px]" : "w-[260px] ml-auto"} rounded-lg`}
                  />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          // Error state
          <div className="flex justify-center p-4">
            <Card className="p-4 text-center text-destructive max-w-md">
              <p>{error}</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={handleRetry}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            </Card>
          </div>
        ) : messages.length === 0 ? (
          // Empty state
          <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground py-12">
            <p>No messages yet</p>
            <p className="text-sm mt-1">
              Start the conversation by sending a message
            </p>
          </div>
        ) : (
          // Messages
          messages.map((message) => {
            return (
              <ChatMessage
                key={message.id}
                id={message.id}
                content={message.content}
                timestamp={message.timestamp}
                isUser={message.isUser}
                status={message.status}
                isStreaming={message.isStreaming}
                autoPlayTTS={autoRead}
                isExistingMessage={message.isExistingMessage}
                citations={message.citations}
              />
            );
          })
        )}

        {/* This div is for auto-scrolling to the bottom */}
        <div ref={messagesEndRef} />
      </div>

      <div className="bg-accent rounded-2xl border-1 border-gray-600 shadow-2xl relative">
        <div className="pointer-events-none absolute inset-0 opacity-0 transition duration-200 ease-in-out bg-gray-900/20 group-hover:opacity-100" />
        <ChatInput
          placeholder="Ask anything related to your project"
          onSubmit={(message) => handleSendStreamMessage(message)}
          onMessageChange={(message) => setNewMessage(message)}
        />
      </div>
    </div>
  );
}
