import uvicorn

def main():
    """Start the server in development mode with auto-reload."""
    uvicorn.run(
        "app.main:app",
        reload=True,
        host="127.0.0.1",
        port=8000
    )

def main_prod():
    """Start the server in production mode with all optimizations."""
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        timeout_keep_alive=1800,  # 30 minutes
        timeout_graceful_shutdown=1800,  # 30 minutes
        workers=4,
        loop="uvloop",
        http="httptools"
    )

if __name__ == "__main__":
    main() 