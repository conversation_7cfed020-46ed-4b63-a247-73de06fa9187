import { Cache } from '@nestjs/cache-manager';
import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable, NestMiddleware } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { sum } from 'radash';
import { TUser } from '../../src/core/user/entities/user.entity';
import { PlanNSubscriptionService } from '../../src/plan-n-subscription/plan-n-subscription.service';
import { NextFunction, request } from 'express';

@Injectable()
export class IsCreditAvailableGuard implements NestMiddleware {
  constructor(
    private CreditUsageService: PlanNSubscriptionService,
    private CacheService: Cache
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    console.log('Checking Available Limit');

    const user = (request as unknown as { user: TUser }).user;

    console.log(request.user);
    if (!user) {
      throw new HttpException('User not found', HttpStatus.UNAUTHORIZED);
    }

    console.log('Query--->', request.query);
    const SubscriptionID = request.params.SubscriptionID;

    const creditLimit = await this.CacheService.get<number>(`${user.UserID}:${SubscriptionID}`);

    console.log('CreditLimit', creditLimit);

    if (creditLimit && creditLimit > 0) {
      return next();
    }

    const creditUsage = await this.CreditUsageService.GetCreditUsageBySubscriptionID({
      SubscriptionID,
    });

    if (creditUsage.length === 0) {
      return next();
    }

    const RemainingSum = sum(creditUsage, (c) => c.RemainingCredit);

    if (RemainingSum < 0) {
      throw new HttpException('Credit limit exceeded', HttpStatus.FORBIDDEN);
    }

    return next();
  }
}
