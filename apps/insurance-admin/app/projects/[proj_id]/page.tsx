"use client";
import { useParams } from "next/navigation";
import ProjectView from "./ProjectView";
import { API } from "@/apis/api";
import { TProjectDetails } from "@/types/project";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function Page() {
  const { proj_id } = useParams();
  const [project, setProject] = useState<TProjectDetails | null>(null);
  const fetchProject = async () => {
    const { data, errors } = await API.PROJECT.GetProjectDetails({
      ProjectID: proj_id as string,
    });
    if (data) {
      setProject(data);
    }
    if (errors) errors.forEach((err) => toast.error(err.message));
  };

  useEffect(() => {
    if (proj_id) fetchProject();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [proj_id]);

  return (
    <div className="w-full h-full p-6 md:p-10">
      <h2 className="text-3xl font-bold tracking-tight">Project</h2>
      {project && <ProjectView project={project} />}
    </div>
  );
}
