"use client";

import React, { useRef, useState, useCallback } from "react";
import { Avatar } from "@workspace/ui/components/avatar";
import { Card } from "@workspace/ui/components/card";
import { <PERSON><PERSON>, User, Co<PERSON> } from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import { cn } from "@workspace/ui/lib/utils";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";
import { PDFViewerSheet } from "../chat/pdf-renderer";
import { TTSButton } from "../message-actions/TTSButton";
import { TTSSettings } from "../message-actions/TTSSettings";
import { TTSProgressBar } from "../message-actions/TTSProgressBar";
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

interface ChatMessageProps {
  id: string;
  content: string;
  timestamp: string;
  isUser: boolean;
  output_language?: string;
  status?:
    | "sending"
    | "sent"
    | "error"
    | "analyzing"
    | "researching"
    | "processing"
    | "completed"
    | "replying";
  isStreaming?: boolean;
  autoPlayTTS?: boolean;
  isExistingMessage?: boolean;
  citations?: {
    id: string;
    source_title: string;
    pdf_url: string | null;
    page: number;
    line_from: number | null;
    line_to: number | null;
    document_page_number: number;
  }[];
}

const CitationNumber = ({
  number,
  citation,
  onClick,
}: {
  number: number;
  citation: NonNullable<ChatMessageProps["citations"]>[number];
  onClick: () => void;
}) => {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <span
            className="inline-flex items-center justify-center h-5 w-5 min-w-[1.25rem] mx-0.5 text-xs font-medium rounded-full bg-primary/20 text-primary hover:bg-primary/30 cursor-pointer align-baseline"
            onClick={onClick}
            style={{ display: "inline-flex", verticalAlign: "baseline" }}
          >
            {number}
          </span>
        </TooltipTrigger>
        <TooltipContent
          side="top"
          align="center"
          className="p-3 max-w-xs bg-popover border border-border shadow-md rounded-md"
        >
          <div className="flex flex-col gap-1.5 text-xs">
            <p className="font-semibold text-sm">
              Source Name: {citation.source_title}
            </p>
            <div className="flex items-center gap-1.5">
              <span className="font-medium">Page Number:</span> {citation.page}
            </div>
            <div className="flex items-center gap-1.5">
              <span className="font-medium">Line:</span> {citation.line_from} -{" "}
              {citation.line_to}
            </div>
            <div className="mt-1 text-primary text-[11px]">
              Click to view source document
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export function ChatMessage({
  id,
  content,
  timestamp,
  isUser,
  output_language,
  status = "sent",
  citations,
  isStreaming = false,
  isExistingMessage = false,
}: ChatMessageProps) {
  const [showControls, setShowControls] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  const [pdfViewerContext, setPDFViewerContext] = useState({
    url: "",
    title: "",
    page: 1,
    document_page_number: 1,
    isOpen: false,
  });
  const [tableOfCitations, setTableOfCitations] = useState<
    {
      url: string;
      title: string;
      page: number;
      document_page_number: number;
    }[]
  >([]);

  const processContentWithCitations = useCallback(
    (text: string) => {
      if (!text || !citations?.length) return text;

      const citationRegex = /\[\[([^\]]+)\]\]/g;
      return text.replace(citationRegex, (match, citationId) => {
        const citation = citations.find((c) => c.id === citationId);
        if (citation) {
          return ` (Source: ${citation.source_title}, Page: ${citation.page}) `;
        }
        return "";
      });
    },
    [citations]
  );

  const handleCopy = useCallback(() => {
    if (content && content !== "typing-indicator") {
      navigator.clipboard
        .writeText(content)
        .then(() => toast.success("Copied to clipboard"))
        .catch(() => toast.error("Failed to copy text"));
    }
  }, [content]);

  const handleCitationClick = useCallback(
    (citation: NonNullable<ChatMessageProps["citations"]>[number]) => {
      if (!citation || !citation.pdf_url) return;

      setPDFViewerContext({
        url: citation.pdf_url,
        title: citation.source_title,
        page: citation.page,
        document_page_number: citation.document_page_number,
        isOpen: true,
      });

      setTableOfCitations([
        {
          url: citation.pdf_url,
          title: citation.source_title,
          page: citation.page,
          document_page_number: citation.document_page_number,
        },
      ]);
    },
    []
  );

  const MarkdownWithCitations = useCallback(() => {
    if (!content || content === "typing-indicator" || !citations?.length) {
      return (
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw, rehypeSanitize]}
        >
          {content || ""}
        </ReactMarkdown>
      );
    }

    const citationMap = new Map<string, number>();
    const uniqueCitationIds = Array.from(new Set(citations.map((c) => c.id)));
    uniqueCitationIds.forEach((id, index) => {
      if (id) {
        citationMap.set(id, index + 1);
      }
    });

    const citationRegex = /\[\[([^\]]+)\]\]/g;

    const processedContent = content.replace(
      citationRegex,
      (match, citationId) => {
        return `\uE000${citationId}\uE001`;
      }
    );

    const renderWithCitations = (
      children: React.ReactNode
    ): React.ReactNode => {
      if (!children || !citations?.length) return children;

      return React.Children.map(children, (child) => {
        if (typeof child !== "string") return child;

        const parts = child.split(/\uE000([^\uE001]+)\uE001/);
        if (parts.length === 1) return child;

        const result: React.ReactNode[] = [];
        parts.forEach((part, i) => {
          if (i % 2 === 0) {
            if (part) result.push(part);
          } else {
            const citationId = part;
            const citation = citations.find((c) => c.id === citationId);

            if (citation) {
              const number = citationMap.get(citationId) || 0;
              result.push(
                <CitationNumber
                  key={`citation-${i}`}
                  number={number}
                  citation={citation}
                  onClick={() => handleCitationClick(citation)}
                />
              );
            } else {
              result.push(`[[${citationId}]]`);
            }
          }
        });

        return result;
      });
    };

    return (
      <div className="markdown-with-citations">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw, rehypeSanitize]}
          components={{
            p: ({ children, ...props }) => {
              return <p {...props}>{renderWithCitations(children)}</p>;
            },
            li: ({ children, ...props }) => {
              return <li {...props}>{renderWithCitations(children)}</li>;
            },
            h1: ({ children, ...props }) => (
              <h1 {...props}>{renderWithCitations(children)}</h1>
            ),
            h2: ({ children, ...props }) => (
              <h2 {...props}>{renderWithCitations(children)}</h2>
            ),
            h3: ({ children, ...props }) => (
              <h3 {...props}>{renderWithCitations(children)}</h3>
            ),
            h4: ({ children, ...props }) => (
              <h4 {...props}>{renderWithCitations(children)}</h4>
            ),
            h5: ({ children, ...props }) => (
              <h5 {...props}>{renderWithCitations(children)}</h5>
            ),
            h6: ({ children, ...props }) => (
              <h6 {...props}>{renderWithCitations(children)}</h6>
            ),
            strong: ({ children, ...props }) => (
              <strong {...props}>{renderWithCitations(children)}</strong>
            ),
            em: ({ children, ...props }) => (
              <em {...props}>{renderWithCitations(children)}</em>
            ),
          }}
        >
          {processedContent}
        </ReactMarkdown>
      </div>
    );
  }, [content, citations, handleCitationClick]);

  return (
    <div className={`flex gap-3 ${isUser ? "flex-row-reverse" : ""} mt-4`}>
      <Avatar className="h-10 w-10 shrink-0 hidden md:block">
        {isUser ? <User className="h-6 w-6" /> : <Bot className="h-6 w-6" />}
      </Avatar>

      <div
        className={`md:max-w-[65%] max-w-[96%] ${isUser ? "text-right ml-auto" : ""}`}
      >
        <div className="text-xs text-muted-foreground mb-1 flex items-center gap-2">
          <div className={`flex items-center gap-1 ${isUser ? "ml-auto" : ""}`}>
            {isUser ? "You" : "Assistant"} •{" "}
            {format(new Date(timestamp), "h:mm a")}
            {status === "sending" && <span className="ml-2 ">Sending...</span>}
            {status === "analyzing" && (
              <span className="ml-2 text-primary ">Analyzing...</span>
            )}
            {status === "researching" && (
              <span className="ml-2 text-primary ">Researching...</span>
            )}
            {status === "processing" && (
              <span className="ml-2 text-primary ">Processing...</span>
            )}
            {status === "error" && (
              <span className="ml-2 text-destructive">Failed to send</span>
            )}
          </div>
        </div>

        <div
          className="relative group"
          onMouseEnter={() => setShowControls(true)}
          onMouseLeave={() => setShowControls(false)}
        >
          <Card
            className={`px-4 py-2 ${
              isUser ? "bg-primary/10" : "bg-muted"
            } rounded-sm relative`}
          >
            <style jsx global>{`
              .markdown-with-citations {
                display: block;
              }
              .markdown-with-citations ol {
                list-style-type: decimal;
                padding-left: 2em;
                margin: 1em 0;
              }
              .markdown-with-citations ol li {
                display: list-item;
                margin: 0.5em 0;
              }
              .markdown-with-citations ul {
                list-style-type: disc;
                padding-left: 2em;
                margin: 1em 0;
              }
              .markdown-with-citations ul li {
                display: list-item;
                margin: 0.5em 0;
              }
              .markdown-with-citations p {
                margin-bottom: 1em;
              }
              .markdown-with-citations p:last-child {
                margin-bottom: 0;
              }
              .prose-sm p {
                margin-bottom: 1em;
              }
              .prose-sm p:last-child {
                margin-bottom: 0;
              }
            `}</style>

            <div
              ref={contentRef}
              className="break-words prose-sm dark:prose-invert prose-headings:my-0 prose-p:my-0 max-w-none"
            >
              {content === "typing-indicator" ? (
                <div className="flex space-x-2 h-5 items-center">
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.2s]"></div>
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.4s]"></div>
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.6s]"></div>
                </div>
              ) : content ? (
                <MarkdownWithCitations />
              ) : isStreaming ? (
                <div className="flex space-x-2 h-5 items-center">
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.2s]"></div>
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.4s]"></div>
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.6s]"></div>
                </div>
              ) : (
                ""
              )}
            </div>
          </Card>

          <div className="flex justify-end items-center mt-2 gap-2">
            {!isUser ? (
              <div className="flex items-center gap-2 w-full">
                <button
                  onClick={handleCopy}
                  className={cn(
                    "p-1 rounded-full hover:bg-muted/80 text-muted-foreground hidden transition-all duration-200 shrink-0",
                    showControls && "block",
                    (!content || content === "typing-indicator") &&
                      "opacity-50 block cursor-not-allowed"
                  )}
                  aria-label="Copy message"
                  disabled={!content || content === "typing-indicator"}
                >
                  <Copy size={16} />
                </button>

                <div className="flex items-center shrink-0">
                  <TTSButton
                    messageId={id}
                    content={content}
                    citations={citations}
                    output_language={output_language}
                    showControls={showControls}
                    className="transition-opacity duration-200"
                  />
                </div>

                <div
                  className={cn(
                    "hidden transition-all duration-200 shrink-0",
                    showControls && "block"
                  )}
                >
                  <TTSSettings />
                </div>
                <div className="w-40 min-w-0 mx-2">
                  <TTSProgressBar messageId={id} />
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <button
                  onClick={handleCopy}
                  className={cn(
                    "p-1.5 rounded-full hover:bg-muted/80 text-muted-foreground opacity-0 transition-opacity",
                    showControls && "opacity-100",
                    (!content || content === "typing-indicator") &&
                      "opacity-50 cursor-not-allowed"
                  )}
                  aria-label="Copy message"
                  disabled={!content || content === "typing-indicator"}
                >
                  <Copy size={16} />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      <PDFViewerSheet
        setPDFViewerContext={setPDFViewerContext}
        url={pdfViewerContext.url}
        title={pdfViewerContext.title}
        isOpen={pdfViewerContext.isOpen}
        tableOfCitations={tableOfCitations}
        onClose={() =>
          setPDFViewerContext({ ...pdfViewerContext, isOpen: false })
        }
        pageNumber={pdfViewerContext.page}
        documentPageNumber={pdfViewerContext.document_page_number}
      />
    </div>
  );
}
