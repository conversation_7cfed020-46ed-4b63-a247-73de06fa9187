"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";

interface AutoReadContextType {
  isAutoReadEnabled: boolean;
  toggleAutoRead: () => void;
}

const AUTO_READ_STORAGE_KEY = "tts-auto-read-enabled";
const DEFAULT_AUTO_READ_STATE = true;

const AutoReadContext = createContext<AutoReadContextType | undefined>(
  undefined
);

export const useAutoRead = () => {
  const context = useContext(AutoReadContext);
  if (!context) {
    throw new Error("useAutoRead must be used within an AutoReadProvider");
  }
  return context;
};

export function AutoReadProvider({ children }: { children: React.ReactNode }) {
  const [isAutoReadEnabled, setIsAutoReadEnabled] = useState(
    DEFAULT_AUTO_READ_STATE
  );

  // Load from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(AUTO_READ_STORAGE_KEY);
      if (stored !== null) {
        setIsAutoReadEnabled(JSON.parse(stored));
      }
    } catch (error) {
      console.error("Failed to load auto-read preference:", error);
      // Fallback to default state
      setIsAutoReadEnabled(DEFAULT_AUTO_READ_STATE);
    }
  }, []);

  // Save to localStorage when state changes
  useEffect(() => {
    try {
      localStorage.setItem(
        AUTO_READ_STORAGE_KEY,
        JSON.stringify(isAutoReadEnabled)
      );
    } catch (error) {
      console.error("Failed to save auto-read preference:", error);
    }
  }, [isAutoReadEnabled]);

  const toggleAutoRead = useCallback(() => {
    setIsAutoReadEnabled((prev) => !prev);
  }, []);

  const contextValue: AutoReadContextType = {
    isAutoReadEnabled,
    toggleAutoRead,
  };

  return (
    <AutoReadContext.Provider value={contextValue}>
      {children}
    </AutoReadContext.Provider>
  );
}
