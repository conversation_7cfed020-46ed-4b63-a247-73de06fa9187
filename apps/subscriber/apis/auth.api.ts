import { TryCatch } from "@/lib/try-catch";
import { KY, TAckResponse } from "./_.index";
import { TErrorResponse } from "./_.index";
import { TUser } from "@/types/user";

const GetCurrentUser = async () => {
  return await TryCatch<TUser, TErrorResponse>(KY.get("auth/current").json());
};

type TSignInParams = {
  Email: string;
  Password: string;
};
const SignIn = async (params: TSignInParams) => {
  return await TryCatch<TAckResponse<TUser>, TErrorResponse>(
    KY.post("auth/sign-in?UserType=Subscriber", { json: params }).json()
  );
};

const SignOut = async () => {
  return await TryCatch<TAckResponse<null>, TErrorResponse>(
    KY.delete("auth/sign-out").json()
  );
};

type OptionalAttributes = {
  Name?: string;
  PhoneNumber?: string;
  Gender?: "Male" | "Female" | "Other";
  DateOfBirth?: string;
  Address?: string;
  ProfilePicture?: string;
  ZoneInfo?: string;
  Locale?: string;
  GivenName?: string;
  FamilyName?: string;
  MiddleName?: string;
  Website?: string;
};

const SignUp = async (
  params: {
    Email: string;
    Password: string;
    PlanID: string;
    AppType: "Industry" | "Academic";
    Variant: string;
  } & OptionalAttributes
) => {
  return await TryCatch<TAckResponse<null>, TErrorResponse>(
    KY.post("subscription/sign-up", { json: params }).json()
  );
};

type TUpdateDetailsParams = {
  Email?: string;
  Password?: string;
  Name?: string;
  PhoneNumber?: string;
  Gender?: "Male" | "Female" | "Other";
  DateOfBirth?: string;
  Address?: string;
  ProfilePicture?: string;
  ZoneInfo?: string;
  Locale?: string;
  GivenName?: string;
  FamilyName?: string;
  MiddleName?: string;
  Website?: string;
};
const UpdateDetails = async (params: TUpdateDetailsParams) => {
  return await TryCatch<TAckResponse<TUser>, TErrorResponse>(
    KY.patch("auth/details", { json: params }).json()
  );
};

const GoogleSignIn = async (RedirectURL: string) => {
  return await TryCatch<TAckResponse<TUser>, TErrorResponse>(
    KY.get(`auth/google?RedirectURL=${RedirectURL}`).json()
  );
};

export const AUTH = {
  GetCurrentUser,
  SignIn,
  SignOut,
  SignUp,
  UpdateDetails,
  GoogleSignIn,
};
