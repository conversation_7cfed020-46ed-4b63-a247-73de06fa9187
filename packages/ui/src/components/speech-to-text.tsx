import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { cn } from "@workspace/ui/lib/utils";
import { Check, ChevronDown, Dot, Mic, X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";

interface TProps {
  onTranscript: (transcript: string) => void;
  onPressEnter?: () => void;
  onListening?: (listening: boolean) => void;
}

const SpeechToText = ({ onTranscript, onPressEnter, onListening }: TProps) => {
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
  } = useSpeechRecognition();

  const [isListening, setIsListening] = useState(false);

  if (!browserSupportsSpeechRecognition) {
    return <span><PERSON><PERSON><PERSON> doesn't support speech recognition.</span>;
  }

  useEffect(() => {
    onTranscript(transcript);
  }, [transcript]);

  const [selectedLanguage, setSelectedLanguage] = useState("en-US");

  const language = {
    "en-US": "English",
    "mr-IN": "Marathi",
    "hi-IN": "Hindi",
  };

  const handleStopListening = useCallback(() => {
    (SpeechRecognition as any).stopListening();
    setIsListening(false);
    onListening?.(false);
    resetTranscript();
    onTranscript(transcript);
  }, [onListening, resetTranscript]);

  const stopAndEnter = useCallback(() => {
    onPressEnter?.();
    handleStopListening();
  }, [onPressEnter, handleStopListening]);

  // on press esc key stop listening
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        handleStopListening();
      }
      if (e.key === "Enter") {
        stopAndEnter();
      }
    };
    document.addEventListener("keydown", handleEscKey);
    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [handleStopListening, stopAndEnter]);

  return (
    <div className="flex items-center w-full">
      <div
        className={cn(
          "flex items-center w-full",
          isListening && "gap-8 justify-between"
        )}
      >
        {!isListening && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <ChevronDown className="size-4" />{" "}
                {language[selectedLanguage as keyof typeof language]}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[200px]">
              <DropdownMenuItem
                onClick={() => {
                  handleStopListening();
                  setSelectedLanguage("en-US");
                }}
              >
                {language["en-US"]}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  handleStopListening();
                  setSelectedLanguage("hi-IN");
                }}
              >
                {language["hi-IN"]}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  handleStopListening();
                  setSelectedLanguage("mr-IN");
                }}
              >
                {language["mr-IN"]}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        {isListening && (
          <div
            onClick={() => {
              handleStopListening();
            }}
            className="cursor-pointer text-white rounded-full p-2 bg-red-500"
          >
            <X className="size-4" />
          </div>
        )}
        {isListening && (
          <div className="flex items-center">
            <Dot className="size-8 animate-pulse text-red-500" />
            <span className="text-xs text-muted-foreground">
              <p className="text-sm">
                Listening...{" "}
                <span className="text-xs">(Press ESC to stop)</span>
              </p>
            </span>
          </div>
        )}
        <div
          onClick={() => {
            if (isListening) {
              stopAndEnter();
            } else {
              onListening?.(true);
              (SpeechRecognition as any).startListening({
                continuous: true,
                interimResults: true,
                language: selectedLanguage,
              });
              setIsListening(true);
            }
          }}
          className={cn(
            "bg-primary/30 text-primary-foreground rounded-full p-2 cursor-pointer",
            isListening && "bg-primary text-primary-foreground"
          )}
        >
          {isListening ? (
            <Check className="size-4" />
          ) : (
            <Mic className="size-4" />
          )}
        </div>
      </div>
    </div>
  );
};
export default SpeechToText;
