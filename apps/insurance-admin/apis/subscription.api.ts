import { TryCatch } from "@/lib/try-catch";
import { KY, TAckResponse, TErrorResponse } from "./_.index";
import { TPlan, TSubscription } from "@/types/plan-subscription";

export const ListPlans = async () => {
  return await TryCatch<TPlan[], TErrorResponse>(KY.get("plan").json());
};

export type TSubscriptionStatus =
  | "Active"
  | "Inactive"
  | "Expired"
  | "Cancelled";
export type TCreateSubscriptionParams = {
  PlanID: string;
  UserID: string;
  Status: TSubscriptionStatus;
  StartDate: string;
  EndDate?: string | null;
  Variant: string;
  SuperUser: boolean;
  Price?: number;
};

const CreateSubscription = async (params: TCreateSubscriptionParams) => {
  return await TryCatch<TAckResponse<TSubscription>, TErrorResponse>(
    KY.post("subscription", { json: params }).json()
  );
};

export type TListSubscriptionsParams = {
  UserIDs: string[];
  PlanIDs?: string[];
  Status?: TSubscriptionStatus;
  ProjectIDs?: string[];
};

const ListSubscriptions = async (params: TListSubscriptionsParams) => {
  return await TryCatch<TSubscription[], TErrorResponse>(
    KY.get("subscription", {
      searchParams: new URLSearchParams({
        UserIDs: params.UserIDs.join(","),
        ...(params.PlanIDs && { PlanIDs: params.PlanIDs.join(",") }),
        ...(params.Status && { Status: params.Status }),
        ...(params.ProjectIDs && { ProjectIDs: params.ProjectIDs.join(",") }),
      }),
    }).json()
  );
};

export const SUBSCRIPTION = {
  CreateSubscription,
  ListSubscriptions,
  ListPlans,
};
