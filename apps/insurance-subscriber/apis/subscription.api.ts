import { TryCatch } from "@/lib/try-catch";
import { KY, TErrorResponse } from "./_.index";

export interface TProject {
  _id: string;
  Name: string;
  Description: string;
  TenantID: string;
  ProjectID: string;
  createdAt: string;
  updatedAt: string;
}

export interface TAudioBook {
  _id: string;
  Name: string;
  Summary: string;
  AudioConfig: {
    SpeechRate: number;
    Pitch: number;
    Gender: "MALE" | "FEMALE";
    VoiceModel: string;
    Language: string;
    SampleRateHertz: number;
    VolumeGainDecibels: number;
  };
  SpeechText: string;
  CharacterCount: number;
  Duration: number;
  Provider: "Google";
  Status: "Completed" | "Processing" | "Failed";
  Scopes: {
    ProjectID: string;
  };
  TenantID: string;
  CreditUsage: {
    Cost: number;
    Currency: string;
    Credits: number | null;
    PerCharacter: number;
  };
  createdAt: string;
  updatedAt: string;
  Event: {
    StartedAt: string;
    FinishedAt: string;
  };
  S3: {
    Location: string;
    Key: string;
    AudioFormat: "MP3" | "WAV" | "AAC";
    FileSize: number;
    Expiration: string;
    AccessUrl: string;
  };
}

export interface TAudioBookMeta
  extends Pick<
    TAudioBook,
    | "_id"
    | "Name"
    | "Summary"
    | "CharacterCount"
    | "Duration"
    | "Provider"
    | "Status"
    | "createdAt"
    | "updatedAt"
  > {}

const GetMyProjects = async () => {
  return await TryCatch<TProject[], TErrorResponse>(
    KY.get("subscription/me/projects").json(),
  );
};

const GetMySubscriptions = async () => {
  return await TryCatch<any[], TErrorResponse>(
    KY.get("subscription/me/subs").json(),
  );
};

const GetAudioBooks = async (ProjectID: string) => {
  return await TryCatch<TAudioBookMeta[], TErrorResponse>(
    KY.get(
      `subscription/project/${ProjectID}/audio-books?Status=Completed`,
    ).json(),
  );
};

const GetAudioBook = async (AudioBookID: string, ProjectID: string) => {
  return await TryCatch<TAudioBook, TErrorResponse>(
    KY.get(
      `subscription/project/${ProjectID}/audio-book/${AudioBookID}`,
    ).json(),
  );
};

export const SUBSCRIPTION = {
  GetMyProjects,
  GetMySubscriptions,
  GetAudioBooks,
  GetAudioBook,
};
