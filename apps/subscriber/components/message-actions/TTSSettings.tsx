"use client";

import React, { useState, useCallback, useMemo } from "react";
import { Settings, Volume2, Check } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { useTTS } from "../../contexts/tts-context";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";

export function TTSSettings() {
  const { ttsState, setProvider, voice, setVoice, getAvailableVoices } =
    useTTS();
  const [isOpen, setIsOpen] = useState(false);

  // Memoize available voices to avoid recalculation
  const availableVoices = useMemo(
    () => getAvailableVoices(),
    [getAvailableVoices]
  );

  const currentVoice = useMemo(
    () => availableVoices.find((v) => v.name === voice),
    [availableVoices, voice]
  );

  const handleProviderChange = useCallback(
    (provider: "browser" | "lemonfox") => {
      try {
        setProvider(provider);

        // Set appropriate default voice for the provider
        if (provider === "lemonfox") {
          setVoice("sarah");
        } else {
          // For browser, try to find an Indian voice or default to first available
          const browserVoices = availableVoices.filter(
            (v) => v.provider === "browser"
          );
          const indianVoice = browserVoices.find(
            (v) =>
              v.label.toLowerCase().includes("indian") ||
              v.label.toLowerCase().includes("en-in")
          );
          setVoice(indianVoice?.name || browserVoices[0]?.name || "");
        }
      } catch (error) {
        console.error("Failed to change TTS provider:", error);
      }
    },
    [setProvider, setVoice, availableVoices]
  );

  const handleVoiceChange = useCallback(
    (voiceName: string) => {
      try {
        setVoice(voiceName);
      } catch (error) {
        console.error("Failed to change TTS voice:", error);
      }
    },
    [setVoice]
  );

  // Filter voices by current provider
  const currentProviderVoices = useMemo(
    () => availableVoices.filter((v) => v.provider === ttsState.provider),
    [availableVoices, ttsState.provider]
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <button
          className={cn(
            "p-1.5 rounded-full hover:bg-muted/80 text-muted-foreground transition-colors",
            "opacity-70 hover:opacity-100"
          )}
          aria-label="TTS Settings"
          title="TTS Settings"
        >
          <Settings size={14} />
        </button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Volume2 size={18} className="text-primary" />
            TTS Settings
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Provider Selection */}
          <div>
            <label className="text-sm font-medium mb-3 block">Provider</label>
            <div className="space-y-2">
              {/* LemonFox Provider */}
              <Button
                variant={
                  ttsState.provider === "lemonfox" ? "default" : "outline"
                }
                className="w-full justify-between h-auto p-4"
                onClick={() => handleProviderChange("lemonfox")}
              >
                <div className="flex items-center gap-3">
                  <div
                    className={cn(
                      "w-2 h-2 rounded-full",
                      ttsState.provider === "lemonfox"
                        ? "bg-primary-foreground"
                        : "bg-muted-foreground"
                    )}
                  />
                  <span className="font-medium">LemonFox AI</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant="secondary"
                    className="bg-orange-100 text-orange-700 hover:bg-orange-100"
                  >
                    Premium
                  </Badge>
                  <Badge
                    variant="secondary"
                    className="bg-green-100 text-green-700 hover:bg-green-100"
                  >
                    Free
                  </Badge>
                  {ttsState.provider === "lemonfox" && <Check size={16} />}
                </div>
              </Button>

              {/* Browser Provider */}
              <Button
                variant={
                  ttsState.provider === "browser" ? "default" : "outline"
                }
                className="w-full justify-between h-auto p-4"
                onClick={() => handleProviderChange("browser")}
              >
                <div className="flex items-center gap-3">
                  <div
                    className={cn(
                      "w-2 h-2 rounded-full",
                      ttsState.provider === "browser"
                        ? "bg-primary-foreground"
                        : "bg-muted-foreground"
                    )}
                  />
                  <span className="font-medium">Browser TTS</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant="secondary"
                    className="bg-blue-100 text-blue-700 hover:bg-blue-100"
                  >
                    Free
                  </Badge>
                  {ttsState.provider === "browser" && <Check size={16} />}
                </div>
              </Button>
            </div>
          </div>

          {/* Voice Selection */}
          <div>
            <label className="text-sm font-medium mb-3 block">
              Voice ({currentProviderVoices.length} available)
            </label>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {currentProviderVoices.map((voiceOption) => (
                <Button
                  key={voiceOption.name}
                  variant={voice === voiceOption.name ? "default" : "outline"}
                  className="w-full justify-between h-auto p-3 text-left"
                  onClick={() => handleVoiceChange(voiceOption.name)}
                >
                  <div className="flex flex-col items-start gap-1">
                    <span className="font-medium text-sm">
                      {voiceOption.label.split(" (")[0]}
                    </span>
                    <span
                      className={cn(
                        "text-xs",
                        voice === voiceOption.name
                          ? "text-primary-foreground/70"
                          : "text-muted-foreground"
                      )}
                    >
                      {voiceOption.label.includes("(")
                        ? voiceOption.label.split("(").slice(1).join("(")
                        : ""}
                    </span>
                  </div>
                  {voice === voiceOption.name && (
                    <Check size={16} className="flex-shrink-0" />
                  )}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
