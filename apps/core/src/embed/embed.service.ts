import { BadRequestException, Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  HeadObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { CreateEmbedDto } from './dto/create-embed.dto';
import { UpdateEmbedDto } from './dto/update-embed.dto';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SourceModel } from './schemas/source.schema';
import { Chunk } from './schemas/chunk.schema';
import { makeRetriever as academicRetriever } from '../../agents/(new)/retrieval-graph';
import { makeRetriever as industryRetriever } from '../../agents/(insurance)/retrieval-graph';
import { NotFoundException } from '@nestjs/common';
import GenerateID from '../../common/lib/generate-id';
import { AppTypeEnum } from '../core/user/entities/user.entity';
import axios from 'axios';
import * as FormData from 'form-data';
import { IndSourceModel } from './schemas/ind-source.schema';
import { IndChunkModel } from './schemas/ind-chunk.schema';

@Injectable()
export class EmbedService {
  private s3Client: S3Client;

  constructor(
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
    @InjectModel(SourceModel.name) private sourceChunkModel: Model<SourceModel>,
    @InjectModel(Chunk.name) private chunkModel: Model<Chunk>,
    @InjectModel(IndSourceModel.name) private indSourceChunkModel: Model<IndSourceModel>,
    @InjectModel(IndChunkModel.name) private indChunkModel: Model<IndChunkModel>
  ) {
    this.s3Client = new S3Client({
      region: this.configService.get('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      },
    });
  }

  async create(createEmbedDto: CreateEmbedDto, file: Express.Multer.File, UserID: string, AppType: AppTypeEnum) {
    const bucketName = this.configService.get('S3_NXTCAMPUS_PLATFORM');
    const sourceId = GenerateID.Generate16Hex('src_');
    const key = `${AppType === AppTypeEnum.Academic ? 'academic-lm' : 'industry-lm'}/${createEmbedDto.TenantID}/${createEmbedDto.ProjectID}/${sourceId}`;

    try {
      // Upload to S3
      await this.s3Client.send(
        new PutObjectCommand({
          Bucket: bucketName,
          Key: key,
          Body: file.buffer,
          ContentType: file.mimetype,
        })
      );

      // Generate presigned URL
      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: key,
      });
      const presignedUrl = await getSignedUrl(this.s3Client, command, { expiresIn: 86400 });

      if (AppType === AppTypeEnum.Academic) {
        // Emit event for embedding process
        const payload = {
          url: presignedUrl,
          ProjectID: createEmbedDto.ProjectID,
          TenantID: createEmbedDto.TenantID,
          fileName: file.originalname,
          sourceId: sourceId,
          presignedUrl: presignedUrl,
          tableOfContentPages: createEmbedDto.tableOfContentPages,
          pageNumberOffset: createEmbedDto.pageNumberOffset,
          sourceName: createEmbedDto.sourceName,
          UserID: UserID,
          language: createEmbedDto.language,
          sourceType: createEmbedDto.sourceType,
          version: createEmbedDto.version,
        };
        this.eventEmitter.emit('document.academic-embed', payload);
      } else if (AppType === AppTypeEnum.Industry) {
        // Emit event for embedding process to agentic-ai
        const payload = {
          url: presignedUrl,
          ProjectID: createEmbedDto.ProjectID,
          TenantID: createEmbedDto.TenantID,
          fileName: file.originalname,
          sourceId: sourceId,
          presignedUrl: presignedUrl,
          tableOfContentPages: createEmbedDto.tableOfContentPages,
          pageNumberOffset: createEmbedDto.pageNumberOffset,
          sourceName: createEmbedDto.sourceName,
          UserID: UserID,
          sourceType: createEmbedDto.sourceType,
          language: createEmbedDto.language,
          IsSuperEmbed: createEmbedDto.IsSuperEmbed,
        };
        if (createEmbedDto.IsSuperEmbed) {
          this.eventEmitter.emit('document.super-embed', payload);
        } else {
          this.eventEmitter.emit('document.agentic-embed', payload);
        }
      } else {
        throw new BadRequestException('Invalid app type');
      }

      return {
        message: 'PDF uploaded successfully, embedding process started',
        ProjectID: createEmbedDto.ProjectID,
        TenantID: createEmbedDto.TenantID,
        fileName: file.originalname,
        s3Key: key,
      };
    } catch (error) {
      throw new Error(`Failed to process file: ${error.message}`);
    }
  }

  findAll() {
    return `This action returns all embed`;
  }

  findOne(id: number) {
    return `This action returns a #${id} embed`;
  }

  update(id: number, updateEmbedDto: UpdateEmbedDto) {
    return `This action updates a #${id} embed`;
  }

  /**
   * Deletes a source and all related data
   * - Deletes the document from MongoDB (both source and chunks)
   * - Deletes vectors from Pinecone
   * - Deletes the file from S3
   *
   * @param sourceId The source ID to delete
   * @param tenantId The tenant ID
   * @param projectId The project ID
   * @returns Success message
   */
  async deleteSource(sourceId: string, tenantId: string, projectId: string, AppType: AppTypeEnum) {
    try {
      // Find the source document in MongoDB
      const sourceDocument =
        AppType === AppTypeEnum.Academic
          ? await this.sourceChunkModel
              .findOne({
                SourceID: sourceId,
                TenantID: tenantId,
                ProjectID: projectId,
              })
              .exec()
          : await this.indSourceChunkModel
              .findOne({
                SourceID: sourceId,
                TenantID: tenantId,
                ProjectID: projectId,
              })
              .exec();
      if (!sourceDocument) {
        throw new NotFoundException(`Source with ID ${sourceId} not found`);
      }

      // Get the AWS S3 key
      const awsKey = sourceDocument.AWSKey;
      const bucketName = this.configService.get('S3_NXTCAMPUS_PLATFORM');

      // 1. Delete from MongoDB - Source Document
      console.log(`Deleting source ${sourceId} from MongoDB`);
      if (AppType === AppTypeEnum.Academic) {
        await this.sourceChunkModel
          .deleteOne({
            SourceID: sourceId,
            TenantID: tenantId,
            ProjectID: projectId,
          })
          .exec();
      } else {
        await this.indSourceChunkModel
          .deleteOne({
            SourceID: sourceId,
            TenantID: tenantId,
            ProjectID: projectId,
          })
          .exec();
      }

      // 1.1 Delete chunks{
      if (AppType === AppTypeEnum.Academic) {
        await this.chunkModel
          .deleteMany({
            SourceID: sourceId,
            TenantID: tenantId,
            ProjectID: projectId,
          })
          .exec();
      } else {
        await this.indChunkModel
          .deleteMany({
            SourceID: sourceId,
            TenantID: tenantId,
            ProjectID: projectId,
          })
          .exec();
      }
      // 2. Delete vectors from Pinecone where metadata.source.source_id = sourceId
      console.log(`Deleting vectors for source ${sourceId} from Pinecone`);
      try {
        if (AppType === AppTypeEnum.Academic) {
          const retriever = await academicRetriever(tenantId, projectId);
          // Use the delete method with a filter to target only vectors with this source ID
          await retriever.delete({
            namespace: projectId,
            filter: {
              'source.source_id': sourceId,
            },
          });
        } else {
          const retriever = await industryRetriever(tenantId, projectId);
          // Use the delete method with a filter to target only vectors with this source ID
          await retriever.delete({
            namespace: projectId,
            filter: {
              source_id: sourceId,
            },
          });
        }
      } catch (pineconeError) {
        console.error(`Error deleting vectors from Pinecone: ${pineconeError.message}`);
        // Continue with deletion even if Pinecone fails
      }

      // 3. Delete the file from S3
      console.log(`Deleting file ${awsKey} from S3`);
      try {
        await this.s3Client.send(
          new DeleteObjectCommand({
            Bucket: bucketName,
            Key: awsKey,
          })
        );
      } catch (s3Error) {
        console.error(`Error deleting file from S3: ${s3Error.message}`);
        // Continue with operation even if S3 deletion fails
      }

      return {
        success: true,
        message: `Source ${sourceId} successfully deleted from all systems`,
        sourceId,
        tenantId,
        projectId,
      };
    } catch (error) {
      console.error(`Failed to delete source ${sourceId}: ${error.message}`);
      throw error;
    }
  }

  async doesFileExist(bucketName: string, key: string): Promise<boolean> {
    try {
      await this.s3Client.send(
        new HeadObjectCommand({
          Bucket: bucketName,
          Key: key,
        })
      );
      return true;
    } catch (err: any) {
      if (err.name === 'NotFound' || err.$metadata?.httpStatusCode === 404) {
        return false;
      }
      throw err; // Unexpected error, rethrow
    }
  }

  /**
   * Get a specific chunk by ID
   */
  async getChunkById(chunkId: string, tenantId: string, projectId: string, AppType: AppTypeEnum) {
    try {
      const chunk =
        AppType === AppTypeEnum.Academic
          ? await this.chunkModel
              .findOne({
                ChunkID: chunkId,
                TenantID: tenantId,
                ProjectID: projectId,
              })
              .exec()
          : await this.indChunkModel
              .findOne({
                ChunkID: chunkId,
                TenantID: tenantId,
                ProjectID: projectId,
              })
              .exec();
      if (!chunk) {
        throw new NotFoundException(`Chunk with ID ${chunkId} not found`);
      }

      return {
        chunk,
        success: true,
      };
    } catch (error) {
      console.error(`Failed to retrieve chunk ${chunkId}: ${error.message}`);
      throw error;
    }
  }
}
