# from agents.embedding_agent.unstructured.state import UnstructuredState
# import unstructured_client
# import os
# from unstructured_client.models import operations, shared
# import json
# from unstructured.staging.base import dict_to_elements
# from unstructured.chunking.title import chunk_by_title
# from unstructured.chunking.base import Element
# import base64
# import zlib
# import asyncio

# client = unstructured_client.UnstructuredClient(
#     api_key_auth=os.getenv("UNSTRUCTURED_API_KEY"),
# )


# async def parse_pdf(state: UnstructuredState) -> operations.PartitionResponse:
#     req = operations.PartitionRequest(
#         partition_parameters=shared.PartitionParameters(
#             files=shared.Files(
#                 content=file_content,
#                 file_name=state.file_name,
#             ),
#             coordinates=state.coordinates,
#             include_orig_elements=True,
#             unique_element_ids=True,
#             strategy=state.strategy,
#             vlm_model=state.vlm_model,
#             vlm_model_provider=state.vlm_model_provider,
#             languages=state.languages,
#             split_pdf_page=state.split_pdf_page,
#             split_pdf_allow_failed=state.split_pdf_allow_failed,
#             split_pdf_concurrency_level=state.split_pdf_concurrency_level,
#         ),
#     )

#     try:
#         res = await client.general.partition_async(request=req)
#         return res
#     except Exception as e:
#         print(f"Error processing {state.file_name}: {e}")


# async def chunk_elements(res: operations.PartitionResponse) -> list[Element]:
#     try:
#         elements = dict_to_elements(res.elements)
#         chunks = chunk_by_title(
#             elements=elements,
#             combine_text_under_n_chars=state.combine_under_n_chars,
#             max_characters=state.max_characters,
#             multipage_sections=state.multipage_sections,
#             include_orig_elements=True,
#         )
#         return chunks
#     except Exception as e:
#         print(e)


# def decode_base64(encoded_data: str) -> dict:
#     decoded_bytes = base64.b64decode(encoded_data)
#     decompressed_bytes = zlib.decompress(decoded_bytes)
#     try:
#         json_data = json.loads(decompressed_bytes)
#         return json_data
#     except Exception as e:
#         print("It might not be JSON:", e)
#         print(decompressed_bytes.decode("utf-8", errors="ignore"))


# if __name__ == "__main__":
#     with open("agents/sample_data/Mediclaim Policy.pdf", "rb") as file:
#         file_content = file.read()
#         state = UnstructuredState(
#             file_content=file_content,
#             file_name="geography.pdf",
#             file_type="application/pdf",
#             aws_path="",
#             source_id="",
#             tenant_id="",
#             project_id="",
#             user_id="",
#             strategy=shared.Strategy.VLM,
#             vlm_model=shared.VLMModel.GPT_4O,
#             vlm_model_provider=shared.VLMModelProvider.OPENAI,
#             languages=["eng"],
#             split_pdf_page=False,
#             split_pdf_allow_failed=False,
#             split_pdf_concurrency_level=15,
#             combine_under_n_chars=1000,
#             extract_image_block_types=["Image", "Table"],
#             starting_page_number=1,
#             unique_element_ids=True,
#             chunking_strategy="by_title",
#             max_characters=1000,
#             multipage_sections=True,
#             coordinates=True,
#             hi_res_model_name="yolox",
#         )

#     res = asyncio.run(parse_pdf(file_content, state))

#     with open("agents/sample_data/Mediclaim Policy-vlm-parsed.json", "w") as f:
#         f.write(json.dumps(res.elements, indent=2))

#     chunks = asyncio.run(chunk_elements(res))
#     chunks = [chunk.to_dict() for chunk in chunks]

#     for chunk in chunks:
#         chunk["metadata"]["orig_elements"] = decode_base64(
#             chunk["metadata"]["orig_elements"]
#         )

#     with open("agents/sample_data/Mediclaim Policy-vlm-chunks.json", "w") as f:
#         f.write(json.dumps(chunks, indent=2))
#     print("Done")
