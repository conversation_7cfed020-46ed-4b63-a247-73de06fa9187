import { useEffect, useState } from "react";
import { API } from "@/apis/api";
import { TUser } from "@/types/user";
import { useRouter, usePathname } from "next/navigation";
import { toast } from "sonner";

interface UseAuthReturn {
  user: TUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signOut: () => Promise<void>;
  refetch: () => Promise<void>;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<TUser | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const router = useRouter();
  const pathname = usePathname();

  const fetchUser = async (): Promise<void> => {
    setIsLoading(true);
    try {
      // Try to get user from localStorage first for immediate UI update
      const storedUser = localStorage.getItem("user");
      if (storedUser) {
        try {
          setUser(JSON.parse(storedUser));
        } catch (error) {
          console.error("Failed to parse user from localStorage", error);
          localStorage.removeItem("user");
        }
      }

      // Then fetch fresh data from API
      const { data, errors } = await API.AUTH.GetCurrentUser();

      if (errors) {
        errors.forEach((error) => {
          if (error.message) toast.error(error.message);
        });
        setUser(null);
        localStorage.removeItem("user");
        return;
      }

      if (data) {
        setUser(data);
        localStorage.setItem("user", JSON.stringify(data));
      } else {
        setUser(null);
        localStorage.removeItem("user");
      }
    } catch (error) {
      console.error("Auth fetch failed:", error);
      setUser(null);
      localStorage.removeItem("user");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!pathname.includes("sign-up")) {
      fetchUser();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const signOut = async (): Promise<void> => {
    try {
      const { data, errors } = await API.AUTH.SignOut();

      if (data) {
        setUser(null);
        localStorage.removeItem("user");
        toast.success(data.message || "Successfully signed out");
        router.push("/sign-in");
      }

      if (errors) {
        throw new Error(errors[0]?.message || "Failed to sign out");
      }
    } catch (error) {
      console.error("Failed to sign out", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to sign out"
      );
    }
  };

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    signOut,
    refetch: fetchUser,
  };
}
