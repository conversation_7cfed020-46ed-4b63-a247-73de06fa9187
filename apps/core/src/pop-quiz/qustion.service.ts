import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { omit } from 'radash';
import { CommonQuestionDto, CreateQuestionDto, ListQuestionsDto, UpdateQuestionDto } from './dto/pop-quiz.service.dto';
import { GenerateQuestionsBodyDto } from './dto/pop-quiz.controller.dto';
import { graph as academicGraph } from '../../agents/(pop-quiz)/graph';
import { graph as insuranceGraph } from '../../agents/(insurance)/(pop-quiz)/graph';
import { SourceModel } from '../embed/schemas/source.schema';
import { Chunk } from '../embed/schemas/chunk.schema';
import { PopQuizQuestionModel } from './schemas/question.schema';
import GenerateID from '../../common/lib/generate-id';
import { QuestionTypes } from './entities/question.entity';
import { TokenCounterService } from '../token-counter/token-counter.service';
import { AppTypeEnum } from '../core/user/entities/user.entity';
import { IndSourceModel } from '../embed/schemas/ind-source.schema';
import { IndChunkModel } from '../embed/schemas/ind-chunk.schema';

@Injectable()
export class QuestionService {
  constructor(
    @InjectModel(PopQuizQuestionModel.name) private readonly QuestionModel: Model<PopQuizQuestionModel>,
    @InjectModel(Chunk.name) private chunkModel: Model<Chunk>,
    @InjectModel(SourceModel.name) private sourceModel: Model<SourceModel>,
    @InjectModel(IndSourceModel.name) private indSourceModel: Model<IndSourceModel>,
    @InjectModel(IndChunkModel.name) private indChunkModel: Model<IndChunkModel>,
    private readonly tokenCounterService: TokenCounterService
  ) {}

  async CreateQuestionBulk(Params: CreateQuestionDto[]) {
    const createdQuestions = await this.QuestionModel.insertMany(
      Params.map((question) => ({
        ...question,
        QuestionID: GenerateID.Generate24Hex('pop_quest_'),
      }))
    );
    return createdQuestions;
  }

  async CreateQuestion(Params: CreateQuestionDto) {
    const question = await this.QuestionModel.create(Params);
    return question;
  }

  async UpdateQuestion(Params: UpdateQuestionDto) {
    const question = await this.QuestionModel.findOneAndUpdate(
      {
        QuestionID: Params.QuestionID,
        TenantID: Params.TenantID,
      },
      Params,
      {
        new: true,
      }
    ).exec();
    return question;
  }

  async DeleteQuestion(Params: CommonQuestionDto) {
    const question = await this.QuestionModel.findOneAndDelete({
      SourceID: Params.SourceID,
      TenantID: Params.TenantID,
      ProjectID: Params.ProjectID,
    }).exec();
    return question;
  }

  async ListQuestions(Params: ListQuestionsDto) {
    const { SourceIDs, Tags, TenantID, ProjectID } = Params;
    const Scopes = omit(Params, ['SourceIDs', 'Tags', 'TenantID', 'ProjectID']);
    const ScopeFilter = Object.keys(Scopes).reduce(
      (acc, key) => {
        acc[`Scopes.${key}`] = Scopes[key];
        return acc;
      },
      {} as Record<string, string>
    );
    const questions = await this.QuestionModel.find({
      TenantID,
      ProjectID,
      ...(SourceIDs && { SourceID: { $in: SourceIDs } }),
      ...(Tags && { Tags: { $in: Tags } }),
      ...ScopeFilter,
    }).lean();
    return questions;
  }

  async ListQuestionsIDs(Params: ListQuestionsDto) {
    const { SourceIDs, Tags, TenantID, ProjectID } = Params;
    const Scopes = omit(Params, ['SourceIDs', 'Tags', 'TenantID', 'ProjectID']);
    const ScopeFilter = Object.keys(Scopes).reduce(
      (acc, key) => {
        acc[`Scopes.${key}`] = Scopes[key];
        return acc;
      },
      {} as Record<string, string>
    );
    const questions = await this.QuestionModel.find({
      TenantID,
      ProjectID,
      ...(SourceIDs && { SourceID: { $in: SourceIDs } }),
      ...(Tags && { Tags: { $in: Tags } }),
      ...ScopeFilter,
    })
      .distinct('QuestionID')
      .lean();
    return questions;
  }

  async GetQuestion(Params: Omit<CommonQuestionDto, 'ProjectID'>) {
    const question = await this.QuestionModel.findOne({
      QuestionID: Params.QuestionID,
      TenantID: Params.TenantID,
    }).lean();
    return question;
  }

  async GenerateQuestions(Body: GenerateQuestionsBodyDto, TenantID: string, UserID: string, AppType: AppTypeEnum) {
    if (AppType === AppTypeEnum.Academic) {
      const source = await this.sourceModel
        .findOne({ TenantID, ProjectID: Body.ProjectID, SourceID: Body.SourceID })
        .lean();
      if (!source) {
        throw new Error('Source not found');
      }
      const chunks = await this.chunkModel
        .find({
          TenantID,
          ProjectID: Body.ProjectID,
          SourceID: Body.SourceID,
          ChapterID: Body.ID,
        })
        .sort({ ChunkNumber: 1 })
        .select('ChunkNumber PageNumber DocumentPageNumber Summary Topic SubTopic Content ChunkID')
        .lean();
      if (!chunks) {
        throw new Error('Chunks not found');
      }
      const Source = {
        ...source,
        Chapter: source.Chapters.filter((chapter) => chapter.ChapterID === Body.ID).map((chapter) => ({
          ...chapter,
          Chunks: chunks,
          ChapterID: undefined,
          ChunkIDs: undefined,
        }))[0],
        Chapters: undefined,
      };
      const response = await academicGraph.invoke({
        ChapterNumber: Source.Chapter.ChapterNumber,
        ChapterName: Source.Chapter.ChapterName,
        ChapterSummary: Source.Chapter.Summary,
        ChapterTopics: Source.Chapter.Topics,
        SourceName: Source.SourceName,
        SourceID: Source.SourceID,
        Chunks: Source.Chapter.Chunks.map((chunk) => ({
          ChunkNumber: chunk.ChunkNumber,
          PageNumber: chunk.PageNumber,
          DocumentPageNumber: chunk.DocumentPageNumber,
          Summary: chunk.Summary,
          Topic: chunk.Topic,
          SubTopic: chunk.SubTopic,
          Content: chunk.Content,
          ChunkID: chunk.ChunkID,
        })),
      });

      const questions = response.questions.map((question) => ({
        Title: Source.Chapter.ChapterName,
        QuestionText: question.question,
        Type: QuestionTypes.Values.SINGLE_CHOICE,
        Tags: [],
        Options: question.options.map((option) => ({
          Title: option.title,
          IsCorrect: option.isCorrect,
          OptionID: GenerateID.Generate24Hex('opt_'),
        })),
        Answer: {
          Explnation: question.answer.explnation,
          Citations: question.answer.citations.map((citation) => ({
            ID: citation.id,
            SourceTitle: citation.source_title,
            SourceID: citation.source_id,
            PageNumber: citation.page_number,
            DocumentPageNumber: citation.document_page_number,
          })),
        },
        Difficulty: question.difficulty,
        QuestionID: GenerateID.Generate16Hex('pop_quest_'),
        Scopes: {
          ChapterID: Body.ID,
        },
        SourceID: Source.SourceID,
        TenantID: Source.TenantID,
        ProjectID: Source.ProjectID,
      }));

      const ack = await this.CreateQuestionBulk(questions);

      const GraphTokenCount = response.TokenCount;

      const TokenCounter = await this.tokenCounterService.create({
        ProjectID: Body.ProjectID,
        TenantID,
        ResourceType: 'pop_quiz',
        ResourceIDs: ack.map((question) => question.QuestionID),
        UserID,
        TokenCount: GraphTokenCount,
        AppType: AppType,
      });

      return { ack, TokenCounter };
    } else {
      const source = await this.indSourceModel
        .findOne({ TenantID, ProjectID: Body.ProjectID, SourceID: Body.SourceID })
        .lean();
      if (!source) {
        throw new Error('Source not found');
      }
      const chunks = await this.indChunkModel
        .find({
          TenantID,
          ProjectID: Body.ProjectID,
          SourceID: Body.SourceID,
          'Sections.SectionID': Body.ID,
        })
        .sort({ ChunkNumber: 1 })
        .select('ChunkNumber PageNumber DocumentPageNumber Summary Sections Content ChunkID')
        .lean();
      if (!chunks) {
        throw new Error('Chunks not found');
      }
      const response = await insuranceGraph.invoke({
        Source: {
          SourceID: source.SourceID,
          SourceName: source.SourceName,
          Sections: source.Sections,
          SourceSummary: {
            Title: source.SourceSummary.Title,
            Summary: source.SourceSummary.Summary,
            MainTopics: source.SourceSummary.MainTopics,
            SectionHighlights: source.SourceSummary.SectionHighlights,
          },
        },
        Chunks: chunks.map((chunk) => ({
          ChunkID: chunk.ChunkID,
          ChunkNumber: chunk.ChunkNumber,
          PageNumber: chunk.PageNumber,
          DocumentPageNumber: chunk.DocumentPageNumber,
          Summary: chunk.Summary,
          Content: chunk.Content,
          Sections: chunk.Sections,
        })),
        NumberOfQuestions: Body.NumberOfQuestions,
      });

      const questions = response.questions.map((question) => ({
        Title: source.SourceName,
        QuestionText: question.question,
        Type: QuestionTypes.Values.SINGLE_CHOICE,
        Tags: [],
        Options: question.options.map((option) => ({
          Title: option.title,
          IsCorrect: option.isCorrect,
          OptionID: GenerateID.Generate24Hex('opt_'),
        })),
        Answer: {
          Explnation: question.answer.explnation,
          Citations: question.answer.citations.map((citation) => ({
            ID: citation.id,
            SourceTitle: citation.source_title,
            SourceID: citation.source_id,
            PageNumber: citation.page_number,
            DocumentPageNumber: citation.document_page_number,
          })),
        },
        Difficulty: question.difficulty,
        QuestionID: GenerateID.Generate16Hex('pop_quest_'),
        Scopes: {
          SectionID: Body.ID,
        },
        SourceID: source.SourceID,
        TenantID: source.TenantID,
        ProjectID: source.ProjectID,
      }));

      const ack = await this.CreateQuestionBulk(questions);

      const GraphTokenCount = response.TokenCount;

      const TokenCounter = await this.tokenCounterService.create({
        ProjectID: Body.ProjectID,
        TenantID,
        ResourceType: 'pop_quiz',
        ResourceIDs: ack.map((question) => question.QuestionID),
        UserID,
        TokenCount: GraphTokenCount,
        AppType: AppType,
      });

      return { ack, TokenCounter };
    }
  }

  async $GetQuestion(Params: Pick<CommonQuestionDto, 'QuestionID'>) {
    const question = await this.QuestionModel.findOne({ QuestionID: Params.QuestionID }).exec();
    return question;
  }
}
