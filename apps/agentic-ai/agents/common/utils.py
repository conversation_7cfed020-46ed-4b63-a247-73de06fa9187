from langchain_openai import ChatOpenAI
from langchain_openai.embeddings import OpenAIEmbeddings
from langchain_pinecone import PineconeVectorStore
import os
from pinecone import Pinecone, ServerlessSpec


def get_llm(
    model_name: str = "gpt-4.1-mini",
    temperature: float = 0,
):
    return ChatOpenAI(
        model=model_name,
        temperature=temperature,
    )


def make_pinecone_store(
    namespace: str,
    embedding_model: OpenAIEmbeddings,
    index_name: str = "industry-lm",
    dimension: int = 3072,
) -> PineconeVectorStore:
    pinecone_client = Pinecone()

    if not pinecone_client.has_index(name=index_name):
        pinecone_client.create_index(
            name=index_name,
            dimension=dimension,
            metric="cosine",
            spec=ServerlessSpec(cloud="aws", region="us-east-1"),
        )

    index = pinecone_client.Index(index_name)

    vstore = PineconeVectorStore(
        index=index,
        embedding=embedding_model,
        index_name=index_name,
        namespace=namespace,
    )
    return vstore


def get_vector_store(
    namespace: str,
    embedding_model_name: str = "text-embedding-3-large",
    database_provider: str = "pinecone",
    index_name: str = "industry-lm",
    dimension: int = 3072
):
    embedding_model = OpenAIEmbeddings(model=embedding_model_name,dimensions=dimension)
    if database_provider == "pinecone":
        return make_pinecone_store(
            namespace=namespace,
            embedding_model=embedding_model,
            index_name=index_name,
            dimension=dimension
        )
    else:
        raise ValueError(f"Unsupported database provider: {database_provider}")
