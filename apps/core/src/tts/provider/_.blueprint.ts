export type IListVoicesParams = object;
export type IListVoicesResponse = object;

export interface ISynthesizeSpeechParams {
  Text: string;
  Voice: string;
  LanguageCode: string;
  Pitch: number;
  SpeechRate: number;
  VolumeGainDb: number;
}

export interface ISupportedVoicesResponse {
  LanguageCode: string;
  Accent: string;
  Name: string;
  Gender: string;
  VoiceModel: string;
  PricePerCharacter: number; // 16usd per 1m characters
}

export interface ISynthesizeSpeechResponse {
  AudioContent: Buffer;
}

export interface IGenerateCostEstimationParams {
  Text: string;
  ModelName: string;
}
export interface IGenerateCostEstimationResponse {
  Cost: number;
  Currency: string;
  PerCharacter: number;
}

interface TTSBlueprint<
  TListVoicesResponses extends IListVoicesResponse,
  TListVoicesParams extends IListVoicesParams,
  TSynthesizeSpeechResponse extends ISynthesizeSpeechResponse,
  TSynthesizeSpeechParams extends ISynthesizeSpeechParams,
  TGenerateCostEstimationResponse extends IGenerateCostEstimationResponse,
  TGenerateCostEstimationParams extends IGenerateCostEstimationParams,
> {
  ListVoices(params: TListVoicesParams): Promise<TListVoicesResponses>;
  SynthesizeSpeech(params: TSynthesizeSpeechParams): Promise<TSynthesizeSpeechResponse>;
  GenerateCostEstimation(params: TGenerateCostEstimationParams): Promise<TGenerateCostEstimationResponse>;
}

export default TTSBlueprint;
