import { TryCatch } from "@/lib/try-catch";
import { KY, TAckResponse, TErrorResponse } from "./_.index";
import { PricingStructure } from "@/types/plan-subscription";

export interface TPlan {
  _id: string;
  Name: string;
  Subtitle: string;
  Description: string;
  Currency: string;
  Features: { Title: string; HelpText?: string }[];
  ProjectIDs: string[];
  Tier: string;
  TenantID: string;
  PlanID: string;
  createdAt: string;
  updatedAt: string;
  Flags: {
    FlagID: string;
    Properties: Record<string, unknown>;
  }[];
  PricingStructures: PricingStructure[];
  ListAsPublic: boolean;
}

export interface TTierGroup {
  _id: string;
  Name: string;
  Description: string;
  PlanIDs: string[];
  TenantID: string;
  InviteCode: string;
  createdAt: string;
  updatedAt: string;
  Plans: TPlan[];
}

const CreateTierGroup = async (
  params: Pick<TTierGroup, "Name" | "Description" | "PlanIDs">
) => {
  return await TryCatch<TAckResponse<TTierGroup>, TErrorResponse>(
    KY.post("tier-group", { json: params }).json()
  );
};

const UpdateTierGroup = async (
  params: Pick<TTierGroup, "_id" | "Name" | "Description" | "PlanIDs">
) => {
  return await TryCatch<TAckResponse<TTierGroup>, TErrorResponse>(
    KY.patch(`tier-group/${params._id}`, { json: params }).json()
  );
};

const DeleteTierGroup = async (params: { _id: string }) => {
  return await TryCatch<TAckResponse<TTierGroup>, TErrorResponse>(
    KY.delete(`tier-group/${params._id}`).json()
  );
};

const GetTierGroup = async (params: { _id: string }) => {
  return await TryCatch<TTierGroup, TErrorResponse>(
    KY.get(`tier-group/${params._id}`).json()
  );
};

const GetTierGroups = async () => {
  return await TryCatch<TTierGroup[], TErrorResponse>(
    KY.get("tier-group").json()
  );
};

const CreatePlan = async (
  params: Pick<
    TPlan,
    | "Name"
    | "Subtitle"
    | "Description"
    | "Currency"
    | "Features"
    | "ProjectIDs"
    | "Tier"
    | "PricingStructures"
  >
) => {
  return await TryCatch<TAckResponse<TPlan>, TErrorResponse>(
    KY.post("plan", { json: params }).json()
  );
};

const UpdatePlan = async (
  params: Pick<
    TPlan,
    | "Name"
    | "Subtitle"
    | "Description"
    | "Currency"
    | "Features"
    | "ProjectIDs"
    | "Tier"
    | "PlanID"
    | "PricingStructures"
  >
) => {
  return await TryCatch<TAckResponse<TPlan>, TErrorResponse>(
    KY.patch(`plan/${params.PlanID}`, { json: params }).json()
  );
};

const DeletePlan = async (params: { PlanID: string }) => {
  return await TryCatch<TAckResponse<TPlan>, TErrorResponse>(
    KY.delete(`plan/${params.PlanID}`).json()
  );
};

const GetPlan = async (params: { PlanID: string }) => {
  return await TryCatch<TPlan, TErrorResponse>(
    KY.get(`plan/${params.PlanID}`).json()
  );
};

const GetPlans = async () => {
  return await TryCatch<TPlan[], TErrorResponse>(KY.get("plan").json());
};

export const PLAN_TIER = {
  CreateTierGroup,
  UpdateTierGroup,
  DeleteTierGroup,
  GetTierGroup,
  GetTierGroups,
  CreatePlan,
  UpdatePlan,
  DeletePlan,
  GetPlan,
  GetPlans,
};
