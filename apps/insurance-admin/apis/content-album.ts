import { TryCatch } from "@/lib/try-catch";
import { KY, TErrorResponse } from "./_.index";

export interface ContentGroup {
  ContentGroupID?: string;
  Title: string;
  Description?: string;
  SortOrder?: number;
}

export interface Content {
  ContentID: string;
  Meta: {
    Title: string;
    Description?: string;
    Transcript?: string; // Plain Text
    Subtitles?: {
      Language: string;
      URL: string;
    }[]; // SRT
    Attachments?: string[]; // URL
  };
  S3: {
    Location: string;
    Key: string;
    Type: string;
    FileSize: number;
    Expiration: string;
    AccessUrl: string;
    FileName?: string;
  };
  CDN: {
    URL: string;
  };
  HLS?: {
    Resolutions: {
      Resolution: string;
      URL: string;
    }[];
  };
  Duration: number;
  SortOrder?: number;
  ContentGroupID?: string;
}

export interface TContentAlbum {
  ContentAlbumID: string;
  Name: string;
  Description?: string;
  CoverImage: string;

  TenantID: string;
  ProjectID: string;

  ContentGroups: ContentGroup[];
  Contents: Content[];

  ContentCount: number;
  ContentDuration: number;

  createdAt: string;
  updatedAt: string;
}

const CreateContentAlbum = async (
  Params: Pick<
    TContentAlbum,
    "Name" | "Description" | "CoverImage" | "ProjectID"
  >
) => {
  return await TryCatch<TContentAlbum, TErrorResponse>(
    KY.post(`content-album`, { json: Params }).json()
  );
};

const UpdateContentAlbum = async (
  Params: Pick<TContentAlbum, "ContentAlbumID"> & Partial<TContentAlbum>
) => {
  return await TryCatch<TContentAlbum, TErrorResponse>(
    KY.patch(`content-album/${Params.ContentAlbumID}`, { json: Params }).json()
  );
};

const UploadContent = async (Params: {
  ContentAlbumID: string;
  Title: string;
  Description?: string;
  SortOrder?: number;
  Transcript?: string;
  ContentGroupID?: string;
  Content: File; // file upload
}) => {
  // upload as form data
  const formData = new FormData();
  formData.append("ContentAlbumID", Params.ContentAlbumID);
  formData.append("Title", Params.Title);
  formData.append("Description", Params.Description || "");
  formData.append("SortOrder", String(Params.SortOrder || 1));
  formData.append("Transcript", Params.Transcript || "");
  formData.append("ContentGroupID", Params.ContentGroupID || "");
  formData.append("Content", Params.Content);
  return await TryCatch<TContentAlbum, TErrorResponse>(
    KY.put(`content-album/${Params.ContentAlbumID}/content`, {
      body: formData,
    }).json()
  );
};

const UpdateContentData = async (Params: {
  ContentAlbumID: string;
  ContentID: string;
  Meta: {
    Title?: string;
    Description?: string;
    Transcript?: string;
  };
  SortOrder?: number;
  Scopes: Record<string, unknown>;
  ContentGroupID?: string;
}) => {
  return await TryCatch<TContentAlbum, TErrorResponse>(
    KY.patch(
      `content-album/${Params.ContentAlbumID}/content/${Params.ContentID}`,
      { json: Params }
    ).json()
  );
};

const UpdateContentMedia = async (Params: {
  ContentAlbumID: string;
  ContentID: string;
  Content: File;
}) => {
  const formData = new FormData();
  formData.append("Content", Params.Content);
  return await TryCatch<TContentAlbum, TErrorResponse>(
    KY.put(
      `content-album/${Params.ContentAlbumID}/content/${Params.ContentID}`,
      { body: formData }
    ).json()
  );
};

const DeleteContent = async (Params: {
  ContentAlbumID: string;
  ContentID: string;
}) => {
  return await TryCatch<TContentAlbum, TErrorResponse>(
    KY.delete(
      `content-album/${Params.ContentAlbumID}/content/${Params.ContentID}`
    ).json()
  );
};

const DeleteContentAlbum = async (Params: { ContentAlbumID: string }) => {
  return await TryCatch<TContentAlbum, TErrorResponse>(
    KY.delete(`content-album/${Params.ContentAlbumID}`).json()
  );
};

const GetContentAlbum = async (Params: { ContentAlbumID: string }) => {
  return await TryCatch<TContentAlbum, TErrorResponse>(
    KY.get(`content-album/${Params.ContentAlbumID}`).json()
  );
};

const GetContentAlbums = async (Params: { ProjectID: string }) => {
  return await TryCatch<TContentAlbum[], TErrorResponse>(
    KY.get(`content-album?ProjectID=${Params.ProjectID}`).json()
  );
};

const GenerateContentAlbum = async (Params: {
  TenantID: string;
  ProjectID: string;
  SourceID: string;
  Name?: string;
  Description?: string;
  CoverImage?: string;
}) => {
  return await TryCatch<
    {
      message: string;
      status: string;
      sourceName: string;
    },
    TErrorResponse
  >(KY.post(`content-album/generate`, { json: Params }).json());
};

export const CONTENT_ALBUM = {
  CreateContentAlbum,
  UpdateContentAlbum,
  DeleteContentAlbum,
  GetContentAlbum,
  GetContentAlbums,
  UploadContent,
  UpdateContentData,
  UpdateContentMedia,
  DeleteContent,
  GenerateContentAlbum,
};
