"use client";

import { useState, ReactNode } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { Input } from "@workspace/ui/components/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Separator } from "@workspace/ui/components/separator";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";

// Mock data for chat threads
const THREADS = [
  {
    id: 1,
    name: "General Discussion",
    lastMessage: "Hello everyone!",
    unread: 3,
  },
  {
    id: 2,
    name: "Project Alpha",
    lastMessage: "Let's discuss the timeline",
    unread: 0,
  },
  { id: 3, name: "Support Group", lastMessage: "How can I help?", unread: 5 },
  { id: 4, name: "Marketing Team", lastMessage: "Campaign updates", unread: 0 },
  {
    id: 5,
    name: "Research & Development",
    lastMessage: "New findings",
    unread: 1,
  },
  {
    id: 6,
    name: "Social Events",
    lastMessage: "Party next Friday!",
    unread: 0,
  },
  {
    id: 7,
    name: "Technical Questions",
    lastMessage: "How to implement...",
    unread: 2,
  },
  {
    id: 8,
    name: "Random Stuff",
    lastMessage: "Check out this meme",
    unread: 0,
  },
];

interface ChatLayoutProps {
  children: ReactNode;
}

export default function ChatLayout({ children }: ChatLayoutProps) {
  const router = useRouter();
  const { user, signOut } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  const filteredThreads = THREADS.filter((thread) =>
    thread.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      {/* Sidebar */}
      <div
        className={`border-r bg-muted/40 transition-all duration-200 ${
          sidebarOpen ? "w-80" : "w-0"
        }`}
      >
        {sidebarOpen && (
          <div className="flex h-full flex-col">
            {/* Sidebar Header */}
            <div className="p-4 border-b">
              <h2 className="text-xl font-semibold">Chats</h2>
            </div>

            {/* Search */}
            <div className="p-4">
              <Input
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>

            {/* Thread List */}
            <ScrollArea className="flex-1">
              <div className="p-2 space-y-1">
                {filteredThreads.map((thread) => (
                  <Button
                    key={thread.id}
                    variant="ghost"
                    className="w-full justify-start h-auto p-3 relative cursor-pointer"
                    onClick={() => router.push(`/chat/${thread.id}`)}
                  >
                    <div className="flex flex-col items-start text-left">
                      <div className="flex items-center w-full">
                        <span className="font-medium">{thread.name}</span>
                        {thread.unread > 0 && (
                          <span className="ml-auto bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs">
                            {thread.unread}
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground mt-1 line-clamp-1">
                        {thread.lastMessage}
                      </span>
                    </div>
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Top Bar */}
        <header className="h-16 border-b flex items-center justify-between px-4 bg-background">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="md:hidden"
            >
              <MenuIcon className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-bold">Zuma LM Chat</h1>
          </div>

          <div className="flex items-center space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <Avatar>
                    <AvatarImage src={user?.ProfilePicture || ""} />
                    <AvatarFallback>
                      {user?.Email?.[0]?.toUpperCase() || "U"}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Profile</DropdownMenuItem>
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <Separator className="my-1" />
                <DropdownMenuItem onClick={signOut}>Sign Out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        {/* Chat Content */}
        <main className="flex-1 overflow-auto p-4 bg-background/95">
          {children}
        </main>
      </div>
    </div>
  );
}

// Simple Menu Icon component
function MenuIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <line x1="3" y1="12" x2="21" y2="12" />
      <line x1="3" y1="6" x2="21" y2="6" />
      <line x1="3" y1="18" x2="21" y2="18" />
    </svg>
  );
}
