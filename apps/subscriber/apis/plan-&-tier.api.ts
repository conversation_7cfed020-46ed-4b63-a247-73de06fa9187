import { TryCatch } from "@/lib/try-catch";
import { KY, TErrorResponse } from "./_.index";

export interface TFlag {
  FlagID: "CHAT" | "STORY" | "POP-QUIZ";
  Properties: Record<string, string>;
}

export type CapType = "Token" | "Message" | "Interaction";

export type CreditUsage = {
  CapType: CapType;
  CapQuota: number;
};
export type PricingStructure = {
  Type: string;
  Price: number;
  Duration?: number;
  CreditUsage: CreditUsage;
};

export interface TPlan {
  _id: string;
  Name: string;
  Subtitle: string;
  Description: string;
  Currency: string;
  Features: { Title: string; HelpText?: string }[];
  ProjectIDs: string[];
  Tier: string;
  TenantID: string;
  PlanID: string;
  createdAt: string;
  updatedAt: string;
  Flags: TFlag[];
  PricingStructures: PricingStructure[];
  ListAsPublic: boolean;
  MarkAsTrailPlan: boolean;
}

export interface TTierGroup {
  _id: string;
  Name: string;
  Description: string;
  PlanIDs: string[];
  TenantID: string;
  InviteCode: string;
  createdAt: string;
  updatedAt: string;
  Plans: TPlan[];
}

const GetTierGroup = async (params: { InviteCode: string }) => {
  return await TryCatch<TTierGroup, TErrorResponse>(
    KY.get(`tier-group/invite/${params.InviteCode}`).json()
  );
};

const GetTierGroupByID = async (params: {
  TierGroupID: string;
  SubDomain: string;
}) => {
  return await TryCatch<TTierGroup, TErrorResponse>(
    KY.get(
      `tier-group/list/public/${params.TierGroupID}?SubDomain=${params.SubDomain}`
    ).json()
  );
};

const GetTierGroups = async (Params: {
  PlanIDs?: string[];
  SubDomain?: string;
}) => {
  return await TryCatch<TTierGroup[], TErrorResponse>(
    KY.get(
      `tier-group/list/public?${Params.PlanIDs?.length ? `PlanIDs=${Params.PlanIDs.join(",")}` : ""}${Params.SubDomain ? `&SubDomain=${Params.SubDomain}` : ""}`
    ).json()
  );
};

const ListTierGroups = async (Params: {
  PlanIDs?: string[];
  SubDomain?: string;
}) => {
  return await TryCatch<TTierGroup[], TErrorResponse>(
    KY.get(
      `tier-group?${Params.PlanIDs?.length ? `PlanIDs=${Params.PlanIDs.join(",")}` : ""}${Params.SubDomain ? `&SubDomain=${Params.SubDomain}` : ""}`
    ).json()
  );
};

export const PLAN_TIER_API = {
  GetTierGroup,
  GetTierGroupByID,
  GetTierGroups,
  ListTierGroups,
};
