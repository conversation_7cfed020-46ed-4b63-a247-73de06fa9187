"use client";

import { API } from "@/apis/api";
import { userAuthStore } from "@/store/useAuthStore";
import {
  SidebarInset,
  SidebarProvider,
} from "@workspace/ui/components/sidebar";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { AppSidebar } from "../app-side-bar";
import { useTenantStore } from "@/store/useTenantStore";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { user, setUser } = userAuthStore();
  const { fetchTenant, getTenant } = useTenantStore();

  const validateSession = async () => {
    const { data } = await API.AUTH.GetCurrentUser();
    if (data) {
      setUser(data);
      return;
    }
    setUser(null);
    router.push("/sign-in");
  };

  useEffect(() => {
    validateSession();
  }, []);

  useEffect(() => {
    if (!localStorage.getItem("tenant")) {
      fetchTenant();
      return;
    }
    getTenant();
  }, [user]);

  return (
    <div>
      <SidebarProvider>
        {user?.UserID ? <AppSidebar /> : <div></div>}
        <SidebarInset>
          <div className="flex-1">{children}</div>
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}
