"use client";

import { API } from "@/apis/api";
import { CapType, PricingStructure, TPlan } from "@/types/plan-subscription";
import {
  AlertDialogCancel,
  AlertDialogTrigger,
} from "@radix-ui/react-alert-dialog";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@workspace/ui/components/alert-dialog";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>ooter,
  SheetHeader,
  SheetTitle,
} from "@workspace/ui/components/sheet";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Textarea } from "@workspace/ui/components/textarea";
import { Copy, Pencil, Plus, Search, Squirrel, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Switch } from "@workspace/ui/components/switch";
import { TProject } from "@/types/project";
import { TTenant } from "@/apis/tenant.api";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";

const host = "https://app.zuma.co.in";

export default function PlanTable() {
  const [plans, setPlans] = useState<TPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [projects, setProjects] = useState<TProject[]>([]);
  const [tenant, setTenant] = useState<TTenant | null>(null);

  useEffect(() => {
    fetchPlans();
    fetchProjects();
    fetchTenant();
  }, []);

  const fetchPlans = async () => {
    setLoading(true);
    try {
      const { data, errors } = await API.SUBSCRIPTION.ListPlans();
      if (data) {
        setPlans(data);
      }
      if (errors) {
        toast.error(errors[0]?.message || "Failed to fetch plans");
      }
    } catch (error) {
      toast.error("An error occurred while fetching plans");
    } finally {
      setLoading(false);
    }
  };

  const fetchProjects = async () => {
    setLoading(true);
    try {
      const { data, errors } = await API.PROJECT.ListProjects();
      if (data) {
        setProjects(data);
      }
      if (errors) {
        toast.error(errors[0]?.message || "Failed to fetch projects");
      }
    } catch (error) {
      toast.error("An error occurred while fetching projects");
    } finally {
      setLoading(false);
    }
  };

  const fetchTenant = async () => {
    setLoading(true);
    try {
      const { data, errors } = await API.TENANT.GetTenant();
      if (data) {
        setTenant(data);
      }
      if (errors) {
        toast.error(errors[0]?.message || "Failed to fetch tenant");
      }
    } catch (error) {
      toast.error("An error occurred while fetching tenant");
    } finally {
      setLoading(false);
    }
  };

  const filteredPlans = plans.filter((plan) => {
    const matchesSearch =
      searchQuery === "" ||
      plan.Name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plan.Description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plan.Tier.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });

  const [isPlanSheetOpen, setIsPlanSheetOpen] = useState(false);

  const ManagePlanSheet = ({
    selectedPlan,
  }: {
    selectedPlan?: TPlan | null;
  }) => {
    const [plan, setPlan] = useState<
      Omit<
        TPlan,
        | "_id"
        | "PlanID"
        | "createdAt"
        | "updatedAt"
        | "TenantID"
        | "Features"
        | "PricingStructures"
      > & {
        Features: { Title: string; HelpText?: string; ID?: string }[];
        PricingStructures: (PricingStructure & { ID: string })[];
      }
    >({
      Name: selectedPlan?.Name || "",
      Subtitle: selectedPlan?.Subtitle || "",
      Description: selectedPlan?.Description || "",
      Currency: "INR",
      Features: selectedPlan?.Features || [],
      ProjectIDs: selectedPlan?.ProjectIDs || [],
      Tier: selectedPlan?.Tier || "",
      Flags: selectedPlan?.Flags || [],
      PricingStructures:
        (selectedPlan?.PricingStructures as (PricingStructure & {
          ID: string;
        })[]) || [],
      ListAsPublic: selectedPlan?.ListAsPublic || false,
      MarkAsTrailPlan: selectedPlan?.MarkAsTrailPlan || false,
    });

    const [type, setType] = useState<string>("");
    const [isFeatureSheetOpen, setIsFeatureSheetOpen] = useState(false);
    const [selectedFeature, setSelectedFeature] = useState<
      TPlan["Features"][0] & { ID?: string }
    >({
      Title: "",
      HelpText: "",
      ID: "",
    });

    const ManageFeatureModal = ({
      props: features,
    }: {
      props?: (TPlan["Features"][0] & { ID?: string }) | null;
    }) => {
      const [feature, setFeature] = useState<
        TPlan["Features"][0] & { ID?: string }
      >({
        Title: features?.Title || "",
        HelpText: features?.HelpText || "",
        ID: features?.ID || new Date().toISOString(),
      });

      return (
        <AlertDialog
          open={isFeatureSheetOpen}
          onOpenChange={setIsFeatureSheetOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Add Feature</AlertDialogTitle>
              <AlertDialogDescription></AlertDialogDescription>
            </AlertDialogHeader>
            <div className="space-y-4 px-4">
              <Input
                placeholder="Title"
                value={feature.Title}
                onChange={(e) =>
                  setFeature({ ...feature, Title: e.target.value })
                }
              />
              <Textarea
                placeholder="Help Text"
                value={feature.HelpText}
                onChange={(e) =>
                  setFeature({ ...feature, HelpText: e.target.value })
                }
              />
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <Button
                onClick={() => {
                  const findFeatureIndex = plan.Features.findIndex(
                    (f) => f.ID === feature.ID
                  );
                  if (findFeatureIndex !== -1) {
                    plan.Features[findFeatureIndex] = feature;
                  } else {
                    plan.Features.push(feature);
                  }
                  setPlan({ ...plan });
                  setIsFeatureSheetOpen(false);
                }}
                className="disabled:opacity-50"
              >
                Continue
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      );
    };

    const [isProjectSheetOpen, setIsProjectSheetOpen] = useState(false);

    const ManageProjectModal = () => {
      const [selectedProject, setSelectedProject] = useState<string[] | null>(
        null
      );
      return (
        <AlertDialog
          open={isProjectSheetOpen}
          onOpenChange={setIsProjectSheetOpen}
        >
          <AlertDialogTrigger asChild>
            <Button variant="outline" size="sm" className="text-xs">
              <Plus className="h-4 w-4" />
              Add Projects
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Add Projects</AlertDialogTitle>
              <AlertDialogDescription></AlertDialogDescription>
            </AlertDialogHeader>
            <div className="space-y-4 px-2 max-h-[400px] overflow-y-scroll">
              {projects
                .filter(
                  (project) => !plan.ProjectIDs.includes(project.ProjectID)
                )
                .map((project) => (
                  <div className="flex items-center gap-2 w-full justify-between">
                    <p>{project.Name}</p>
                    <Button
                      variant={
                        selectedProject?.includes(project.ProjectID)
                          ? "destructive"
                          : "outline"
                      }
                      size="sm"
                      onClick={() => {
                        selectedProject?.includes(project.ProjectID)
                          ? setSelectedProject(
                              selectedProject.filter(
                                (id) => id !== project.ProjectID
                              )
                            )
                          : setSelectedProject([
                              ...new Set([
                                ...(selectedProject || []),
                                project.ProjectID,
                              ]),
                            ]);
                      }}
                    >
                      {selectedProject?.includes(project.ProjectID)
                        ? "Remove"
                        : "Add"}
                    </Button>
                  </div>
                ))}
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel
                onClick={() => {
                  setIsProjectSheetOpen(false);
                }}
              >
                Cancel
              </AlertDialogCancel>
              <Button
                className="disabled:opacity-50"
                onClick={() => {
                  if (selectedProject) {
                    setPlan({
                      ...plan,
                      ProjectIDs: [
                        ...new Set([...plan.ProjectIDs, ...selectedProject]),
                      ],
                    });
                    setIsProjectSheetOpen(false);
                  }
                }}
              >
                Continue
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      );
    };

    const validatePricingStructures = (
      pricingStructures: TPlan["PricingStructures"]
    ) => {
      // pricing type should be unique
      const uniquePricingTypes = new Set(
        pricingStructures.map((ps) => ps.Type)
      );
      if (uniquePricingTypes.size !== pricingStructures.length) {
        toast.error("Pricing type should be unique");
        return false;
      }
      return true;
    };

    const handleCreatePlan = async () => {
      if (!validatePricingStructures(plan.PricingStructures)) return;
      const { data, errors } = await API.PLAN_TIER.CreatePlan({
        ...plan,
        PricingStructures: plan.PricingStructures.map((ps) => ({
          ...ps,
          Price: Number(ps.Price) * 100,
        })),
      });
      if (errors) {
        errors.forEach((error) => toast.error(error.message));
      }
      toast.success(data?.message);
      fetchPlans();
      setIsPlanSheetOpen(false);
    };

    const handleUpdatePlan = async () => {
      if (!selectedPlan) return;
      if (!validatePricingStructures(plan.PricingStructures)) return;
      const { data, errors } = await API.PLAN_TIER.UpdatePlan({
        ...plan,
        PricingStructures: plan.PricingStructures.map((ps) => ({
          ...ps,
          Price: Number(ps.Price) * 100,
        })),
        PlanID: selectedPlan.PlanID,
      });
      if (errors) {
        errors.forEach((error) => toast.error(error.message));
      }
      toast.success(data?.message);
      fetchPlans();
      setIsPlanSheetOpen(false);
    };

    const SystemTypes = ["Daily", "Weekly", "Monthly", "Yearly"];

    return (
      <>
        <Sheet
          open={isPlanSheetOpen}
          onOpenChange={() => {
            setIsPlanSheetOpen(false);
            setSelectedPlan(null);
          }}
        >
          <SheetContent className="min-w-xl">
            <SheetHeader>
              <SheetTitle>
                {selectedPlan ? "Update Plan" : "Create Plan"}
              </SheetTitle>
            </SheetHeader>
            <div className="space-y-3 px-4 max-h-screen overflow-y-auto">
              <Tabs defaultValue="plan">
                <TabsList>
                  <TabsTrigger value="plan">Plan</TabsTrigger>
                  <TabsTrigger value="pricing">Pricing & Credits</TabsTrigger>
                  <TabsTrigger value="features">Features</TabsTrigger>
                </TabsList>
                <TabsContent value="plan" className="space-y-2">
                  <Input
                    placeholder="Name"
                    value={plan.Name}
                    onChange={(e) => setPlan({ ...plan, Name: e.target.value })}
                  />
                  <Input
                    placeholder="Subtitle"
                    value={plan.Subtitle}
                    onChange={(e) =>
                      setPlan({ ...plan, Subtitle: e.target.value })
                    }
                  />
                  <Textarea
                    placeholder="Description"
                    value={plan.Description || ""}
                    onChange={(e) =>
                      setPlan({ ...plan, Description: e.target.value })
                    }
                  />
                  <div className="flex justify-between items-center gap-2 mt-8">
                    <p className="text-sm font-semibold text-muted-foreground">
                      Projects
                    </p>
                    <ManageProjectModal />
                  </div>
                  <div className="w-full space-y-2">
                    {projects
                      .filter((project) =>
                        plan.ProjectIDs.includes(project.ProjectID)
                      )
                      .map((project) => (
                        <div
                          key={project.ProjectID}
                          className="bg-muted flex items-center justify-between p-2 rounded-md"
                        >
                          <p className="text-sm font-semibold">
                            {project.Name}
                          </p>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setPlan({
                                ...plan,
                                ProjectIDs: plan.ProjectIDs.filter(
                                  (id) => id !== project.ProjectID
                                ),
                              });
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                  </div>
                </TabsContent>
                <TabsContent value="pricing" className="space-y-2">
                  <Input
                    placeholder="Tier"
                    value={plan.Tier}
                    onChange={(e) =>
                      setPlan({
                        ...plan,
                        Tier: e.target.value?.toUpperCase(),
                      })
                    }
                  />
                  <div className="flex items-center justify-between gap-2 bg-muted p-3 rounded-md">
                    <div className="flex flex-col items-start gap-1 max-w-sm">
                      <Label>Mark as Trail Plan</Label>
                      <p className="text-xs text-muted-foreground">
                        When marked as trail plan, User with subscription will
                        have to upgrade to this plan to continue using the
                        application.
                      </p>
                    </div>
                    <Switch
                      checked={plan.MarkAsTrailPlan}
                      onCheckedChange={(checked) =>
                        setPlan({
                          ...plan,
                          MarkAsTrailPlan: checked,
                        })
                      }
                    />
                  </div>
                  <div className="flex justify-between items-center gap-2 mt-4">
                    <Select
                      value={type}
                      onValueChange={(value) => setType(value)}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select Interval" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {SystemTypes.filter((interval) =>
                            plan.PricingStructures.every(
                              (ps) => ps.Type !== interval
                            )
                          ).map((interval) => (
                            <SelectItem key={interval} value={interval}>
                              {interval}
                            </SelectItem>
                          ))}
                          <SelectItem value="Custom">Custom</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={() => {
                        setPlan({
                          ...plan,
                          PricingStructures: [
                            ...plan.PricingStructures,
                            {
                              ID: new Date().getTime().toString(),
                              Type: type,
                              Price: 0,
                              Duration: undefined,
                              CreditUsage: {
                                CapType: "Token",
                                CapQuota: 0,
                              },
                            },
                          ],
                        });
                        setType("");
                      }}
                      variant="outline"
                      size="sm"
                      className="text-xs self-end"
                    >
                      <Plus className="h-4 w-4" />
                      Add Pricing Structure
                    </Button>
                  </div>
                  <h2 className="text-sm font-semibold text-muted-foreground mt-6">
                    Pricing Structure
                  </h2>
                  {plan.PricingStructures.map((ps) => (
                    <div key={ps.ID} className="mb-6 p-4 border rounded-lg">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        {/* Pricing Type Field */}
                        <div className="space-y-2">
                          <Label
                            htmlFor={`type-${ps.ID}`}
                            className="font-medium"
                          >
                            Pricing Type
                          </Label>
                          <Input
                            id={`type-${ps.ID}`}
                            placeholder="Type"
                            value={ps.Type}
                            // disabled={SystemTypes.includes(ps.Type)}
                            className="w-full"
                            onChange={(e) =>
                              setPlan({
                                ...plan,
                                PricingStructures: plan.PricingStructures.map(
                                  (_ps) =>
                                    _ps.ID === ps.ID
                                      ? { ..._ps, Type: e.target.value }
                                      : _ps
                                ),
                              })
                            }
                          />
                          <p className="text-xs text-muted-foreground">
                            {SystemTypes.includes(ps.Type)
                              ? "System pricing type cannot be modified"
                              : "Custom pricing type name should be unique"}
                          </p>
                        </div>

                        {/* Price Field */}
                        <div className="space-y-2">
                          <Label
                            htmlFor={`price-${ps.ID}`}
                            className="font-medium"
                          >
                            Price
                          </Label>
                          <Input
                            id={`price-${ps.ID}`}
                            placeholder="Enter price"
                            value={ps.Price.toString()}
                            className="w-full"
                            onChange={(e) =>
                              setPlan({
                                ...plan,
                                PricingStructures: plan.PricingStructures.map(
                                  (_ps) =>
                                    _ps.ID === ps.ID
                                      ? {
                                          ..._ps,
                                          Price: Number(e.target.value),
                                        }
                                      : _ps
                                ),
                              })
                            }
                          />
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Price in {plan.Currency || "default currency"}
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        {/* Duration Field */}
                        <div className="space-y-2">
                          <Label
                            htmlFor={`duration-${ps.ID}`}
                            className="font-medium"
                          >
                            Duration (Days)
                          </Label>
                          <Input
                            id={`duration-${ps.ID}`}
                            placeholder="Duration in days"
                            value={
                              !SystemTypes.includes(ps.Type)
                                ? ps.Duration?.toString()
                                : {
                                    Yearly: "365",
                                    Monthly: "30",
                                    Weekly: "7",
                                    Daily: "1",
                                  }[ps.Type]
                            }
                            disabled={SystemTypes.includes(ps.Type)}
                            className="w-full"
                            onChange={(e) =>
                              setPlan({
                                ...plan,
                                PricingStructures: plan.PricingStructures.map(
                                  (_ps) =>
                                    _ps.ID === ps.ID
                                      ? {
                                          ..._ps,
                                          Duration: Number(e.target.value),
                                        }
                                      : _ps
                                ),
                              })
                            }
                          />
                          <p className="text-xs text-muted-foreground">
                            {SystemTypes.includes(ps.Type)
                              ? `Fixed duration for ${ps.Type} pricing`
                              : "Custom duration in days"}
                          </p>
                        </div>

                        {/* Cap Type Field */}
                        <div className="space-y-2">
                          <Label
                            htmlFor={`captype-${ps.ID}`}
                            className="font-medium"
                          >
                            Cap Type
                          </Label>
                          <Select
                            value={ps.CreditUsage.CapType}
                            onValueChange={(value) =>
                              setPlan({
                                ...plan,
                                PricingStructures: plan.PricingStructures.map(
                                  (_ps) =>
                                    _ps.ID === ps.ID
                                      ? {
                                          ..._ps,
                                          CreditUsage: {
                                            ..._ps.CreditUsage,
                                            CapType: value as CapType,
                                          },
                                        }
                                      : _ps
                                ),
                              })
                            }
                          >
                            <SelectTrigger
                              id={`captype-${ps.ID}`}
                              className="w-full"
                            >
                              <SelectValue placeholder="Select Cap Type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                <SelectItem value="Token">Token</SelectItem>
                                <SelectItem value="Interaction">
                                  Interaction
                                </SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                          <p className="text-xs text-muted-foreground">
                            {ps.CreditUsage.CapType === "Token"
                              ? "Input + Output tokens"
                              : "1 interaction = 1 message"}
                          </p>
                        </div>
                      </div>

                      {/* Cap Quota Field */}
                      <div className="mb-4">
                        <Label
                          htmlFor={`capquota-${ps.ID}`}
                          className="font-medium"
                        >
                          Cap Quota
                        </Label>
                        <Input
                          id={`capquota-${ps.ID}`}
                          placeholder="Enter cap quota"
                          value={ps.CreditUsage.CapQuota.toString()}
                          className="w-full mt-2"
                          onChange={(e) =>
                            setPlan({
                              ...plan,
                              PricingStructures: plan.PricingStructures.map(
                                (_ps) =>
                                  _ps.ID === ps.ID
                                    ? {
                                        ..._ps,
                                        CreditUsage: {
                                          ..._ps.CreditUsage,
                                          CapQuota: Number(e.target.value),
                                        },
                                      }
                                    : _ps
                              ),
                            })
                          }
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Maximum {ps.CreditUsage.CapType} usage allowed (enter
                          0 for unlimited)
                        </p>
                      </div>

                      {/* Delete Button */}
                      <div className="flex justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          onClick={() =>
                            setPlan({
                              ...plan,
                              PricingStructures: plan.PricingStructures.filter(
                                (_ps) => _ps.ID !== ps.ID
                              ),
                            })
                          }
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                  {!plan.PricingStructures.length && (
                    <div className="flex items-center justify-center gap-2 flex-col mt-8">
                      <Squirrel className="h-12 w-12" strokeWidth={1} />
                      <p className="text-muted-foreground">
                        No Pricing Structure
                      </p>
                    </div>
                  )}
                </TabsContent>
                <TabsContent value="features">
                  <div className="flex justify-between items-center gap-2">
                    <p className="text-sm font-semibold text-muted-foreground">
                      Features
                    </p>
                    <Button
                      onClick={() => setIsFeatureSheetOpen(true)}
                      variant={"outline"}
                      size="sm"
                      className="text-xs   self-end"
                    >
                      <Plus className="h-4 w-4" />
                      Add Feature
                    </Button>
                  </div>
                  <div className="flex flex-col gap-2 w-full">
                    {plan.Features.map((feature) => (
                      <div className="bg-muted p-2 rounded-md flex justify-between items-center">
                        <div key={feature.ID} className="flex flex-col gap-1">
                          <h2 className="text-sm font-semibold">
                            {feature.Title}
                          </h2>
                          <p className="text-xs text-muted-foreground">
                            {feature.HelpText}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant={"outline"}
                            size="sm"
                            className="text-xs"
                            onClick={() => {
                              setSelectedFeature(feature);
                              setIsFeatureSheetOpen(true);
                            }}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant={"outline"}
                            size="sm"
                            className="text-xs self-end"
                            onClick={() => {
                              setPlan({
                                ...plan,
                                Features: plan.Features.filter(
                                  (f) => f.ID !== feature.ID
                                ),
                              });
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-6"></div>
                  <div className="w-full flex flex-col gap-2">
                    <p className="text-sm font-semibold text-muted-foreground">
                      Include Features
                    </p>
                    <div className="flex items-center justify-between">
                      <Label
                        htmlFor="chats"
                        className="text-sm text-muted-foreground"
                      >
                        Chats
                      </Label>
                      <Switch
                        id="chats"
                        checked={plan.Flags.some((f) => f.FlagID === "CHAT")}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            plan.Flags.push({ FlagID: "CHAT", Properties: {} });
                          } else {
                            plan.Flags = plan.Flags.filter(
                              (f) => f.FlagID !== "CHAT"
                            );
                          }
                          setPlan({ ...plan });
                        }}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label
                        htmlFor="contentAlbum"
                        className="text-sm text-muted-foreground"
                      >
                        Content Album
                      </Label>
                      <Switch
                        id="contentAlbum"
                        checked={plan.Flags.some((f) => f.FlagID === "STORY")}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            plan.Flags.push({
                              FlagID: "STORY",
                              Properties: {},
                            });
                          } else {
                            plan.Flags = plan.Flags.filter(
                              (f) => f.FlagID !== "STORY"
                            );
                          }
                          setPlan({ ...plan });
                        }}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label
                        htmlFor="popQuiz"
                        className="text-sm text-muted-foreground"
                      >
                        Pop Quiz
                      </Label>
                      <Switch
                        id="popQuiz"
                        checked={plan.Flags.some(
                          (f) => f.FlagID === "POP-QUIZ"
                        )}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            plan.Flags.push({
                              FlagID: "POP-QUIZ",
                              Properties: {},
                            });
                          } else {
                            plan.Flags = plan.Flags.filter(
                              (f) => f.FlagID !== "POP-QUIZ"
                            );
                          }
                          setPlan({ ...plan });
                        }}
                      />
                    </div>
                  </div>
                  <div className="mt-6"></div>
                  <div className="bg-muted p-2 rounded-md">
                    <div className="flex items-center justify-between">
                      <p> List As Public</p>
                      <Switch
                        id="listAsPublic"
                        checked={plan.ListAsPublic}
                        onCheckedChange={(checked) => {
                          setPlan({ ...plan, ListAsPublic: checked });
                        }}
                      />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            <ManageFeatureModal props={selectedFeature || null} />

            <SheetFooter className="border-t">
              <div className="flex w-full justify-end gap-2">
                <Button
                  onClick={() => {
                    setIsPlanSheetOpen(false);
                  }}
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    if (!selectedPlan) {
                      handleCreatePlan();
                    } else {
                      handleUpdatePlan();
                    }
                  }}
                  className="disabled:opacity-50 flex-1"
                >
                  {selectedPlan ? "Update Plan" : "Create Plan"}
                </Button>
              </div>
            </SheetFooter>
          </SheetContent>
        </Sheet>
      </>
    );
  };

  const [selectedPlan, setSelectedPlan] = useState<TPlan | null>(null);

  return (
    <div className="space-y-6">
      {/* Search */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative w-full sm:w-72">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search plans..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button onClick={() => setIsPlanSheetOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Plan
        </Button>
        <ManagePlanSheet selectedPlan={selectedPlan} />
      </div>

      {/* Plans Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Tier</TableHead>
              <TableHead className="text-center">Price</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-10">
                  <div className="flex items-center justify-center">
                    <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredPlans.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-10">
                  {searchQuery
                    ? "No plans found matching the search"
                    : "No plans found"}
                </TableCell>
              </TableRow>
            ) : (
              filteredPlans.map((plan) => (
                <TableRow key={plan.PlanID}>
                  <TableCell className="font-medium">{plan.Name}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">{plan.Tier}</Badge>
                  </TableCell>
                  <TableCell>
                    {plan?.PricingStructures?.map((ps) => (
                      <div key={ps.Type} className="grid grid-cols-2 gap-3">
                        <span className="text-right">{ps.Type} - </span>
                        <span>
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: plan.Currency,
                          }).format(ps.Price / 100)}
                        </span>
                      </div>
                    ))}
                  </TableCell>

                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline">
                            <Copy />
                            Invites
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="flex flex-col gap-2 max-w-xs">
                          {plan?.PricingStructures?.map((ps) => (
                            <Button
                              variant="outline"
                              onClick={() => {
                                navigator.clipboard.writeText(
                                  `${host}/sign-up?plan=${plan.PlanID}&variant=${ps.Type}&domain=${tenant?.WorkSpaceDomain}`
                                );
                                toast.success(
                                  `${ps.Type} Variant copied to clipboard for ${plan.Name}`
                                );
                              }}
                            >
                              <Copy />
                              {ps.Type}
                            </Button>
                          ))}
                        </PopoverContent>
                      </Popover>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSelectedPlan({
                            ...plan,
                            PricingStructures:
                              plan?.PricingStructures?.map((ps, index) => ({
                                ...ps,
                                Price: ps.Price / 100,
                                ID: index,
                              })) ?? [],
                          });
                          setIsPlanSheetOpen(true);
                        }}
                      >
                        Edit
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
