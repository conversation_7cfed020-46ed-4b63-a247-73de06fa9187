from datetime import datetime
from typing import List, Optional
from app.db.mongodb import db
from app.models.source import Source
from app.models.enums import DocumentStatus


class SourceService:
    def __init__(self):
        self.collection = db["ind_source"]
        self._create_indexes()

    async def _create_indexes(self):
        """Create indexes for better query performance"""
        await self.collection.create_index("SourceID", unique=True)
        await self.collection.create_index([("TenantID", 1), ("ProjectID", 1)])
        await self.collection.create_index("Status")

    async def create_source(self, source: Source) -> Source:
        """Create a new source document"""
        source_dict = source.model_dump()
        await self.collection.insert_one(source_dict)
        return source

    async def get_source(self, source_id: str) -> Optional[Source]:
        """Get a source document by ID"""
        source_dict = await self.collection.find_one({"SourceID": source_id})
        return Source(**source_dict) if source_dict else None

    async def update_source(
        self, source_id: str, update_data: dict
    ) -> Optional[Source]:
        """Update a source document"""
        update_data["updatedAt"] = datetime.utcnow()
        result = await self.collection.find_one_and_update(
            {"SourceID": source_id}, {"$set": update_data}, return_document=True
        )
        return Source(**result) if result else None

    async def update_status(
        self,
        source_id: str,
        status: DocumentStatus,
        error_message: Optional[str] = None,
    ) -> Optional[Source]:
        """Update source status and optionally error message"""
        update_data = {"Status": status, "updatedAt": datetime.utcnow()}
        if error_message:
            update_data["ErrorMessage"] = error_message
        return await self.update_source(source_id, update_data)

    async def list_sources(self, tenant_id: str, project_id: str) -> List[Source]:
        """List all sources for a tenant and project"""
        cursor = self.collection.find({"TenantID": tenant_id, "ProjectID": project_id})
        sources = await cursor.to_list(length=None)
        return [Source(**source) for source in sources]

    async def delete_source(self, source_id: str) -> bool:
        """Delete a source document"""
        result = await self.collection.delete_one({"SourceID": source_id})
        return result.deleted_count > 0
