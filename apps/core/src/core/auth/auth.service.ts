// src/auth/strategies/google.strategy.ts
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';

export interface IGoogleUser {
  email: string;
  firstName: string;
  lastName: string;
  picture: string;
  accessToken: string;
  refreshToken: string;
  sub: string;
  state: object;
}
@Injectable()
export class AuthService {
  async validateGoogleUser(profile: any, accessToken: string, refreshToken: string): Promise<IGoogleUser> {
    return {
      email: profile.emails[0].value,
      firstName: profile.name.givenName,
      lastName: profile.name.familyName,
      picture: profile.photos[0].value,
      accessToken,
      refreshToken,
      sub: profile.id,
      state: profile.state,
    };
  }

  async validateUser(payload: any): Promise<any> {
    // Here you would fetch user from database using payload.sub (user ID)
    return {
      id: payload.sub,
      email: payload.email,
      name: payload.name,
      picture: payload.picture,
    };
  }
}

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(private authService: AuthService) {
    super({
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: process.env.GOOGLE_CALLBACK_URL,
      scope: ['email', 'profile'],
      passReqToCallback: true, // this is important
    });
  }

  async validate(req: Request, accessToken: string, refreshToken: string, profile: any, done: VerifyCallback) {
    try {
      // Extract state from req.query
      const stateRaw = (req.query as unknown as any).state as string;
      const state = stateRaw ? JSON.parse(decodeURIComponent(stateRaw)) : null;

      // Attach to profile so AuthService can access
      profile.state = state;

      const user = await this.authService.validateGoogleUser(profile, accessToken, refreshToken);
      done(null, user);
    } catch (error) {
      done(error, null);
    }
  }
}
