"use client";

import React, { useCallback } from "react";
import { Play, Pause, Square, Volume2, Loader2 } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { useTTS } from "../../contexts/tts-context";
import { toast } from "sonner";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

interface TTSButtonProps {
  messageId: string;
  content: string;
  output_language?: string;
  citations?: {
    id: string;
    source_title: string;
    pdf_url: string | null;
    page: number;
    line_from: number | null;
    line_to: number | null;
    document_page_number: number;
  }[];
  className?: string;
  showControls?: boolean;
}

export function TTSButton({
  messageId,
  content,
  output_language,
  citations,
  className,
  showControls = false,
}: TTSButtonProps) {
  const { ttsState, playMessage, pauseMessage, resumeMessage, stopMessage } =
    useTTS();

  if (!content || content === "typing-indicator") {
    return null;
  }

  const isCurrentMessage = ttsState.currentMessageId === messageId;
  const canPause = isCurrentMessage && ttsState.isPlaying && !ttsState.isPaused;
  const canResume = isCurrentMessage && ttsState.isPaused;
  const canStop = isCurrentMessage && (ttsState.isPlaying || ttsState.isPaused);
  const isLoading = isCurrentMessage && ttsState.isLoading;

  const handlePlay = useCallback(async () => {
    if (!content.trim()) {
      toast.error("No text available to speak");
      return;
    }

    try {
      await playMessage(messageId, content, citations, output_language);
    } catch (error) {
      console.error("TTS Error:", error);
      toast.error("Failed to play text-to-speech");
    }
  }, [messageId, content, citations, playMessage]);

  const handlePause = useCallback(() => {
    try {
      pauseMessage();
    } catch (error) {
      console.error("TTS Pause Error:", error);
      toast.error("Failed to pause text-to-speech");
    }
  }, [pauseMessage]);

  const handleResume = useCallback(() => {
    try {
      resumeMessage();
    } catch (error) {
      console.error("TTS Resume Error:", error);
      toast.error("Failed to resume text-to-speech");
    }
  }, [resumeMessage]);

  const handleStop = useCallback(() => {
    try {
      stopMessage();
    } catch (error) {
      console.error("TTS Stop Error:", error);
      toast.error("Failed to stop text-to-speech");
    }
  }, [stopMessage]);

  const renderMainButton = () => {
    const shouldBeVisible = isCurrentMessage || showControls;
    const baseClasses = "p-1.5 rounded-full transition-opacity";
    const visibilityClasses = cn("opacity-0", shouldBeVisible && "opacity-100");

    if (isLoading) {
      return (
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <button
              className={cn(
                baseClasses,
                visibilityClasses,
                "text-muted-foreground cursor-not-allowed",
                className
              )}
              disabled
              aria-label="Loading text-to-speech"
            >
              <Loader2 size={16} className="animate-spin" />
            </button>
          </TooltipTrigger>
          <TooltipContent side="top">Loading...</TooltipContent>
        </Tooltip>
      );
    }

    if (canResume) {
      return (
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <button
              onClick={handleResume}
              className={cn(
                baseClasses,
                visibilityClasses,
                "hover:bg-muted/80 text-primary",
                className
              )}
              aria-label="Resume text-to-speech"
            >
              <Play size={16} />
            </button>
          </TooltipTrigger>
          <TooltipContent side="top">Resume</TooltipContent>
        </Tooltip>
      );
    }

    if (canPause) {
      return (
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <button
              onClick={handlePause}
              className={cn(
                baseClasses,
                visibilityClasses,
                "hover:bg-muted/80 text-primary",
                className
              )}
              aria-label="Pause text-to-speech"
            >
              <Pause size={16} />
            </button>
          </TooltipTrigger>
          <TooltipContent side="top">Pause</TooltipContent>
        </Tooltip>
      );
    }

    return (
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <button
            onClick={handlePlay}
            className={cn(
              baseClasses,
              visibilityClasses,
              "hover:bg-muted/80 text-muted-foreground",
              className
            )}
            aria-label="Play text-to-speech"
          >
            <Volume2 size={16} />
          </button>
        </TooltipTrigger>
        <TooltipContent side="top">Listen</TooltipContent>
      </Tooltip>
    );
  };

  const renderStopButton = () => {
    if (!canStop) return null;

    return (
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <button
            onClick={handleStop}
            className={cn(
              "p-1.5 rounded-full hover:bg-muted/80 text-muted-foreground transition-opacity ml-1 opacity-100",
              className
            )}
            aria-label="Stop text-to-speech"
          >
            <Square size={14} />
          </button>
        </TooltipTrigger>
        <TooltipContent side="top">Stop</TooltipContent>
      </Tooltip>
    );
  };

  return (
    <TooltipProvider>
      <div className="flex items-center">
        {renderMainButton()}
        {renderStopButton()}
      </div>
    </TooltipProvider>
  );
}
