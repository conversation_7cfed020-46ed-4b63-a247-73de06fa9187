"use client";

import ChatScreen from "@/components/chat/chat-screen";
import { useParams } from "next/navigation";

// Mock data for thread names
const threadNames: Record<string, string> = {
  "1": "General Discussion",
  "2": "Project Alpha",
  "3": "Support Group",
  "4": "Marketing Team",
  "5": "Research & Development",
  "6": "Social Events",
  "7": "Technical Questions",
  "8": "Random Stuff",
};

export default function ThreadPage() {
  const params = useParams();
  const threadId = parseInt(params.threadId as string, 10);
  const threadName =
    threadNames[params.threadId as string] || `Thread ${params.threadId}`;

  return <ChatScreen threadId={threadId} threadName={threadName} />;
}
