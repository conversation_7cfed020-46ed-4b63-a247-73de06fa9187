import {
  Body,
  Controller,
  Delete,
  Get,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { omit } from 'radash';
import { UserService } from '../user/user.service';
import { ChangePasswordDto, ForgotPasswordDto, LoginDto } from './dto/auth.service.dto';
import { AuthGuard, SkipAuth } from '../../../common/guards/auth.guard';
import { User } from '../../../common/decorators/core.decorators';
import { AppTypeEnum, TUser, TUserType } from '../user/entities/user.entity';
import { UpdateUserDto } from '../user/dto/update-user.dto';
import { GoogleOAuthGuard } from '../../../common/guards/google.guard';
import { IGoogleUser } from './auth.service';

@UseGuards(AuthGuard)
@Controller('auth')
export class AuthController {
  constructor(
    private readonly UserService: UserService,
    private readonly JwtService: JwtService
  ) {}

  @SkipAuth()
  @Post('sign-in')
  async Login(
    @Body() params: LoginDto,
    @Res({
      passthrough: true,
    })
    res: Response,
    @Query('UserType') UserType?: TUserType,
    @Query('AppType') AppType?: AppTypeEnum
  ) {
    const user = await this.UserService.GetUserByEmail(params.Email);
    if (UserType && !UserType.split(',').includes(user.UserType)) {
      throw new UnauthorizedException('App Access Denied, Invalid User Type');
    }

    if (AppType && AppType !== user.AppType) {
      throw new UnauthorizedException('App Access Denied, Invalid App Type');
    }

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordMatch = await this.UserService.ComparePassword(params.Password, user.PasswordHash);

    if (!isPasswordMatch) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const payload = omit(user, ['PasswordHash']);

    // generate token
    const token = await this.JwtService.signAsync(payload);

    // set cookie
    res.cookie('_session', token, {
      httpOnly: false,
      secure: true,
      sameSite: 'strict',
      expires: new Date(Date.now() + 86400000), // 1 day
    });

    return {
      message: 'Login successful',
      ack: {
        ...payload,
        token,
      },
    };
  }

  @Get('current')
  async GetCurrentUser(@User() user: TUser) {
    // if user type is employee
    if (user.UserType === 'Employee') {
      return this.UserService.GetUserById(user.UserID, user.TenantID);
    }
    return user;
  }

  @Delete('sign-out')
  async Logout(
    @Res({
      passthrough: true,
    })
    res: Response
  ) {
    res.clearCookie('_session');
    return {
      message: 'Logout successful',
    };
  }

  @Post('change-password')
  async ChangePassword(@Body() params: ChangePasswordDto, @User() user: TUser) {
    const updatedUser = await this.UserService.Update(user.UserID, user.TenantID, { Password: params.NewPassword });

    return {
      message: 'Password changed successfully',
      updatedUser,
    };
  }

  @Patch('details')
  async UpdateUser(@Body() params: UpdateUserDto, @User() user: TUser) {
    const updatedUser = await this.UserService.Update(user.UserID, user.TenantID, omit(params, ['Password']));
    return {
      message: 'User details updated successfully',
      ack: updatedUser,
    };
  }

  @Post('forgot-password')
  async ForgotPassword(@Body() params: ForgotPasswordDto) {}

  private readonly GOOGLE_OAUTH_URL = 'https://accounts.google.com/o/oauth2/v2/auth';

  @SkipAuth()
  @Get('google')
  // @UseGuards(GoogleOAuthGuard)
  async googleAuth(@Req() req: Request, @Res() res: Response, @Query('RedirectURL') RedirectURL?: string) {
    const oauthUrl =
      `${this.GOOGLE_OAUTH_URL}?` +
      `client_id=${process.env.GOOGLE_CLIENT_ID}&` +
      `redirect_uri=${encodeURIComponent(process.env.GOOGLE_CALLBACK_URL)}&` +
      `scope=${encodeURIComponent('email profile')}&` +
      `response_type=code&` +
      `state=${JSON.stringify({ RedirectURL })}`;

    res.redirect(oauthUrl);
  }

  @SkipAuth()
  @Get('google/callback')
  @UseGuards(GoogleOAuthGuard)
  async googleAuthCallback(@Req() req: Request, @Res() res: Response) {
    const googleUser = req.user as unknown as IGoogleUser;
    console.log('googleUser', googleUser);
    const user = await this.UserService.GetUserByEmail(googleUser.email);
    if (!user) {
      return res.redirect(`${(googleUser.state as { RedirectURL: string }).RedirectURL}?error=No User Found`);
    }
    if (!user?.OAuth?.Google?.Connected) {
      await this.UserService.Update(user.UserID, user.TenantID, {
        ProfilePicture: googleUser.picture,
        OAuth: {
          Google: {
            Connected: true,
            UserID: googleUser.sub,
          },
        },
      });
    }
    // [Domain Based Routing]
    const token = await this.JwtService.signAsync(user);
    // Redirect to frontend with token
    const redirectUrl = `${(googleUser.state as { RedirectURL: string }).RedirectURL}?token=${token}`;
    res.redirect(redirectUrl);
  }
}
