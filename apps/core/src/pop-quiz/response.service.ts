import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PlanNSubscriptionService } from '../plan-n-subscription/plan-n-subscription.service';
import {
  CreatePopQuizResponseDto,
  GenerateSelfQuizDto,
  GetMyPopQuizResponsesDto,
  GetResponseDto,
  GetResponsesForPopQuizDto,
  ListMyQuizesDto,
  PreFlightPopQuizDto,
} from './dto/pop-quiz.service.dto';
import { PopQuiz, PopQuizStatus, SubscriberStatus } from './entities/pop-quiz.entity';
import { PopQuizQuestion } from './entities/question.entity';
import { PopQuizModel } from './schemas/quiz.schema';
import { PopQuizResponseModel } from './schemas/response.schema';
import { QuestionService } from './qustion.service';
import { ProjectService } from '../project/project.service';
import { AppTypeEnum } from '../core/user/entities/user.entity';
@Injectable()
export class ResponseService {
  constructor(
    @InjectModel(PopQuizResponseModel.name) private readonly PopQuizResponseModel: Model<PopQuizResponseModel>,
    @InjectModel(PopQuizModel.name) private readonly PopQuizModel: Model<PopQuizModel>,
    private readonly PlanNSubscriptionService: PlanNSubscriptionService,
    private readonly EventEmitter: EventEmitter2,
    private readonly QuestionService: QuestionService,
    private readonly ProjectService: ProjectService
  ) {}

  /**
   * Get All Pop Quiz Which is not taken by the user & subscribed to the project
   *
   */
  async PreFlightPopQuiz(Params: PreFlightPopQuizDto, Options?: { NoOmit?: boolean }) {
    const OmitValues = Options?.NoOmit
      ? ''
      : '-TenantID -ProjectID -Scopes -Answer -Options.IsCorrect -_id -createdAt -updatedAt';
    // All Project User is Subscribed too
    const projects = await this.PlanNSubscriptionService.ListProjectForSubscribers({
      TenantID: Params.TenantID,
      UserIDs: [Params.UserID],
    });
    const FloatingSubscribersPopQuiz = await this.PopQuizModel.find({
      'Subscribers.UserID': { $nin: [Params.UserID] },
      ProjectID: { $in: projects.map((project) => project.ProjectID) },
      TenantID: Params.TenantID,
      FloatingSubscribers: true,
      Status: PopQuizStatus.enum.Active,
    })
      .populate({
        path: 'QuestionDetails',
        select: OmitValues,
      })
      .select('-Subscribers')
      .lean();
    const SubscribedPopQuiz = await this.PopQuizModel.find({
      Subscribers: { $elemMatch: { UserID: Params.UserID, Status: SubscriberStatus.enum.Pending } },
      TenantID: Params.TenantID,
      FloatingSubscribers: false,
      ProjectID: { $in: projects.map((project) => project.ProjectID) },
      Status: PopQuizStatus.enum.Active,
    })
      .populate({
        path: 'QuestionDetails',
        select: OmitValues,
      })
      .select('-Subscribers')
      .lean();
    return FloatingSubscribersPopQuiz.concat(SubscribedPopQuiz) as unknown as (PopQuiz & {
      QuestionDetails: PopQuizQuestion[];
    })[];
  }

  async ListMyQuizes(Params: ListMyQuizesDto, Options?: { NoOmit?: boolean }) {
    const OmitValues = Options?.NoOmit
      ? ''
      : '-TenantID -ProjectID -Scopes -Answer -Options.IsCorrect -_id -createdAt -updatedAt';

    // All Project User is Subscribed too
    const projects = await this.PlanNSubscriptionService.ListProjectForSubscribers({
      TenantID: Params.TenantID,
      UserIDs: [Params.UserID],
    });

    const ProjectIDs = Params.ProjectID ? [Params.ProjectID] : projects.map((project) => project.ProjectID);

    const Quizes = await this.PopQuizModel.find({
      $or: [
        {
          Subscribers: { $elemMatch: { UserID: Params.UserID } },
        },
        {
          FloatingSubscribers: true,
        },
      ],
      TenantID: Params.TenantID,
      ProjectID: { $in: ProjectIDs },
      Status: PopQuizStatus.enum.Active,
    })
      .populate({
        path: 'QuestionDetails',
        select: OmitValues,
      })
      .lean();
    return Quizes;
  }

  async GenerateSelfQuiz(Params: GenerateSelfQuizDto, AppType: AppTypeEnum, Options?: { NoOmit?: boolean }) {
    const OmitValues = Options?.NoOmit
      ? ''
      : '-TenantID -ProjectID -Scopes -Answer -Options.IsCorrect -_id -createdAt -updatedAt';

    const project = await this.ProjectService.GetProjectDetails(Params.TenantID, Params.ProjectID, AppType);

    if (!project) {
      throw new BadRequestException('Project not found');
    }

    const questions = await this.QuestionService.ListQuestionsIDs({
      TenantID: Params.TenantID,
      ProjectID: Params.ProjectID,
    });
    const prevPopQuizQuestions = await this.PopQuizModel.find({
      TenantID: Params.TenantID,
      ProjectID: Params.ProjectID,
      $or: [
        {
          FloatingSubscribers: true,
        },
        {
          Subscribers: {
            $elemMatch: {
              UserID: Params.UserID,
            },
          },
        },
      ],
    })
      .distinct('QuestionID')
      .lean();
    const prevPopQuizQuestionsIDs = prevPopQuizQuestions || [];
    const notInPrevPopQuizQuestionIDs = questions.filter((question) => !prevPopQuizQuestionsIDs.includes(question));
    if (notInPrevPopQuizQuestionIDs.length < Params.QuestionCount) {
      const randomQuestions = notInPrevPopQuizQuestionIDs
        .sort(() => 0.5 - Math.random())
        .slice(0, Params.QuestionCount);
      return randomQuestions;
    }
    const randomQuestions = notInPrevPopQuizQuestionIDs.sort(() => 0.5 - Math.random()).slice(0, Params.QuestionCount);

    const popQuizAck = await this.PopQuizModel.create({
      Title: `${project.Name}: Self Quiz`,
      Description: `Self Quiz with ${Params.QuestionCount} questions`,
      TenantID: Params.TenantID,
      ProjectID: Params.ProjectID,
      Questions: randomQuestions,
      Status: PopQuizStatus.enum.Active,
      IsSelfQuiz: true,
      FloatingSubscribers: false,
      QuestionCount: Params.QuestionCount,
      Subscribers: [
        {
          UserID: Params.UserID,
          Status: SubscriberStatus.enum.Pending,
        },
      ],
    });

    const popQuiz = await this.PopQuizModel.findOne({
      PopQuizID: popQuizAck.PopQuizID,
    })
      .populate({
        path: 'QuestionDetails',
        select: OmitValues,
      })
      .lean();

    return popQuiz;
  }

  async GetPopQuizResponsesForUser(Params: GetMyPopQuizResponsesDto) {
    const responses = (await this.PopQuizResponseModel.find({
      UserID: Params.UserID,
      TenantID: Params.TenantID,
    })
      .populate({
        path: 'PopQuiz',
        select: '-Subscribers',
        match: {
          ...(Params.ProjectID ? { ProjectID: Params.ProjectID } : {}),
          ...(Params.PopQuizID ? { PopQuizID: Params.PopQuizID } : {}),
        },
      })
      .populate({
        path: 'QuestionDetails',
        select: '-TenantID -ProjectID',
      })
      .lean()) as unknown as (PopQuizResponseModel & {
      PopQuiz: PopQuiz;
    })[];
    return responses.filter((response) => response.PopQuiz);
  }

  async GetResponse(Params: GetResponseDto) {
    const response = await this.PopQuizResponseModel.findOne({
      ResponseID: Params.ResponseID,
      UserID: Params.UserID,
      TenantID: Params.TenantID,
    })
      .populate({
        path: 'PopQuiz',
        select: '-Subscribers',
      })
      .populate({
        path: 'QuestionDetails',
        select: '-TenantID -ProjectID',
      })
      .lean();
    return response;
  }

  async CreateResponse(Params: CreatePopQuizResponseDto) {
    // Validate Access
    const getQuizForUser = await this.PreFlightPopQuiz(
      {
        TenantID: Params.TenantID,
        UserID: Params.UserID,
      },
      { NoOmit: true }
    );
    const quiz = getQuizForUser.find((quiz) => quiz.PopQuizID === Params.PopQuizID);
    if (!quiz) {
      throw new BadRequestException('Quiz not found');
    }
    // Validate Question Answers
    const answers = quiz.Questions.map((question) => {
      const answer = Params.Answers.find((answer) => answer.QuestionID === question);
      if (!answer) {
        throw new BadRequestException('Some Question Answers are missing');
      }
      return answer;
    });

    // Generate Results [Single Option]
    const answerChecks = answers.map((answer) => {
      const question = quiz.QuestionDetails.find((question) => question.QuestionID === answer.QuestionID);
      const correctOption = question.Options.find((option) => option.IsCorrect);
      return {
        QuestionID: answer.QuestionID,
        IsCorrect: correctOption.OptionID === answer.OptionIDs[0],
        OptionIDs: answer.OptionIDs,
      };
    });
    const CorrectAnswers = answerChecks.filter((answer) => answer.IsCorrect);
    const IncorrectAnswers = answerChecks.filter((answer) => !answer.IsCorrect);
    const TotalAnswers = answerChecks.length;
    const Score = Math.round((CorrectAnswers.length / TotalAnswers) * 100);
    const Result = {
      CorrectAnswers: CorrectAnswers.length,
      IncorrectAnswers: IncorrectAnswers.length,
      TotalAnswers,
      Score,
    };
    const response = await this.PopQuizResponseModel.create({
      ...Params,
      Result,
      Questions: answerChecks,
    });

    // Update PopQuiz Subscribers Status via Event
    this.EventEmitter.emit('popQuiz.response.created', {
      PopQuizID: Params.PopQuizID,
      TenantID: Params.TenantID,
      UserID: Params.UserID,
    });

    return await this.GetResponse({
      ResponseID: response.ResponseID,
      UserID: Params.UserID,
      TenantID: Params.TenantID,
    });
  }

  async GetResponsesForPopQuiz(Params: GetResponsesForPopQuizDto) {
    const responses = await this.PopQuizResponseModel.find({
      PopQuizID: Params.PopQuizID,
      TenantID: Params.TenantID,
    })
      .populate({
        path: 'User',
        select: 'Email Name Gender FamilyName',
      })
      .lean();
    return responses;
  }
}
