import { Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateThreadDto, ThreadDto, ThreadListDto } from './dto/thread.dto';
import { Thread } from './schemas/thread.schema';

@Injectable()
export class ThreadService {
  constructor(@InjectModel(Thread.name) private threadModel: Model<Thread>) {}

  /**
   * Create a new thread for a user in a specific project
   */
  async createThread(UserID: string, TenantID: string, createThreadDto: CreateThreadDto): Promise<ThreadDto> {
    const { ProjectID, title } = createThreadDto;

    // Check if the user has access to this project in this tenant
    await this.validateProjectAccess(UserID, TenantID, ProjectID);

    // Create and save the new thread
    const newThread = new this.threadModel({
      ProjectID,
      title: title || `Thread ${new Date().toLocaleString()}`,
      UserID,
      TenantID,
      messageCount: 0,
    });

    const savedThread = await newThread.save();

    // Convert to DTO format
    return this.toThreadDto(savedThread);
  }

  /**
   * Get all threads for a user
   */
  async getThreads(UserID: string): Promise<ThreadListDto> {
    const threads = await this.threadModel.find({ UserID }).sort({ updatedAt: -1 }).exec();

    return {
      threads: threads.map((thread) => this.toThreadDto(thread)),
      count: threads.length,
    };
  }

  /**
   * Get threads for a user in a specific project
   */
  async getThreadsByProject(UserID: string, ProjectID: string): Promise<ThreadListDto> {
    const threads = await this.threadModel.find({ UserID, ProjectID }).sort({ updatedAt: -1 }).exec();

    return {
      threads: threads.map((thread) => this.toThreadDto(thread)),
      count: threads.length,
    };
  }

  /**
   * Get a specific thread
   */
  async getThread(ThreadID: string): Promise<ThreadDto | null> {
    const thread = await this.threadModel.findOne({ ThreadID }).exec();
    return thread ? this.toThreadDto(thread) : null;
  }

  /**
   * Check if a thread belongs to a user
   */
  async validateThreadOwnership(ThreadID: string, UserID: string): Promise<boolean> {
    const thread = await this.threadModel.findOne({ ThreadID }).exec();
    return thread?.UserID === UserID;
  }

  /**
   * Throws an error if the thread doesn't exist or doesn't belong to the user
   */
  async validateAndGetThread(ThreadID: string, UserID: string): Promise<ThreadDto> {
    const thread = await this.threadModel.findOne({ ThreadID }).exec();

    if (!thread) {
      throw new NotFoundException(`Thread with ID ${ThreadID} not found`);
    }

    if (thread.UserID !== UserID) {
      throw new UnauthorizedException('You do not have access to this thread');
    }

    return this.toThreadDto(thread);
  }

  /**
   * Validates that the user has access to the project in the given tenant
   */
  async validateProjectAccess(UserID: string, TenantID: string, ProjectID: string): Promise<boolean> {
    if (!UserID || !TenantID || !ProjectID) {
      throw new UnauthorizedException('Invalid user, tenant, or project ID');
    }

    return true;
  }

  /**
   * Validates that the thread belongs to the specified tenant and project
   */
  async validateThreadTenantAndProject(ThreadID: string, TenantID: string, ProjectID: string): Promise<boolean> {
    const thread = await this.threadModel.findOne({ ThreadID }).exec();

    if (!thread) {
      throw new NotFoundException(`Thread with ID ${ThreadID} not found`);
    }

    if (thread.TenantID !== TenantID) {
      throw new UnauthorizedException(`Thread does not belong to tenant ${TenantID}`);
    }

    if (thread.ProjectID !== ProjectID) {
      throw new UnauthorizedException(`Thread does not belong to project ${ProjectID}`);
    }

    return true;
  }

  /**
   * Update thread message count and last updated time
   */
  async updateThreadActivity(ThreadID: string, threadTitle?: string): Promise<void> {
    await this.threadModel
      .updateOne(
        { ThreadID },
        {
          $inc: { messageCount: 1 },
          $set: { updatedAt: new Date(), title: threadTitle },
        }
      )
      .exec();
  }

  /**
   * Delete a thread
   */
  async deleteThread(ThreadID: string): Promise<boolean> {
    const result = await this.threadModel.deleteOne({ ThreadID }).exec();
    return result.deletedCount > 0;
  }

  /**
   * Update thread title
   */
  async updateThreadTitle(ThreadID: string, UserID: string, title: string): Promise<ThreadDto | null> {
    // Validate that the thread belongs to this user
    await this.validateAndGetThread(ThreadID, UserID);

    // Update the thread title
    const updatedThread = await this.threadModel
      .findOneAndUpdate(
        { ThreadID, UserID },
        {
          $set: {
            title,
            updatedAt: new Date(),
          },
        },
        { new: true }
      )
      .exec();

    return updatedThread ? this.toThreadDto(updatedThread) : null;
  }

  /**
   * Convert Thread model to ThreadDto
   */
  private toThreadDto(thread: Thread): ThreadDto {
    return {
      ThreadID: thread.ThreadID,
      ProjectID: thread.ProjectID,
      title: thread.title,
      userId: thread.UserID,
      tenantId: thread.TenantID,
      createdAt: thread.createdAt,
      updatedAt: thread.updatedAt,
      messageCount: thread.messageCount,
    };
  }
}
