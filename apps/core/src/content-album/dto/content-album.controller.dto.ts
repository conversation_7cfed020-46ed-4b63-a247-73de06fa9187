import { OmitType } from '@nestjs/mapped-types';
import {
  CreateContentAlbumDto,
  CreateContentDto,
  GenerateContentAlbumDto,
  UpdateContentAlbumDto,
} from './content-album.service.dto';

export class CreateContentAlbumRequestDto extends OmitType(CreateContentAlbumDto, ['TenantID'] as const) {}

export class UpdateContentAlbumRequestDto extends OmitType(UpdateContentAlbumDto, [
  'TenantID',
  'ContentAlbumID',
] as const) {}

export class UploadContentRequestDto extends OmitType(CreateContentDto, [
  'TenantID',
  'Content',
  'ContentAlbumID',
  'Meta',
  'Duration',
] as const) {
  Title: string;
  Description?: string;
  Transcript?: string; // Plain Text
  Subtitles?: {
    Language: string;
    Content: string;
  }[]; // SRT
  Attachments?: string[]; // URL
  Scopes?: Record<string, string | boolean | number>;
}

export class UpdateContentMetaRequestDto extends OmitType(CreateContentDto, [
  'TenantID',
  'Content',
  'Duration',
] as const) {}

export class CreateContentGroupRequestDto extends OmitType(CreateContentDto, ['TenantID'] as const) {}

export class GenerateContentAlbumRequestDto extends OmitType(GenerateContentAlbumDto, ['TenantID'] as const) {}
