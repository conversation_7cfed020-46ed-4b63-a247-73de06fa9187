"use client";

import React, { useMemo } from "react";
import { cn } from "@workspace/ui/lib/utils";
import { useTTS } from "../../contexts/tts-context";

interface TTSProgressBarProps {
  messageId: string;
  className?: string;
}

export function TTSProgressBar({ messageId, className }: TTSProgressBarProps) {
  const { ttsState } = useTTS();

  const isCurrentMessage = ttsState.currentMessageId === messageId;
  const isActive =
    isCurrentMessage &&
    (ttsState.isLoading || ttsState.isPlaying || ttsState.isPaused);

  // Memoize formatted time to avoid unnecessary recalculations
  const { currentTimeFormatted, durationFormatted, providerLabel } =
    useMemo(() => {
      const formatTime = (seconds: number) => {
        if (!seconds || !isFinite(seconds)) return "0:00";
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, "0")}`;
      };

      const getProviderLabel = (provider: string) => {
        switch (provider) {
          case "lemonfox":
            return "LF";
          case "browser":
            return "BR";
          default:
            return provider.slice(0, 2).toUpperCase();
        }
      };

      return {
        currentTimeFormatted: formatTime(ttsState.currentTime),
        durationFormatted:
          ttsState.duration > 0 ? formatTime(ttsState.duration) : "--:--",
        providerLabel: getProviderLabel(ttsState.provider),
      };
    }, [ttsState.currentTime, ttsState.duration, ttsState.provider]);

  // Don't render if not active
  if (!isActive) {
    return null;
  }

  const progress = Math.max(0, Math.min(100, ttsState.progress));

  return (
    <div className={cn("flex items-center gap-2 w-full", className)}>
      {/* Current time */}
      <span className="text-xs text-muted-foreground font-mono min-w-[35px]">
        {currentTimeFormatted}
      </span>

      {/* Progress bar */}
      <div className="flex-1 h-1.5 bg-muted rounded-full overflow-hidden">
        <div
          className={cn(
            "h-full rounded-full transition-all duration-150 ease-out",
            ttsState.isLoading
              ? "bg-muted-foreground animate-pulse"
              : "bg-primary"
          )}
          style={{
            width: `${progress}%`,
            transition: ttsState.isLoading ? "none" : "width 0.15s ease-out",
          }}
        />
      </div>

      {/* Duration */}
      <span className="text-xs text-muted-foreground font-mono min-w-[35px]">
        {durationFormatted}
      </span>

      {/* Provider indicator */}
      <div className="ml-1 px-1.5 py-0.5 text-xs bg-primary/10 text-primary rounded text-[10px] font-medium">
        {providerLabel}
      </div>
    </div>
  );
}
