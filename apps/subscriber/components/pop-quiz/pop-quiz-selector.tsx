import { usePopQuizContext } from "@/contexts/pop-quiz-context";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { <PERSON>R<PERSON>, Bird, CircleX, ShieldAlert } from "lucide-react";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@workspace/ui/components/alert";
import { toast } from "sonner";

export default function PopQuizSelector() {
  const {
    popQuiz,
    quizzesList,
    isLoading,
    setPopQuiz,
    isSelectorOpen,
    setIsSelectorOpen,
  } = usePopQuizContext();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-svh">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
      </div>
    );
  }

  return (
    <Dialog open={isSelectorOpen && !popQuiz} onOpenChange={setIsSelectorOpen}>
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>Available Pop Quizzes</DialogTitle>
          <DialogDescription>
            You have {quizzesList?.length} quiz
            {quizzesList?.length > 1 ? "zes" : ""} to complete
          </DialogDescription>
          <Alert className="mt-2">
            <Bird size={24} />
            <AlertTitle>
              Knowledge not tested is knowledge not gained.
            </AlertTitle>
          </Alert>
        </DialogHeader>
        <div className="grid gap-2 overflow-y-auto max-h-[calc(100vh-20rem)]">
          {quizzesList?.map((quiz) => (
            <div
              key={quiz.PopQuizID}
              className="cursor-pointer flex justify-between items-center gap-2 p-4 border rounded-lg hover:bg-accent"
              onClick={() => {
                setPopQuiz(quiz);
              }}
            >
              <div className="flex flex-col gap-2">
                <h3 className="font-semibold">{quiz.Title}</h3>
                {quiz.Description && (
                  <p className="text-sm text-muted-foreground">
                    {quiz.Description}
                  </p>
                )}
                <p className="text-sm text-muted-foreground bg-accent p-2 rounded-md max-w-fit">
                  {quiz.QuestionCount} Questions
                </p>
              </div>
              <div className="flex">
                <Button
                  onClick={() => {
                    setPopQuiz(quiz);
                  }}
                  className="cursor-pointer"
                >
                  Get Started
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        <Button
          onClick={() => {
            toast.info("Quiz closed");
            setPopQuiz(null);
            setIsSelectorOpen(false);
          }}
          className="cursor-pointer"
        >
          <CircleX className="h-4 w-4" />
          Close
        </Button>
      </DialogContent>
    </Dialog>
  );
}
