import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import GenerateID from '../../../common/lib/generate-id';
import { AppTypeEnum } from '../../core/user/entities/user.entity';

@Schema({
  timestamps: true,
  versionKey: false,
  collection: 'projects',
})
export class ProjectModel {
  @Prop()
  Name: string;

  @Prop()
  Description: string;

  @Prop({
    type: String,
  })
  TenantID: string;

  @Prop({
    default: () => GenerateID.Generate16Hex('proj_'),
    unique: true,
    index: true,
  })
  ProjectID: string;

  @Prop({
    type: String,
    enum: AppTypeEnum,
    required: true,
  })
  AppType: AppTypeEnum;
}

export const ProjectSchema = SchemaFactory.createForClass(ProjectModel);
