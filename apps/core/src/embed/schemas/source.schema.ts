import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export enum DocumentStatus {
  UPLOADING = 'uploading',
  UPLOADED = 'uploaded',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

@Schema({ collection: 'source', timestamps: true, versionKey: false })
export class SourceModel {
  @Prop({ required: true })
  SourceID: string;

  @Prop({ required: true })
  SourceName: string;

  @Prop({ required: true, default: [] })
  Chapters: {
    ChapterID: string;
    ChapterName: string;
    ChapterNumber: number;
    ChunkIDs: string[];
    Summary?: string;
    Topics?: {
      Topic: string;
      PageNumber: number;
      SubTopics?: string[];
    }[];
    PageNumber: {
      From: number;
      To: number;
    };
    DocumentPageNumber: {
      From: number;
      To: number;
    };
  }[];

  @Prop({
    type: String,
    enum: Object.values(DocumentStatus),
    default: DocumentStatus.UPLOADING,
  })
  Status: DocumentStatus;

  @Prop({ type: String, required: true })
  TenantID: string;

  @Prop({ type: String, required: true })
  ProjectID: string;

  @Prop({ type: String, required: true })
  AWSKey: string;

  @Prop()
  ErrorMessage?: string;

  @Prop({ type: Object })
  BookSummary?: {
    Title: string;
    Summary: string;
    MainTopics: string[];
    ChapterHighlights: {
      ChapterName: string;
      Highlight: string;
    }[];
  };

  @Prop({ type: String, default: 'english' })
  Language: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const SourceSchema = SchemaFactory.createForClass(SourceModel);

// Add indexes for better query performance
SourceSchema.index({ SourceID: 1 }, { unique: true });
SourceSchema.index({ TenantID: 1, ProjectID: 1 });
SourceSchema.index({ status: 1 });
