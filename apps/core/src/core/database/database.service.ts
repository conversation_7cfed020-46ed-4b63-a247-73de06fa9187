import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MongooseModuleOptions, MongooseOptionsFactory } from '@nestjs/mongoose';
@Injectable()
export class DatabaseService implements MongooseOptionsFactory {
  private readonly MONGODB_URI = this.configService.get<string>('MONGODB_URI');
  private readonly DB_NAME = this.configService.get<string>('DB_NAME');
  private readonly POOL_SIZE = 100;

  constructor(private readonly configService: ConfigService) {}

  createMongooseOptions(options?: { sslPemPath?: string }): MongooseModuleOptions {
    return {
      uri: this.MONGODB_URI,
      dbName: this.DB_NAME,
      maxPoolSize: this.POOL_SIZE,
      tlsCertificateKeyFile: options.sslPemPath,
      onConnectionCreate(connection) {
        connection.on('connected', () => {
          Logger.log(`Mongoose default connection open to ${connection.name}`);
        });
        connection.on('disconnected', () => {
          Logger.log(`Mongoose default connection disconnected`);
        });
        connection.on('error', (error) => {
          Logger.log(`Mongoose default connection error: ${error}`);
        });
      },
    };
  }
}
