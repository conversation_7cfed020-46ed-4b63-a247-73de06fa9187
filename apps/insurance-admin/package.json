{"name": "insurance-admin", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.0.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-slot": "^1.1.2", "@workspace/ui": "workspace:*", "date-fns": "^4.1.0", "ky": "^1.8.0", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "sonner": "^2.0.3", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "typescript": "^5.7.3"}}