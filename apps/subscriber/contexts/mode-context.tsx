import { createContext, useContext, useEffect, useState } from "react";
import { ReactNode } from "react";
import { useProjectContext } from "./project-context";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "./auth-context";

interface ModeContextType {
  mode: "doubt" | "story" | "quiz" | null;
  setMode: (mode: "doubt" | "story" | "quiz") => void;
}

export const ModeContext = createContext<ModeContextType>({
  mode: null,
  setMode: () => {},
});

export function ModeProvider({ children }: { children: ReactNode }) {
  const [mode, setMode] = useState<ModeContextType["mode"] | null>(null);
  const { selectedProject } = useProjectContext();
  const { isAuthenticated } = useAuth();
  const router = useRouter();

  // persist in localStorage
  useEffect(() => {
    if (mode) {
      localStorage.setItem("mode", mode);
      if (mode === "story") {
        router.push(`/${selectedProject?.id}/content`);
      }
      if (mode === "quiz") {
        router.push(`/${selectedProject?.id}/quiz`);
      }
      if (mode === "doubt") {
        router.push("/");
      }
    }
  }, [mode]);

  // get from localStorage
  useEffect(() => {
    if (!isAuthenticated) return;
    const storedMode = localStorage.getItem("mode");
    if (storedMode) {
      setMode(storedMode as ModeContextType["mode"]);
    }
    if (!storedMode) {
      setMode("doubt");
    }
  }, [isAuthenticated]);

  const value = {
    mode,
    setMode,
  };
  return <ModeContext.Provider value={value}>{children}</ModeContext.Provider>;
}

export const useModeContext = () => useContext(ModeContext);
