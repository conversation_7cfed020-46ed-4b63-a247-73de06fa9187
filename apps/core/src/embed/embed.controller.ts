import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Query,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { EmbedService } from './embed.service';
import { CreateEmbedDto } from './dto/create-embed.dto';
import { UpdateEmbedDto } from './dto/update-embed.dto';
import { AuthAdminGuard } from '../../common/guards/auth.guard';
import { User } from '../../common/decorators/core.decorators';
import { TUser } from '../core/user/entities/user.entity';

@UseGuards(AuthAdminGuard)
@Controller('embed')
export class EmbedController {
  constructor(private readonly embedService: EmbedService) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  async create(@Body() createEmbedDto: CreateEmbedDto, @UploadedFile() file: Express.Multer.File, @User() user: TUser) {
    if (!file) {
      throw new BadRequestException('PDF file is required');
    }

    if (file.mimetype !== 'application/pdf') {
      throw new BadRequestException('Only PDF files are allowed');
    }

    return this.embedService.create(createEmbedDto, file, user.UserID, user.AppType);
  }

  @Get()
  findAll() {
    return this.embedService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.embedService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateEmbedDto: UpdateEmbedDto) {
    return this.embedService.update(+id, updateEmbedDto);
  }

  @Delete(':sourceId')
  deleteSource(
    @Param('sourceId') sourceId: string,
    @Query('tenantId') tenantId: string,
    @Query('projectId') projectId: string,
    @User() user: TUser
  ) {
    return this.embedService.deleteSource(sourceId, tenantId, projectId, user.AppType);
  }

  @Get('chunk/:chunkId')
  getChunk(
    @Param('chunkId') chunkId: string,
    @Query('tenantId') tenantId: string,
    @Query('projectId') projectId: string,
    @User() user: TUser
  ) {
    if (!tenantId || !projectId) {
      throw new BadRequestException('TenantID and ProjectID are required');
    }
    return this.embedService.getChunkById(chunkId, tenantId, projectId, user.AppType);
  }
}
