"use client";
import { API } from "@/apis/api";
import { TContentAlbum } from "@/apis/content-album";
import { PROJECT } from "@/apis/project.api";
import { toTitleCase } from "@/lib/utils";
import { DocumentStatus, TProjectDetails } from "@/types/project";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@workspace/ui/components/alert-dialog";
import { Badge } from "@workspace/ui/components/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@workspace/ui/components/breadcrumb";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Skeleton } from "@workspace/ui/components/skeleton";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import { Textarea } from "@workspace/ui/components/textarea";
import { formatDistanceToNow } from "date-fns";
import {
  AlertCircle,
  Boxes,
  Clapperboard,
  Clock8,
  MoreVertical,
  Pencil,
  Plus,
  ScanEye,
  Trash2,
  Upload,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState, useCallback } from "react";
import { toast } from "sonner";
import { Switch } from "@workspace/ui/components/switch";

const statusColors: Record<DocumentStatus, string> = {
  [DocumentStatus.UPLOADING]: "bg-blue-100 text-blue-800",
  [DocumentStatus.UPLOADED]: "bg-gray-100 text-gray-800",
  [DocumentStatus.PROCESSING]: "bg-yellow-100 text-yellow-800",
  [DocumentStatus.COMPLETED]: "bg-green-100 text-green-800",
  [DocumentStatus.FAILED]: "bg-red-100 text-red-800",
};

function getStatusCount(
  sources: TProjectDetails["Sources"]
): Record<DocumentStatus, number> {
  const counts: Record<DocumentStatus, number> = {
    [DocumentStatus.UPLOADING]: 0,
    [DocumentStatus.UPLOADED]: 0,
    [DocumentStatus.PROCESSING]: 0,
    [DocumentStatus.COMPLETED]: 0,
    [DocumentStatus.FAILED]: 0,
  };
  sources?.forEach((s) => counts[s.Status]++);
  return counts;
}

const ProjectView = ({ project }: { project: TProjectDetails }) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [sourceName, setSourceName] = useState("");
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [sourceType, setSourceType] = useState<string>("pdf");
  const [pageOffset, setPageOffset] = useState(0);
  const [language, setLanguage] = useState<string>("en");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [contentAlbums, setContentAlbums] = useState<TContentAlbum[]>([]);

  const [tab, setTab] = useState<"content-albums" | "sources">("sources");
  const [isDragging, setIsDragging] = useState(false);
  const [isSuperEmbed, setIsSuperEmbed] = useState(false);

  if (!project) {
    return (
      <div className="w-full h-full py-6 space-y-6">
        <div className="border rounded-lg bg-muted/30 p-6">
          <Skeleton className="h-8 w-1/3 mb-4" />
          <Skeleton className="h-4 w-2/3 mb-2" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-1/4" />
          </div>
        </div>
      </div>
    );
  }

  const totalSections =
    project.Sources?.reduce((acc, s) => acc + (s.Sections?.length || 0), 0) ||
    0;

  const statusCount = getStatusCount(project.Sources);

  const isFormValid = useCallback(() => {
    return (
      sourceName.trim() !== "" &&
      pdfFile !== null &&
      sourceType !== "" &&
      language !== ""
    );
  }, [sourceName, pdfFile, sourceType, language]);

  async function handleAddSource(e: React.FormEvent) {
    e.preventDefault();
    if (!sourceName.trim()) {
      toast.error("Source name is required");
      return;
    }
    if (!pdfFile) {
      toast.error("PDF file is required");
      return;
    }
    if (pdfFile.type !== "application/pdf") {
      toast.error("Only PDF files are allowed");
      return;
    }
    setLoading(true);
    const { data, errors } = await PROJECT.AddSource({
      ProjectID: project.ProjectID,
      TenantID: project.TenantID,
      sourceName,
      file: pdfFile,
      pageNumberOffset: pageOffset,
      sourceType,
      language,
      IsSuperEmbed: isSuperEmbed,
    });
    setLoading(false);
    if (data) {
      toast.success("PDF uploaded successfully, embedding process started");
      setDialogOpen(false);
      setSourceName("");
      setPdfFile(null);
      setPageOffset(0);
      setSourceType("pdf");
      setLanguage("en");
      if (fileInputRef.current) fileInputRef.current.value = "";
      // Optionally, trigger a refresh of the sources list here if possible
      window.location.reload();
    }
    if (errors) (errors as Error[]).forEach((err) => toast.error(err.message));
  }

  async function handleDeleteSource(sourceId: string) {
    setDeleteLoading(true);
    const { data, errors } = await PROJECT.DeleteSource({
      ProjectID: project.ProjectID,
      TenantID: project.TenantID,
      SourceID: sourceId,
    });
    setDeleteLoading(false);
    setDeleteDialogOpen(null);

    if (data) {
      toast.success("Source deleted successfully");
      window.location.reload();
    }
    if (errors) {
      (errors as Error[]).forEach((err) => toast.error(err.message));
    }
  }

  const getContentAlbums = async () => {
    const { data, errors } = await API.CONTENT_ALBUM.GetContentAlbums({
      ProjectID: project.ProjectID,
    });

    if (!errors && data.length > 0) {
      setContentAlbums(data);
    }
  };

  useEffect(() => {
    getContentAlbums();
  }, []);

  const router = useRouter();

  const ContentLibrary = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 w-full gap-4">
        {contentAlbums.map((album) => (
          <div
            className=" rounded-lg border flex flex-col gap-3 hover:shadow-lg pb-2"
            key={album.ContentAlbumID}
            onClick={() => {
              // router.push(
              //   `/projects/${project.ProjectID}/content-album/${album.ContentAlbumID}`
              // );
            }}
          >
            <div className="flex items-center gap-2 p-2">
              <img
                src={album.CoverImage}
                alt={album.Name}
                width={"100%"}
                height={"100%"}
                className="w-full h-full object-cover rounded-lg max-h-40 overflow-hidden"
              />
            </div>
            <div className="px-2">
              <h2 className="text-lg font-semibold">{album.Name}</h2>
              <p className="text-sm text-muted-foreground line-clamp-2">
                {album.Description ?? "..."}
              </p>
            </div>

            <div className="flex items-center gap-3 text-sm px-2 mt-auto">
              <span className="flex items-center gap-1">
                <Clock8 size={16} />
                <span className="text-xs text-muted-foreground  ">
                  {Math.floor((album.ContentDuration ?? 0) / 60 / 60) > 0
                    ? Math.floor((album.ContentDuration ?? 0) / 60 / 60) +
                      " hours"
                    : Math.floor((album.ContentDuration ?? 0) / 60) +
                      " minutes"}
                </span>
              </span>
              <span className="flex items-center gap-1">
                <Clapperboard size={16} />
                <span className="text-xs text-muted-foreground">
                  {album.ContentCount ?? 0} contents
                </span>
              </span>
              <span className="flex items-center gap-1">
                <Boxes size={16} />
                <span className="text-xs text-muted-foreground">
                  {album.ContentGroups.length} modules
                </span>
              </span>
            </div>

            <div className="flex gap-2 items-center justify-center w-full px-2">
              <Button
                variant="outline"
                onClick={() => {
                  setContentAlbum(album);
                  setAlbumDialogOpen(true);
                }}
              >
                <Pencil size={16} />
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  router.push(
                    `/projects/${project.ProjectID}/content-album/${album.ContentAlbumID}`
                  );
                }}
                className="flex-1"
              >
                <ScanEye size={16} />
                View
              </Button>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const [albumDialogOpen, setAlbumDialogOpen] = useState(false);
  const [contentAlbum, setContentAlbum] = useState<TContentAlbum | null>(null);

  const AddContentAlbum = () => {
    const [contentAlbumState, setContentAlbumState] = useState<
      Pick<TContentAlbum, "Name" | "Description" | "CoverImage" | "ProjectID">
    >({
      Name: contentAlbum?.Name ?? "",
      Description: contentAlbum?.Description ?? "",
      CoverImage: contentAlbum?.CoverImage ?? "",
      ProjectID: project?.ProjectID!,
    });

    return (
      <AlertDialog open={albumDialogOpen} onOpenChange={setAlbumDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {contentAlbum ? "Edit Album" : "New Album"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {contentAlbum
                ? "Update the album details"
                : "Create a new album for your project"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <form>
            <div className="flex flex-col gap-4">
              <Input
                placeholder="Album Name"
                value={contentAlbumState.Name}
                onChange={(e) =>
                  setContentAlbumState({
                    ...contentAlbumState,
                    Name: e.target.value,
                  })
                }
              />
              <Textarea
                placeholder="Description"
                value={contentAlbumState.Description}
                onChange={(e) =>
                  setContentAlbumState({
                    ...contentAlbumState,
                    Description: e.target.value,
                  })
                }
              />
              <Input
                placeholder="Cover Image"
                // value={contentAlbumState.CoverImage}
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    const reader = new FileReader();
                    reader.onloadend = () => {
                      setContentAlbumState({
                        ...contentAlbumState,
                        CoverImage: reader.result as string,
                      });
                    };
                    reader.readAsDataURL(file);
                  }
                }}
                type="file"
                accept="image/*"
              />
            </div>
          </form>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={async (e) => {
                e.preventDefault();
                if (
                  !contentAlbumState.Name.trim() ||
                  !contentAlbumState.CoverImage
                ) {
                  toast.error("Album name and cover image are required");
                  return;
                }
                const { data, errors } = contentAlbum
                  ? await API.CONTENT_ALBUM.UpdateContentAlbum({
                      ContentAlbumID: contentAlbum.ContentAlbumID,
                      Name: contentAlbumState.Name,
                      Description: contentAlbumState.Description,
                      CoverImage: contentAlbumState.CoverImage,
                    })
                  : await API.CONTENT_ALBUM.CreateContentAlbum(
                      contentAlbumState
                    );
                if (!errors && data) {
                  setDialogOpen(false);
                  setContentAlbumState({
                    Name: "",
                    Description: "",
                    CoverImage: "",
                    ProjectID: project?.ProjectID!,
                  });
                  toast.success(
                    contentAlbum
                      ? "Album updated successfully"
                      : "Album created successfully"
                  );
                  getContentAlbums();
                }
                if (errors) {
                  errors.forEach((error) => toast.error(error.message));
                }
                setAlbumDialogOpen(false);
              }}
            >
              Save changes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  const onDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const file = e.dataTransfer.files[0];
    if (file && file.type === "application/pdf") {
      setPdfFile(file);
    } else {
      toast.error("Please upload a PDF file");
    }
  }, []);

  const onDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const onDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  return (
    <div className="w-full h-full py-6 space-y-8">
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/projects">Projects</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{project?.Name || "Loading..."}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Project Meta */}
      <div className="border rounded-lg bg-muted/30 p-6 flex flex-col md:flex-row md:justify-between md:items-center gap-6">
        <div>
          <h2 className="text-3xl font-bold mb-2 flex items-center gap-4">
            {project.Name}
          </h2>
          <p className="text-muted-foreground mb-2">
            {project.Description || "No description available."}
          </p>
          <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
            <span>
              <b>Project ID:</b> {project.ProjectID}
            </span>
            <span>
              <b>Tenant ID:</b> {project.TenantID}
            </span>
            <span>
              <b>Created:</b>{" "}
              {formatDistanceToNow(new Date(project.createdAt), {
                addSuffix: true,
              })}
            </span>
            <span>
              <b>Last updated:</b>{" "}
              {formatDistanceToNow(new Date(project.updatedAt), {
                addSuffix: true,
              })}
            </span>
          </div>
        </div>
        <div className="flex flex-col gap-2 min-w-[200px] items-end">
          <div className="flex gap-6 mb-2">
            <div className="flex flex-col items-center">
              <span className="font-bold text-lg">
                {project.Sources?.length || 0}
              </span>
              <span className="text-xs text-muted-foreground">Sources</span>
            </div>
            <div className="flex flex-col items-center">
              <span className="font-bold text-lg">{totalSections}</span>
              <span className="text-xs text-muted-foreground">Sections</span>
            </div>
          </div>
          <div className="flex gap-2 flex-wrap mt-3">
            {Object.entries(statusCount).map(([status, count]) => (
              <span
                key={status}
                className={`px-2 py-1 rounded text-xs font-medium ${statusColors[status as DocumentStatus]}`}
                style={{ textTransform: "none" }}
              >
                {toTitleCase(status)}: {count}
              </span>
            ))}
          </div>
        </div>
      </div>
      {/* Sources Grid */}
      <div className="flex flex-col gap-4">
        <Tabs
          defaultValue={
            new URLSearchParams(window.location.search).get("tab") || "sources"
          }
          onValueChange={(value) => {
            setTab(value as "content-albums" | "sources");
            router.push(`/projects/${project.ProjectID}?tab=${value}`);
          }}
        >
          <TabsList>
            <TabsTrigger value="sources" className="cursor-pointer">
              Sources
            </TabsTrigger>
            <TabsTrigger value="content" className="cursor-pointer">
              Content Albums
            </TabsTrigger>
          </TabsList>
          <TabsContent value="sources">
            <div className="flex items-center justify-between">
              <p className="text-xl font-bold">Sources</p>
              <AlertDialog
                open={dialogOpen}
                onOpenChange={(open) => {
                  if (loading) return; // Prevent closing while loading
                  setDialogOpen(open);
                }}
              >
                <AlertDialogTrigger asChild>
                  <Button className="ml-4 cursor-pointer" variant="default">
                    Add New Source
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent className="sm:max-w-[800px]">
                  <AlertDialogHeader className="space-y-3">
                    <AlertDialogTitle className="text-2xl font-bold">
                      Add New Source
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-base">
                      Upload a new source document for your project. Fill in the
                      details below to get started.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <form
                    className="flex flex-col gap-6 mt-6"
                    onSubmit={handleAddSource}
                  >
                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label
                          htmlFor="sourceName"
                          className="text-sm font-medium"
                        >
                          Source Name
                        </Label>
                        <Input
                          id="sourceName"
                          placeholder="e.g. Policy Document"
                          value={sourceName}
                          onChange={(e) => setSourceName(e.target.value)}
                          required
                          disabled={loading}
                          className="h-10"
                        />
                        <p className="text-xs text-muted-foreground">
                          A descriptive name for your source document
                        </p>
                      </div>
                      <div className="space-y-2">
                        <Label
                          htmlFor="sourceType"
                          className="text-sm font-medium"
                        >
                          Source Type
                        </Label>
                        <Select
                          value={sourceType}
                          onValueChange={(value) => setSourceType(value)}
                        >
                          <SelectTrigger className="h-10">
                            <SelectValue placeholder="Select Source Type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pdf">PDF Document</SelectItem>
                            <SelectItem disabled value="docx">
                              Word Document
                            </SelectItem>
                            <SelectItem disabled value="pptx">
                              PowerPoint
                            </SelectItem>
                            <SelectItem disabled value="txt">
                              Text File
                            </SelectItem>
                            <SelectItem disabled value="url">
                              Web URL
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground">
                          The type of document you're uploading
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label
                          htmlFor="language"
                          className="text-sm font-medium"
                        >
                          Language
                        </Label>
                        <Select
                          value={language}
                          onValueChange={(value) => setLanguage(value)}
                        >
                          <SelectTrigger className="h-10">
                            <SelectValue placeholder="Select Language" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="en">English</SelectItem>
                            <SelectItem disabled value="hi">
                              Hindi
                            </SelectItem>
                            <SelectItem disabled value="mr">
                              Marathi
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground">
                          The primary language of the document
                        </p>
                      </div>
                      <div className="space-y-2">
                        <Label
                          htmlFor="pageOffset"
                          className="text-sm font-medium"
                        >
                          Page Number Offset
                        </Label>
                        <Input
                          id="pageOffset"
                          type="number"
                          value={pageOffset}
                          onChange={(e) =>
                            setPageOffset(Number(e.target.value))
                          }
                          min={0}
                          disabled={loading}
                          className="h-10"
                          placeholder="0"
                        />
                        <p className="text-xs text-muted-foreground">
                          Number of pages before actual content starts
                        </p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="pdfFile" className="text-sm font-medium">
                        Document File
                      </Label>
                      <div
                        className={`
                          relative border-2 border-dashed rounded-lg p-8
                          ${isDragging ? "border-primary bg-primary/5" : "border-muted-foreground/25"}
                          transition-colors duration-200 ease-in-out
                          hover:border-primary/50 hover:bg-primary/5
                        `}
                        onDrop={onDrop}
                        onDragOver={onDragOver}
                        onDragLeave={onDragLeave}
                      >
                        <input
                          id="pdfFile"
                          type="file"
                          accept="application/pdf"
                          ref={fileInputRef}
                          onChange={(e) =>
                            setPdfFile(e.target.files?.[0] || null)
                          }
                          required
                          disabled={loading}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                        <div className="flex flex-col items-center justify-center gap-4 text-center">
                          <div className="p-3 rounded-full bg-primary/10">
                            <Upload className="w-6 h-6 text-primary" />
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium">
                              {pdfFile
                                ? pdfFile.name
                                : "Drop your PDF file here"}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {pdfFile
                                ? "Click or drag to replace file"
                                : "or click to browse files (max 10MB)"}
                            </p>
                          </div>
                          {pdfFile && (
                            <Badge variant="secondary" className="mt-2">
                              {Math.round((pdfFile.size / 1024 / 1024) * 100) /
                                100}{" "}
                              MB
                            </Badge>
                          )}
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Supported format: PDF only
                      </p>
                    </div>

                    <div className="border rounded-lg p-4 bg-muted/30">
                      <div className="flex items-start gap-4">
                        <div className="flex items-center h-6">
                          <Switch
                            id="super-embed"
                            checked={isSuperEmbed}
                            onCheckedChange={setIsSuperEmbed}
                            disabled={loading}
                          />
                        </div>
                        <div className="space-y-1">
                          <Label
                            htmlFor="super-embed"
                            className="text-base font-semibold"
                          >
                            Enable Super Embed
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            One-click solution that will automatically upload,
                            embed, generate content library, and create pop
                            quizzes for your document. This process may take
                            longer but provides a complete setup.
                          </p>
                        </div>
                      </div>
                    </div>

                    <AlertDialogFooter className="mt-4">
                      <AlertDialogCancel asChild>
                        <Button
                          type="button"
                          variant="outline"
                          disabled={loading}
                          className="h-10"
                        >
                          Cancel
                        </Button>
                      </AlertDialogCancel>
                      <AlertDialogAction asChild>
                        <Button
                          type="submit"
                          variant="default"
                          disabled={loading || !isFormValid()}
                          className="h-10"
                        >
                          {loading ? (
                            <div className="flex items-center gap-2">
                              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                              Uploading...
                            </div>
                          ) : (
                            "Upload Source"
                          )}
                        </Button>
                      </AlertDialogAction>
                    </AlertDialogFooter>
                    {!isFormValid() && !loading && (
                      <div className="w-full flex justify-center mt-2">
                        <span className="text-sm text-destructive font-medium">
                          Please fill all required fields
                        </span>
                      </div>
                    )}
                  </form>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            {!project.Sources ||
              (project.Sources.length === 0 && (
                <div className="border rounded-lg p-6 text-center text-muted-foreground">
                  No sources available
                </div>
              ))}

            {project.Sources && project.Sources.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-2">
                {project.Sources.map((source) => {
                  const sectionCount = source.Sections?.length || 0;

                  return (
                    <div
                      key={source.SourceID}
                      className="rounded-xl border shadow-sm hover:shadow-lg transition flex flex-col h-full"
                      style={{ minHeight: 320 }}
                    >
                      <div className="flex items-center justify-between px-6 pt-6 pb-2 border-b">
                        <div className="font-semibold text-lg truncate text-foreground">
                          {source.SourceName}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            className={`px-3 py-1 font-semibold`}
                            style={{ textTransform: "none" }}
                          >
                            {toTitleCase(source.Status)}
                          </Badge>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                              >
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <DropdownMenuItem
                                    variant="destructive"
                                    onClick={() =>
                                      setDeleteDialogOpen(source.SourceID)
                                    }
                                    className="text-white"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete Source
                                  </DropdownMenuItem>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>
                                      Are you sure?
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                      This action cannot be undone. This will
                                      permanently delete the source and all its
                                      associated data.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel disabled={deleteLoading}>
                                      Cancel
                                    </AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() =>
                                        handleDeleteSource(source.SourceID)
                                      }
                                      disabled={deleteLoading}
                                    >
                                      {deleteLoading ? "Deleting..." : "Delete"}
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                      <div className="flex-1 flex flex-col gap-2 px-6 py-4">
                        <div className="flex flex-wrap gap-4 text-xs text-muted-foreground mb-1">
                          <span>
                            <span className="font-medium">Sections:</span>{" "}
                            {sectionCount}
                          </span>
                        </div>
                        {source.SourceSummary?.MainTopics &&
                          source.SourceSummary.MainTopics.length > 0 && (
                            <div className="mb-1">
                              <div className="text-xs font-medium mb-1 text-foreground">
                                Main Topics:
                              </div>
                              <div className="flex flex-wrap gap-1">
                                {source.SourceSummary.MainTopics.slice(
                                  0,
                                  3
                                ).map((topic, idx) => (
                                  <p
                                    key={idx}
                                    className="text-xs font-normal bg-accent px-2 py-1 rounded-sm max-w-fit"
                                  >
                                    {topic}
                                  </p>
                                ))}
                                {Array.isArray(
                                  source.SourceSummary.MainTopics
                                ) &&
                                  source.SourceSummary.MainTopics.length >
                                    3 && (
                                    <Badge
                                      variant="secondary"
                                      className="text-xs font-normal"
                                    >
                                      +
                                      {(source.SourceSummary.MainTopics
                                        .length ?? 0) - 3}{" "}
                                      more
                                    </Badge>
                                  )}
                              </div>
                            </div>
                          )}
                        <div className="text-xs text-muted-foreground line-clamp-3 mb-1">
                          {source.SourceSummary?.Summary || (
                            <span className="italic">No summary</span>
                          )}
                        </div>
                        <div className="flex flex-wrap gap-4 text-xs text-muted-foreground mb-1">
                          <span>
                            <span className="font-medium">Created:</span>{" "}
                            {formatDistanceToNow(new Date(source.createdAt), {
                              addSuffix: true,
                            })}
                          </span>
                          <span>
                            <span className="font-medium">Updated:</span>{" "}
                            {formatDistanceToNow(new Date(source.updatedAt), {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                        {source.ErrorMessage && (
                          <div className="inline-flex items-center gap-1 text-red-600 text-xs mt-1">
                            <AlertCircle className="w-4 h-4" />{" "}
                            {source.ErrorMessage}
                          </div>
                        )}
                      </div>
                      <div className="px-6 pb-4 pt-2 border-t flex justify-end">
                        <Link
                          href={`/projects/${project.ProjectID}/sources/${source.SourceID}`}
                          className="text-primary underline text-sm font-medium"
                        >
                          View Details
                        </Link>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </TabsContent>
          <TabsContent value="content">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold">Content Library</h2>
              <Button
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  setContentAlbum(null);
                  setAlbumDialogOpen(true);
                }}
              >
                <Plus size={16} />
                New Album
              </Button>
            </div>
            <ContentLibrary />
          </TabsContent>
        </Tabs>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!deleteDialogOpen}
        onOpenChange={(open) => !open && setDeleteDialogOpen(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              source and all its associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteLoading}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                deleteDialogOpen && handleDeleteSource(deleteDialogOpen)
              }
              disabled={deleteLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteLoading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AddContentAlbum />
    </div>
  );
};

export default ProjectView;
