import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import GenerateID from '../../../common/lib/generate-id';
import { S3 } from '@aws-sdk/client-s3';

@Schema({
  _id: null,
})
export class S3Model {
  @Prop({
    type: String,
  })
  Location: string;

  @Prop({
    type: String,
  })
  Key: string;

  @Prop({
    type: String,
  })
  Type: string;

  @Prop({
    type: Number,
  })
  FileSize: number;

  @Prop({
    type: Date,
  })
  Expiration: Date;

  @Prop({
    type: String,
  })
  AccessUrl: string;

  @Prop({
    type: String,
  })
  FileName: string;
}

@Schema({
  _id: null,
})
export class CDNModel {
  @Prop()
  URL: string;
}

@Schema({
  _id: null,
})
class Meta {
  @Prop({
    type: String,
    required: true,
  })
  Title: string;

  @Prop({
    type: String,
  })
  Description?: string;

  @Prop({
    type: String,
  })
  Transcript?: string;

  @Prop({
    type: [
      {
        Language: String,
        Content: String,
      },
    ],
  })
  Subtitles?: {
    Language: string;
    Content: string;
  }[];

  @Prop({
    type: [String],
  })
  Attachments?: string[];
}

@Schema({
  _id: null,
  timestamps: true,
})
class Content {
  @Prop({
    type: String,
    default: () => GenerateID.Generate24Hex('cont_'),
  })
  ContentID: string;

  @Prop({
    type: String,
  })
  ContentGroupID?: string;

  @Prop({
    type: Meta,
  })
  Meta: Meta;

  @Prop({
    type: Number,
  })
  Duration: number;

  @Prop({
    type: S3Model,
    required: false,
  })
  S3: S3Model;

  @Prop({
    type: CDNModel,
  })
  CDN?: CDNModel;

  @Prop({
    type: Object,
  })
  HLS?: {
    Resolutions: {
      Resolution: string;
      URL: string;
    }[];
  };

  @Prop({
    type: Number,
  })
  SortOrder?: number;
}

@Schema({
  _id: null,
  timestamps: true,
})
class ContentGroup {
  @Prop({
    type: String,
    default: () => GenerateID.Generate16Hex('cont_grp_'),
  })
  ContentGroupID: string;

  @Prop({
    type: String,
    required: true,
  })
  Title: string;

  @Prop({
    type: String,
  })
  Description?: string;

  @Prop({
    type: Number,
  })
  SortOrder?: number;
}

@Schema({
  timestamps: true,
  collection: 'content_albums',
  versionKey: false,
})
export class ContentAlbumModel {
  @Prop({
    type: String,
    required: true,
    default: () => GenerateID.Generate16Hex('cont_alb_'),
  })
  ContentAlbumID: string;

  @Prop({
    type: String,
    required: true,
  })
  Name: string;

  @Prop({
    type: String,
  })
  Description?: string;

  @Prop({
    type: String,
  })
  CoverImage: string;

  @Prop({
    type: String,
    required: true,
  })
  TenantID: string;

  @Prop({
    type: String,
    required: true,
  })
  ProjectID: string;

  @Prop({
    type: [ContentGroup],
    default: [],
  })
  ContentGroups: ContentGroup[];

  @Prop({
    type: [Content],
    default: [],
  })
  Contents: Content[];

  @Prop({
    type: Number,
    default: 0,
  })
  ContentCount: number;

  @Prop({
    type: Number,
    default: 0,
  })
  ContentDuration: number; // Total Duration of all contents in minutes
}

export const ContentAlbumSchema = SchemaFactory.createForClass(ContentAlbumModel);
