[tool.poetry]
name = "industry_lm"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <ram<PERSON><PERSON>@nextcampus.in>"]
readme = "README.md"
# This is the correct place for Python version restriction
packages = [
    { include = "app" }
]

[tool.poetry.dependencies]
python = ">=3.10,<3.14"
fastapi = ">=0.115.12,<0.116.0"
uvicorn = ">=0.34.2,<0.35.0"
langchain = ">=0.3.25,<0.4.0"
openai = ">=1.79.0,<2.0.0"
tiktoken = ">=0.9.0,<0.10.0"
langgraph = ">=0.4.5,<0.5.0"
pydantic = ">=2.11.4,<3.0.0"
langchain-openai = ">=0.3.17,<0.4.0"
python-dotenv = ">=1.1.0,<2.0.0"
langchain-community = ">=0.3.24,<0.4.0"
python-magic = ">=0.4.27,<0.5.0"
pymupdf = "^1.25.5"
langchain-experimental = "^0.3.4"
langchain-pinecone = "^0.2.6"
motor = "^3.7.1"
python-multipart = "^0.0.20"
requests = "^2.32.3"
uvloop = "^0.19.0"
httptools = "^0.6.1"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
mypy = "^1.15.0"
ruff = "^0.11.10"
black = "^25.1.0"

[tool.poetry.scripts]
start = "app.run:main"
start-prod = "app.run:main_prod"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
