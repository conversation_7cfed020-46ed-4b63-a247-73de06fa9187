import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { User } from '../../core/user/schemas/user.model';
import { PricingType, RenewalStatus, SubscriptionStatus } from '../entities/plan-n-subscription.entity';
import { PlanModel } from './plan.schema';
import { CreditUsageModel } from './credit-usage.schema';

@Schema({
  timestamps: true,
  versionKey: false,
})
class Renewal {
  @Prop({ type: Date, required: true })
  FromDate: Date;

  @Prop({ type: Date, required: true })
  ToDate: Date;

  @Prop({ type: String })
  OrderID?: string;

  @Prop({ type: Number, default: 0 })
  Price: number;

  @Prop({ type: String, enum: RenewalStatus, required: true })
  Status: RenewalStatus;
}

@Schema({
  timestamps: true,
  versionKey: false,
  collection: 'subscriptions',
})
export class SubscriptionModel {
  @Prop({ type: mongoose.Types.ObjectId, required: true })
  UserID: string;

  @Prop({ type: mongoose.Types.ObjectId, required: true })
  TenantID: string;

  @Prop({ type: Number, required: true, default: 0 })
  Price: number;

  @Prop({ type: String, enum: SubscriptionStatus, required: true })
  Status: SubscriptionStatus;

  @Prop({ type: Date })
  StartDate: Date;

  @Prop({ type: Date })
  EndDate?: Date;

  @Prop({ type: mongoose.Types.ObjectId, required: true })
  PlanID: string;

  @Prop({ type: Boolean, default: false })
  AutoRenew: boolean;

  @Prop({ type: [Renewal], default: [] })
  RenewalLogs: Renewal[];

  @Prop({ type: String, required: true })
  Variant: PricingType;

  @Prop({ type: Number })
  VariantDuration?: number; // for custom duration

  @Prop({ type: String })
  Reason?: string;
}

export const SubscriptionSchema = SchemaFactory.createForClass(SubscriptionModel);

SubscriptionSchema.virtual('Plan', {
  ref: PlanModel.name,
  localField: 'PlanID',
  foreignField: 'PlanID',
  justOne: true,
});

SubscriptionSchema.virtual('User', {
  ref: User.name,
  localField: 'UserID',
  foreignField: 'UserID',
  justOne: true,
});

SubscriptionSchema.virtual('Credits', {
  ref: CreditUsageModel.name,
  localField: '_id',
  foreignField: 'SubscriptionID',
  justOne: false,
});
