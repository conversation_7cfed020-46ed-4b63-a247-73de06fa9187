import { PartialType } from '@nestjs/mapped-types';
import { Flag, PricingStructure } from '../entities/plan-n-subscription.entity';

export class CreatePlanDto {
  Name: string;
  Subtitle: string;
  Description: string;
  Currency: string;
  Features: { Title: string; HelpText?: string }[];
  ProjectIDs: string[];
  Flags: Flag[];
  Tier: string; // "Free" | "Pro" | "Premium" | "Enterprise"
  TenantID: string;
  PricingStructures: PricingStructure[];
}

export class UpdatePlanDto extends PartialType(CreatePlanDto) {
  PlanID: string;
  TenantID: string;
}

export class ListPlansDto {
  PlanIDs?: string[];
  ProjectIDs?: string[];
  TenantID: string;
}
