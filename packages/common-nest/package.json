{"name": "@package/common-nest", "type": "commonjs", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "tsc --watch", "build": "tsc"}, "dependencies": {"@nestjs/common": "^10.0.5", "@nextcampus/common": "^1.0.26", "amqplib": "^0.10.4", "aws-jwt-verify": "^4.0.1", "aws-sdk": "^2.1354.0", "@supercharge/promise-pool": "^3.2.0"}, "devDependencies": {"@tooling/eslint-config": "workspace:tooling", "@tooling/prettier": "workspace:tooling", "@tooling/typescript-config": "workspace:tooling", "typescript": "latest"}}