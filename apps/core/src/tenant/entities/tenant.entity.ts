import { z } from 'zod';
import { IsEnum, IsUrl } from 'class-validator';

export const HeaderPlacing = z.enum(['justify-between', 'justify-end', 'justify-center', 'justify-start']);

export type THeaderPlacing = z.infer<typeof HeaderPlacing>;
export class Personalization {
  ShowSocialCards?: boolean;
  ShowTagline?: boolean;
  ShowMarquee?: boolean;
  ShowWebsiteURL?: boolean;
  MarqueeDirection?: 'vertical' | 'horizontal';
  HeaderPlacing?: THeaderPlacing;
  ShowTenantName?: boolean;
  ShowTenantLogo?: boolean;
}

export const SocialPlatform = z.enum([
  'Facebook',
  'Twitter',
  'LinkedIn',
  'Instagram',
  'YouTube',
  'Discord',
  'Pinterest',
  'Reddit',
  'Snapchat',
  'TikTok',
  'Twitch',
  'WhatsApp',
  'WeChat',
  'Weibo',
  'Xing',
  'Yelp',
  'Other',
]);

export type TSocialPlatform = z.infer<typeof SocialPlatform>;

export class SocialCard {
  @IsEnum(SocialPlatform, { message: 'Invalid Social Platform' })
  Platform: TSocialPlatform;

  @IsUrl()
  URL: string;
}

export class LandingPage {
  ActiveTemplateID: string;
  Templates: {
    TemplateID: string;
    TemplateName: string;
    TemplateContent: Record<string, any>;
  }[];
}

export class Tenant {
  Name: string;
  Logo?: string;
  WebsiteURL?: string;
  Tagline?: string;
  TenantID: string;
  AdminUserID: string;
  WorkSpaceDomain: string;
  SocialCards?: SocialCard[];
  Personalization?: Personalization;
  Pictures?: string[];
}
