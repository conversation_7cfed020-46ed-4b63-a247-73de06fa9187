import { Test, TestingModule } from '@nestjs/testing';
import { ThreadService } from './thread.service';
import { getModelToken } from '@nestjs/mongoose';
import { Thread } from './schemas/thread.schema';
import { Model } from 'mongoose';
import { CreateThreadDto, ThreadDto } from './dto/thread.dto';
import { UnauthorizedException, NotFoundException } from '@nestjs/common';

describe('ThreadService', () => {
  let service: ThreadService;
  let threadModel: Model<Thread>;

  // Sample data for testing
  const mockUserID = 'user123';
  const mockTenantID = 'tenant123';
  const mockProjectID = 'project123';
  const mockThreadId = 'thread123';
  const mockThreadTitle = 'Test Thread';

  // Mock thread data (MongoDB document format)
  const mockThreadData = {
    threadId: mockThreadId,
    ProjectID: mockProjectID,
    title: mockThreadTitle,
    UserID: mockUserID,
    TenantID: mockTenantID,
    messageCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Mock thread DTO (service response format)
  const mockThreadDto: ThreadDto = {
    ThreadID: mockThreadId,
    ProjectID: mockProjectID,
    title: mockThreadTitle,
    userId: mockUserID,
    tenantId: mockTenantID,
    messageCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockThreadDocument = {
    ...mockThreadData,
    save: jest.fn().mockResolvedValue(mockThreadData),
    toObject: jest.fn().mockReturnValue(mockThreadData),
  };

  beforeEach(async () => {
    // Create mock for ThreadModel
    const threadModelMock = {
      new: jest.fn().mockResolvedValue(mockThreadDocument),
      constructor: jest.fn().mockResolvedValue(mockThreadDocument),
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      exec: jest.fn(),
      deleteOne: jest.fn(),
      updateOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ThreadService,
        {
          provide: getModelToken(Thread.name),
          useValue: threadModelMock,
        },
      ],
    }).compile();

    service = module.get<ThreadService>(ThreadService);
    threadModel = module.get<Model<Thread>>(getModelToken(Thread.name));

    // Mock the private toThreadDto method to return the DTO
    jest.spyOn<any, any>(service, 'toThreadDto').mockImplementation(() => mockThreadDto);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createThread', () => {
    it('should successfully create a thread', async () => {
      const createThreadDto: CreateThreadDto = {
        ProjectID: mockProjectID,
        title: mockThreadTitle,
      };

      threadModel.create = jest.fn().mockResolvedValue(mockThreadDocument);

      const result = await service.createThread(mockUserID, mockTenantID, createThreadDto);

      expect(threadModel.create).toHaveBeenCalledWith({
        ProjectID: mockProjectID,
        title: mockThreadTitle,
        UserID: mockUserID,
        TenantID: mockTenantID,
        messageCount: 0,
      });
      expect(result).toEqual(mockThreadDto);
    });

    it('should use default title if not provided', async () => {
      const createThreadDto: CreateThreadDto = {
        ProjectID: mockProjectID,
      };

      threadModel.create = jest.fn().mockResolvedValue({
        ...mockThreadDocument,
        title: expect.any(String),
      });

      await service.createThread(mockUserID, mockTenantID, createThreadDto);

      expect(threadModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          title: expect.any(String),
        })
      );
    });
  });

  describe('getThreads', () => {
    it('should return all threads for a user', async () => {
      const mockThreads = [mockThreadData, { ...mockThreadData, threadId: 'thread456' }];

      threadModel.find = jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockThreads),
        }),
      });

      const result = await service.getThreads(mockUserID);

      expect(threadModel.find).toHaveBeenCalledWith({ UserID: mockUserID });
      expect(result.threads.length).toBe(2);
      expect(result.count).toBe(2);
    });
  });

  describe('getThreadsByProject', () => {
    it('should return threads filtered by project', async () => {
      const mockThreads = [mockThreadData];

      threadModel.find = jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockThreads),
        }),
      });

      const result = await service.getThreadsByProject(mockUserID, mockProjectID);

      expect(threadModel.find).toHaveBeenCalledWith({
        UserID: mockUserID,
        ProjectID: mockProjectID,
      });
      expect(result.threads.length).toBe(1);
      expect(result.count).toBe(1);
    });
  });

  describe('validateThreadOwnership', () => {
    it('should not throw an error if thread belongs to user', async () => {
      threadModel.findOne = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockThreadDocument),
      });

      await expect(service.validateThreadOwnership(mockThreadId, mockUserID)).resolves.toBe(true);
    });

    it('should return false if thread does not belong to user', async () => {
      threadModel.findOne = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          ...mockThreadDocument,
          UserID: 'anotherUser',
        }),
      });

      await expect(service.validateThreadOwnership(mockThreadId, mockUserID)).resolves.toBe(false);
    });

    it('should return false if thread does not exist', async () => {
      threadModel.findOne = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.validateThreadOwnership(mockThreadId, mockUserID)).resolves.toBe(false);
    });
  });

  describe('validateAndGetThread', () => {
    it('should return thread if it belongs to user', async () => {
      threadModel.findOne = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockThreadDocument),
      });

      const result = await service.validateAndGetThread(mockThreadId, mockUserID);

      expect(threadModel.findOne).toHaveBeenCalledWith({ threadId: mockThreadId });
      expect(result).toEqual(mockThreadDto);
    });

    it('should throw UnauthorizedException if thread does not belong to user', async () => {
      threadModel.findOne = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          ...mockThreadDocument,
          UserID: 'anotherUser',
        }),
      });

      await expect(service.validateAndGetThread(mockThreadId, mockUserID)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('updateThreadActivity', () => {
    it('should increment message count and update time', async () => {
      threadModel.updateOne = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue({ acknowledged: true, modifiedCount: 1 }),
      });

      await service.updateThreadActivity(mockThreadId);

      expect(threadModel.updateOne).toHaveBeenCalledWith(
        { threadId: mockThreadId },
        expect.objectContaining({
          $inc: { messageCount: 1 },
          $set: expect.any(Object),
        })
      );
    });
  });

  describe('deleteThread', () => {
    it('should delete thread if it belongs to user', async () => {
      threadModel.deleteOne = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 1 }),
      });

      const result = await service.deleteThread(mockThreadId);

      expect(threadModel.deleteOne).toHaveBeenCalledWith({ threadId: mockThreadId });
      expect(result).toBe(true);
    });

    it('should throw if thread deletion fails', async () => {
      threadModel.deleteOne = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 0 }),
      });

      const result = await service.deleteThread(mockThreadId);
      expect(result).toBe(false);
    });
  });

  describe('updateThreadTitle', () => {
    it('should update thread title if it belongs to user', async () => {
      const newTitle = 'New Thread Title';

      // Mock validateAndGetThread since it's tested separately
      jest.spyOn(service, 'validateAndGetThread').mockResolvedValue(mockThreadDto);

      threadModel.findOneAndUpdate = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          ...mockThreadDocument,
          title: newTitle,
        }),
      });

      const result = await service.updateThreadTitle(mockThreadId, mockUserID, newTitle);

      expect(service.validateAndGetThread).toHaveBeenCalledWith(mockThreadId, mockUserID);
      expect(threadModel.findOneAndUpdate).toHaveBeenCalledWith(
        { threadId: mockThreadId, UserID: mockUserID },
        expect.objectContaining({
          $set: expect.objectContaining({
            title: newTitle,
          }),
        }),
        { new: true }
      );
      expect(result.title).toBe(newTitle);
    });
  });
});
