import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import GenerateID from '../../../common/lib/generate-id';
import { AppTypeEnum } from '../../core/user/entities/user.entity';

@Schema({
  _id: null,
})
class LLMInvocation {
  @Prop({
    type: String,
    default: () => GenerateID.Generate16Hex('llm_inv_'),
  })
  LLMInvocationID: string;

  @Prop({
    type: {
      InputTokens: Number,
      OutputTokens: Number,
      TotalTokens: Number,
    },
  })
  TokenCount: {
    InputTokens: number;
    OutputTokens: number;
    TotalTokens: number;
  };

  @Prop({
    type: String,
  })
  Model: string;

  @Prop({
    type: String,
  })
  Node: string;

  @Prop({
    type: {
      InputTokens: Number,
      OutputTokens: Number,
      TotalTokens: Number,
      InputTokenDetails: {
        AudioTokens: Number,
        CachedTokens: Number,
      },
      OutputTokenDetails: {
        AudioTokens: Number,
        ReasoningTokens: Number,
        AcceptedPredictionTokens: Number,
        RejectedPredictionTokens: Number,
      },
      SystemFingerprint: String,
    },
  })
  Usage: {
    InputTokens: number;
    OutputTokens: number;
    TotalTokens: number;
    InputTokenDetails: {
      AudioTokens: number;
      CachedTokens: number;
    };
    OutputTokenDetails: {
      AudioTokens: number;
      ReasoningTokens: number;
      AcceptedPredictionTokens: number;
      RejectedPredictionTokens: number;
    };
    SystemFingerprint: string;
  };
}

@Schema({
  _id: null,
})
class TokenCounter {
  @Prop({
    type: Number,
    required: true,
  })
  InputTokens: number;

  @Prop({
    type: Number,
    required: true,
  })
  OutputTokens: number;

  @Prop({
    type: Number,
    required: true,
  })
  TotalTokens: number;

  @Prop({
    type: [LLMInvocation],
    default: [],
  })
  LLMInvocations: LLMInvocation[];
}

@Schema({
  collection: 'token_counters',
  timestamps: true,
  versionKey: false,
})
export class TokenCounterModel {
  @Prop({
    type: String,
    required: true,
    default: () => GenerateID.Generate16Hex('grh_inv_'),
  })
  GraphInvocationID: string;

  @Prop({
    type: String,
    required: true,
  })
  ProjectID: string;

  @Prop({
    type: String,
    required: true,
  })
  TenantID: string;

  @Prop({
    type: TokenCounter,
    required: true,
  })
  TokenCount: TokenCounter;

  @Prop({
    type: String,
    enum: ['message', 'lecture', 'pop_quiz', 'embedding'],
    required: true,
  })
  ResourceType: string;

  @Prop({
    type: [String],
    required: true,
  })
  ResourceIDs: string[];

  @Prop({
    type: String,
    required: true,
  })
  UserID: string;
}

export const TokenCounterSchema = SchemaFactory.createForClass(TokenCounterModel);
