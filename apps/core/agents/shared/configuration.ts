import { RunnableConfig } from '@langchain/core/runnables';
import { Annotation } from '@langchain/langgraph';

export const BaseConfigurationAnnotation = Annotation.Root({
  /**
   * Name of embedding model to use. Must be a valid embedding model name.
   */
  embeddingModel: Annotation<string>,

  /**
   * The vector store provider to use for retrieval.
   * Options are 'elastic', 'elastic-local', 'pinecone', 'mongodb', 'chroma'
   */
  retrieverProvider: Annotation<'elastic' | 'elastic-local' | 'pinecone' | 'mongodb' | 'chroma'>,

  /**
   * AAdditional keyword arguments to pass to the search funtion of the retriever
   */
  searchKwargs: Annotation<Record<string, any>>,
});

export function ensureBaseConfiguration(config: RunnableConfig): typeof BaseConfigurationAnnotation.State {
  const configurable = (config?.configurable || {}) as Partial<typeof BaseConfigurationAnnotation.State>;
  return {
    embeddingModel: configurable.embeddingModel || 'openai/text-embedding-3-large',
    retrieverProvider: configurable.retrieverProvider || 'pinecone',
    searchKwargs: configurable.searchKwargs || {},
  };
}
