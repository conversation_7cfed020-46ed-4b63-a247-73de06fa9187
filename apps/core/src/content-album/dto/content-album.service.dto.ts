import { PartialType } from '@nestjs/mapped-types';
import { ContentGroup, TContent } from '../entities/content-album.entity';

export class Content {
  Meta: {
    Title: string;
    Description?: string;
    Transcript?: string; // SRT, URL
    Subtitles?: {
      Language: string;
      Content: string;
    }[]; // SRT
    Attachments?: string[]; // URL
  };
  ContentGroupID?: string; // For Content Group
  Content: Express.Multer.File;
  ContentSubtitles?: Express.Multer.File[];
  Duration?: number;
  SortOrder?: number;
  Scopes?: Record<string, string | boolean | number>;
}

export class CreateContentAlbumDto {
  Name: string;
  Description?: string;
  CoverImage: string;

  ContentGroups: Omit<ContentGroup, 'ContentGroupID'>[];

  Contents: Omit<Content, 'ContentID'>[];

  TenantID: string;
  ProjectID: string;

  Scopes?: Record<string, string | boolean | number>;
}

export class UpdateContentAlbumDto extends PartialType(CreateContentAlbumDto) {
  ContentAlbumID: string;
}

export class ListContentAlbumDto {
  TenantID: string;
  ProjectID?: string;
}

export class CommonContentAlbumDto {
  TenantID: string;
  ContentAlbumID: string;
}

export class CreateContentDto extends Content {
  ContentAlbumID?: string;
  TenantID: string;
}

export class UpdateContentMediaDto {
  ContentAlbumID: string;
  ContentID: string;
  TenantID: string;
  Content: Express.Multer.File;
  ContentSubtitles?: Express.Multer.File[];
}

export class UpdateContentDto extends Content {
  ContentAlbumID?: string;
  TenantID: string;
}

export class CommonContentDto {
  TenantID: string;
  ContentID: string;
  ContentAlbumID: string;
}

export class CreateContentRawDto extends TContent {
  ContentAlbumID: string;
  TenantID: string;
}

export class GenerateContentAlbumDto {
  TenantID: string;
  ProjectID: string;
  SourceID: string;
  Name: string;
  Description?: string;
  CoverImage: string;
}
