"use client";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { useCallback, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Separator } from "@workspace/ui/components/separator";
import { Textarea } from "@workspace/ui/components/textarea";
import { Skeleton } from "@workspace/ui/components/skeleton";
import {
  AlertCircle,
  RefreshCw,
  Loader2,
  CircleHelp,
  BookOpen,
  Book,
  History,
  MessageSquare,
  Clock,
  MessageCircle,
  Plus,
  ArrowDown,
  ChevronDown,
} from "lucide-react";
import { API } from "@/apis/api";
import { toast } from "sonner";
import { useProjectContext } from "@/contexts/project-context";
import { useThreadContext } from "@/contexts/thread-context";
import ChatInput from "@/components/chat/chat-input";
import { cn } from "@workspace/ui/lib/utils";
import { formatDate } from "date-fns";
import TypingText from "@/components/ui/type-text";
import { Blinker } from "next/font/google";
import { useModeContext } from "@/contexts/mode-context";
import { Generate16Hex } from "@/lib/common";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

export default function Page() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const {
    projects,
    isLoading: projectsLoading,
    error: projectsError,
    selectedProject,
    setSelectedProject,
    refetchProjects,
  } = useProjectContext();
  const { refetchThreads, threads } = useThreadContext();
  const { mode, setMode } = useModeContext();

  const [startConversation, setStartConversation] = useState(false);
  const [message, setMessage] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleStartConversation = async (chat: string) => {
    if (!selectedProject || !chat.trim() || isSubmitting) return;
    setIsSubmitting(true);
    router.push(`/thread/new-thread?message=${chat}`);
  };

  const isLoading = authLoading || projectsLoading;

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-svh">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
      </div>
    );
  }

  const GreetingMessages = [
    "Welcome! How can I assist you with your insurance needs today?",
    "Hello there! Need help understanding your policy or filing a claim?",
    "Good to see you! I'm here to answer your insurance-related questions.",
    "Hi! Looking for information on coverage, claims, or renewals?",
    "Welcome back! Let's make sure you're fully covered and informed.",
    "Hello! I can help you review policy details or explain coverage options.",
    "Hi there! Whether it’s health, fire, or EAR insurance — I’ve got you covered.",
    "Good day! Have questions about premiums, claims, or exclusions?",
    "Welcome! I’m here to help you navigate your insurance documents easily.",
    "Hello! Let’s make insurance simple — how can I assist you today?",
    "Hi! I’m ready to support you with any questions related to your policies.",
    "Welcome! I'm here to guide you through your insurance coverage confidently.",
    "Hello again! Let’s ensure you have the right support for your insurance queries.",
    "Good to see you! Ask away — I’m here to help with policy and claim clarifications.",
    "Hi! Let's take the guesswork out of your insurance questions.",
  ];

  const getGreetingMessage = useCallback(() => {
    return (
      GreetingMessages[Math.floor(Math.random() * GreetingMessages.length)] ||
      ""
    );
  }, []);

  const [showFullList, setShowFullList] = useState(projects?.length > 4);

  return (
    <div className="flex flex-col items-center justify-center h-full">
      <div className="flex flex-col items-center  gap-4 w-full">
        <h1 className="text-2xl font-bold">
          Hi, <span className="text-primary">{user?.Name}</span>
        </h1>
        <h2 className="text-lg font-semibold text-center text-secondary-foreground mb-4">
          {getGreetingMessage()}
        </h2>

        {projects?.length > 1 && (
          <>
            <div className="flex items-center gap-2">
              <p className="text-muted-foreground text-md max-w-lg text-center text-sm">
                Select a Topic to start a conversation.
              </p>
              <Tooltip>
                <TooltipTrigger asChild>
                  <CircleHelp size={16} className="cursor-pointer" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">
                    Topics let you chat with your insurance advisor and ask
                    questions related to your policies.
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>

            {!showFullList && (
              <div className="flex flex-wrap gap-2">
                {projects.slice(0, 4).map((project) => (
                  <div
                    key={project.id}
                    className={cn(
                      "flex items-center gap-2 rounded-sm p-4 hover:bg-primary/10 cursor-pointer border border-1",
                      selectedProject?.id === project.id
                        ? "bg-primary/20 border-primary/60"
                        : "bg-transparent"
                    )}
                    onClick={() => setSelectedProject(project)}
                  >
                    <span className="text-sm text-gray-400 hover:text-white">
                      {project.name}
                    </span>
                  </div>
                ))}
              </div>
            )}

            {showFullList && (
              <Select
                value={selectedProject?.id || ""}
                onValueChange={(value) => {
                  const project = projects.find((p) => p.id === value) || null;
                  setSelectedProject(project);
                }}
                disabled={projectsLoading}
              >
                <SelectTrigger className="w-sm ">
                  {projectsLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
                      <span>Loading projects...</span>
                    </div>
                  ) : (
                    <SelectValue placeholder="Select a Project" />
                  )}
                </SelectTrigger>
                <SelectContent>
                  {projectsLoading ? (
                    <div className="p-4 space-y-2">
                      <Skeleton className="h-5 w-full" />
                      <Skeleton className="h-5 w-full" />
                      <Skeleton className="h-5 w-full" />
                    </div>
                  ) : projects.length === 0 ? (
                    <div className="p-2 text-center text-muted-foreground">
                      No projects available
                    </div>
                  ) : (
                    projects.map((project) => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            )}

            {!showFullList && (
              <Button
                variant="ghost"
                className="flex items-center gap-2 text-xs"
                onClick={() => setShowFullList(true)}
                size="sm"
              >
                <ChevronDown size={16} />
                Show Full List
              </Button>
            )}
          </>
        )}

        {isAuthenticated &&
          user &&
          (!startConversation ? (
            <div className="flex flex-col gap-4 w-full items-center justify-center max-w-lg mt-2">
              <ChatInput
                placeholder="Ask anything related to your project"
                isDisabled={isSubmitting}
                onSubmit={(message) => handleStartConversation(message)}
                onMessageChange={(message) => setMessage(message)}
              />

              <div className="flex flex-col items-center justify-center gap-2 mt-2">
                <h3 className="text-sm font-semibold flex items-center gap-2 text-gray-400">
                  <History size={18} />
                  Open Recent
                </h3>
                <div className="flex flex-col gap-[0.1rem]">
                  {threads
                    ?.sort((a, b) => {
                      return (
                        new Date(b.updatedAt || b.createdAt).getTime() -
                        new Date(a.updatedAt || a.createdAt).getTime()
                      );
                    })
                    .slice(0, 4)
                    .map((thread) => (
                      <div
                        key={thread.ThreadID}
                        className="flex items-center gap-2 rounded-lg px-2 py-1 hover:bg-primary/10 cursor-pointer"
                        onClick={() =>
                          router.push(`/thread/${thread.ThreadID}`)
                        }
                      >
                        <MessageCircle size={16} />
                        <span className="text-sm text-gray-400 hover:text-white">
                          {thread.title}
                        </span>
                        <span className="text-xs  gap-1 flex items-center text-muted-foreground">
                          <Clock size={12} />
                          {new Date(
                            thread.updatedAt || thread.createdAt
                          ).toLocaleTimeString()}
                        </span>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="border rounded-2xl w-[700px] p-4 space-y-4">
              {projectsError && (
                <div className="bg-destructive/10 p-3 rounded-md flex items-center gap-2 text-destructive">
                  <AlertCircle size={16} />
                  <span className="flex-1">{projectsError}</span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={refetchProjects}
                    className="h-8 gap-1"
                    disabled={projectsLoading}
                  >
                    <RefreshCw
                      size={14}
                      className={projectsLoading ? "animate-spin" : ""}
                    />
                    Retry
                  </Button>
                </div>
              )}

              <Select
                value={selectedProject?.id || ""}
                onValueChange={(value) => {
                  const project = projects.find((p) => p.id === value) || null;
                  setSelectedProject(project);
                }}
                disabled={projectsLoading || isSubmitting}
              >
                <SelectTrigger className="w-full">
                  {projectsLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
                      <span>Loading projects...</span>
                    </div>
                  ) : (
                    <SelectValue placeholder="Select a Project" />
                  )}
                </SelectTrigger>
                <SelectContent>
                  {projectsLoading ? (
                    <div className="p-4 space-y-2">
                      <Skeleton className="h-5 w-full" />
                      <Skeleton className="h-5 w-full" />
                      <Skeleton className="h-5 w-full" />
                    </div>
                  ) : projects.length === 0 ? (
                    <div className="p-2 text-center text-muted-foreground">
                      No projects available
                    </div>
                  ) : (
                    projects.map((project) => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              <Separator />
              <div className="flex gap-4 flex-col">
                <Textarea
                  placeholder="Enter your message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  disabled={isSubmitting}
                  className="min-h-[100px]"
                />
                <Button
                  onClick={() => handleStartConversation(message)}
                  disabled={
                    !selectedProject ||
                    !message.trim() ||
                    isLoading ||
                    isSubmitting
                  }
                  className="gap-2"
                >
                  {isSubmitting && (
                    <Loader2 size={16} className="animate-spin" />
                  )}
                  {isSubmitting
                    ? "Starting Conversation..."
                    : "Start New Conversation"}
                </Button>
              </div>
            </div>
          ))}
      </div>
    </div>
  );
}
