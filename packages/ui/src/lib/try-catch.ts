// Tailored For API FETCH Based On Backend Response
type Success<T> = {
  data: T;
  errors: null;
};

type Failure<E> = {
  data: null;
  errors: E;
};

type Result<T, E = Error> = Success<T> | Failure<E>;

// Main wrapper function
export async function TryCatch<T, E = Error>(
  promise: Promise<T>,
): Promise<Result<T, E>> {
  try {
    const data = await promise;
    return { data, errors: null };
  } catch (error) {
    const errorJson = await (error as { response: Response })?.response?.json();
    return { data: null, errors: errorJson.errors };
  }
}
