import { Type } from 'class-transformer';
import { <PERSON>Array, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateEmbedDto {
  @IsNotEmpty()
  @IsString()
  ProjectID: string;

  @IsNotEmpty()
  @IsString()
  TenantID: string;

  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  tableOfContentPages: number[];

  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  pageNumberOffset: number;

  @IsNotEmpty()
  @IsString()
  sourceName: string;

  @IsString()
  sourceType: string;

  @IsString()
  language: string;

  @IsString()
  @IsOptional()
  IsSuperEmbed: boolean;

  @IsString()
  @IsOptional()
  version: string;
}
