import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator';

export class GenerateQuestionsBodyDto {
  @IsString()
  @IsNotEmpty()
  ProjectID: string;

  @IsString()
  @IsNotEmpty()
  SourceID: string;

  @IsString()
  @IsNotEmpty()
  ID: string;

  @IsNumber()
  @IsNotEmpty()
  NumberOfQuestions: number;
}
import { OmitType, PartialType } from '@nestjs/mapped-types';
import { PopQuiz } from '../entities/pop-quiz.entity';
import { CreateQuestionDto, GenerateSelfQuizDto, ListQuestionsDto } from './pop-quiz.service.dto';

export class CreatePopQuizRequestDto extends OmitType(PopQuiz, ['PopQuizID', 'TenantID']) {}

export class UpdatePopQuizRequestDto extends PartialType(CreatePopQuizRequestDto) {
  PopQuizID: string;
}

export class ListPopQuizRequestDto {
  PopQuizIDs: string[];
  ProjectID: string;
  TenantID: string;
}

export class CreateQuestionRequestDto extends OmitType(CreateQuestionDto, ['TenantID'] as const) {}

export class CreateQuestionBulkRequestDto {
  Questions: CreateQuestionDto[];
}

export class UpdateQuestionRequestDto extends PartialType(CreateQuestionRequestDto) {
  QuestionID: string;
}

export class ListQuestionsRequestDto extends OmitType(ListQuestionsDto, ['TenantID'] as const) {}

export class GenerateSelfQuizRequestDto extends OmitType(GenerateSelfQuizDto, ['TenantID', 'UserID'] as const) {}
