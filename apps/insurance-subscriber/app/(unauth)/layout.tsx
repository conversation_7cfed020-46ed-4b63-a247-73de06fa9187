"use client";
import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function UnauthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    // If user is authenticated, redirect to home
    if (isAuthenticated && !isLoading) {
      router.push("/");
    }
  }, [isAuthenticated, isLoading, router]);

  return (
    <div>
      {isLoading ? (
        <div className="flex min-h-svh items-center justify-center">
          <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
        </div>
      ) : (
        children
      )}
    </div>
  );
}
