import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PopQuizModel } from './schemas/quiz.schema';
import { CreatePopQuizDto, GeneratePopQuizRandomQuestionsDto, UpdatePopQuizDto } from './dto/pop-quiz.service.dto';
import { QuestionService } from './qustion.service';
import { CommonPopQuizDto, ListPopQuizDto, GetPopQuizDto } from './dto/pop-quiz.service.dto';
import { omit } from 'radash';
import { OnEvent } from '@nestjs/event-emitter';
import { SubscriberStatus } from './entities/pop-quiz.entity';
import { QuestionTypes } from './entities/question.entity';

@Injectable()
export class PopQuizService {
  constructor(
    @InjectModel(PopQuizModel.name) private readonly PopQuizModel: Model<PopQuizModel>,
    private readonly QuestionService: QuestionService
  ) {}

  // Context Aware Question Selection which returns random questions based on the given scopes taking previous pop quiz questions into account
  async GeneratePopQuizRandomQuestions(Params: GeneratePopQuizRandomQuestionsDto) {
    const questions = await this.QuestionService.ListQuestionsIDs({
      ...Params.Scopes,
      TenantID: Params.TenantID,
      ProjectID: Params.ProjectID,
    });
    const prevPopQuizQuestions = await this.PopQuizModel.find({
      Scopes: Params.Scopes,
      TenantID: Params.TenantID,
      ProjectID: Params.ProjectID,
    })
      .distinct('Questions')
      .lean();
    const prevPopQuizQuestionIDs = prevPopQuizQuestions || [];
    const notInPrevPopQuizQuestionIDs = questions.filter((question) => !prevPopQuizQuestionIDs.includes(question));
    if (notInPrevPopQuizQuestionIDs.length < Params.QuestionCount) {
      const randomQuestions = notInPrevPopQuizQuestionIDs
        .sort(() => 0.5 - Math.random())
        .slice(0, Params.QuestionCount);
      return randomQuestions;
    }
    const randomQuestions = notInPrevPopQuizQuestionIDs.sort(() => 0.5 - Math.random()).slice(0, Params.QuestionCount);
    return randomQuestions;
  }

  async GeneratePopQuiz(Params: CreatePopQuizDto) {
    const questions = await this.GeneratePopQuizRandomQuestions({
      ...Params,
      QuestionCount: Params.QuestionCount,
      TenantID: Params.TenantID,
      ProjectID: Params.ProjectID,
      Scopes: Params.Scopes,
    });
    const popQuiz = await this.PopQuizModel.create({
      ...Params,
      Questions: questions,
    });
    return popQuiz;
  }

  async CreatePopQuiz(Params: CreatePopQuizDto) {
    const popQuiz = await this.PopQuizModel.create(Params);
    return popQuiz;
  }

  async UpdatePopQuiz(Params: UpdatePopQuizDto) {
    const popQuiz = await this.PopQuizModel.findOneAndUpdate(
      {
        PopQuizID: Params.PopQuizID,
        TenantID: Params.TenantID,
      },
      Params,
      {
        new: true,
      }
    ).exec();
    return popQuiz;
  }

  async DeletePopQuiz(Params: CommonPopQuizDto) {
    const popQuiz = await this.PopQuizModel.findOneAndDelete({
      PopQuizID: Params.PopQuizID,
      TenantID: Params.TenantID,
      ProjectID: Params.ProjectID,
    }).exec();
    return popQuiz;
  }

  async ListPopQuiz(Params: ListPopQuizDto) {
    const { TenantID, ProjectID } = Params;
    const ScopeValues = omit(Params, ['TenantID', 'ProjectID', 'PopQuizIDs']);
    const ScopeFilter = Object.keys(ScopeValues).reduce(
      (acc, key) => {
        acc[`Scopes.${key}`] = ScopeValues[key];
        return acc;
      },
      {} as Record<string, string>
    );
    const popQuizzes = await this.PopQuizModel.find({
      TenantID,
      ...(ProjectID && { ProjectID }),
      ...ScopeFilter,
      ...(Params.PopQuizIDs && { PopQuizID: { $in: Params.PopQuizIDs } }),
    })
      .populate({
        path: 'QuestionDetails',
        select: '-TenantID -ProjectID -Scopes',
      })
      .lean();
    return popQuizzes;
  }

  async GetPopQuiz(Params: GetPopQuizDto) {
    const popQuiz = await this.PopQuizModel.findOne({
      PopQuizID: Params.PopQuizID,
      TenantID: Params.TenantID,
    })
      .populate('QuestionDetails')
      .lean();
    return popQuiz;
  }

  @OnEvent('popQuiz.response.created')
  async UpdatePopQuizSubscribersStatus(Params: { PopQuizID: string; TenantID: string; UserID: string }) {
    const popQuiz = await this.PopQuizModel.findOne({
      PopQuizID: Params.PopQuizID,
      TenantID: Params.TenantID,
    }).exec();
    const findUser = popQuiz.Subscribers.find((user) => user.UserID === Params.UserID);
    if (findUser) {
      findUser.Status = SubscriberStatus.enum.Submitted;
    }
    if (!findUser) {
      popQuiz.Subscribers.push({
        UserID: Params.UserID,
        Status: SubscriberStatus.enum.Submitted,
      });
    }
    await popQuiz.save();
  }
}
