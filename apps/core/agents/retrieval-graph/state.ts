import { BaseMessage } from '@langchain/core/messages';
import { Annotation, MessagesAnnotation } from '@langchain/langgraph';
import { reduceDocs } from '../shared/state';
import { Document } from '@langchain/core/documents';

/**
 * Represents the input state for the agent.
 * This is a restricted version of the State that defines a narrower interface
 * to the outside world compared to what is maintained internally.
 */
export const InputStateAnnotation = Annotation.Root({
  /**
   * Messages track the primary execution state of the agent.
   * @type {BaseMessage[]}
   * @description
   * Typically accumulates a pattern of Human/AI/Human/AI messages. If combined with a
   * tool-calling ReAct agent pattern, it may follow this sequence:
   * 1. HumanMessage - user input
   * 2. AIMessage with .tool_calls - agent picking tool(s) to use
   * 3. ToolMessage(s) - responses (or errors) from executed tools
   *    (... repeat steps 2 and 3 as needed ...)
   * 4. AIMessage without .tool_calls - agent's unstructured response to user
   * 5. HumanMessage - user's next conversational turn
   *    (... repeat steps 2-5 as needed ...)
   */
  ...MessagesAnnotation.spec,

  /**
   * The tenant ID for scoping document retrieval
   */
  tenantId: Annotation<string>({
    default: () => '',
    value: (old, _new) => _new || old,
  }),

  /**
   * The project ID for scoping document retrieval
   */
  projectId: Annotation<string>({
    default: () => '',
    value: (old, _new) => _new || old,
  }),
});

/**
 * Classifies user query.
 * @typedef {Object} Router
 * @property {string} logic - The logic behind the classification.
 * @property {'more-info' | 'syllabus' | 'wrong-syllabus' | 'exam-prep' | 'general'} type - The type of the query.
 */

type Router = {
  logic: string;
  type: 'more-info' | 'syllabus' | 'wrong-syllabus' | 'exam-prep' | 'general';
};

/**
 * Represents the state of the retrieval graph / agent.
 */
export const AgentStateAnnotation = Annotation.Root({
  ...InputStateAnnotation.spec,

  /**
   * The router's classification of the user's query.
   * @type {Router}
   */
  router: Annotation<Router>({
    default: () => ({ type: 'general', logic: '' }),
    reducer: (existing: Router, newRouter: Router) => ({
      ...existing,
      ...newRouter,
    }),
  }),

  /**
   * Whether the query was verified to be in the syllabus.
   * @type {boolean}
   */
  syllabusVerified: Annotation<boolean>({
    default: () => false,
    value: (old, _new) => (_new !== undefined ? _new : old),
  }),

  /**
   * Documents retrieved during syllabus verification.
   * @type {Document[]}
   */
  verificationDocuments: Annotation<Document<Record<string, any>>[]>({
    default: () => [],
    reducer: reduceDocs,
  }),

  /**
   * A list of steps in the research plan.
   * @type {string[]}
   */
  steps: Annotation<string[]>,

  /**
   * Populated by the retriever. This is a list of documents that the agent can reference.
   * Documents are scoped by tenant ID and project ID.
   * @type {Document[]}
   */
  documents: Annotation<
    Document<Record<string, any>>[],
    Document<Record<string, any>>[] | { [key: string]: any }[] | string[] | string | 'delete'
  >({
    default: () => [],
    reducer: reduceDocs,
  }),

  // Additional attributes can be added here as needed
  // Examples might include retrieved documents, extracted entities, API connections, etc.
});
