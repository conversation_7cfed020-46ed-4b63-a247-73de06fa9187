from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field
from app.models.enums import DocumentStatus


class SubSection(BaseModel):
    SubSectionName: str
    PageNumber: int
    DocumentPageNumber: int


class PageRange(BaseModel):
    From: int
    To: int


class Section(BaseModel):
    SectionID: str
    SectionName: str
    ChunkIDs: List[str] = []
    Description: Optional[str] = None
    SubSections: Optional[List[SubSection]] = None
    PageNumber: PageRange
    DocumentPageNumber: PageRange


class SectionHighlight(BaseModel):
    SectionName: str
    Highlight: str


class SourceSummary(BaseModel):
    Title: str
    Summary: str
    MainTopics: List[str]
    SectionHighlights: List[SectionHighlight]


class Source(BaseModel):
    SourceID: str
    SourceName: str
    Sections: List[Section] = []
    Status: DocumentStatus = DocumentStatus.UPLOADING
    TenantID: str
    ProjectID: str
    AWSKey: str
    ErrorMessage: Optional[str] = None
    PageNumberOffset: int = 0
    createdAt: datetime = Field(default_factory=datetime.utcnow)
    updatedAt: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        collection = "ind_source"
        json_schema_extra = {
            "example": {
                "SourceID": "src_123",
                "SourceName": "Example Document",
                "Sections": [],
                "Status": "uploading",
                "TenantID": "tenant_123",
                "ProjectID": "proj_123",
                "AWSKey": "aws_key_123",
            }
        }
