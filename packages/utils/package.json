{"name": "@package/utils", "type": "commonjs", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "tsc --watch", "build": "tsc"}, "dependencies": {"@supercharge/promise-pool": "^3.2.0", "aws-sdk": "^2.1687.0", "flat": "^6.0.1", "lodash": "^4.17.21", "mongodb": "^6.8.0", "ua-parser-js": "^1.0.38"}, "devDependencies": {"@tooling/eslint-config": "workspace:tooling", "@tooling/prettier": "workspace:tooling", "@tooling/typescript-config": "workspace:tooling", "@types/node": "^22.14.0", "typescript": "latest"}}