from app.services.source_service import SourceService
from app.models.enums import DocumentStatus
from agents.embedding_agent.py_mu_pdf.graph import graph
from agents.embedding_agent.state import EmbeddingState
from typing import TypedDict
from agents.embedding_agent.py_mu_pdf.state import (
    Section,
    Page,
    Chunk as Graph<PERSON>hunk,
    EnhancedChunks,
    SummarizedChunks,
)
# Import academic embedding components
from agents.embedding_agent.academic.py_mu_pdf.graph import graph as academic_graph
from agents.embedding_agent.academic.py_mu_pdf.state import (
    InputState as AcademicInputState,
    State as AcademicState,
    Page as AcademicPage,
    Toc,
    ChapterWiseRawPages,
    SummarizedChunks as AcademicSummarizedChunks,
    ChapterSummaries,
    BookSummary
)
from app.services.chunk_service import ChunkService
from app.models.chunk import Chunk as DBChunk

source_service = SourceService()
chunk_service = ChunkService()


class OutputState(TypedDict):
    pages: list[Page]
    sections: list[Section]
    chunks: list[GraphChunk]
    enhanced_chunks: list[EnhancedChunks]
    summarized_chunks: list[SummarizedChunks]


class AcademicOutputState(TypedDict):
    pages: list[AcademicPage]
    toc: list[Toc]
    chunked_pages: list[AcademicPage]
    chapter_wise_raw_pages: list[ChapterWiseRawPages]
    summarized_chunks: list[AcademicSummarizedChunks]
    chapter_summaries: list[ChapterSummaries]
    book_summary: BookSummary
    document_ids: list[str]


async def process_embed_file(
    source_content: bytes,
    source_id: str,
    source_type: str,
    language: str,
    page_number_offset: int,
    user_id: str,
    tenant_id: str,
    project_id: str,
    source_name: str,
):
    try:
        # Update status to processing
        print("Updating status to processing")
        await source_service.update_status(source_id, DocumentStatus.PROCESSING)

        # Create input state for the graph
        input_state = EmbeddingState(
            source_content=source_content,
            source_id=source_id,
            source_name=source_name,
            source_type=source_type,
            tenant_id=tenant_id,
            project_id=project_id,
            user_id=user_id,
            language=language,
            page_number_offset=page_number_offset,
        )

        # Invoke the graph with the input state
        print("Invoking the graph")
        output_state: OutputState = graph.invoke(input_state)

        # Convert graph chunks to database chunks
        db_chunks = [
            DBChunk(
                ChunkID=chunk.chunk_id,
                SourceID=source_id,
                Sections=[
                    {
                        "SectionID": section.section_id,
                        "SectionName": section.section_name,
                        "SubSections": [
                            {"SubSectionName": sub.subsection_name}
                            for sub in section.subsections
                        ],
                    }
                    for section in chunk.sections
                ],
                ChunkNumber=chunk.chunk_number,
                PageNumber=chunk.metadata.page_number + page_number_offset,
                DocumentPageNumber=chunk.metadata.page_number,
                Summary=chunk.summarized_content,
                Content=chunk.chunk_content,
                TenantID=tenant_id,
                ProjectID=project_id,
            )
            for chunk in output_state["summarized_chunks"]
        ]

        # Save chunks to database
        await chunk_service.create_chunks(db_chunks)

        # Update source with sections and chunks
        await source_service.update_source(
            source_id=source_id,
            update_data={
                "Sections": [
                    {
                        "SectionID": section.section_id,
                        "SectionName": section.section_name,
                        "Description": section.section_description,
                        "SubSections": [
                            {
                                "SubSectionName": sub.subsection_name,
                                "PageNumber": sub.page_number + page_number_offset,
                                "DocumentPageNumber": sub.page_number,
                            }
                            for sub in section.subsections
                        ],
                        "PageNumber": {
                            "From": section.page_numbers.start_page_number
                            + page_number_offset,
                            "To": section.page_numbers.end_page_number
                            + page_number_offset,
                        },
                        "DocumentPageNumber": {
                            "From": section.page_numbers.start_page_number,
                            "To": section.page_numbers.end_page_number,
                        },
                    }
                    for section in output_state["sections"]
                ],
                "SourceSummary": {
                    "Title": source_name,
                    "Summary": "Document processed successfully",
                    "MainTopics": [
                        section.section_name for section in output_state["sections"]
                    ],
                    "SectionHighlights": [
                        {
                            "SectionName": section.section_name,
                            "Highlight": section.section_description,
                        }
                        for section in output_state["sections"]
                    ],
                },
                "PageNumberOffset": page_number_offset,
            },
        )

        # Update status to completed
        await source_service.update_status(source_id, DocumentStatus.COMPLETED)
        print("Source updated completed")

    except Exception as e:
        # Update status to failed with error message
        await source_service.update_status(
            source_id, DocumentStatus.FAILED, error_message=str(e)
        )
        raise e


async def process_academic_embed_file(
    source_content: bytes,
    source_id: str,
    source_type: str,
    language: str,
    page_number_offset: int,
    user_id: str,
    tenant_id: str,
    project_id: str,
    source_name: str,
    toc_pages: list[int],
):
    try:
        # Update status to processing
        print("Updating status to processing (Academic)")
        await source_service.update_status(source_id, DocumentStatus.PROCESSING)

        # Create input state for the academic graph
        input_state = AcademicInputState(
            source_content=source_content,
            source_id=source_id,
            source_name=source_name,
            source_type=source_type,
            tenant_id=tenant_id,
            project_id=project_id,
            user_id=user_id,
            language=language,
            page_number_offset=page_number_offset,
            toc_pages=toc_pages,
        )

        # Invoke the academic graph with the input state
        print("Invoking the academic graph")
        output_state = academic_graph.invoke(input_state)

        # Convert academic chunks to database chunks - use dictionary access
        db_chunks = []
        summarized_chunks = output_state.get("summarized_chunks", [])
        chunked_pages = output_state.get("chunked_pages", [])
        toc = output_state.get("toc", [])
        
        for chunk in summarized_chunks:
            # Find the corresponding chunked page for additional metadata
            chunked_page = next(
                (page for page in chunked_pages 
                 if getattr(page, 'chunk_id', '') == chunk.chunk_id),
                None
            )
            
            # Find the chapter for this chunk
            chapter = next(
                (toc_entry for toc_entry in toc 
                 if toc_entry.chapter_id == chunk.chapter_id),
                None
            )
            
        #     db_chunk = DBChunk(
        #         ChunkID=chunk.chunk_id,
        #         SourceID=source_id,
        #         Sections=[
        #             {
        #                 "SectionID": chunk.chapter_id,
        #                 "SectionName": chapter.chapter_name if chapter else "Unknown Chapter",
        #                 "SubSections": [
        #                     {"SubSectionName": chunk.topic}
        #                 ],
        #             }
        #         ] if chunk.chapter_id else [],
        #         ChunkNumber=chunk.chunk_number,
        #         PageNumber=(chunked_page.metadata.page_number if chunked_page else 0) + page_number_offset,
        #         DocumentPageNumber=chunked_page.metadata.page_number if chunked_page else 0,
        #         Summary=chunk.summary,
        #         Content=chunked_page.content if chunked_page else "",
        #         TenantID=tenant_id,
        #         ProjectID=project_id,
        #     )
        #     db_chunks.append(db_chunk)

        # # Save chunks to database
        # if db_chunks:
        #     await chunk_service.create_chunks(db_chunks)

        # Get other state data with dictionary access
        chapter_summaries = output_state.get("chapter_summaries", [])
        book_summary = output_state.get("book_summary", None)
        document_ids = output_state.get("document_ids", [])

        # Update source with chapters and book summary
        # await source_service.update_source(
        #     source_id=source_id,
        #     update_data={
        #         "Sections": [
        #             {
        #                 "SectionID": toc_entry.chapter_id,
        #                 "SectionName": toc_entry.chapter_name or "Unknown Chapter",
        #                 "Description": next(
        #                     (summary.summary for summary in chapter_summaries 
        #                      if summary.chapter_id == toc_entry.chapter_id),
        #                     "No description available"
        #                 ),
        #                 "SubSections": [],
        #                 "PageNumber": {
        #                     "From": (toc_entry.page_from or 0) + page_number_offset,
        #                     "To": (toc_entry.page_to or 0) + page_number_offset,
        #                 },
        #                 "DocumentPageNumber": {
        #                     "From": toc_entry.document_page_from or 0,
        #                     "To": toc_entry.document_page_to or 0,
        #                 },
        #             }
        #             for toc_entry in toc
        #         ],
        #         "SourceSummary": {
        #             "Title": book_summary.title if book_summary else source_name,
        #             "Summary": book_summary.summary if book_summary else "No summary available",
        #             "MainTopics": book_summary.main_topics if book_summary else [],
        #             "SectionHighlights": [
        #                 {
        #                     "SectionName": highlight.chapter_name,
        #                     "Highlight": highlight.highlight,
        #                 }
        #                 for highlight in (book_summary.chapter_highlights if book_summary else [])
        #             ],
        #         },
        #         "PageNumberOffset": page_number_offset,
        #     },
        # )

        # # Update status to completed
        # await source_service.update_status(source_id, DocumentStatus.COMPLETED)
        print("Academic source processing completed")

        return {
            "chunkedDocs": [
                {
                    "pageContent": chunked_page.page_content,
                    "metadata": {
                        "page_number": chunked_page.metadata.page_number,
                        "chunk": {
                            "chunk_id": chunked_page.metadata.chunk.chunk_id,
                            "chunk_number": chunked_page.metadata.chunk.chunk_number,
                            "document_page_number": chunked_page.metadata.chunk.document_page_number,
                            "page_number": chunked_page.metadata.chunk.page_number
                        },
                        "source": {
                            "source_id": chunked_page.metadata.source.source_id,
                            "source_name": chunked_page.metadata.source.source_name
                        },
                        "chapter": {
                            "chapter_id": chunked_page.metadata.chapter.chapter_id,
                            "chapter_number": chunked_page.metadata.chapter.chapter_number,
                            "chapter_name": chunked_page.metadata.chapter.chapter_name,
                            "page_from": chunked_page.metadata.chapter.page_from,
                            "page_to": chunked_page.metadata.chapter.page_to,
                            "document_page_from": chunked_page.metadata.chapter.document_page_from,
                            "document_page_to": chunked_page.metadata.chapter.document_page_to
                        },
                        "indexPage": chunked_page.metadata.indexPage,
                        "projectId": chunked_page.metadata.projectId,
                        "tenantId": chunked_page.metadata.tenantId
                    }
                }
                for chunked_page in chunked_pages
            ],
            "summarizedChunks": [
                {
                    "chunk_id": chunk.chunk_id,
                    "chunk_number": chunk.chunk_number,
                    "chapter_id": chunk.chapter_id,
                    "summary": chunk.summary,
                    "topic": chunk.topic,
                    "subtopic": chunk.subtopic
                }
                for chunk in summarized_chunks
            ],
            "chapterSummaries": [
                {
                    "chapter_id": summary.chapter_id,
                    "chapter_name": summary.chapter_name,
                    "summary": summary.summary,
                    "topics": summary.topics
                }
                for summary in chapter_summaries
            ],
            "chapterWiseTopics": [
                {
                    "chapter_id": chapter_topic.chapter_id,
                    "topics": [
                        {
                            "chapter_name": topic.chapter_name,
                            "topic": topic.topic,
                            "page_number": topic.page_number,
                            "sub_topics": topic.sub_topics
                        }
                        for topic in chapter_topic.topics
                    ]
                }
                for chapter_topic in output_state.get("chapter_wise_topics", [])
            ],
            "bookSummary": {
                "title": book_summary.title,
                "summary": book_summary.summary,
                "main_topics": book_summary.main_topics,
                "chapter_highlights": [
                    {
                        "chapter_name": highlight.chapter_name,
                        "highlight": highlight.highlight
                    }
                    for highlight in book_summary.chapter_highlights
                ]
            } if book_summary else None,
            "toc": [
                {
                    "chapter_id": toc_entry.chapter_id,
                    "chapter_number": toc_entry.chapter_number,
                    "chapter_name": toc_entry.chapter_name,
                    "page_from": toc_entry.page_from,
                    "page_to": toc_entry.page_to,
                    "document_page_from": toc_entry.document_page_from,
                    "document_page_to": toc_entry.document_page_to
                }
                for toc_entry in toc
            ],
            "documentIds": document_ids,
        }

    except Exception as e:
        # Update status to failed with error message
        await source_service.update_status(
            source_id, DocumentStatus.FAILED, error_message=str(e)
        )
        print(f"Academic embedding failed: {str(e)}")
        raise e
