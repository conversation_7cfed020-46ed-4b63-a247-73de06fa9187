import { IsEnum, MinLength } from 'class-validator';
import { AudioConfig, Status, TTSParams, TTSProviders } from '../entities/tt.entity';

export class NewProcessDto {
  @MinLength(3)
  Name: string;

  @MinLength(3)
  Summary: string;

  @MinLength(10)
  SpeechText: string;

  AudioConfig: AudioConfig;

  @IsEnum(TTSProviders.options)
  Provider: TTSProviders;

  TTSParams: TTSParams;
  Scopes: Record<string, string>;
  TenantID: string;
}

export class GenerateCostEstimationDto {
  Provider: TTSProviders;
  Text: string;
  ModelName: string;
  [key: string]: string | number | boolean | undefined;
}

export class BaseProcessParams {
  ProcessID: string;
  TenantID: string;
}

export class ListProcessesDto {
  TenantID: string;
  Status?: Status;
  Provider?: TTSProviders;
  [key: string]: string | number | boolean | undefined;
}

export class QuickCreditEstimationDto {
  Provider: TTSProviders;
  Text: string;
  ModelName: string;
}
