"use client";

import { useTTS } from "@/contexts/tts-context";
import { Switch } from "@workspace/ui/components/switch";
import { Volume2, VolumeX } from "lucide-react";
import { Label } from "@workspace/ui/components/label";

export default function TTSAutoReadToggle() {
  const { autoRead, toggleAutoRead } = useTTS();

  return (
    <div className="flex items-center gap-2">
      {autoRead ? (
        <Volume2 size={18} className="text-primary" />
      ) : (
        <VolumeX size={18} className="text-muted-foreground" />
      )}
      <Label htmlFor="auto-read" className="text-xs md:block hidden">
        Auto-read
      </Label>
      <Switch
        id="auto-read"
        checked={autoRead}
        onCheckedChange={toggleAutoRead}
        aria-label="Toggle auto-read"
      />
    </div>
  );
}
