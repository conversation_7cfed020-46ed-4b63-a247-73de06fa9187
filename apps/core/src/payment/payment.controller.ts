import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { ICreateOrder, IVerifyOrder } from './dto/order.controller.dto';

@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Post('order')
  CreateOrder(@Body() body: ICreateOrder) {
    return this.paymentService.CreateOrder(body);
  }

  @Post('order/verify')
  VerifyOrder(@Body() body: IVerifyOrder) {
    const order = this.paymentService.VerifyOrder(body);
    return {
      message: 'Order verified successfully',
      ack: order,
    };
  }
}
