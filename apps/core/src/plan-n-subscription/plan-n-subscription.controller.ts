import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { max, sum } from 'radash';
import { TenantID, User } from '../../common/decorators/core.decorators';
import { AuthAdminGuard, AuthSubscriberGuard, SkipAuth } from '../../common/guards/auth.guard';
import { ContentAlbumService } from '../content-album/content-album.service';
import { TUser } from '../core/user/entities/user.entity';
import { UserService } from '../core/user/user.service';
import { OrderStatus } from '../payment/entities/payment.entity';
import { PaymentService } from '../payment/payment.service';
import { TTSService } from '../tts/tts.service';
import { CreatePlanReqBodyDto, ListPlansReqBodyDto, UpdatePlanReqBodyDto } from './dto/plan.controller.dto';
import {
  CreateSubscriptionReqBodyDto,
  ListSubscriptionsReqBodyDto,
  SignUpReqBodyDto,
  UpdateSubscriptionReqBodyDto,
  UpgradeSubscriptionReqBodyDto,
} from './dto/subscription.controller.dto';
import {
  CreateTierGroupDto,
  ListTierGroupsDto,
  ListTierGroupsParamsDto,
  UpdateTierGroupDto,
} from './dto/tier-group.service.dto';
import { RenewalStatus, SubscriptionStatus } from './entities/plan-n-subscription.entity';
import { PlanNSubscriptionService } from './plan-n-subscription.service';
import { TenantService } from '../tenant/tenant.service';

@UseGuards(AuthAdminGuard)
@Controller('')
export class PlanNSubscriptionController {
  constructor(private readonly PlanSubService: PlanNSubscriptionService) {}

  @Post('/plan')
  async CreatePlan(@Body() body: CreatePlanReqBodyDto, @TenantID() TenantID: string) {
    const plan = await this.PlanSubService.CreatePlan({ ...body, TenantID });
    return {
      message: 'Plan created successfully',
      ack: plan,
    };
  }

  @Patch('/plan/:PlanID')
  async UpdatePlan(@Param('PlanID') PlanID: string, @Body() body: UpdatePlanReqBodyDto, @TenantID() TenantID: string) {
    const plan = await this.PlanSubService.UpdatePlan({ ...body, TenantID, PlanID });
    return {
      message: 'Plan updated successfully',
      ack: plan,
    };
  }

  @Get('/plan')
  async ListPlans(@Query() query: ListPlansReqBodyDto, @TenantID() TenantID: string) {
    const plans = await this.PlanSubService.ListPlans({ ...query, TenantID });
    return plans;
  }

  @Delete('/plan/:PlanID')
  async DeletePlan(@Param('PlanID') PlanID: string, @TenantID() TenantID: string) {
    const plan = await this.PlanSubService.DeletePlan({ PlanID, TenantID });
    return {
      message: 'Plan deleted successfully',
      ack: plan,
    };
  }

  @Post('/subscription')
  async CreateSubscription(@Body() body: CreateSubscriptionReqBodyDto, @TenantID() TenantID: string) {
    console.log('CreateSubscription', body);
    const subscription = await this.PlanSubService.CreateSubscription({ ...body, TenantID });
    if (!body.SuperUser) {
      const getPlan = await this.PlanSubService.GetPlanByID({ PlanID: subscription.PlanID });
      const planVariant = getPlan.PricingStructures.find((ps) => ps.Type === subscription.Variant);
      await this.PlanSubService.AddRenewalLog({
        SubscriptionID: subscription._id.toString(),
        Renewal: {
          OrderID: '',
          FromDate: subscription.StartDate,
          ToDate: subscription.EndDate,
          Price: planVariant?.Price,
          Status: subscription?.Status === SubscriptionStatus.Active ? RenewalStatus.Paid : RenewalStatus.Pending,
          Type: subscription.Variant,
        },
      });
    }
    return {
      message: 'Subscription created successfully',
      ack: subscription,
    };
  }

  @Patch('/subscription/:SubscriptionID')
  async UpdateSubscription(
    @Param('SubscriptionID') SubscriptionID: string,
    @Body() body: UpdateSubscriptionReqBodyDto,
    @TenantID() TenantID: string
  ) {
    const subscription = await this.PlanSubService.UpdateSubscription({ ...body, TenantID, SubscriptionID });
    return {
      message: 'Subscription updated successfully',
      ack: subscription,
    };
  }

  @Get('/subscription/:SubscriptionID')
  async GetSubscription(@Param('SubscriptionID') SubscriptionID: string, @TenantID() TenantID: string) {
    return await this.PlanSubService.GetSubscription({ SubscriptionID });
  }

  @Delete('/subscription/:SubscriptionID')
  async DeleteSubscription(@Param('SubscriptionID') SubscriptionID: string, @TenantID() TenantID: string) {
    const subscription = await this.PlanSubService.DeleteSubscription({ SubscriptionID, TenantID });
    return {
      message: 'Subscription deleted successfully',
      ack: subscription,
    };
  }

  @Get('/subscription')
  async ListSubscriptions(@Query() query: ListSubscriptionsReqBodyDto, @TenantID() TenantID: string) {
    return await this.PlanSubService.ListSubscriptions({ ...query, TenantID });
  }
}

@UseGuards(AuthSubscriberGuard)
@Controller('subscription')
export class PlanNSubscriptionSubscriberController {
  constructor(
    private readonly PlanSubService: PlanNSubscriptionService,
    private readonly TTSService: TTSService,
    private readonly ContentAlbumService: ContentAlbumService,
    private readonly UserService: UserService,
    private readonly PaymentService: PaymentService
  ) {}

  private CalcDuration(Type: string, StartDate: Date, Duration: number) {
    switch (Type) {
      case 'Daily':
        return new Date(new Date(StartDate).setDate(StartDate.getDate() + 1));
      case 'Weekly':
        return new Date(new Date(StartDate).setDate(StartDate.getDate() + 7));
      case 'Monthly':
        return new Date(new Date(StartDate).setMonth(StartDate.getMonth() + 1));
      case 'Yearly':
        return new Date(new Date(StartDate).setFullYear(StartDate.getFullYear() + 1));
      default:
        return new Date(new Date(StartDate).setDate(StartDate.getDate() + Duration));
    }
  }

  @SkipAuth()
  @Post('sign-up')
  async SignUp(@Body() body: SignUpReqBodyDto) {
    const userByEmail = await this.UserService.GetUserByEmail(body.Email);
    if (userByEmail) {
      throw new UnauthorizedException('User already exists');
    }

    // Get Plan and Pricing Config
    const plan = await this.PlanSubService.GetPlanByID({ PlanID: body.PlanID });

    // Create User account
    const user = await this.UserService.Create({ ...body, UserType: 'Subscriber', TenantID: plan.TenantID });

    const planVariant = plan.PricingStructures.find((ps) => ps.Type === body.Variant);

    const noPaymentInit = planVariant?.Price === 0;
    const SubStartDate = new Date();
    const SubEndDate = this.CalcDuration(planVariant?.Type, SubStartDate, planVariant?.Duration);

    // Create Subscription
    const subscription = await this.PlanSubService.CreateSubscription({
      UserID: user.UserID,
      TenantID: user.TenantID,
      PlanID: plan.PlanID,
      Price: planVariant?.Price,
      Status: noPaymentInit ? SubscriptionStatus.Active : SubscriptionStatus.Pending,
      StartDate: SubStartDate,
      EndDate: SubEndDate,
      AutoRenew: noPaymentInit,
      Variant: body.Variant,
      VariantDuration: plan.PricingStructures.find((ps) => ps.Type === body.Variant)?.Duration,
    });

    if (noPaymentInit) {
      await this.PlanSubService.AddRenewalLog({
        SubscriptionID: subscription._id.toString(),
        Renewal: {
          FromDate: SubStartDate,
          ToDate: SubEndDate,
          OrderID: 'no_capture',
          Price: planVariant?.Price,
          Status: RenewalStatus.Paid,
          Type: subscription.Variant,
        },
      });
    }

    return {
      message: 'Subscription created successfully',
      ack: subscription,
    };
  }

  @Post('payment/:SubscriptionID')
  async SubscriptionPayment(@Param('SubscriptionID') SubscriptionID: string, @User() User: TUser) {
    const subscription = await this.PlanSubService.RevalidateSubscription({ SubscriptionID });
    if (subscription.UserID !== User.UserID) {
      throw new UnauthorizedException('Unauthorized');
    }

    if (subscription.Status === SubscriptionStatus.Active) {
      throw new BadRequestException('Subscription is already active');
    }

    const SubStartDate = new Date();
    const SubEndDate = this.CalcDuration(subscription.Variant, SubStartDate, subscription.VariantDuration);

    const IdempotentKey = `${subscription._id}-${SubStartDate.toDateString()}-${SubEndDate.toDateString()}`;

    const FinalPrice = max([
      subscription.Plan.PricingStructures.find((ps) => ps.Type === subscription.Variant)?.Price,
      subscription.Price,
    ]);

    // for handling when subscription is free
    if (FinalPrice === 0) {
      // Check if renewal log already exists
      const findRenewalLog = subscription.RenewalLogs.find((rl) => rl.FromDate === SubStartDate);

      if (findRenewalLog) {
        throw new BadRequestException('Subscription is already active for this period');
      }

      const updatedSubscription = await this.PlanSubService.UpdateSubscription({
        SubscriptionID: subscription._id.toString(),
        PlanID: subscription.PlanID,
        Variant: subscription.Variant,
        Status: SubscriptionStatus.Active,
        TenantID: subscription.TenantID,
      });

      // add renewal log
      await this.PlanSubService.AddRenewalLog({
        SubscriptionID: subscription._id.toString(),
        Renewal: {
          FromDate: SubStartDate,
          ToDate: SubEndDate,
          OrderID: 'no_capture',
          Price: FinalPrice,
          Status: RenewalStatus.Paid,
          Type: subscription.Variant,
        },
      });

      return {
        message: 'Subscription updated successfully',
        ack: updatedSubscription,
      };
    }

    const CreatePayment = await this.PaymentService.CreateOrder({
      Amount: FinalPrice,
      TenantID: subscription.TenantID,
      IdempotentKey,
      Scope: subscription.UserID,
      UserDetails: {
        Email: User.Email,
        Mobile: User.PhoneNumber,
        UserID: User.UserID,
      },
    });

    if (CreatePayment.Status === OrderStatus.enum.Success) {
      throw new BadRequestException('Order already paid');
    }

    await this.PlanSubService.AddRenewalLog({
      SubscriptionID: subscription._id,
      Renewal: {
        FromDate: SubStartDate,
        ToDate: SubEndDate,
        OrderID: CreatePayment.OrderID,
        Price: FinalPrice,
        Status: RenewalStatus.Pending,
        Type: subscription.Variant,
      },
    });

    return {
      message: 'Order created successfully',
      ack: CreatePayment,
    };
  }

  @Put('upgrade')
  async UpgradeSubscription(@User() user: TUser, @Body() body: UpgradeSubscriptionReqBodyDto) {
    const { SubscriptionID, PlanID, Variant, ForceCancel } = body;
    const subscription = await this.PlanSubService.GetSubscription({ SubscriptionID });
    const plan = await this.PlanSubService.GetPlanByID({ PlanID });

    // Upgrade plan should not be trail plan
    if (!plan || plan.MarkAsTrailPlan) {
      throw new NotFoundException('Plan not found');
    }

    const planVariant = plan.PricingStructures.find((ps) => ps.Type === Variant);
    const SubStartDate = new Date();
    const SubEndDate = this.CalcDuration(planVariant?.Type, SubStartDate, planVariant?.Duration);

    const userSubscriptions = await this.PlanSubService.ListSubscriptions({
      UserIDs: [user.UserID],
      TenantID: subscription.TenantID,
    });

    // Check For Credit
    const remainingCredit = sum(subscription.Credits, (sub) => sub.RemainingCredit);

    // Check Current Subscription Validity [if active or not expired]
    if (
      (subscription.Status === SubscriptionStatus.Active && !ForceCancel) ||
      (subscription.EndDate && new Date(subscription.EndDate) > new Date() && !ForceCancel)
    ) {
      throw new BadRequestException('Subscription is already active or not expired');
    }

    // -> If in credit -> Upgrade the subscription [todo]

    // -> If subscription is expired -> Upgrade the subscription
    const isSubscriptionForPlan = userSubscriptions.some((sub) => sub.PlanID === PlanID && sub.Variant === Variant);
    if (isSubscriptionForPlan) {
      throw new BadRequestException('Selected Plan Subscription already exists for this plan');
    }

    // -> If out of credit ->  Cancel the subscription
    if (remainingCredit <= 0 || (remainingCredit > 0 && ForceCancel)) {
      await this.PlanSubService.CancelSubscription({
        SubscriptionID: subscription._id.toString(),
        TenantID: subscription.TenantID,
        Reason: 'Upgrade Subscription',
      });
    }

    const upgradeSubscription = await this.PlanSubService.CreateSubscription({
      UserID: user.UserID,
      TenantID: user.TenantID,
      PlanID: PlanID,
      Price: planVariant?.Price,
      Status: SubscriptionStatus.Pending,
      StartDate: SubStartDate,
      EndDate: SubEndDate,
      AutoRenew: planVariant?.Price && planVariant?.Price === 0,
      Variant,
      VariantDuration: plan.PricingStructures.find((ps) => ps.Type === Variant)?.Duration,
    });

    return await this.SubscriptionPayment(upgradeSubscription._id.toString(), user);
  }

  @Get('me/subs')
  async GetMySubscription(@TenantID() TenantID: string, @User() User: TUser) {
    const subs = await this.PlanSubService.ListSubscriptions({ TenantID, UserIDs: [User.UserID] });
    // expired subs
    const expiredSubs = subs.filter((sub) => sub.EndDate < new Date() && sub.AutoRenew && sub.Price === 0);
    for (const sub of expiredSubs) {
      await this.PlanSubService.AddRenewalLog({
        SubscriptionID: sub._id.toString(),
        Renewal: {
          FromDate: new Date(),
          ToDate: this.CalcDuration(sub.Variant, new Date(), sub.VariantDuration),
          OrderID: 'no_capture',
          Price: sub.Price,
          Status: RenewalStatus.Paid,
          Type: sub.Variant,
        },
      });
    }
    return subs;
  }

  @Get('me/projects')
  async GetMyProjectSubscription(@TenantID() TenantID: string, @User() User: TUser) {
    const subscriptions = await this.PlanSubService.ListSubscriptions({ TenantID, UserIDs: [User.UserID] });
    const planIDs = subscriptions
      .filter((sub) => sub.Status !== 'Cancelled')
      .map((subscription) => subscription.Plan.PlanID);

    if (!planIDs.length) {
      return [];
    }

    const projects = (await this.PlanSubService.ListPlans({ TenantID, PlanIDs: planIDs }))
      .map((plan) => plan.Projects)
      .flat();
    return projects;
  }

  async AuthorizeProject(Params: { UserID: string; TenantID: string; ProjectID: string }) {
    const subscriptions = await this.PlanSubService.ListSubscriptions({
      UserIDs: [Params.UserID],
      TenantID: Params.TenantID,
    });
    const planIDs = subscriptions
      .filter((sub) => sub.Status !== 'Cancelled')
      .map((subscription) => subscription.Plan.PlanID);
    const projects = (await this.PlanSubService.ListPlans({ TenantID: Params.TenantID, PlanIDs: planIDs }))
      .map((plan) => plan.Projects)
      .flat();

    return projects.some((project) => project.ProjectID === Params.ProjectID);
  }

  @Get('project/:ProjectID/audio-books')
  async GetAudioBooks(@Param('ProjectID') ProjectID: string, @TenantID() TenantID: string, @User() User: TUser) {
    const authorized = await this.AuthorizeProject({ ProjectID, TenantID, UserID: User.UserID });
    if (!authorized) {
      throw new UnauthorizedException('Unauthorized');
    }
    const audioBooks = await this.TTSService.ListProcesses({ ProjectID, TenantID });
    return audioBooks;
  }

  @Get('project/:ProjectID/audio-book/:AudioBookID')
  async GetAudioBook(
    @Param('AudioBookID') AudioBookID: string,
    @Param('ProjectID') ProjectID: string,
    @TenantID() TenantID: string,
    @User() User: TUser
  ) {
    const authorized = await this.AuthorizeProject({ ProjectID, TenantID, UserID: User.UserID });
    if (!authorized) {
      throw new UnauthorizedException('Unauthorized');
    }
    const audioBook = await this.TTSService.GetProcess({ ProcessID: AudioBookID, TenantID });
    return audioBook;
  }

  @Get('project/:ProjectID/content-albums')
  async GetContentAlbums(@Param('ProjectID') ProjectID: string, @TenantID() TenantID: string, @User() User: TUser) {
    const authorized = await this.AuthorizeProject({ ProjectID, TenantID, UserID: User.UserID });
    if (!authorized) {
      throw new UnauthorizedException('Unauthorized');
    }
    const contentAlbums = await this.ContentAlbumService.ListContentAlbums({ TenantID, ProjectID });
    return contentAlbums;
  }

  @Get('project/:ProjectID/content-album/:ContentAlbumID')
  async GetContentAlbum(
    @Param('ContentAlbumID') ContentAlbumID: string,
    @Param('ProjectID') ProjectID: string,
    @TenantID() TenantID: string,
    @User() User: TUser
  ) {
    const authorized = await this.AuthorizeProject({ ProjectID, TenantID, UserID: User.UserID });
    if (!authorized) {
      throw new UnauthorizedException('Unauthorized');
    }
    const contentAlbum = await this.ContentAlbumService.GetContentAlbum({ ContentAlbumID, TenantID });
    return contentAlbum;
  }

  @Get('project/:ProjectID/content-album/:ContentAlbumID/content/:ContentID')
  async GetContentAlbumContents(
    @Param('ContentAlbumID') ContentAlbumID: string,
    @Param('ProjectID') ProjectID: string,
    @Param('ContentID') ContentID: string,
    @TenantID() TenantID: string,
    @User() User: TUser
  ) {
    const authorized = await this.AuthorizeProject({ ProjectID, TenantID, UserID: User.UserID });
    if (!authorized) {
      throw new UnauthorizedException('Unauthorized');
    }
    const contentAlbumContents = await this.ContentAlbumService.GetContent({ ContentAlbumID, ContentID, TenantID });
    return contentAlbumContents;
  }

  @Get('tier-groups')
  async ListTierGroups(@Query() query: ListTierGroupsDto) {
    return await this.PlanSubService.ListTierGroups({ ...query });
  }
}

@UseGuards(AuthAdminGuard)
@Controller('tier-group')
export class PlanNSubscriptionTierGroupController {
  constructor(
    private readonly PlanSubService: PlanNSubscriptionService,
    private readonly TenantService: TenantService
  ) {}

  @Post()
  async CreateTierGroup(@Body() body: CreateTierGroupDto, @TenantID() TenantID: string) {
    const tierGroup = await this.PlanSubService.CreateTierGroup({ ...body, TenantID });
    return {
      message: 'Tier group created successfully',
      ack: tierGroup,
    };
  }

  @Patch('/:TierGroupID')
  async UpdateTierGroup(
    @Param('TierGroupID') TierGroupID: string,
    @Body() body: UpdateTierGroupDto,
    @TenantID() TenantID: string
  ) {
    const tierGroup = await this.PlanSubService.UpdateTierGroup({ ...body, TenantID, TierGroupID });
    return {
      message: 'Tier group updated successfully',
      ack: tierGroup,
    };
  }

  @Get('')
  async ListTierGroups(@Query() query: ListTierGroupsDto, @TenantID() TenantID: string) {
    return await this.PlanSubService.ListTierGroups({ ...query, TenantID });
  }

  @Get('/:TierGroupID')
  async GetTierGroup(@Param('TierGroupID') TierGroupID: string, @TenantID() TenantID: string) {
    return await this.PlanSubService.GetTierGroupByID({ TierGroupID, TenantID });
  }

  @SkipAuth()
  @Get('/invite/:InviteCode')
  async GetTierGroupByInviteCode(@Param('InviteCode') InviteCode: string) {
    return await this.PlanSubService.GetTierGroupByInviteCode({ InviteCode });
  }

  @SkipAuth()
  @Get('/list/public/:TierGroupID')
  async GetTierGroupByTierGroupID(@Param('TierGroupID') TierGroupID: string, @Query() query: { SubDomain: string }) {
    const getDomain = await this.TenantService.GetTenantByWorkspaceDomain(query.SubDomain);
    const tierGroup = await this.PlanSubService.GetTierGroupByID({ TierGroupID, TenantID: getDomain.TenantID });
    return {
      ...tierGroup,
      Plans: tierGroup.Plans.filter((plan) => plan.ListAsPublic),
    };
  }

  @SkipAuth()
  @Get('/list/public')
  async ListPublicTierGroups(@Query() query: ListTierGroupsParamsDto) {
    const getDomain = await this.TenantService.GetTenantByWorkspaceDomain(query.SubDomain);
    const tierGroups = await this.PlanSubService.ListTierGroups({
      ...query,
      ListAsPublic: true,
      TenantID: getDomain.TenantID,
    });
    return tierGroups.map((tierGroup) => ({
      ...tierGroup,
      InviteCode: undefined,
      Plans: tierGroup.Plans.filter((plan) => plan.ListAsPublic),
    }));
  }

  @Delete('/:TierGroupID')
  async DeleteTierGroup(@Param('TierGroupID') TierGroupID: string, @TenantID() TenantID: string) {
    const tierGroup = await this.PlanSubService.DeleteTierGroup({ TierGroupID, TenantID });
    return {
      message: 'Tier group deleted successfully',
      ack: tierGroup,
    };
  }
}
