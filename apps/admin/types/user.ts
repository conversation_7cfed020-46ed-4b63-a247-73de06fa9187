export type TGender = "Male" | "Female" | "Other";
export type TUserType = "Admin" | "Subscriber" | "Employee";
export type TAppType = "Academic" | "Industry";
export type Resource = {
  ResourceID: string;
  Properties: Record<string, any>;
};

export type Access = {
  Resources: Resource[];
};

export type PartialUserAttrs = {
  Name: string;
  PhoneNumber: string;
  Gender: TGender;
  DateOfBirth: string;
  Address: string;
  ProfilePicture: string;
  ZoneInfo: string;
  Locale: string;
  GivenName: string;
  FamilyName: string;
  MiddleName: string;
  Website: string;
  UserID: string;
  UserType: TUserType;
  Access?: Access;
};

export type TUser = {
  Email: string;
  TenantID: string;
} & Partial<PartialUserAttrs>;
