import { OmitType } from '@nestjs/mapped-types';
import { CreateSubscriptionDto, ListSubscriptions, UpdateSubscriptionDto } from './subscription.service.dto';
import { TUser } from '../../core/user/entities/user.entity';
import { PricingType } from '../entities/plan-n-subscription.entity';

export class CreateSubscriptionReqBodyDto extends OmitType(CreateSubscriptionDto, ['TenantID'] as const) {
  SuperUser?: boolean;
}

export class UpdateSubscriptionReqBodyDto extends OmitType(UpdateSubscriptionDto, ['TenantID'] as const) {
  SubscriptionID: string;
}

export class ListSubscriptionsReqBodyDto extends OmitType(ListSubscriptions, ['TenantID'] as const) {}

export class SignUpReqBodyDto extends TUser {
  Email: string;
  Password: string;
  PlanID: string;
  Variant: PricingType;
}

export class UpgradeSubscriptionReqBodyDto {
  SubscriptionID: string; // To Upgrade From
  PlanID: string; // To Upgrade To
  ForceCancel?: boolean;
  Variant: PricingType;
}
