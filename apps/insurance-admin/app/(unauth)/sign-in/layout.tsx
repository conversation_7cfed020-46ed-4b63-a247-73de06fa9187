"use client";

import { API } from "@/apis/api";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function SignInLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  useEffect(() => {
    API.AUTH.GetCurrentUser().then(({ data }) => {
      if (data) {
        router.push("/projects");
      }
    });
  }, []);
  return <div>{children}</div>;
}
