/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import Link from "next/link";
import { TProjectDetails, TSource, DocumentStatus } from "@/types/project";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@workspace/ui/components/accordion";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import {
  ChevronLeft,
  AlertCircle,
  FileText,
  Trash2,
  Loader2,
  Volume2,
  FileQuestion,
  Plus,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@workspace/ui/components/breadcrumb";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@workspace/ui/components/alert-dialog";
import { API } from "@/apis/api";
import { LECTURE } from "@/apis/lecture.api";
import { toast } from "sonner";
import { toTitleCase } from "@/lib/utils";
import { TeachingStyle, AgeGroup, TLecture } from "@/types/lecture";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { CONTENT_ALBUM, TContentAlbum } from "@/apis/content-album";

const statusColors: Record<DocumentStatus, string> = {
  [DocumentStatus.UPLOADING]: "bg-blue-100 text-blue-800",
  [DocumentStatus.UPLOADED]: "bg-gray-100 text-gray-800",
  [DocumentStatus.PROCESSING]: "bg-yellow-100 text-yellow-800",
  [DocumentStatus.COMPLETED]: "bg-green-100 text-green-800",
  [DocumentStatus.FAILED]: "bg-red-100 text-red-800",
};

export default function SourceDetailPage() {
  const params = useParams();
  const projectId = params.proj_id as string;
  const sourceId = params.source_id as string;

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [project, setProject] = useState<TProjectDetails | null>(null);
  const [source, setSource] = useState<TSource | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [lectures, setLectures] = useState<TLecture[]>([]);
  const [generatingScripts, setGeneratingScripts] = useState<
    Record<string, boolean>
  >({});
  const [generatingTTS, setGeneratingTTS] = useState<Record<string, boolean>>(
    {}
  );
  const [showTab, setShowTab] = useState("summary");
  const [showFullSummary, setShowFullSummary] = useState(false);
  const [albumDialogOpen, setAlbumDialogOpen] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const [projectResponse, lecturesResponse] = await Promise.all([
          API.PROJECT.GetProjectDetails({ ProjectID: projectId }),
          LECTURE.GetAllLectures({
            TenantID: source?.TenantID || "",
            ProjectID: projectId,
            SourceID: sourceId,
          }),
        ]);

        if (projectResponse.data) {
          setProject(projectResponse.data);
          const foundSource = projectResponse.data.Sources?.find(
            (s) => s.SourceID === sourceId
          );
          if (foundSource) {
            setSource(foundSource);
          } else {
            setError("Source not found");
          }
        }

        if (lecturesResponse.data) {
          setLectures(lecturesResponse.data);
        }
      } catch (error) {
        console.error("Failed to fetch data:", error);
        setError("An error occurred while fetching data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId, sourceId, source?.TenantID]);

  const handleGenerateScript = async (chapterId: string) => {
    if (!source) return;

    setGeneratingScripts((prev) => ({ ...prev, [chapterId]: true }));
    try {
      const response = await LECTURE.GenerateLectureScript({
        TenantID: source.TenantID,
        ProjectID: projectId,
        SourceID: sourceId,
        ChapterID: chapterId,
        TeachingStyle: TeachingStyle.CONVERSATIONAL,
        AgeGroup: AgeGroup.HIGH_SCHOOL,
        CreativityLevel: 0.7,
      });

      if (response.data) {
        toast.success("Lecture script generated successfully");
        // Refresh lectures
        const lecturesResponse = await LECTURE.GetAllLectures({
          TenantID: source.TenantID,
          ProjectID: projectId,
          SourceID: sourceId,
        });
        if (lecturesResponse.data) {
          setLectures(lecturesResponse.data);
        }
      }
    } catch (error) {
      console.log(error);
      toast.error("Failed to generate lecture script");
    } finally {
      setGeneratingScripts((prev) => ({ ...prev, [chapterId]: false }));
    }
  };

  const handleGenerateTTS = async (lectureId: string) => {
    if (!source) return;

    setGeneratingTTS((prev) => ({ ...prev, [lectureId]: true }));
    try {
      const response = await LECTURE.GenerateTTS({
        TenantID: source.TenantID,
        ProjectID: projectId,
        SourceID: sourceId,
        LectureID: lectureId,
      });

      if (response.data) {
        toast.success("TTS generation started");
      }
    } catch (error) {
      console.log(error);
      toast.error("Failed to start TTS generation");
    } finally {
      setGeneratingTTS((prev) => ({ ...prev, [lectureId]: false }));
    }
  };

  async function handleDeleteSource() {
    if (!source) return;

    setDeleteLoading(true);
    const { data, errors } = await API.PROJECT.DeleteSource({
      ProjectID: projectId,
      TenantID: source.TenantID,
      SourceID: sourceId,
    });
    setDeleteLoading(false);
    setDeleteDialogOpen(false);

    if (data) {
      toast.success("Source deleted successfully");
      window.location.href = `/projects/${projectId}`;
    }
    if (errors) {
      (errors as Error[]).forEach((err) => toast.error(err.message));
    }
  }

  const handleGenerateContentAlbum = async ({
    Name,
    Description,
    CoverImage,
  }: {
    Name: string;
    Description?: string;
    CoverImage: string;
  }) => {
    if (!source) return;

    const { data, errors } = await CONTENT_ALBUM.GenerateContentAlbum({
      TenantID: source.TenantID,
      ProjectID: projectId,
      SourceID: sourceId,
      Name,
      Description,
      CoverImage,
    });
    if (!errors && data) {
      setAlbumDialogOpen(false);
      toast.success(data.message ?? "Content album generation started");
    }
    if (errors) {
      (errors as Error[]).forEach((err) => toast.error(err.message));
    }
    setAlbumDialogOpen(false);
  };

  const AddContentAlbum = () => {
    const [contentAlbumState, setContentAlbumState] = useState<
      Pick<TContentAlbum, "Name" | "Description" | "CoverImage" | "ProjectID">
    >({
      Name: source?.SourceName ?? "",
      Description: "",
      CoverImage: "",
      ProjectID: project?.ProjectID!,
    });

    return (
      <AlertDialog open={albumDialogOpen} onOpenChange={setAlbumDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{"Generate Content Album"}</AlertDialogTitle>
            <AlertDialogDescription>
              Generate a new album for project
            </AlertDialogDescription>
          </AlertDialogHeader>
          <form>
            <div className="flex flex-col gap-4">
              <Input
                placeholder="Album Name"
                value={contentAlbumState.Name}
                onChange={(e) =>
                  setContentAlbumState({
                    ...contentAlbumState,
                    Name: e.target.value,
                  })
                }
              />
              <Textarea
                placeholder="Description"
                value={contentAlbumState.Description}
                onChange={(e) =>
                  setContentAlbumState({
                    ...contentAlbumState,
                    Description: e.target.value,
                  })
                }
              />
              <Input
                placeholder="Cover Image"
                // value={contentAlbumState.CoverImage}
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    const reader = new FileReader();
                    reader.onloadend = () => {
                      setContentAlbumState({
                        ...contentAlbumState,
                        CoverImage: reader.result as string,
                      });
                    };
                    reader.readAsDataURL(file);
                  }
                }}
                type="file"
                accept="image/*"
              />
            </div>
          </form>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={async (e) => {
                e.preventDefault();
                if (
                  !contentAlbumState.Name.trim() ||
                  !contentAlbumState.CoverImage
                ) {
                  toast.error("Album name and cover image are required");
                  return;
                }
                handleGenerateContentAlbum({
                  Name: contentAlbumState.Name,
                  Description: contentAlbumState.Description,
                  CoverImage: contentAlbumState.CoverImage,
                });
              }}
            >
              Save changes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  if (loading) {
    return (
      <div className="w-full h-full p-6 md:p-10 space-y-6">
        <div className="border rounded-lg bg-muted/30 p-6">
          <Skeleton className="h-8 w-1/3 mb-4" />
          <Skeleton className="h-4 w-2/3 mb-2" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-1/4" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !project || !source) {
    return (
      <div className="w-full h-full py-6 space-y-6">
        <div className="border rounded-lg bg-muted/30 p-6">
          <h2 className="text-2xl font-bold mb-4">Error</h2>
          <p className="text-muted-foreground">{error || "Source not found"}</p>
          <Link
            href={`/projects/${projectId}`}
            className="mt-4 inline-flex items-center text-primary hover:underline"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to Project
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="w-full h-full p-6 md:p-10 space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/projects">Projects</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href={`/projects/${projectId}`}>
                {project.Name}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{source.SourceName}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Source Meta */}
        <div className="border rounded-lg bg-muted/30 p-6 mb-6 flex flex-col md:flex-row md:justify-between md:items-center gap-6">
          <div>
            <h2 className="text-2xl font-bold mb-1 flex items-center gap-2">
              <FileText className="w-6 h-6 text-muted-foreground" />{" "}
              {source.SourceName}
            </h2>
            <div className="flex flex-wrap gap-4 text-xs text-muted-foreground mb-2">
              <span>
                <b>Source ID:</b> {source.SourceID}
              </span>
              <span>
                <b>Project ID:</b> {source.ProjectID}
              </span>
              <span>
                <b>Tenant ID:</b> {source.TenantID}
              </span>
              <span>
                <b>AWS Key:</b>{" "}
                <span title={source.AWSKey}>
                  {source.AWSKey.slice(0, 12)}...
                </span>
              </span>
            </div>
            <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
              <span>
                <b>Created:</b>{" "}
                {formatDistanceToNow(new Date(source.createdAt), {
                  addSuffix: true,
                })}
              </span>
              <span>
                <b>Last updated:</b>{" "}
                {formatDistanceToNow(new Date(source.updatedAt), {
                  addSuffix: true,
                })}
              </span>
            </div>
          </div>
          <div className="flex flex-col gap-2 min-w-[200px]">
            <div className="flex flex-col items-center gap-2">
              <span
                className={`px-2 py-1 rounded text-xs font-medium ${statusColors[source.Status as DocumentStatus]}`}
                style={{ textTransform: "none" }}
              >
                {toTitleCase(source.Status)}
              </span>
              <AlertDialog
                open={deleteDialogOpen}
                onOpenChange={setDeleteDialogOpen}
              >
                <AlertDialogTrigger asChild>
                  <Button
                    variant="destructive"
                    className="text-white"
                    onClick={() => setDeleteDialogOpen(true)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete
                      the source and all its associated data.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel disabled={deleteLoading}>
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDeleteSource}
                      disabled={deleteLoading}
                    >
                      {deleteLoading ? "Deleting..." : "Delete"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            {source.ErrorMessage && (
              <span className="inline-flex items-center gap-1 text-red-600 text-xs">
                <AlertCircle className="w-4 h-4" /> {source.ErrorMessage}
              </span>
            )}
          </div>
        </div>

        {/* Book Summary */}
        {source.BookSummary && (
          <div className="border rounded-lg bg-muted/20 p-6 mb-6">
            <div className="flex border-b mb-4">
              <div className="tabs flex space-x-4">
                <button
                  className={`pb-2 px-1 font-medium ${showTab === "summary" ? "border-b-2 border-primary" : "text-muted-foreground"}`}
                  onClick={() => setShowTab("summary")}
                >
                  Book Summary
                </button>
                <button
                  className={`pb-2 px-1 font-medium ${showTab === "conceptMap" ? "border-b-2 border-primary" : "text-muted-foreground"}`}
                  onClick={() => setShowTab("conceptMap")}
                >
                  Concept Map
                </button>
              </div>
            </div>

            {showTab === "summary" && (
              <div className="space-y-4">
                <div className="mb-2 text-sm">
                  <b>Title:</b> {source.BookSummary.Title}
                </div>
                <div className="mb-2 text-sm">
                  <b>Summary:</b>
                  {showFullSummary ? (
                    source.BookSummary.Summary
                  ) : (
                    <>
                      {source.BookSummary.Summary.slice(0, 300)}
                      {source.BookSummary.Summary.length > 300 && "..."}
                    </>
                  )}
                  {source.BookSummary.Summary.length > 300 && (
                    <button
                      onClick={() => setShowFullSummary(!showFullSummary)}
                      className="ml-2 text-primary hover:underline"
                    >
                      {showFullSummary ? "Read less" : "Read more"}
                    </button>
                  )}
                </div>
                {source.BookSummary.MainTopics &&
                  source.BookSummary.MainTopics.length > 0 && (
                    <div className="mb-2">
                      <b className="text-sm">Main Topics:</b>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {source.BookSummary.MainTopics.map((topic, idx) => (
                          <Badge
                            key={idx}
                            variant="secondary"
                            className="text-xs"
                          >
                            {topic}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                {source.BookSummary.ChapterHighlights &&
                  source.BookSummary.ChapterHighlights.length > 0 && (
                    <div className="mt-2">
                      <b className="text-sm">Chapter Highlights:</b>
                      <ul className="list-disc ml-6 mt-1 space-y-1">
                        {source.BookSummary.ChapterHighlights.map(
                          (highlight, idx) => (
                            <li key={idx} className="text-xs">
                              <b>{highlight.ChapterName}:</b>{" "}
                              {highlight.Highlight}
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                  )}
              </div>
            )}

            {showTab === "conceptMap" && (
              <div className="concept-map">
                <div className="mb-4">
                  <h3 className="text-md font-medium">Book Concept Map</h3>
                  <p className="text-sm text-muted-foreground">
                    Visualization of key topics and concepts across all chapters
                  </p>
                </div>

                <div className="concept-tree p-4 bg-white rounded-lg border">
                  <div className="text-center mb-4">
                    <span className="px-4 py-2 bg-primary/10 border border-primary/30 rounded-lg text-primary font-medium inline-block">
                      {source.BookSummary.Title}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {source.Chapters && source.Chapters.length > 0 ? (
                      source.Chapters.map((chapter) => (
                        <div
                          key={chapter.ChapterID}
                          className="chapter-card border rounded-md p-3 bg-muted/5"
                        >
                          <div className="bg-secondary/20 border border-secondary/30 rounded-md p-2 mb-2 text-sm font-medium">
                            Chapter {chapter.ChapterNumber}:{" "}
                            {chapter.ChapterName}
                          </div>

                          {chapter.Topics && chapter.Topics.length > 0 ? (
                            <div className="space-y-2">
                              {chapter.Topics.map((topic, idx) => (
                                <div key={idx} className="topic-item">
                                  <div className="flex items-center gap-1 mb-1">
                                    <div className="w-1 h-1 rounded-full bg-primary"></div>
                                    <div className="text-xs font-medium">
                                      {topic.Topic}
                                    </div>
                                  </div>

                                  {topic.SubTopics &&
                                    topic.SubTopics.length > 0 && (
                                      <div className="ml-3 pl-2 border-l border-gray-200">
                                        {topic.SubTopics.map(
                                          (subTopic, subIdx) => (
                                            <div
                                              key={subIdx}
                                              className="text-xs text-muted-foreground py-0.5 flex items-center gap-1"
                                            >
                                              <div className="w-1 h-1 rounded-full bg-gray-400"></div>
                                              {subTopic}
                                            </div>
                                          )
                                        )}
                                      </div>
                                    )}
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="text-xs text-muted-foreground italic">
                              No topics available
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="col-span-full text-center text-muted-foreground">
                        No chapter data available to generate concept map
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Chapters Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold mb-4">
              Chapters ({source?.Chapters?.length || 0})
            </h3>
            <Button
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                setAlbumDialogOpen(true);
              }}
            >
              <Plus size={16} />
              Generate Content Album
            </Button>
          </div>
          {!source?.Chapters || source.Chapters.length === 0 ? (
            <div className="border rounded-lg p-6 text-center text-muted-foreground">
              No chapters available
            </div>
          ) : (
            <Accordion type="multiple" className="w-full">
              {source.Chapters.map((chapter) => {
                const lecture = lectures?.find(
                  (l) => l.ChapterID === chapter.ChapterID
                );
                return (
                  <AccordionItem
                    key={chapter.ChapterID}
                    value={chapter.ChapterID}
                  >
                    <AccordionTrigger className="hover:bg-muted/50 px-4">
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2 text-left">
                          <span className="font-medium">
                            Chapter {chapter.ChapterNumber}:{" "}
                            {chapter.ChapterName}
                          </span>
                          <Badge variant="outline" className="ml-2">
                            {chapter.ChunkIDs?.length || 0} chunks
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleGenerateScript(chapter.ChapterID)
                            }
                            disabled={generatingScripts[chapter.ChapterID]}
                          >
                            {generatingScripts[chapter.ChapterID] && (
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            )}
                            <FileText className="w-6 h-6 text-muted-foreground" />
                            {generatingScripts[chapter.ChapterID]
                              ? "Generating..."
                              : lecture
                                ? "Regenerate Script"
                                : "Generate Script"}
                          </Button>
                          {lecture && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                handleGenerateTTS(lecture.LectureID)
                              }
                              disabled={generatingTTS[lecture.LectureID]}
                            >
                              {generatingTTS[lecture.LectureID] ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              ) : (
                                <Volume2 className="mr-2 h-4 w-4" />
                              )}
                              {generatingTTS[lecture.LectureID]
                                ? "Generating TTS..."
                                : "Generate TTS"}
                            </Button>
                          )}
                          <Button variant="outline" size="sm" asChild>
                            <Link
                              href={`/projects/${projectId}/sources/${sourceId}/chapters/${chapter.ChapterID}/quiz`}
                            >
                              <FileQuestion className="mr-2 h-4 w-4" />
                              Pop Quiz
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 pt-2">
                      <Card>
                        <CardContent className="p-4 space-y-4">
                          <div className="flex flex-wrap gap-4 text-xs text-muted-foreground mb-2">
                            <span>
                              <b>Chapter ID:</b> {chapter.ChapterID}
                            </span>
                            <span>
                              <b>Pages:</b> {chapter.PageNumber?.From ?? "N/A"}{" "}
                              - {chapter.PageNumber?.To ?? "N/A"}
                            </span>
                            <span>
                              <b>Document Pages:</b>{" "}
                              {chapter.DocumentPageNumber?.From ?? "N/A"} -{" "}
                              {chapter.DocumentPageNumber?.To ?? "N/A"}
                            </span>
                            <span>
                              <b>Chunks:</b> {chapter.ChunkIDs?.length || 0}
                            </span>
                          </div>
                          {chapter.Summary && (
                            <div>
                              <h4 className="text-sm font-medium mb-1">
                                Summary
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                {chapter.Summary}
                              </p>
                            </div>
                          )}
                          {chapter.Topics && chapter.Topics.length > 0 && (
                            <div>
                              <h4 className="text-sm font-medium mb-1">
                                Topics
                              </h4>
                              <div className="flex flex-wrap gap-1">
                                {chapter.Topics.map((topic, index) => (
                                  <Badge
                                    key={index}
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {topic.Topic}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                          <div className="pt-4 border-t">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="text-sm font-medium">
                                Lecture Script
                              </h4>
                            </div>
                            <div className="mt-2 space-y-4">
                              {/* <div>
                                <h5 className="text-xs font-medium mb-1">
                                  Teaching Plan
                                </h5>
                                <p className="text-sm text-muted-foreground whitespace-pre-line">
                                  {lecture.TeachingPlan}
                                </p>
                              </div> */}
                              <div>
                                {/* <h5 className="text-xs font-medium mb-1">
                                  Script
                                </h5> */}
                                {lecture?.LectureScript ? (
                                  <ReactMarkdown
                                    remarkPlugins={[remarkGfm]}
                                    rehypePlugins={[rehypeRaw]}
                                    components={{
                                      h1: ({ node, ...props }) => (
                                        <h1
                                          className="text-lg font-bold mt-4 mb-2"
                                          {...props}
                                        />
                                      ),
                                      h2: ({ node, ...props }) => (
                                        <h2
                                          className="text-md font-bold mt-3 mb-2"
                                          {...props}
                                        />
                                      ),
                                      h3: ({ node, ...props }) => (
                                        <h3
                                          className="text-sm font-bold mt-2 mb-1"
                                          {...props}
                                        />
                                      ),
                                      ul: ({ node, ...props }) => (
                                        <ul
                                          className="list-disc pl-5 my-2 space-y-1 p-5 m-10"
                                          {...props}
                                        />
                                      ),
                                      ol: ({ node, ...props }) => (
                                        <ol
                                          className="list-decimal pl-5 my-2 space-y-1"
                                          {...props}
                                        />
                                      ),
                                      li: ({ node, ...props }) => (
                                        <li className="text-sm" {...props} />
                                      ),
                                      p: ({ node, ...props }) => (
                                        <p
                                          className="text-sm my-2"
                                          {...props}
                                        />
                                      ),
                                      code: ({ node, ...props }) => (
                                        <code
                                          className="bg-gray-100 rounded px-1 py-0.5 text-xs"
                                          {...props}
                                        />
                                      ),
                                      pre: ({ node, ...props }) => (
                                        <pre
                                          className="bg-gray-100 p-2 rounded overflow-x-auto text-xs my-2"
                                          {...props}
                                        />
                                      ),
                                      blockquote: ({ node, ...props }) => (
                                        <blockquote
                                          className="border-l-4 border-gray-200 pl-4 italic my-2"
                                          {...props}
                                        />
                                      ),
                                    }}
                                  >
                                    {lecture.LectureScript}
                                  </ReactMarkdown>
                                ) : (
                                  <p className="text-sm text-muted-foreground">
                                    No script generated
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          )}
        </div>
      </div>
      <AddContentAlbum />
    </>
  );
}
