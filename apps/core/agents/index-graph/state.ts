import { Annotation } from '@langchain/langgraph';
import { Document } from '@langchain/core/documents';
import { reduceDocs } from '../shared/state';

/**
 * Represents the state for document indexing and retrieval.
 *
 * This interface defines the structure of the index state, which includes
 * the documents to be indexed and the retriever used for searching
 * these documents.
 */
export const IndexStateAnnotation = Annotation.Root({
  /**
   * A list of documents that the agent can index.
   * Each document contains tenant and project ID metadata for scoping.
   */
  docs: Annotation<Document[], Document[] | { [key: string]: any }[] | string[] | string | 'delete'>({
    default: () => [],
    reducer: reduceDocs,
  }),

  /**
   * The tenant ID for the documents being indexed
   */
  tenantId: Annotation<string>({
    default: () => '',
    value: (old, _new) => _new || old,
  }),

  /**
   * The project ID for the documents being indexed
   */
  projectId: Annotation<string>({
    default: () => '',
    value: (old, _new) => _new || old,
  }),

  /**
   * The book ID for the document being indexed
   */
  sourceId: Annotation<string>({
    default: () => '',
    value: (old, _new) => _new,
  }),

  /**
   * The source name for the document being indexed
   */
  sourceName: Annotation<string>({
    default: () => '',
    value: (old, _new) => _new,
  }),

  /**
   * The pages of content to index
   */
  pagesOfContent: Annotation<number[]>({
    default: () => [],
    value: (old, _new) => _new,
  }),

  pageNumberOffset: Annotation<number>({
    default: () => 0,
    value: (old, _new) => _new,
  }),
});

export type IndexStateType = typeof IndexStateAnnotation.State;
