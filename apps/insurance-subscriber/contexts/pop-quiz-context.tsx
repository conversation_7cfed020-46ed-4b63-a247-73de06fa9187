import { API } from "@/apis/api";
import { TPopQuiz } from "@/apis/pop-quiz.api";
import PopQuizSelector from "@/components/pop-quiz/pop-quiz-selector";
import Quiz from "@/components/pop-quiz/quiz";
import { useAuth } from "@/hooks/use-auth";
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import { toast } from "sonner";

export const usePopQuizContext = () => useContext(PopQuizContext);

interface PopQuizContextType {
  popQuiz: null | TPopQuiz;
  quizzesList: TPopQuiz[];
  isLoading: boolean;
  refetch: () => Promise<void>;
  setPopQuiz: (popQuiz: TPopQuiz | null) => void;
  isSelectorOpen: boolean;
  setIsSelectorOpen: (isSelectorOpen: boolean) => void;
}

export const PopQuizContext = createContext<PopQuizContextType>({
  popQuiz: null,
  quizzesList: [],
  isLoading: false,
  refetch: async () => {},
  setPopQuiz: () => {},
  isSelectorOpen: false,
  setIsSelectorOpen: () => {},
});

export function PopQuizProvider({ children }: { children: ReactNode }) {
  const [popQuiz, setPopQuiz] = useState<TPopQuiz | null>(null);
  const [quizzesList, setQuizzesList] = useState<TPopQuiz[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSelectorOpen, setIsSelectorOpen] = useState<boolean>(false);
  const { isAuthenticated } = useAuth();

  const fetchPopQuiz = async (): Promise<void> => {
    setIsLoading(true);
    const { data, errors } = await API.POP_QUIZ.PreFlightPopQuiz();

    if (errors) {
      errors.forEach((error) => {
        if (error.message) toast.error(error.message);
      });
      setQuizzesList([]);
      setIsLoading(false);
      return;
    }

    if (data) {
      setQuizzesList(data);
    }

    setIsLoading(false);
    // setIsSelectorOpen(data?.length > 3);
  };

  useEffect(() => {
    if (!isAuthenticated) return;
    fetchPopQuiz();
    const storedPopQuiz = localStorage.getItem("popQuiz");
    if (storedPopQuiz) {
      setPopQuiz(JSON.parse(storedPopQuiz));
    }
  }, [isAuthenticated]);

  const value = {
    popQuiz,
    quizzesList,
    isLoading,
    refetch: fetchPopQuiz,
    setPopQuiz: (popQuiz: TPopQuiz | null) => {
      setPopQuiz(popQuiz);
      // set localStorage
      localStorage.setItem("popQuiz", JSON.stringify(popQuiz));
    },
    isSelectorOpen,
    setIsSelectorOpen,
  };

  return (
    <PopQuizContext.Provider value={value}>
      <PopQuizSelector />
      <Quiz />
      {children}
    </PopQuizContext.Provider>
  );
}
