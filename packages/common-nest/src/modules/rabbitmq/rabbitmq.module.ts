import { DynamicModule, Logger, Module, Provider } from '@nestjs/common';
import channelProfile from './rabbitmq.service';
import amqp from 'amqplib';

@Module({})
export class RabbitMQModule {
  static forRoot(amqpURI: string, cb: (connection: RabbitMQConnection) => void): DynamicModule {
    const provider: Provider = {
      provide: 'RabbitMQ',
      useFactory: async () => {
        const rabbitmqService = new channelProfile(amqpURI);
        await rabbitmqService.link();
        Logger.log('RabbitMQ Connected');
        cb(rabbitmqService.getConnection());
        return rabbitmqService;
      },
    };
    return {
      module: RabbitMQModule,
      imports: [],
      providers: [provider],
      exports: [provider],
      global: true,
    };
  }
}

export type RabbitMQConnection = amqp.Connection;
