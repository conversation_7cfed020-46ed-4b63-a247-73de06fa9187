{"name": "subscriber", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-collapsible": "^1.1.10", "@workspace/ui": "workspace:*", "ky": "^1.8.0", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "ogl": "^1.0.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-razorpay": "^3.0.1", "rehype-react": "^8.0.0", "short-unique-id": "^5.2.0", "sonner": "^2.0.3", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-speech-recognition": "^3.9.6", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "react-speech-recognition": "^4.0.1", "tailwind-scrollbar": "^4.0.2", "typescript": "^5.7.3"}}