import { BadRequestException, Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { TenantID, User } from '../../common/decorators/core.decorators';
import { AuthAdminGuard } from '../../common/guards/auth.guard';
import { PlanNSubscriptionService } from '../plan-n-subscription/plan-n-subscription.service';
import { CreateProjectRequestBody } from './dto/project.controller.dto';
import { ProjectService } from './project.service';
import { TUser } from '../core/user/entities/user.entity';
import { CapType } from '../plan-n-subscription/entities/plan-n-subscription.entity';

@UseGuards(AuthAdminGuard)
@Controller('project')
export class ProjectController {
  constructor(
    private readonly ProjectService: ProjectService,
    private PlanSubsService: PlanNSubscriptionService
  ) {}

  @Post()
  async CreateProject(@Body() body: CreateProjectRequestBody, @TenantID() TenantID: string, @User() user: TUser) {
    const project = await this.ProjectService.CreateProject({
      ...body,
      TenantID,
      AppType: user.AppType,
    });
    await this.PlanSubsService.CreatePlan({
      Name: body.Name,
      Subtitle: '',
      Description: body.Description,
      Currency: 'INR',
      Features: [],
      ProjectIDs: [project.ProjectID],
      Flags: [
        {
          FlagID: 'CHAT',
          Properties: {},
        },
        {
          FlagID: 'STORY',
          Properties: {},
        },
        {
          FlagID: 'POP-QUIZ',
          Properties: {},
        },
      ],
      TenantID,
      Tier: 'Free',
      PricingStructures: [
        {
          Type: 'Monthly',
          Price: 0,
          Duration: 30,
          CreditUsage: {
            CapType: CapType.Token,
            CapQuota: 0,
          },
        },
      ],
    });
    return {
      message: 'Project created successfully',
      ack: project,
    };
  }

  @Get()
  async ListProjectsByTenantID(@TenantID() TenantID: string, @User() user: TUser) {
    const projects = await this.ProjectService.ListProjectsByTenantID(TenantID, user.AppType);
    return projects;
  }

  @Get(':ProjectID')
  async GetProjectByTenantID(@Param('ProjectID') ProjectID: string, @TenantID() TenantID: string, @User() user: TUser) {
    const project = await this.ProjectService.GetProjectByTenantID(TenantID, ProjectID, user.AppType);
    return project;
  }

  @Patch(':ProjectID')
  async UpdateProject(
    @Param('ProjectID') ProjectID: string,
    @TenantID() TenantID: string,
    @Body() body: CreateProjectRequestBody,
    @User() user: TUser
  ) {
    const project = await this.ProjectService.UpdateProject(TenantID, ProjectID, {
      ...body,
      TenantID,
      AppType: user.AppType,
    });
    const getPlan = (
      await this.PlanSubsService.ListPlans({
        TenantID,
        ProjectIDs: [ProjectID],
      })
    ).find((plan) => plan.ProjectIDs.length === 1);
    if (!getPlan) {
      throw new BadRequestException('Plan not found');
    }
    const plan = await this.PlanSubsService.UpdatePlan({
      PlanID: getPlan.PlanID,
      Name: body.Name,
      Subtitle: '',
      Description: body.Description,
      Currency: 'INR',
      Features: [],
      TenantID,
      Tier: 'Free',
    });
    return {
      message: 'Project updated successfully',
      ack: { project, plan },
    };
  }

  @Delete(':ProjectID')
  async DeleteProject(@Param('ProjectID') ProjectID: string, @TenantID() TenantID: string, @User() user: TUser) {
    const project = await this.ProjectService.DeleteProject(TenantID, ProjectID, user.AppType);
    return {
      message: 'Project deleted successfully',
      ack: project,
    };
  }

  @Get(':ProjectID/details')
  async GetProjectDetails(@Param('ProjectID') ProjectID: string, @TenantID() TenantID: string, @User() user: TUser) {
    const project = await this.ProjectService.GetProjectDetails(TenantID, ProjectID, user.AppType);
    return project;
  }
}
