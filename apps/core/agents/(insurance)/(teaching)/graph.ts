import { Annotation, END, START, StateGraph } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';
import { LLMInvocation, LLMOutput } from '../../shared/utils';
import { calculateLLMInvocations } from '../../shared/utils';
import { PROMPTS } from './prompts';
import GenerateID from '../../../common/lib/generate-id';

const ZLectureOutput = z.object({
  Lectures: z.array(
    z.object({
      content_title: z.string(),
      description: z.string(),
      transcript: z.string(),
    })
  ),
});

const ZHTMLLectureOutput = z.object({
  Lectures: z.array(
    z.object({
      content_id: z.string(),
      formatted_transcript: z.string(),
    })
  ),
});

const InputStateAnnotation = Annotation.Root({
  TeachingStyle: Annotation<string>(),
  AgeGroup: Annotation<string>(),
  CreativityLevel: Annotation<number>(),
  OtherSectionsSummary: Annotation<
    {
      SectionID: string;
      SectionName: string;
      Description?: string;
      SubSections?: {
        SubSectionName: string;
        PageNumber: number;
        DocumentPageNumber: number;
      }[];
      PageNumber: {
        From: number;
        To: number;
      };
      DocumentPageNumber: {
        From: number;
        To: number;
      };
    }[]
  >(),
  CurrentSectionSummary: Annotation<{
    SectionID: string;
    SectionName: string;
    Description?: string;
    Summary: string;
    SubSections?: {
      SubSectionName: string;
      PageNumber: number;
      DocumentPageNumber: number;
    }[];
    PageNumber: {
      From: number;
      To: number;
    };
    DocumentPageNumber: {
      From: number;
      To: number;
    };
  }>(),
  SourceSummary: Annotation<{
    Title: string;
    Summary: string;
    MainTopics: string[];
    SectionHighlights: {
      SectionName: string;
      Highlight: string;
    }[];
  }>(),
  Chunks: Annotation<
    {
      ChunkID: string;
      ChunkNumber: number;
      PageNumber: number;
      DocumentPageNumber: number;
      Summary: string;
      Content: string;
      Sections: {
        SectionID: string;
        SectionName: string;
        SubSections?: {
          SubSectionName: string;
        }[];
      }[];
    }[]
  >(),
});

const AgentAnnotationState = Annotation.Root({
  ...InputStateAnnotation.spec,
  TeachingPlan: Annotation<string>(),
  LectureScript: Annotation<string>(),
  SSMLScript: Annotation<string>(),
  Lectures: Annotation<
    {
      ContentID: string;
      ContentTitle: string;
      Description: string;
      Transcript: string;
      FormattedTranscript: string;
    }[]
  >(),
  TokenCount: Annotation<{
    PromptTokens: number;
    CompletionTokens: number;
    TotalTokens: number;
    LLMInvocations: LLMInvocation[];
  }>({
    default: () => ({
      PromptTokens: 0,
      CompletionTokens: 0,
      TotalTokens: 0,
      LLMInvocations: [],
    }),
    reducer: (acc, curr) => ({
      PromptTokens: acc.PromptTokens + curr.PromptTokens,
      CompletionTokens: acc.CompletionTokens + curr.CompletionTokens,
      TotalTokens: acc.TotalTokens + curr.TotalTokens,
      LLMInvocations: [...acc.LLMInvocations, ...curr.LLMInvocations],
    }),
  }),
});

const createTeachingPlan = async (
  state: typeof AgentAnnotationState.State
): Promise<typeof AgentAnnotationState.Update> => {
  const {
    TeachingStyle,
    AgeGroup,
    CreativityLevel,
    OtherSectionsSummary,
    CurrentSectionSummary,
    SourceSummary,
    Chunks,
  } = state;

  const topicsAndSubtopics = Chunks.reduce(
    (acc, chunk) => {
      if (chunk.Sections && !acc.sections.includes(chunk.Sections.map((section) => section.SectionName).join(', '))) {
        acc.sections.push(chunk.Sections.map((section) => section.SectionName).join(', '));
      }
      return acc;
    },
    { sections: [] as string[] }
  );

  const previousSections = OtherSectionsSummary.filter(
    (c) => c.SectionID && CurrentSectionSummary.SectionID && c.SectionID < CurrentSectionSummary.SectionID
  );

  const nextSections = OtherSectionsSummary.filter(
    (c) => c.SectionID && CurrentSectionSummary.SectionID && c.SectionID > CurrentSectionSummary.SectionID
  );

  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.2,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'create_teaching_plan'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const prompt = PROMPTS.CREATE_TEACHING_PLAN(
    state.SourceSummary,
    state.CurrentSectionSummary,
    topicsAndSubtopics,
    previousSections,
    nextSections,
    TeachingStyle,
    CreativityLevel,
    AgeGroup
  );

  const response = await model.invoke(prompt);
  const teachingPlan = response.content.toString();
  return {
    TeachingPlan: teachingPlan,
    TokenCount,
  };
};

const generateLectureScript = async (
  state: typeof AgentAnnotationState.State
): Promise<typeof AgentAnnotationState.Update> => {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.2,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'generate_lecture_script'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const previousSections = state.OtherSectionsSummary.filter(
    (c) => c.SectionID && state.CurrentSectionSummary.SectionID && c.SectionID < state.CurrentSectionSummary.SectionID
  );

  const nextSections = state.OtherSectionsSummary.filter(
    (c) => c.SectionID && state.CurrentSectionSummary.SectionID && c.SectionID > state.CurrentSectionSummary.SectionID
  );

  const topicsAndSubtopics = state.Chunks.reduce(
    (acc, chunk) => {
      if (chunk.Sections && !acc.sections.includes(chunk.Sections.map((section) => section.SectionName).join(', '))) {
        acc.sections.push(chunk.Sections.map((section) => section.SectionName).join(', '));
      }
      return acc;
    },
    { sections: [] as string[] }
  );

  const scriptPrompt = PROMPTS.GENERATE_MODULE_BASED_LECTURE_SCRIPTS(
    state.SourceSummary,
    state.CurrentSectionSummary,
    topicsAndSubtopics,
    previousSections,
    nextSections,
    state.TeachingPlan,
    state.CreativityLevel,
    state.AgeGroup,
    state.TeachingStyle,
    state.Chunks
  );

  const response = await model.withStructuredOutput(ZLectureOutput).invoke(scriptPrompt);
  const lectures = response.Lectures.map((lecture) => ({
    ContentID: GenerateID.Generate16Hex('cont_'),
    ContentTitle: lecture.content_title || '',
    Description: lecture.description || '',
    Transcript: lecture.transcript || '',
    FormattedTranscript: '',
  }));
  return {
    LectureScript: response.Lectures.map((lecture) => lecture.transcript).join('\n'),
    Lectures: lectures,
    TokenCount,
  };
};

const formatHTMLLectureScript = async (
  state: typeof AgentAnnotationState.State
): Promise<typeof AgentAnnotationState.Update> => {
  let TokenCount = state.TokenCount;

  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.2,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'format_html_lecture_script'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const prompt = PROMPTS.FORMAT_MODULE_BASED_LECTURE_SCRIPTS(
    state.Lectures.map((lecture) => ({
      content_title: lecture.ContentTitle,
      description: lecture.Description,
      transcript: lecture.Transcript,
    }))
  );

  const response = await model.withStructuredOutput(ZHTMLLectureOutput).invoke(prompt);

  const contentMap = state.Lectures.reduce(
    (acc, lecture) => {
      acc[lecture.ContentID] = lecture;
      return acc;
    },
    {} as Record<string, (typeof AgentAnnotationState.State)['Lectures'][number]>
  );

  const lectures = response.Lectures.map((lecture, index) => ({
    ...state.Lectures[index],
    FormattedTranscript: lecture.formatted_transcript,
  }));

  return {
    Lectures: lectures,
    TokenCount,
    LectureScript: lectures.map((lecture) => lecture.FormattedTranscript).join(''),
  };
};

const formatWithSSML = async (
  state: typeof AgentAnnotationState.State
): Promise<typeof AgentAnnotationState.Update> => {
  const prompt = PROMPTS.FORMAT_WITH_SSML(state.LectureScript, state.TeachingStyle, state.AgeGroup);

  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.2,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'format_with_ssml'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const SSMLSchema = z.object({
    SSMLScript: z.string(),
  });

  const response = await model.withStructuredOutput(SSMLSchema).invoke(prompt);
  return {
    SSMLScript: response.SSMLScript,
    TokenCount,
  };
};

const builder = new StateGraph(AgentAnnotationState)
  .addNode('create_teaching_plan', createTeachingPlan)
  .addNode('generate_lecture_script', generateLectureScript)
  .addNode('format_html_lecture_script', formatHTMLLectureScript)
  // .addNode('format_with_ssml', formatWithSSML)
  .addEdge(START, 'create_teaching_plan')
  .addEdge('create_teaching_plan', 'generate_lecture_script')
  .addEdge('generate_lecture_script', 'format_html_lecture_script')
  // .addEdge('generate_lecture_script', 'format_with_ssml')
  .addEdge('format_html_lecture_script', END);

export const graph = builder.compile();
