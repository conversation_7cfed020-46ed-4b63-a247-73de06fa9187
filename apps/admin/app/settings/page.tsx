"use client";
import { API } from "@/apis/api";
import { TTenant } from "@/apis/tenant.api";
import { ImageUpload } from "@/components/image-upload";
import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Switch } from "@workspace/ui/components/switch";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function Settings() {
  const [tenant, setTenant] = useState<TTenant | null>(null);
  const [loading, setLoading] = useState(false);
  const [socialCards, setSocialCards] = useState({
    LinkedIn: "",
    Facebook: "",
    Twitter: "",
    Instagram: "",
    YouTube: "",
    Discord: "",
    WhatsApp: "",
  });

  useEffect(() => {
    const getTenant = async () => {
      const { data, errors } = await API.TENANT.GetTenant();
      if (data) {
        setTenant(data);
        setSocialCards(
          (data?.SocialCards || []).reduce(
            (acc, card) => {
              acc[card.Platform] = card.URL;
              return acc;
            },
            {} as { [key: string]: string },
          ) as {
            LinkedIn: string;
            Facebook: string;
            Twitter: string;
            Instagram: string;
            YouTube: string;
            Discord: string;
            WhatsApp: string;
          },
        );
      }
      if (errors) {
        console.log(errors);
      }
    };
    getTenant();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tenant) return;

    setLoading(true);
    const { data, errors } = await API.TENANT.UpdateTenant({
      ...tenant,
      SocialCards: Object.entries(socialCards).map(([platform, url]) => ({
        Platform: platform,
        URL: url,
      })),
    });
    if (data) {
      toast.success(data.message);
    }
    if (errors) {
      errors.forEach((error) => toast.error(error.message));
    }
    setLoading(false);
  };

  const handleImageUpload = (type: "logo" | "picture", base64: string) => {
    if (!tenant) return;

    if (type === "logo") {
      setTenant({ ...tenant, Logo: base64 });
    } else {
      const pictures = tenant.Pictures || [];
      setTenant({ ...tenant, Pictures: [...pictures, base64] });
    }
  };

  const handleRemovePicture = (index: number) => {
    if (!tenant?.Pictures) return;
    const newPictures = [...tenant.Pictures];
    newPictures.splice(index, 1);
    setTenant({ ...tenant, Pictures: newPictures });
  };

  if (!tenant) {
    return <div>Loading...</div>;
  }

  return (
    <form
      onSubmit={handleSubmit}
      className="container mx-auto py-10 space-y-8 px-4"
    >
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Manage your organization's basic details
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Organization Name</Label>
            <Input
              id="name"
              value={tenant.Name}
              onChange={(e) => setTenant({ ...tenant, Name: e.target.value })}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="tagline">Tagline</Label>
            <Input
              id="tagline"
              value={tenant.Tagline || ""}
              onChange={(e) =>
                setTenant({ ...tenant, Tagline: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="website">Website URL</Label>
            <Input
              id="website"
              type="url"
              value={tenant.WebsiteURL || ""}
              onChange={(e) =>
                setTenant({ ...tenant, WebsiteURL: e.target.value })
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Media */}
      <Card>
        <CardHeader>
          <CardTitle>Media</CardTitle>
          <CardDescription>
            Manage your organization's logo and pictures
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label>Logo</Label>
            <ImageUpload
              currentImage={tenant.Logo}
              onUpload={(base64) => handleImageUpload("logo", base64)}
              className="w-32 h-32"
            />
          </div>
          <div className="space-y-2">
            <Label>Pictures</Label>
            <div className="grid grid-cols-3 gap-4">
              {tenant.Pictures?.map((picture, index) => (
                <div key={index} className="relative">
                  <img
                    src={picture}
                    alt={`Picture ${index + 1}`}
                    className="w-full h-40 object-cover rounded-lg"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={() => handleRemovePicture(index)}
                  >
                    Remove
                  </Button>
                </div>
              ))}
              <ImageUpload
                onUpload={(base64) => handleImageUpload("picture", base64)}
                className="w-full h-40"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Social Cards */}
      <Card>
        <CardHeader>
          <CardTitle>Social Media</CardTitle>
          <CardDescription>Manage your social media links</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-2 gap-x-6">
            <div className="flex gap-2 w-full">
              <Label className="min-w-[100px]">LinkedIn</Label>
              <Input
                placeholder="URL"
                type="url"
                value={socialCards?.LinkedIn || ""}
                onChange={(e) => {
                  setSocialCards({
                    ...socialCards,
                    LinkedIn: e.target.value,
                  });
                }}
              />
            </div>
            <div className="flex gap-2 w-full">
              <Label className="min-w-[100px]">Facebook</Label>
              <Input
                placeholder="URL"
                type="url"
                value={socialCards?.Facebook || ""}
                onChange={(e) => {
                  setSocialCards({
                    ...socialCards,
                    Facebook: e.target.value,
                  });
                }}
              />
            </div>
            <div className="flex gap-2 w-full">
              <Label className="min-w-[100px]">Twitter</Label>
              <Input
                placeholder="URL"
                type="url"
                value={socialCards?.Twitter || ""}
                onChange={(e) => {
                  setSocialCards({
                    ...socialCards,
                    Twitter: e.target.value,
                  });
                }}
              />
            </div>
            <div className="flex gap-2 w-full">
              <Label className="min-w-[100px]">Instagram</Label>
              <Input
                placeholder="URL"
                type="url"
                value={socialCards?.Instagram || ""}
                onChange={(e) => {
                  setSocialCards({
                    ...socialCards,
                    Instagram: e.target.value,
                  });
                }}
              />
            </div>
            <div className="flex gap-2 w-full">
              <Label className="min-w-[100px]">YouTube</Label>
              <Input
                placeholder="URL"
                type="url"
                value={socialCards?.YouTube || ""}
                onChange={(e) => {
                  setSocialCards({
                    ...socialCards,
                    YouTube: e.target.value,
                  });
                }}
              />
            </div>
            <div className="flex gap-2 w-full">
              <Label className="min-w-[100px]">Discord</Label>
              <Input
                placeholder="URL"
                type="url"
                value={socialCards?.Discord || ""}
                onChange={(e) => {
                  setSocialCards({
                    ...socialCards,
                    Discord: e.target.value,
                  });
                }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Personalization */}
      <Card>
        <CardHeader>
          <CardTitle>Personalization</CardTitle>
          <CardDescription>Customize your tenant's appearance</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Show Social Cards</Label>
              <p className="text-sm text-muted-foreground">
                Display social media links on your site
              </p>
            </div>
            <Switch
              checked={tenant.Personalization?.ShowSocialCards || false}
              onCheckedChange={(checked) =>
                setTenant({
                  ...tenant,
                  Personalization: {
                    ...(tenant.Personalization || {}),
                    ShowSocialCards: checked,
                  },
                })
              }
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Show Website URL</Label>
              <p className="text-sm text-muted-foreground">
                Display your website URL
              </p>
            </div>
            <Switch
              checked={tenant.Personalization?.ShowWebsiteURL || false}
              onCheckedChange={(checked) =>
                setTenant({
                  ...tenant,
                  Personalization: {
                    ...(tenant.Personalization || {}),
                    ShowWebsiteURL: checked,
                  },
                })
              }
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Show Marquee</Label>
              <p className="text-sm text-muted-foreground">
                Display a scrolling marquee
              </p>
            </div>
            <Switch
              checked={tenant.Personalization?.ShowMarquee || false}
              onCheckedChange={(checked) =>
                setTenant({
                  ...tenant,
                  Personalization: {
                    ...(tenant.Personalization || {}),
                    ShowMarquee: checked,
                  },
                })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Header Placement</Label>
            <select
              className="w-full p-2 border rounded-md"
              value={tenant.Personalization?.HeaderPlacing || "justify-between"}
              onChange={(e) =>
                setTenant({
                  ...tenant,
                  Personalization: {
                    ...(tenant.Personalization || {}),
                    HeaderPlacing: e.target.value as any,
                  },
                })
              }
            >
              <option value="justify-between">Between</option>
              <option value="justify-end">End</option>
              <option value="justify-center">Center</option>
              <option value="justify-start">Start</option>
            </select>
          </div>
          <div className="space-y-2">
            <Label>Marquee Direction</Label>
            <select
              className="w-full p-2 border rounded-md"
              value={tenant.Personalization?.MarqueeDirection || "horizontal"}
              onChange={(e) =>
                setTenant({
                  ...tenant,
                  Personalization: {
                    ...(tenant.Personalization || {}),
                    MarqueeDirection: e.target.value as any,
                  },
                })
              }
            >
              <option value="horizontal">Horizontal</option>
              <option value="vertical">Vertical</option>
            </select>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button type="submit" disabled={loading}>
          {loading ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </form>
  );
}
