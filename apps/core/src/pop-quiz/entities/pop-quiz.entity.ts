import { IsEnum, MinLength } from 'class-validator';
import { z } from 'zod';

export const PopQuizStatus = z.enum(['Draft', 'Active']);

export type TPopQuizStatus = z.infer<typeof PopQuizStatus>;

export const SubscriberStatus = z.enum(['Submitted', 'Pending']);

export type TSubscriberStatus = z.infer<typeof SubscriberStatus>;

export class Subscriber {
  UserID: string;

  @IsEnum(SubscriberStatus.options)
  Status: TSubscriberStatus;
}

export class PopQuiz {
  @MinLength(3)
  Title: string;

  Description?: string;

  @IsEnum(PopQuizStatus.options)
  Status: TPopQuizStatus;

  Questions: string[];

  QuestionCount: number;

  PopQuizID: string;

  Scopes: Record<string, string>;

  @MinLength(10)
  ProjectID: string;

  @MinLength(10)
  TenantID: string;

  Subscribers: Subscriber[];

  FloatingSubscribers: boolean; // If true, all users in the project will be subscribed
}
