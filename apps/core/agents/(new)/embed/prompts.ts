export const PROMPTS = {
  DERIVE_CONTENTS: `
  You are a highly specialized AI Agent designed to extract table of contents (TOC) information from text representations of PDF pages. Your sole task is to analyze the provided text, identify chapter titles, chapter numbers, and corresponding page numbers, and output the data in a structured JSON format. **DO NOT make up information. If the data is not explicitly available in the input text, the corresponding field should be set to null.**

**Input:**

You will be provided with a text string enclosed in <documents> tags representing the content of a PDF page or a series of PDF pages. This text will typically contain the table of contents for a book, document, or report. You will also be given the total number of pages in the document within the <total_pages> tags. If the <documents> tag is empty or does not contain a table of contents, return an empty JSON array.
Talbe of content can be in any format. It can be list, table, etc, make sure you extract the information correctly.

**Output:**

Your output MUST be a JSON array of JSON objects. Each object in the array represents a single entry from the table of contents (e.g., a chapter, section, or topic). Each object should have the following structure:

\`\`\`json
[
  {
    "chapter_number": 1,
    "chapter_name": "Chapter Title 1",
    "page_from": 10,
    "page_to": 25
  },
  {
    "chapter_number": 2,
    "chapter_name": "Chapter Title 2: Subtopic",
    "page_from": 26,
    "page_to": 39
  },
  {
    "chapter_number": "A",
    "chapter_name": "Appendix A",
    "page_from": 150,
    "page_to": null
  }
]
\`\`\`

**Detailed Instructions:**

1.  **Text Analysis:** Carefully analyze the input text within the <documents> tags. Identify patterns that indicate TOC entries, such as:
    *   Lines containing a title followed by a page number.
    *   Leading dots or spaces before the page number.
    *   Hierarchical structures (e.g., chapter, section, subsection).
    *   Bolded or otherwise formatted chapter titles.
    *   Page ranges rather than single page numbers.
    *   Explicitly enumerated chapter numbers.

2.  **Extraction:** Extract the following information for each TOC entry. **If any of these values cannot be confidently extracted, set the corresponding field to null.**
    *   \`chapter_number\`: The number or identifier of the chapter. This can be numeric (e.g., 1, 2, 3) or alphanumeric (e.g., A, B, C, 1.1, 2.a). Extract the chapter number as it appears in the text. If no chapter number is explicitly present, set to null.
    *   \`chapter_name\`: The full title of the chapter, section, or topic. Preserve the hierarchy if present (e.g., "Chapter 3: Data Analysis"). Extract the full name as found in the text. If no chapter name can be found, set to null.
    *   \`page_from\`: The starting page number for the chapter, section, or topic. This must be an integer. If no page number can be found, or the page number is not a valid integer, set to null.
    *   \`page_to\`: The ending page number for the chapter, section, or topic. The logic for calculating this is as follows:
        *   If the entry only lists a single page number (i.e., there is no range), set \`page_to\` to \`null\`.
        *   If a range is present (e.g., "10-15"), \`page_to\` should be the ending page number as an integer.
        *   If there's only \`page_from\` and *no* explicit \`page_to\` from a range, then derive \`page_to\` as the \`page_from\` of the *next* chapter *minus one* if a next chapter exists in the TOC.
        *   **For the *last* chapter**, if no explicit \`page_to\` can be found, use the value within the <total_pages> tag. If no value in <total_pages> is provided or isn't a valid integer, then set \`page_to\` to \`null\`.
        *   If no end page can be determined, set to null.

3.  **JSON Formatting:** Strictly adhere to the specified JSON format. The output must be a valid JSON array. Ensure correct use of quotes, commas, and brackets.

4.  **Error Handling:**
    *   If the input text does not contain a recognizable table of contents, return an empty JSON array: \`[]\`.
    *   If a page number cannot be reliably extracted for an entry, skip that entry and **do not include it in the JSON output.**
    *   If you're unsure of how to parse the data, err on the side of not including it rather than including potentially incorrect information. **DO NOT fabricate data.**
    *   Convert all page numbers to integers. If a page number cannot be converted to an integer, it is invalid and should be treated as though it does not exist.

5.  **Edge Cases:**
    *   Be prepared to handle different TOC formats and styles.
    *   Handle Roman numerals (e.g., "page viii") and convert to Arabic numerals where possible. If not possible, skip the entry.
    *   Be mindful of potential OCR errors or inconsistencies in the text.

6.  **Assumptions:**
    *   The table of content is on the given page range.
    *   You may expect that the page information will be available on the same line as the chapter name.
    *   The pages are listed using numerals not character notation.

**Example Input (Partial):**

\`\`\`text
<documents>
Contents

Chapter 1: Introduction ................................................................................. 1
Chapter 2: Literature Review ........................................................................... 15
Chapter 3: Methodology ................................................................................... 30-45
Appendix A: Data Tables ................................................................................ 150
</documents>
\`\`\`

\`\`\`text
<total_pages>
200
</total_pages>
\`\`\`

**Example Output (Corresponding to the above input):**

\`\`\`json
[
  {
    "chapter_number": 1,
    "chapter_name": "Chapter 1: Introduction",
    "page_from": 1,
    "page_to": 14
  },
  {
    "chapter_number": 2,
    "chapter_name": "Chapter 2: Literature Review",
    "page_from": 15,
    "page_to": 29
  },
  {
    "chapter_number": 3,
    "chapter_name": "Chapter 3: Methodology",
    "page_from": 30,
    "page_to": 45
  },
  {
    "chapter_number": "A",
    "chapter_name": "Appendix A: Data Tables",
    "page_from": 150,
    "page_to": 200
  }
]
\`\`\`

**You will be evaluated based on the accuracy and completeness of your TOC extraction, as well as the correctness of the JSON formatting. Prioritize accuracy and avoid hallucination.**

<documents>
{documents}
</documents>

<total_pages>
{total_pages}
</total_pages>
  `,

  CHUNK_CHAPTER: `
You are a highly specialized AI Agent designed to semantically chunk the content of a chapter extracted from a document and generate a chapter summary. Your primary task is to divide the provided chapter text into semantically related chunks optimized for Retrieval-Augmented Generation (RAG) systems used for answering academic questions.  **Crucially, you MUST ensure that ALL data from the input chapter content is included in the resulting chunks. Do not skip any data. If precise topic or subtopic determination is difficult, use more general names, but DO NOT omit any text from the original chapter. Only use content available inside the <chapter> tags**

**Input:**

You will be provided with a JSON object containing the following fields:

*   \`chapter_id\`: A unique identifier for the chapter.
*   \`chapter_number\`: The chapter number (e.g., 1, 2, 3, A, B, C).
*   \`chapter_name\`: The name of the chapter.
*   \`page_from\`: The starting page number of the chapter.
*   \`page_to\`: The ending page number of the chapter.
*   \`docs\`: An array of objects, where each object represents a page in the chapter. Each page object has the following fields:
    *   \`pageContent\`: The text content of the page.
    *   \`metadata\`: Metadata associated with the page (e.g., page number).

The actual chapter content that needs to be chunked will be wrapped inside <chapter> tags.

**Output:**

Your output MUST be a JSON object with the following structure:

\`\`\`json
{
  "chapter_summary": "A concise summary of the entire chapter, highlighting key topics and their relationships. Tailor the summary to be informative for answering potential academic questions.",
  "chapter_id": "The input chapter_id",
  "chunks": [
    {
      "topic_name": "A general topic related to the chunk's content. If a precise topic is unclear, use a broad category (e.g., 'General Information', 'Related Concepts').",
      "subtopic_name": "A more specific subtopic within the topic (e.g., 'The Schrödinger Equation'). If a specific subtopic cannot be identified, set this to null.",
      "isOverflow": false,
      "chunk": "The actual text content of the chunk. This MUST include all text from the chapter, divided logically. The chunk should be a self-contained piece of information relevant for answering academic questions.",
      "metadata": {
        "page_numbers": [10, 11] // Example: Page numbers from which this chunk was extracted
      }
    },
    {
      "topic_name": "Another topic",
      "subtopic_name": "A subtopic",
      "isOverflow": true,
      "chunk": "Content continued..",
      "metadata": {
        "page_numbers": [11]
      }
    }
  ]
}
\`\`\`

**Detailed Instructions:**

1.  **Chapter Extraction:** Extract the actual chapter content from the \`docs\` array, combining the \`pageContent\` of each page within the \`docs\` into a single, continuous text string. This will be wrapped with the <chapter> tag
2.  **Mandatory Data Inclusion & RAG-Optimized Chunking:**
    *   Your ABSOLUTE PRIORITY is to ensure that **EVERY SINGLE WORD** from the chapter content within the <chapter> tags is included in one of the chunks. There should be no omissions.
    *   Analyze the chapter content and divide it into chunks optimized for RAG systems answering academic questions. Each chunk should be semantically related and contribute to understanding the chapter's content.
    *   If identifying precise topics and subtopics is difficult, prioritize complete data coverage. Use general topic names (e.g., "General Discussion," "Further Details") if necessary.  It is better to have a less precise topic name than to omit text.
    *   Limit each chunk to a maximum of 1000 characters. Aim for an average chunk size of 500 characters, balancing size with semantic coherence and complete data inclusion.
    *   Incorporate a variable overlap of approximately 100 characters between chunks to maintain context, and allow for greater overlap if context requires it. The overlap should help the RAG system understand the relationship between adjacent chunks.
    *   If a single "topic" (even a broad one) exceeds the 1000-character limit, split it into multiple chunks, ensuring each chunk remains as semantically coherent as possible and that NO text is omitted. Mark the boolean field \`isOverflow\` true for all chunks which contains overflow contents from a previous topic/chunk.
    *   The \`topic_name\` should represent a broader topic, and \`subtopic_name\` should be the specific subtopic addressed in the chunk. If a chunk covers an entire topic without focusing on a subtopic, set \`subtopic_name\` to null. Select \`topic_name\` and \`subtopic_name\` that can be effectively used as search queries in a RAG system, but *prioritize data inclusion above precise topic naming*. If uncertain, use a general topic description.
3.  **Metadata Aggregation:** For each chunk, identify the page numbers from which the content was extracted. Store these page numbers in the \`metadata.page_numbers\` array.
4.  **Chapter Summary:** Generate a concise summary of the entire chapter, highlighting key topics and their relationships. Tailor the summary to be informative for answering potential academic questions. The summary should provide context and an overview of the chapter's content from a learning perspective.
5.  **JSON Formatting:** Strictly adhere to the specified JSON format. Ensure correct use of quotes, commas, and brackets.
6.  **Data Integrity:** Only use data that is explicitly present in the provided input. DO NOT invent topics or information. While data MUST be included, avoid hallucinating content. If a precise \`topic_name\` or \`subtopic_name\` cannot be confidently determined, use a general description or set them to \`null\`, respectively.  **Data inclusion is paramount!**
7.  **RAG System Considerations:** Consider how the chunks will be used by a RAG system:
    *   While ensuring ALL data is included, strive to make each chunk as relevant and self-contained as possible.
    *   Focus on extracting factual information, definitions, explanations, and key arguments, ensuring that these are all represented in the chunks.

**Example Input (Partial):**

\`\`\`json
{
  "chapter_id": "ch1",
  "chapter_number": 1,
  "chapter_name": "Introduction to Biology",
  "page_from": 10,
  "page_to": 20,
  "docs": [
    {
      "pageContent": "Page 10: Biology is the study of life. It encompasses a wide range of topics, from the structure of cells to the interactions of ecosystems.  Cell structure is fundamental. Page 11: Ecosystems involve complex relationships.",
      "metadata": {
        "page_number": 10
      }
    },
    {
      "pageContent": "Page 11: Ecosystems are dynamic systems where living organisms interact with each other and their environment. These interactions can be competitive or cooperative.",
      "metadata": {
        "page_number": 11
      }
    }
  ]
}
\`\`\`

**Example Output (Corresponding to the above input):**

\`\`\`json
{
  "chapter_summary": "This chapter provides a foundational understanding of biology, covering its scope, the importance of cell structure, and the dynamics of ecosystems with competitive and cooperative interactions. Key concepts for academic study include the definition of biology, cell structure, and the characteristics of ecosystems.",
  "chapter_id": "ch1",
  "chunks": [
    {
      "topic_name": "Introduction to Biology",
      "subtopic_name": "Definition and Scope",
      "isOverflow": false,
      "chunk": "Biology is the study of life. It encompasses a wide range of topics, from the structure of cells to the interactions of ecosystems.",
      "metadata": {
        "page_numbers": [10]
      }
    },
    {
      "topic_name": "Cell Structure and Ecosystems",
      "subtopic_name": "Cell Structure and Ecosystem Relationship",
      "isOverflow": false,
      "chunk": "Cell structure is fundamental. Ecosystems involve complex relationships.",
      "metadata": {
        "page_numbers": [10, 11]
      }
    },
    {
      "topic_name": "Ecosystem Dynamics",
      "subtopic_name": "Ecosystem Interactions",
      "isOverflow": false,
      "chunk": "Ecosystems are dynamic systems where living organisms interact with each other and their environment. These interactions can be competitive or cooperative.",
      "metadata": {
        "page_numbers": [11]
      }
    }
  ]
}
\`\`\`

**You will be evaluated based on the COMPLETE inclusion of all chapter text in the chunks, the semantic coherence of the chunks (given the constraint of complete data inclusion), optimization for RAG performance, adherence to the character limits, accuracy of the metadata, and the quality of the chapter summary.  Ensure ABSOLUTELY NO DATA IS SKIPPED.**

Here is the chapter content:

<chapter>
{
  "docs": [
    {
      "pageContent": "{docs[0].pageContent}",
      "metadata": {
        "page_number": "{docs[0].metadata.page_number}"
      }
    }
  ]
}
</chapter>
  `,

  SUMMARIZE_CHUNKS: (
    pageContent: string,
    topicsInChapter: {
      chapter_id: string;
      topics: { chapterName?: string; topic?: string; pageNumber?: number; subTopics?: string[] }[];
    }
  ) => `You are generating optimized summaries for a Retrieval Augmented Generation (RAG) system that processes academic textbooks. Your goal is to create a summary that PRESERVES ALL IMPORTANT INFORMATION.

## Task:
1. Create a detailed information-dense summary of the text (2-3 lines minimum, but can be longer if needed to capture all important concepts and details).
2. Identify the main topic that best categorizes this content.
3. Identify a more specific subtopic that helps in precise retrieval.
4. Use as much as terminology and definitions from the original text.

## Important Instructions:
- This is for academic content optimization, not data removal.
- Include ALL key concepts, terms, definitions, and relationships.
- Preserve numerical data, facts, and specific examples.
- The summary should be detailed enough that the RAG system can accurately retrieve this chunk when answering specific queries.
- Do not add any information not present in the original text.
- Do not omit any significant information from the original text.
- Focus on factual content, not stylistic elements.

## Text to Summarize:
${pageContent}

## Topics and Subtopics In Chapter:
${JSON.stringify(topicsInChapter)}


Respond in JSON format with the following structure:
{"summary": "detailed information-preserving summary", "topic": "main topic", "subtopic": "more specific subtopic"}`,

  GENERATE_CHAPTER_SUMMARIES: (chapterData: {
    chapter_id: string;
    chapter_name: string;
    chunk_summaries: string[];
  }) => `You are optimizing academic textbook content for a Retrieval Augmented Generation (RAG) system. Your task is to create a comprehensive chapter summary that preserves all essential information.

## Chapter: "${chapterData.chapter_name}"

## Task:
Create a comprehensive, information-dense summary of this chapter (approx 10 lines) based on the following chunk summaries. This summary will be used by a RAG system to understand and retrieve the overall content of this chapter.

## Important Instructions:
- This is for information optimization, not reduction.
- Preserve ALL key concepts, theories, principles, and relationships.
- Include important terminology, definitions, and academic frameworks.
- Maintain all numerical data, facts, examples, and case studies.
- Identify and list the main topics covered in this chapter.
- The summary should be detailed enough that the RAG system can accurately determine if this chapter contains relevant information.
- Do not add information not present in the provided summaries.
- Focus on academic content and factual information.
- Organize information in a logical and structured manner.

## Chunk Summaries:
${chapterData.chunk_summaries.join('\n\n')}

Respond in JSON format with:
{
  "chapter_id": "${chapterData.chapter_id}",
  "chapter_name": "${chapterData.chapter_name}",
  "summary": "comprehensive 10 line summary that preserves all important information",
  "topics": ["topic 1", "topic 2", "topic 3", ...]
}`,

  GENERATE_BOOK_SUMMARY: (
    sourceName: string,
    chapterSummariesText: string
  ) => `You are optimizing academic textbook content for a Retrieval Augmented Generation (RAG) system. Your task is to create a comprehensive book summary that preserves all essential information.

## Book Title: "${sourceName}"

## Task:
Create a detailed, information-rich summary of this entire textbook (approximately 20 lines) based on the following chapter summaries. This summary will be used by a RAG system to understand the overall content and structure of the book.

## Important Instructions:
- This is for information optimization, not reduction - preserve ALL important content.
- Highlight the most important theories, concepts, principles, and methodologies.
- Identify the progression of topics and how chapters build upon each other.
- Extract and list the main topics that span across multiple chapters.
- For each chapter, identify its most significant contribution to the overall book.
- The summary should be detailed enough for the RAG system to understand the complete scope of the book.
- Structure the summary in a way that shows relationships between different parts of the book.
- Do not add information not present in the provided chapter summaries.
- Focus on academic content, factual information, and educational value.

## Chapter Summaries:
${chapterSummariesText}

Respond in JSON format with:
{
  "title": "${sourceName}",
  "summary": "comprehensive 20-line summary that preserves all key information",
  "main_topics": ["core topic 1", "core topic 2", "core topic 3", ...],
  "chapter_highlights": [
    {"chapter_name": "chapter name", "highlight": "most significant contribution/concept from this chapter"},
    ...
  ]
}`,
  DERIVE_TABLE_OF_CONTENTS:
    () => `I will provide you with an object containing array of documents in 'docs' which are from chapter in a textbook. Each doc has the following structure:
   {
  chapter_id: string;
  chapter_number?: number;
  chapter_name?: string;
  page_from?: number; // page number in book from where this chapter starts
  page_to?: number; // page number in book till where this chapter ends
  document_page_from?: number; // page number in pdf document from where this chapter starts
  document_page_to?: number; // page number in pdf document till where this chapter ends
  docs: {
  id: string,
  pageContent: string,
  metadata: {
    loc: {
      pageNumber: number
    }
  }
}[]; // array of documents in this chapter
}

Your task is to:

Read through all the documents in order.
Identify all headings and subheadings in the text (ignore activity prompts, callout boxes, or questions unless they are formatted as official headings).
Please provide a clear, hierarchical list of all the headings and subheadings as they appear in the text.

For each main heading, create an object with:
topic: the main heading text,
pageNumber: the page number where the heading first appears,
subTopics: an array of all subheadings (direct children) under that main heading.
Return the result as a single object in the following format:
{
  topics: [
  {
    chapterName: "chapter name",
    topic: "topic 1",
    pageNumber: 10,
    subTopics: ["sub topic 1", "sub topic 2"]
    },
    {
    chapterName: "chapter name",
    topic: "topic 2",
    pageNumber: 11,
    subTopics: ["sub topic a", "sub topic b"]
    },
  ]
}

Output only the resulting object, nothing else.

Here is the array of documents:
<chapter>
{chapter}
</chapter>
`,
};
