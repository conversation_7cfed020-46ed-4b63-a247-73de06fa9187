import { DeleteObjectCommand, GetObjectCommand, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as mm from 'music-metadata';
import { omit } from 'radash';
import GenerateID from '../../common/lib/generate-id';
import { AppTypeEnum } from '../core/user/entities/user.entity';
import { IndSourceModel } from '../embed/schemas/ind-source.schema';
import { SourceModel } from '../embed/schemas/source.schema';
import { LectureService } from '../lecture/lecture.service';
import {
  CommonContentAlbumDto,
  CommonContentDto,
  CreateContentAlbumDto,
  CreateContentDto,
  CreateContentRawDto,
  GenerateContentAlbumDto,
  ListContentAlbumDto,
  UpdateContentAlbumDto,
  UpdateContentMediaDto,
} from './dto/content-album.service.dto';
import { Content } from './entities/content-album.entity';
import { ContentAlbumModel } from './schemas/content-album.schema';

@Injectable()
export class ContentAlbumService {
  private readonly S3_EXPIRATION = 604800; // 1 week
  private s3Client: S3Client;

  constructor(
    @InjectModel(ContentAlbumModel.name)
    private readonly ContentAlbumModel: Model<ContentAlbumModel>,
    @InjectModel(IndSourceModel.name)
    private readonly IndSourceModel: Model<IndSourceModel>,
    @InjectModel(SourceModel.name)
    private readonly SourceModel: Model<SourceModel>,
    private readonly configService: ConfigService,
    private readonly lectureService: LectureService,
    private readonly eventEmitter: EventEmitter2
  ) {
    this.s3Client = new S3Client({
      region: this.configService.get('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      },
    });
  }

  async HandleCoverImage(Image: string, Key: string) {
    if (!Image?.includes('base64')) {
      return {
        Location: Image,
        Key,
      };
    }

    const Bucket = this.configService.get('S3_NXTCAMPUS_PLATFORM');
    const upload = await this.s3Client.send(
      new PutObjectCommand({
        Bucket,
        Key,
        Body: Buffer.from(Image.split(',')[1], 'base64'),
        ContentType: Image.split(';')[0].split(':')[1],
        ACL: 'public-read',
      })
    );

    return {
      Location: `https://${Bucket}.s3.${this.configService.get('AWS_REGION')}.amazonaws.com/${Key}`,
      Key,
    };
  }

  async CreateContentAlbum(Params: CreateContentAlbumDto) {
    const { CoverImage, ...rest } = Params;
    const ContentAlbumID = GenerateID.Generate16Hex('cont_alb_');
    const coverImage = await this.HandleCoverImage(
      CoverImage,
      `academic-lm/content-album/${Params.TenantID}/${ContentAlbumID}/cover`
    );
    return await this.ContentAlbumModel.create({
      ...rest,
      CoverImage: coverImage.Location,
      ContentAlbumID,
    });
  }

  async UpdateContentAlbum(Params: UpdateContentAlbumDto) {
    const { ContentAlbumID, TenantID } = Params;
    const coverImage = await this.HandleCoverImage(
      Params.CoverImage,
      `academic-lm/content-album/${Params.TenantID}/${ContentAlbumID}/cover`
    );
    const updatedContentAlbum = await this.ContentAlbumModel.findOneAndUpdate(
      {
        ContentAlbumID,
        TenantID,
      },
      {
        $set: omit(
          {
            ...Params,
            ...(Params.CoverImage ? { CoverImage: coverImage.Location } : {}),
          },
          ['ContentAlbumID', 'TenantID', 'Contents']
        ),
      },
      {
        new: true,
      }
    );
    return updatedContentAlbum;
  }

  async DeleteContentAlbum(contentAlbum: CommonContentAlbumDto) {
    const getContentAlbum = await this.ContentAlbumModel.findOne({ ContentAlbumID: contentAlbum.ContentAlbumID });
    if (!getContentAlbum) {
      throw new Error('Content album not found');
    }
    // delete all contents
    getContentAlbum.Contents.forEach(async (content) => {
      await this.DeleteContent({
        ContentID: content.ContentID,
        TenantID: contentAlbum.TenantID,
        ContentAlbumID: contentAlbum.ContentAlbumID,
      });
    });
    const deletedContentAlbum = await this.ContentAlbumModel.deleteOne({ ContentAlbumID: contentAlbum.ContentAlbumID });

    return deletedContentAlbum;
  }

  async ListContentAlbums(Params: ListContentAlbumDto) {
    return await this.ContentAlbumModel.find({
      TenantID: Params.TenantID,
      ...(Params.ProjectID ? { ProjectID: Params.ProjectID } : {}),
    })
      .select('-Contents')
      .lean();
  }

  async SaveToS3(Params: { Content: Express.Multer.File; Key: string }) {
    const Bucket = this.configService.get('S3_NXTCAMPUS_PLATFORM');
    const upload = await this.s3Client.send(
      new PutObjectCommand({
        Bucket,
        Key: Params.Key,
        Body: Params.Content.buffer,
        ContentType: Params.Content.mimetype,
      })
    );

    // Generate presigned URL
    const command = new GetObjectCommand({
      Bucket,
      Key: Params.Key,
    });
    const presignedUrl = await getSignedUrl(this.s3Client, command, { expiresIn: this.S3_EXPIRATION });

    const location = `https://${Bucket}.s3.${this.configService.get('AWS_REGION')}.amazonaws.com/${Params.Key}`;

    return {
      SignedUrl: presignedUrl,
      Location: location,
      Key: Params.Key,
    };
  }

  async DeleteFromS3(Key: string) {
    const Bucket = this.configService.get('S3_NXTCAMPUS_PLATFORM');
    const deleteObjectCommand = new DeleteObjectCommand({
      Bucket,
      Key,
    });
    await this.s3Client.send(deleteObjectCommand);
  }

  async FileStats(Params: { File: Express.Multer.File }) {
    const buffer = Params.File.buffer;
    const metadata = await mm.parseBuffer(buffer);
    return {
      FileSize: Params.File.size,
      Duration: Math.round(metadata.format.duration), // in seconds
    };
  }

  async CreateContentRaw(Params: CreateContentRawDto) {
    const { ContentAlbumID, TenantID } = Params;

    const contentAlbum = await this.ContentAlbumModel.findOne({ ContentAlbumID, TenantID });

    if (!contentAlbum) {
      throw new NotFoundException('Content album not found');
    }

    const ContentID = GenerateID.Generate24Hex('cont_');

    const newContent: Content = {
      ...Params,
      ContentID,
      Duration: Params?.Duration ?? 0,
    };

    contentAlbum.Contents.push(newContent);
    contentAlbum.ContentCount++;
    contentAlbum.ContentDuration += newContent.Duration;
    await contentAlbum.save();

    return newContent;
  }

  async CreateContent(Params: CreateContentDto) {
    const { ContentAlbumID, TenantID, Meta, Content, ContentGroupID } = Params;
    const contentAlbum = await this.ContentAlbumModel.findOne({ ContentAlbumID, TenantID });
    if (!contentAlbum) {
      throw new NotFoundException('Content album not found');
    }

    const ContentID = GenerateID.Generate24Hex('cont_');

    const ContentKey = `academic-lm/content-album/${TenantID}/${ContentAlbumID}/${ContentID}`;
    const FileType = Content?.mimetype;

    // Upload to S3
    const S3Upload =
      Params?.Content &&
      (await this.SaveToS3({
        Content: Content,
        Key: ContentKey,
      }));

    const Duration = Params?.Duration ?? (Params?.Content ? (await this.FileStats({ File: Content })).Duration : 0);
    const ContentModel = {
      ContentID,
      ContentGroupID,
      Meta,
      Duration,
      S3: Params?.Content && {
        Location: S3Upload.Location,
        Key: S3Upload.Key,
        Type: FileType,
        FileSize: Content.size / 1024, // in KB
        Expiration: new Date(Date.now() + this.S3_EXPIRATION * 1000),
        AccessUrl: S3Upload.SignedUrl,
        FileName: Content.originalname,
      },
      CDN: {
        URL: `${this.configService.get('CDN_URL')}/${ContentKey}?t=${Date.now()}`,
      },
      SortOrder: Params?.SortOrder ?? contentAlbum.Contents.length + 1,
    };

    // HLS??

    // Save to DB
    contentAlbum.Contents.push(ContentModel);
    contentAlbum.ContentCount++;
    contentAlbum.ContentDuration += Duration ?? 0;

    return await contentAlbum.save();
  }

  async UploadContentMedia(Params: UpdateContentMediaDto) {
    const { ContentAlbumID, TenantID, Content, ContentID } = Params;
    const contentAlbum = await this.ContentAlbumModel.findOne({ ContentAlbumID, TenantID });
    if (!contentAlbum) {
      throw new NotFoundException('Content album not found');
    }

    const ContentKey = `academic-lm/content-album/${TenantID}/${ContentAlbumID}/${ContentID}`;
    const FileType = Content.mimetype;

    const content = contentAlbum.Contents.find((content) => content.ContentID === ContentID);

    // Upload to S3
    const { SignedUrl, Location, Key } = await this.SaveToS3({
      Content: Content,
      Key: ContentKey,
    });

    const Duration = (await this.FileStats({ File: Content })).Duration;
    content.S3 = {
      Location,
      Key,
      Type: FileType,
      FileSize: Content.size / 1024, // in KB
      Expiration: new Date(Date.now() + this.S3_EXPIRATION * 1000),
      AccessUrl: SignedUrl,
      FileName: Content.originalname,
    };
    content.CDN = {
      URL: `${this.configService.get('CDN_URL')}/${ContentKey}?t=${Date.now()}`,
    };

    // Save to DB
    contentAlbum.ContentDuration += Duration;
    contentAlbum.ContentDuration -= content.Duration;

    content.Duration = Duration;

    return await contentAlbum.save();
  }

  async UpdateContentMeta(
    Params: Pick<CreateContentDto, 'ContentAlbumID' | 'TenantID' | 'Meta' | 'ContentGroupID' | 'Scopes'> & {
      ContentID: string;
      SortOrder?: number;
    }
  ) {
    const { ContentAlbumID, TenantID, Meta, ContentID, ContentGroupID, Scopes, SortOrder } = Params;
    const contentAlbum = await this.ContentAlbumModel.findOne({ ContentAlbumID, TenantID });

    if (!contentAlbum) {
      throw new NotFoundException('Content album not found');
    }

    const updatedContentAlbum = await this.ContentAlbumModel.findOneAndUpdate(
      {
        ContentAlbumID,
        TenantID,
      },
      {
        $set: {
          'Contents.$[elem].Meta': Meta,
          ...(ContentGroupID && { 'Contents.$[elem].ContentGroupID': ContentGroupID }),
          ...(Scopes && { 'Contents.$[elem].Scopes': Scopes }),
          ...(SortOrder && { 'Contents.$[elem].SortOrder': SortOrder }),
        },
      },
      {
        arrayFilters: [
          {
            'elem.ContentID': ContentID,
          },
        ],
      }
    );
    return updatedContentAlbum;
  }

  async GetContentAlbum(Params: CommonContentAlbumDto) {
    const { ContentAlbumID, TenantID } = Params;
    const contentAlbum = await this.ContentAlbumModel.findOne({ ContentAlbumID, TenantID });
    return contentAlbum;
  }

  async GetContent(Params: CommonContentDto) {
    const { ContentID, TenantID, ContentAlbumID } = Params;
    const contentAlbum = await this.ContentAlbumModel.findOne({ ContentAlbumID, TenantID });
    if (!contentAlbum) {
      throw new NotFoundException('Content album not found');
    }
    const content = contentAlbum.Contents.find((content) => content.ContentID === ContentID);
    if (!content) {
      throw new NotFoundException('Content not found');
    }
    // if content presigned url expired, generate new one
    if (content.S3.Expiration < new Date()) {
      const command = new GetObjectCommand({
        Bucket: this.configService.get('S3_NXTCAMPUS_PLATFORM'),
        Key: content.S3.Key,
      });
      content.S3.AccessUrl = await getSignedUrl(this.s3Client, command, { expiresIn: this.S3_EXPIRATION });
      content.S3.Expiration = new Date(Date.now() + this.S3_EXPIRATION * 1000);
      await contentAlbum.save();
    }

    return content;
  }

  async DeleteContent(Params: CommonContentDto) {
    const { ContentID, TenantID, ContentAlbumID } = Params;
    const contentAlbum = await this.ContentAlbumModel.findOne({ ContentAlbumID, TenantID });
    if (!contentAlbum) {
      throw new NotFoundException('Content album not found');
    }

    const updatedContentAlbum = await this.ContentAlbumModel.findOneAndUpdate(
      {
        ContentAlbumID,
        TenantID,
      },
      {
        $pull: {
          Contents: {
            ContentID,
          },
        },
      }
    );

    const content = updatedContentAlbum.Contents.find((content) => content.ContentID === ContentID);
    updatedContentAlbum.ContentCount--;
    updatedContentAlbum.ContentDuration -= content.Duration;
    await updatedContentAlbum.save();

    // Delete from S3
    await this.DeleteFromS3(content.S3.Key);
    return updatedContentAlbum;
  }

  async GenerateContentAlbum(Params: GenerateContentAlbumDto, UserID: string, AppType: AppTypeEnum) {
    const { TenantID, ProjectID, SourceID, Name, Description, CoverImage } = Params;

    const source =
      AppType === 'Academic'
        ? await this.SourceModel.findOne({ SourceID, TenantID, ProjectID })
        : await this.IndSourceModel.findOne({ SourceID, TenantID, ProjectID });
    if (!source) {
      throw new NotFoundException('Source not found');
    }

    const ContentAlbumID = GenerateID.Generate16Hex('cont_alb_');

    const coverImage = await this.HandleCoverImage(
      CoverImage,
      `academic-lm/content-album/${TenantID}/${ContentAlbumID}/cover`
    );

    // Emit event for background processing
    this.eventEmitter.emit('content-album.generate', {
      ContentAlbumID,
      TenantID,
      ProjectID,
      SourceID,
      Name,
      Description,
      CoverImageLocation: coverImage.Location,
      UserID,
      AppType,
    });

    // Return immediately with a message
    return {
      message: 'Content album generation started in background',
      status: 'processing',
      sourceName: source.SourceName,
    };
  }
}
