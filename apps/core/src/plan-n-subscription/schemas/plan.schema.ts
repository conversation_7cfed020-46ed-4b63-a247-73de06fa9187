import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import GenerateID from '../../../common/lib/generate-id';
import { ProjectModel } from '../../project/schemas/project.schema';
import mongoose from 'mongoose';
import { CapType, CreditUsageProperties, PricingType } from '../entities/plan-n-subscription.entity';

@Schema({
  _id: null,
})
export class CreditUsage {
  @Prop({
    type: String,
    required: true,
  })
  CapType: CapType;

  @Prop({
    type: Number,
    required: true,
  })
  CapQuota: number; // 0 for unlimited
}

@Schema({
  _id: null,
})
export class PricingStructure {
  @Prop({
    type: String,
    required: true,
  })
  Type: PricingType;

  @Prop({
    type: Number,
    required: true,
  })
  Price: number;

  @Prop({
    type: Number,
  })
  Duration: number; // in days

  @Prop({
    type: CreditUsage,
    default: {},
  })
  CreditUsage: CreditUsage;
}

@Schema({
  timestamps: true,
  versionKey: false,
  collection: 'plans',
})
export class PlanModel {
  @Prop({
    type: String,
    default: () => GenerateID.Generate10Hex('plan_'),
  })
  PlanID: string;

  @Prop({
    type: String,
    required: true,
  })
  Name: string;

  @Prop({
    type: String,
  })
  Subtitle: string;

  @Prop({
    type: String,
  })
  Description: string;

  @Prop({
    type: String,
  })
  Currency: string;

  @Prop({
    type: Array,
  })
  Features: { Title: string; HelpText?: string }[];

  @Prop({
    type: Array,
    default: [],
  })
  ProjectIDs: string[];

  @Prop({
    type: String,
  })
  Tier: string;

  @Prop({
    type: String,
    required: true,
  })
  TenantID: string;

  @Prop({
    type: [
      {
        FlagID: String,
        Properties: mongoose.Schema.Types.Mixed,
      },
    ],
  })
  Flags: { FlagID: string; Properties: Record<string, unknown> }[];

  @Prop({
    type: [PricingStructure],
  })
  PricingStructures: PricingStructure[];

  @Prop({
    type: Boolean,
    default: false,
  })
  ListAsPublic: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  MarkAsTrailPlan: boolean;

  @Prop({
    type: Boolean,
  })
  Archived: boolean;
}

export const PlanSchema = SchemaFactory.createForClass(PlanModel);

PlanSchema.virtual('Projects', {
  ref: ProjectModel.name,
  localField: 'ProjectIDs',
  foreignField: 'ProjectID',
  justOne: false,
});
