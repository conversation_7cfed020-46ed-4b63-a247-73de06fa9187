import { DynamicModule, Module, Provider } from '@nestjs/common';
import { S3 } from 'aws-sdk';
import s3Service from './s3.service';

@Module({})
export class S3Module {
  static forRoot(s3Client: S3, cb: () => void): DynamicModule {
    const provider: Provider = {
      provide: 'S3Client',
      useFactory: async () => {
        return new s3Service(s3Client);
      },
    };
    return {
      module: S3Module,
      imports: [],
      providers: [provider],
      exports: [provider],
      global: true,
    };
  }
}
