# Zuma AI

[![Build Status]](https://gitlab.com/nextcampus/academic-lm-sda.git/badges/main/pipeline.svg)

## Project Overview

This platform consists of multiple applications:

- **Admin**: Next.js-based administrative interface for content management
- **Insurance Admin**: Specialized admin interface for insurance-related content
- **Core**: NestJS backend services powering the platform
- **Subscriber**: Next.js frontend for regular subscribers
- **Insurance Subscriber**: Specialized subscriber interface for insurance content
- **Agentic AI**: FastAPI service for AI agent

## Tech Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: NestJS (Node.js), FastAPI (Python 3.11)
- **Build System**: Turborepo
- **Package Manager**: pnpm
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui

## Project Structure

```
academic-lm/
├── apps/
│   ├── admin/              # Admin interface
│   ├── insurance-admin/    # Insurance admin interface
│   ├── core/               # NestJS backend services
│   ├── subscriber/         # Subscriber frontend
│   ├── insurance-subscriber/ # Insurance subscriber frontend
│   └── agentic-ai/         # FastAPI service for AI agent
├── packages/
│   ├── ui/                 # Shared UI components
│   ├── common-nest/        # Shared NestJS utilities
│   ├── utils/              # Shared utilities
│   ├── eslint-config/      # ESLint configuration
│   └── typescript-config/  # TypeScript configuration
└── tooling/                # Development tools
```

## Getting Started

### Prerequisites

- Node.js 20 or later
- pnpm 10.4.1 or later
- Python 3.11 (for API service)

### Installation

```bash
# Install dependencies
pnpm install

# Development mode
pnpm dev

# Build all applications
pnpm build
```

## Adding UI Components

This project uses shadcn/ui for component library. To add components:

```bash
# Add a component to the shared UI package
pnpm dlx shadcn@latest add button -c packages/ui
```

## Using UI Components

Import components from the UI package:

```tsx
import { Button } from "@workspace/ui/components/button";
```

## Docker Support

The project includes Docker configuration for containerized deployment:

```bash
# Build Core NodeJS application
docker build -t zuma-core --target production .

# Run container
docker run -p 3001:3001 zuma-core

# Build API Python application
docker build -t agentic-ai apps/agentic-ai

# Run container
docker run -p 8000:8000 agentic-ai
```

## Key Features

- Plan and subscription management
- Content album organization
- Multi-tenant architecture (admin/subscriber)
- Insurance-specific interfaces
- Python-based API services alongside Node.js
