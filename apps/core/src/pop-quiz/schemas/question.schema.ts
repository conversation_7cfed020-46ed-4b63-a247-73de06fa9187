import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { QuestionTypes, TQuestionType } from '../entities/question.entity';
import GenerateID from '../../../common/lib/generate-id';
import mongoose from 'mongoose';

@Schema({
  _id: null,
})
export class Options {
  @Prop({
    type: String,
    required: true,
  })
  Title: string;

  @Prop({
    type: Boolean,
  })
  IsCorrect: boolean;

  @Prop({
    type: String,
    required: true,
    default: () => GenerateID.Generate24Hex('opt_'),
  })
  OptionID: string;
}

@Schema({ _id: null })
export class Citation {
  @Prop({
    type: String,
    required: true,
  })
  ID: string;

  @Prop({
    type: String,
    required: true,
  })
  SourceTitle: string;

  @Prop({
    type: String,
    required: true,
  })
  SourceID: string;

  @Prop({
    type: Number,
    required: true,
  })
  PageNumber: number;

  @Prop({
    type: Number,
    required: true,
  })
  DocumentPageNumber: number;
}

@Schema({ _id: null })
export class Answer {
  @Prop({
    type: String,
    required: true,
  })
  Explnation: string;

  @Prop({
    type: [Citation],
    required: true,
  })
  Citations: Citation[];
}

@Schema({
  collection: 'pop_quiz_questions',
  timestamps: true,
  versionKey: false,
})
export class PopQuizQuestionModel {
  @Prop({
    type: String,
    required: true,
  })
  Title: string;

  @Prop({
    type: String,
    required: true,
  })
  QuestionText: string;

  @Prop({
    type: String,
    enum: QuestionTypes.options,
    required: true,
  })
  Type: TQuestionType;

  @Prop({
    type: [String],
    required: true,
  })
  Tags: string[];

  @Prop({
    type: [Options],
    required: true,
  })
  Options: Options[];

  @Prop({ type: Answer, required: true })
  Answer: Answer;

  @Prop({
    type: String,
    enum: ['easy', 'medium', 'hard'],
    required: true,
  })
  Difficulty: 'easy' | 'medium' | 'hard';

  @Prop({
    type: String,
    required: true,
    default: () => GenerateID.Generate24Hex('pop_quest_'),
  })
  QuestionID: string;

  @Prop({
    type: mongoose.Schema.Types.Mixed,
    default: {},
  })
  Scopes: Record<string, string>;

  @Prop({
    type: String,
    required: true,
  })
  SourceID: string;

  @Prop({
    type: String,
    required: true,
  })
  TenantID: string;

  @Prop({
    type: String,
    required: true,
  })
  ProjectID: string;
}

export const PopQuizQuestionSchema = SchemaFactory.createForClass(PopQuizQuestionModel);

PopQuizQuestionSchema.index({ QuestionID: 1 }, { unique: true });
PopQuizQuestionSchema.index({ QuestionID: 1, SourceID: 1, TenantID: 1 }, { unique: true });
