"use client";
import { useEffect, useState } from "react";
import { TOKEN_COUNTER } from "@/apis/token-counter";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { useRouter } from "next/navigation";
import { formatNumber } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";

type TGetTenantTokenUsageResponse = {
  TotalCounts: {
    TotalTokens: number;
    InteractionCount: number;
  };
  Projects: {
    ProjectID: string;
    Project: {
      Name: string;
      Description: string;
    };
    Resources: {
      ResourceType: "message" | "lecture" | "pop_quiz" | "embedding";
      TotalTokens: number;
      InteractionCount: number;
    }[];
  }[];
};

export default function BillingPage() {
  const [data, setData] = useState<TGetTenantTokenUsageResponse | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchTenantTokenUsage = async () => {
      const { data: responseData } = await TOKEN_COUNTER.GetTenantTokenUsage();
      if (responseData)
        setData(responseData as unknown as TGetTenantTokenUsageResponse);
    };
    fetchTenantTokenUsage();
  }, []);

  if (!data) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="w-full h-full bg-muted/50 min-h-screen">
      <div className="flex flex-col gap-6 p-6 md:p-8">
        {/* Breadcrumbs */}
        <nav className="text-sm mb-2" aria-label="Breadcrumb">
          <ol className="flex items-center gap-2 text-muted-foreground">
            <li>
              <button
                onClick={() => router.push("/")}
                className="hover:underline"
              >
                Home
              </button>
            </li>
            <li>/</li>
            <li className="font-semibold text-foreground">Billing</li>
          </ol>
        </nav>

        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-semibold tracking-tight">Billing</h2>
            <p className="text-sm text-muted-foreground mt-1">
              Manage your billing information and token usage
            </p>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card className="shadow-md rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Tokens
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(data.TotalCounts.TotalTokens)}
              </div>
            </CardContent>
          </Card>
          <Card className="shadow-md rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Interactions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(data.TotalCounts.InteractionCount)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Projects Table */}
        <Card className="shadow-md rounded-xl">
          <CardHeader>
            <CardTitle>Project Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table className="min-w-full border rounded-lg">
                <TableHeader>
                  <TableRow className="bg-muted/70">
                    <TableHead className="border text-left">Project</TableHead>
                    <TableHead className="border text-center" colSpan={2}>
                      Messages
                    </TableHead>
                    <TableHead className="border text-center" colSpan={2}>
                      Lectures
                    </TableHead>
                    <TableHead className="border text-center" colSpan={2}>
                      Pop Quizzes
                    </TableHead>
                    <TableHead className="border text-center" colSpan={2}>
                      Embeddings
                    </TableHead>
                    <TableHead className="border text-center" colSpan={2}>
                      Total
                    </TableHead>
                  </TableRow>
                  <TableRow className="bg-muted/40">
                    <TableHead className="border"></TableHead>
                    <TableHead className="border text-center">Tokens</TableHead>
                    <TableHead className="border text-center">
                      Interactions
                    </TableHead>
                    <TableHead className="border text-center">Tokens</TableHead>
                    <TableHead className="border text-center">
                      Interactions
                    </TableHead>
                    <TableHead className="border text-center">Tokens</TableHead>
                    <TableHead className="border text-center">
                      Interactions
                    </TableHead>
                    <TableHead className="border text-center">Tokens</TableHead>
                    <TableHead className="border text-center">
                      Interactions
                    </TableHead>
                    <TableHead className="border text-center">Tokens</TableHead>
                    <TableHead className="border text-center">
                      Interactions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.Projects.map((project) => {
                    const resourceMap = project.Resources.reduce(
                      (acc, resource) => {
                        acc[resource.ResourceType] = resource;
                        return acc;
                      },
                      {} as Record<string, (typeof project.Resources)[0]>
                    );

                    const totalTokens = project.Resources.reduce(
                      (sum, r) => sum + r.TotalTokens,
                      0
                    );
                    const totalInteractions = project.Resources.reduce(
                      (sum, r) => sum + r.InteractionCount,
                      0
                    );

                    return (
                      <TableRow
                        key={project.ProjectID}
                        className="cursor-pointer transition-colors hover:bg-primary/10 border-b border-muted/60"
                        onClick={() =>
                          router.push(`/billing/project/${project.ProjectID}`)
                        }
                      >
                        <TableCell className="font-medium border text-left">
                          {project.Project.Name}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(resourceMap.message?.TotalTokens || 0)}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(
                            resourceMap.message?.InteractionCount || 0
                          )}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(resourceMap.lecture?.TotalTokens || 0)}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(
                            resourceMap.lecture?.InteractionCount || 0
                          )}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(resourceMap.pop_quiz?.TotalTokens || 0)}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(
                            resourceMap.pop_quiz?.InteractionCount || 0
                          )}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(
                            resourceMap.embedding?.TotalTokens || 0
                          )}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(
                            resourceMap.embedding?.InteractionCount || 0
                          )}
                        </TableCell>
                        <TableCell className="font-medium border text-center">
                          {formatNumber(totalTokens)}
                        </TableCell>
                        <TableCell className="font-medium border text-center">
                          {formatNumber(totalInteractions)}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
