import { Injectable, NotFoundException } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import axios from 'axios';
import { graph as EmbedGraph } from '../../agents/(new)/embed/graph';
import { DocumentStatus } from './schemas/source.schema';
import { Chunk } from './schemas/chunk.schema';
import { TokenCounterService } from '../token-counter/token-counter.service';
import { SourceModel } from './schemas/source.schema';
import GenerateID from '../../common/lib/generate-id';
import { IndSourceModel } from './schemas/ind-source.schema';
import { AppTypeEnum } from '../core/user/entities/user.entity';
import { QuestionService } from '../pop-quiz/qustion.service';
import * as FormData from 'form-data';

interface DocumentUploadedEvent {
  url: string;
  ProjectID: string;
  TenantID: string;
  fileName: string;
  sourceId: string;
  sourceName: string;
  tableOfContentPages: number[];
  pageNumberOffset: number;
  UserID: string;
  language: string;
  sourceType: string;
  version: string;
}

type AgenticEmbedEvent = DocumentUploadedEvent & {
  sourceType: string;
  language: string;
  IsSuperEmbed: boolean;
};

type SuperEmbedEvent = AgenticEmbedEvent;

@Injectable()
export class EmbedProcessorService {
  constructor(
    private configService: ConfigService,
    @InjectModel(SourceModel.name) private sourceChunkModel: Model<SourceModel>,
    @InjectModel(Chunk.name) private chunkModel: Model<Chunk>,
    @InjectModel(IndSourceModel.name) private IndSourceModel: Model<IndSourceModel>,
    private readonly TokenCounterService: TokenCounterService,
    private readonly eventEmitter: EventEmitter2,
    private readonly questionService: QuestionService
  ) {}

  @OnEvent('document.academic-embed')
  async handleDocumentUploaded(payload: DocumentUploadedEvent) {
    this.startEmbedding(payload);
  }

  private async startEmbedding(payload: DocumentUploadedEvent) {
    try {
      // Construct AWS key using the same pattern as in chat.service.ts
      const awsKey = `academic-lm/${payload.TenantID}/${payload.ProjectID}/${payload.sourceId}`;

      // Update status to UPLOADED when the event is received
      await this.updateDocumentStatus(
        payload.sourceId,
        payload.sourceName,
        DocumentStatus.UPLOADED,
        payload.TenantID,
        payload.ProjectID,
        awsKey
      );

      const response = await axios.get(payload.url, {
        responseType: 'arraybuffer',
      });

      const pdfBlob = new Blob([response.data], { type: 'application/pdf' });

      // Update status to PROCESSING before running the graph
      await this.updateDocumentStatus(
        payload.sourceId,
        payload.sourceName,
        DocumentStatus.PROCESSING,
        payload.TenantID,
        payload.ProjectID,
        awsKey
      );

      // Run the index graph with tenant and project scoping
      console.log(
        `Processing document for tenant ${payload.TenantID}, project ${payload.ProjectID}: ${payload.fileName}`
      );

      const result =
        payload.version === 'v1'
          ? await EmbedGraph.invoke({
              pdfBlob,
              tenantId: payload.TenantID,
              projectId: payload.ProjectID,
              sourceId: payload.sourceId,
              sourceName: payload.sourceName,
              tableOfContentPages: payload.tableOfContentPages,
              pageNumberOffset: payload.pageNumberOffset,
            })
          : await this.academicEmbedV2(payload);

      if (payload.version === 'v1') {
        const tokenCounter = await this.TokenCounterService.create({
          TenantID: payload.TenantID,
          ProjectID: payload.ProjectID,
          ResourceIDs: [payload.sourceId],
          ResourceType: 'embedding',
          TokenCount: result.TokenCount,
          UserID: payload.UserID,
          AppType: AppTypeEnum.Academic,
        });
      }

      if (result && typeof result === 'object' && result.chunkedDocs) {
        // Create a map to merge chunk summaries with their corresponding chunks
        const chunkSummariesMap = new Map();

        // Populate the map with summary information if available
        if (result.summarizedChunks && Array.isArray(result.summarizedChunks)) {
          result.summarizedChunks.forEach((summary) => {
            chunkSummariesMap.set(summary.chunk_id, {
              summary: summary.summary,
              topic: summary.topic,
              subtopic: summary.subtopic,
            });
          });
        }

        // Group by chapter
        const chapterMap = new Map();

        // Store chunk documents to be saved
        const chunkDocuments = [];

        result.chunkedDocs.forEach((doc) => {
          if (!doc.metadata || !doc.metadata.chapter) return;

          const chapter = doc.metadata.chapter;
          const chapterId = chapter.chapter_id;

          if (!chapterMap.has(chapterId)) {
            // Find chapter summary if available
            const chapterSummary = result.chapterSummaries?.find((summary) => summary.chapter_id === chapterId);
            const topics = result.chapterWiseTopics?.find((topic) => topic.chapter_id === chapterId)?.topics || [];

            chapterMap.set(chapterId, {
              ChapterID: chapterId,
              ChapterName: chapter.chapter_name || 'Unnamed Chapter',
              ChapterNumber: chapter.chapter_number || 0,
              ChunkIDs: [], // Now just array of IDs
              Summary: chapterSummary?.summary || undefined,
              Topics:
                topics?.map((topic) => ({
                  Topic: topic.topic,
                  PageNumber: topic.pageNumber,
                  SubTopics: topic.subTopics,
                })) || undefined,
              PageNumber: {
                From: chapter?.page_from,
                To: chapter?.page_to,
              },
              DocumentPageNumber: {
                From: chapter?.document_page_from,
                To: chapter?.document_page_to,
              },
            });
          }

          if (doc.metadata.chunk) {
            const chunkId = doc.metadata.chunk.chunk_id;
            const summaryInfo = chunkSummariesMap.get(chunkId) || {};

            // Add chunk ID to the chapter's chunkIDs array
            chapterMap.get(chapterId).ChunkIDs.push(chunkId);

            // Create chunk document for separate collection
            chunkDocuments.push({
              ChunkID: chunkId,
              SourceID: payload.sourceId,
              ChapterID: chapterId,
              ChapterName: chapter.chapter_name || 'Unnamed Chapter',
              ChunkNumber: doc.metadata.chunk.chunk_number,
              PageNumber: doc.metadata.chunk.page_number,
              DocumentPageNumber: doc.metadata.chunk.document_page_number,
              Content: doc.pageContent,
              Summary: summaryInfo.summary,
              Topic: summaryInfo.topic,
              SubTopic: summaryInfo.subtopic,
              TenantID: payload.TenantID,
              ProjectID: payload.ProjectID,
            });
          }
        });

        const chaptersArray = Array.from(chapterMap.values());

        // Convert book summary to our schema format if available
        let bookSummary;
        if (result.bookSummary) {
          bookSummary = {
            Title: result.bookSummary.title,
            Summary: result.bookSummary.summary,
            MainTopics: result.bookSummary.main_topics,
            ChapterHighlights:
              result.bookSummary.chapter_highlights?.map((highlight) => ({
                ChapterName: highlight.chapter_name,
                Highlight: highlight.highlight,
              })) || [],
          };
        }

        if (chaptersArray.length > 0 && chunkDocuments.length > 0) {
          // 1. First, save all chunks to the chunks collection
          console.log(`Saving ${chunkDocuments.length} chunks to database`);
          await this.chunkModel.insertMany(chunkDocuments);

          // 2. Then save the source document with references to chunks
          await this.saveSourceDocument(
            payload.sourceId,
            payload.sourceName,
            {
              chapters: chaptersArray,
              status: DocumentStatus.COMPLETED,
              bookSummary,
            },
            payload.TenantID,
            payload.ProjectID,
            awsKey,
            payload.language
          );
        }
      } else {
        // but don't save chunks (or save empty chunks)
        await this.updateDocumentStatus(
          payload.sourceId,
          payload.sourceName,
          DocumentStatus.FAILED,
          payload.TenantID,
          payload.ProjectID,
          awsKey
        );
      }

      console.log(`Successfully indexed document: ${payload.fileName}`);
    } catch (error) {
      console.error('Failed to process document:', error);

      // Construct AWS key for error handling
      const awsKey = `academic-lm/${payload.TenantID}/${payload.ProjectID}/${payload.sourceId}`;

      // Update status to FAILED in case of error
      await this.updateDocumentStatus(
        payload.sourceId,
        payload.sourceName,
        DocumentStatus.FAILED,
        payload.TenantID,
        payload.ProjectID,
        awsKey,
        error.message
      );

      throw new Error(`Failed to process document: ${error.message}`);
    }
  }

  private async updateDocumentStatus(
    sourceId: string,
    sourceName: string,
    status: DocumentStatus,
    tenantId: string,
    projectId: string,
    awsKey: string,
    errorMessage?: string
  ) {
    try {
      const existingSourceChunk = await this.sourceChunkModel.findOne({ SourceID: sourceId }).exec();

      if (existingSourceChunk) {
        // Update existing document status
        await this.sourceChunkModel
          .updateOne(
            { SourceID: sourceId },
            {
              Status: status,
              ErrorMessage: errorMessage,
              TenantID: tenantId,
              ProjectID: projectId,
              AWSKey: awsKey,
              updatedAt: new Date(),
            }
          )
          .exec();
      } else {
        // Create a new document with status if it doesn't exist yet
        const newSourceChunk = new this.sourceChunkModel({
          SourceID: sourceId,
          SourceName: sourceName,
          Status: status,
          ErrorMessage: errorMessage,
          Chapters: [],
          TenantID: tenantId,
          ProjectID: projectId,
          AWSKey: awsKey,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
        await newSourceChunk.save();
      }

      console.log(`Updated document status to '${status}' for source: ${sourceId}`);
    } catch (error) {
      console.error(`Failed to update document status to '${status}':`, error);
    }
  }

  private async saveSourceDocument(
    sourceId: string,
    sourceName: string,
    sourceData: {
      chapters: any[];
      status?: DocumentStatus;
      bookSummary?: {
        Title: string;
        Summary: string;
        MainTopics: string[];
        ChapterHighlights: {
          ChapterName: string;
          Highlight: string;
        }[];
      };
    },
    tenantId: string,
    projectId: string,
    awsKey: string,
    language: string
  ) {
    try {
      const existingSourceChunk = await this.sourceChunkModel.findOne({ SourceID: sourceId }).exec();
      const status = sourceData.status || DocumentStatus.COMPLETED;

      if (existingSourceChunk) {
        await this.sourceChunkModel
          .updateOne(
            { SourceID: sourceId },
            {
              SourceName: sourceName,
              Chapters: sourceData.chapters,
              Status: status,
              TenantID: tenantId,
              ProjectID: projectId,
              AWSKey: awsKey,
              BookSummary: sourceData.bookSummary,
              Language: language,
              updatedAt: new Date(),
            }
          )
          .exec();
        console.log(`Updated source document for source: ${sourceId} with status: ${status}`);
      } else {
        const newSourceChunk = new this.sourceChunkModel({
          SourceID: sourceId,
          SourceName: sourceName,
          Chapters: sourceData.chapters,
          Status: status,
          TenantID: tenantId,
          ProjectID: projectId,
          AWSKey: awsKey,
          BookSummary: sourceData.bookSummary,
          Language: language,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
        await newSourceChunk.save();
        console.log(`Created new source document for source: ${sourceId} with status: ${status}`);
      }
    } catch (error) {
      console.error('Failed to save source document:', error);
      throw new Error(`Failed to save source document: ${error.message}`);
    }
  }

  private async academicEmbedV2(payload: DocumentUploadedEvent) {
    const form = new FormData();
    form.append('url', payload.url);
    form.append('source_id', payload.sourceId);
    form.append('source_name', payload.sourceName);
    form.append('source_type', payload.sourceType);
    form.append('tenant_id', payload.TenantID);
    form.append('project_id', payload.ProjectID);
    form.append('user_id', payload.UserID);
    form.append('language', payload.language);
    form.append('page_number_offset', payload.pageNumberOffset.toString());
    form.append('toc_pages', `[${payload.tableOfContentPages.join(',')}]`);
    form.append('run_in_background', false.toString());

    const response = await axios.post(`${this.configService.get('AGENTIC_AI_URL')}/api/v1/embedding/academic`, form, {
      headers: form.getHeaders(),
      timeout: 3600000, // 60min for sync
    });

    if (response.status === 200) {
      return response.data.result;
    } else {
      throw new Error('Failed to process document');
    }
  }

  private async startAgenticEmbed(payload: AgenticEmbedEvent) {
    const form = new FormData();
    form.append('url', payload.url);
    form.append('source_id', payload.sourceId);
    form.append('source_name', payload.sourceName);
    form.append('source_type', payload.sourceType);
    form.append('tenant_id', payload.TenantID);
    form.append('project_id', payload.ProjectID);
    form.append('user_id', payload.UserID);
    form.append('language', payload.language);
    form.append('page_number_offset', payload.pageNumberOffset.toString());
    form.append('run_in_background', (payload.IsSuperEmbed ? false : true).toString()); // Convert boolean to string

    try {
      const response = await axios.post(`${this.configService.get('AGENTIC_AI_URL')}/api/v1/embedding`, form, {
        headers: form.getHeaders(),
        timeout: payload.IsSuperEmbed ? 60000 : 1800000, // 1min for background, 30min for sync
      });

      if (response.status === 200) {
        if (payload.IsSuperEmbed) {
          // Background processing started
          return {
            success: true,
            message: response.data.message,
            sourceId: response.data.source_id,
            status: response.data.status,
          };
        } else {
          // Synchronous processing completed
          return {
            success: true,
            message: response.data.message,
            sourceId: response.data.source_id,
            status: response.data.status,
            result: response.data.result,
          };
        }
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new Error('Request timed out while processing the document');
        }
        throw new Error(`Embedding API error: ${error.response?.data?.detail || error.message}`);
      }
      throw error;
    }
  }

  @OnEvent('document.agentic-embed')
  async handleAgenticEmbed(payload: AgenticEmbedEvent) {
    await this.startAgenticEmbed(payload);
  }

  @OnEvent('document.super-embed')
  async startSuperEmbed(payload: SuperEmbedEvent) {
    // 1. Embed the document
    console.log('Started Embedding');
    const result = await this.startAgenticEmbed(payload);
    console.log('Completed Embedding');

    console.log('Started Content Library Generation');
    // 2. Create a content album
    const source = await this.IndSourceModel.findOne({
      SourceID: payload.sourceId,
      TenantID: payload.TenantID,
      ProjectID: payload.ProjectID,
    });
    if (!source) {
      throw new NotFoundException('Source not found');
    }
    const ContentAlbumID = GenerateID.Generate16Hex('cont_alb_');
    // Emit event for background processing
    this.eventEmitter.emit('content-album.generate', {
      ContentAlbumID,
      TenantID: payload.TenantID,
      ProjectID: payload.ProjectID,
      SourceID: payload.sourceId,
      Name: source.SourceName,
      Description: source.SourceSummary,
      CoverImageLocation:
        'https://www.google.com/url?sa=i&url=https%3A%2F%2Fwww.svgrepo.com%2Fsvg%2F508699%2Flandscape-placeholder&psig=AOvVaw2xNyfbC-K1mtuqI9RDZ15a&ust=1748661540400000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPCowYSeyo0DFQAAAAAdAAAAABAE',
      UserID: payload.UserID,
      AppType: AppTypeEnum.Industry,
    });
    console.log('Completed Content Library Generation');

    // 3.  Generate Pop Quiz
    console.log('Started Pop Quiz Generation');
    await Promise.all(
      source.Sections.map(async (section) => {
        await this.questionService.GenerateQuestions(
          {
            ProjectID: payload.ProjectID,
            SourceID: payload.sourceId,
            ID: section.SectionID,
            NumberOfQuestions: 10,
          },
          payload.TenantID,
          payload.UserID,
          AppTypeEnum.Industry
        );
      })
    );
    console.log('Completed Pop Quiz Generation');
    return result;
  }
}
