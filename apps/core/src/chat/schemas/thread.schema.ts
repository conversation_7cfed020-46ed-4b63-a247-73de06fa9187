import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import GenerateID from '../../../common/lib/generate-id';

@Schema({ collection: 'threads', timestamps: true, versionKey: false })
export class Thread {
  @Prop({
    required: true,
    default: () => GenerateID.Generate16Hex('thread_'),
  })
  ThreadID: string;

  @Prop({ required: true })
  ProjectID: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  UserID: string;

  @Prop({ required: true })
  TenantID: string;

  @Prop({ default: 0 })
  messageCount: number;

  @Prop()
  createdAt: Date;

  @Prop()
  updatedAt: Date;
}

export const ThreadSchema = SchemaFactory.createForClass(Thread);
