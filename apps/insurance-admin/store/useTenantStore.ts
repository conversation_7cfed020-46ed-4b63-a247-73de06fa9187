import { API } from "@/apis/api";
import { TTenant } from "@/apis/tenant.api";
import { create } from "zustand";

interface TenantStore {
  tenant: TTenant | null;
  setTenant: (tenant: TTenant | null) => void;
  getTenant: () => TTenant | null;
  fetchTenant: () => Promise<TTenant | null>;
}

export const useTenantStore = create<TenantStore>((set) => ({
  tenant: null,
  setTenant: (tenant: TTenant | null) => {
    set({ tenant });
    localStorage.setItem("tenant", JSON.stringify(tenant));
  },
  getTenant: () => {
    const tenant = localStorage.getItem("tenant");
    if (tenant) {
      set({ tenant: JSON.parse(tenant) });
      return JSON.parse(tenant);
    }
    return null;
  },
  fetchTenant: async () => {
    const { data } = await API.TENANT.GetTenant();
    set({ tenant: data });
    localStorage.setItem("tenant", JSON.stringify(data));
    return data;
  },
}));
