import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import GenerateID from '../../../common/lib/generate-id';
import { HeaderPlacing, THeaderPlacing, TSocialPlatform } from '../entities/tenant.entity';
@Schema({
  _id: false,
})
class Personalization {
  @Prop({
    default: true,
  })
  ShowSocialCards?: boolean;

  @Prop({
    default: true,
  })
  ShowTagline?: boolean;

  @Prop({
    default: true,
  })
  ShowMarquee?: boolean;

  @Prop({
    default: true,
  })
  ShowWebsiteURL?: boolean;

  @Prop({
    default: 'vertical',
  })
  MarqueeDirection?: 'vertical' | 'horizontal';

  @Prop({
    type: String,
    enum: HeaderPlacing.options,
    default: HeaderPlacing.enum['justify-center'],
  })
  HeaderPlacing?: THeaderPlacing;

  @Prop({ default: true })
  ShowTenantName?: boolean;

  @Prop({ default: true })
  ShowTenantLogo?: boolean;
}

@Schema({ _id: null })
class SocialCard {
  @Prop({ required: true })
  Platform: TSocialPlatform;

  @Prop({ required: true })
  URL: string;
}

@Schema({ collection: 'tenants', timestamps: true, versionKey: false })
export class TenantModel {
  @Prop({ required: true, unique: true })
  Name: string;

  @Prop({})
  WebsiteURL?: string;

  @Prop({})
  Tagline?: string;

  @Prop({
    required: true,
    unique: true,
    default: () => GenerateID.Generate16Hex('tnt_'),
  })
  TenantID: string;

  @Prop({ required: true })
  AdminUserID: string;

  @Prop({ required: true, unique: true })
  WorkSpaceDomain: string;

  @Prop({ type: String })
  Logo?: string;

  @Prop({ type: [String] })
  Pictures?: string[];

  @Prop({ type: Personalization })
  Personalization?: Personalization;

  @Prop({ type: [SocialCard] })
  SocialCards?: SocialCard[];
}

export const TenantSchema = SchemaFactory.createForClass(TenantModel);
