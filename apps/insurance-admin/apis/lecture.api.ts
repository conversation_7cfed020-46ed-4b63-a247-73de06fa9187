import { TryCatch } from "@/lib/try-catch";
import { AgeGroup, TLecture, TTSProcess } from "@/types/lecture";
import { TeachingStyle } from "@/types/lecture";
import { TAckResponse, TErrorResponse, KY } from "./_.index";

type TGenerateTTSParams = {
  TenantID: string;
  ProjectID: string;
  SourceID: string;
  LectureID: string;
};

type TGenerateTTSResponse = {
  success: boolean;
  message: string;
  data: TTSProcess;
};

const GenerateLectureScript = async (body: {
  TenantID: string;
  ProjectID: string;
  SourceID: string;
  ChapterID: string;
  TeachingStyle: TeachingStyle;
  AgeGroup: AgeGroup;
  CreativityLevel: number;
}) => {
  return await TryCatch<
    TAckResponse<{
      TeachingPlan: string;
      LectureScript: string;
      SSMLScript: string;
    }>,
    TErrorResponse
  >(KY.post(`lecture/generate`, { json: body }).json());
};

const GetLecture = async (LectureID: string) => {
  return await TryCatch<TAckResponse<TLecture>, TErrorResponse>(
    KY.get(`lecture/${LectureID}`).json(),
  );
};

const GetAllLectures = async (query: {
  TenantID: string;
  ProjectID: string;
  SourceID: string;
  ChapterID?: string;
}) => {
  return await TryCatch<TLecture[], TErrorResponse>(
    KY.get(`lecture`, { searchParams: query }).json(),
  );
};

const DeleteLecture = async (LectureID: string) => {
  return await TryCatch<TAckResponse<void>, TErrorResponse>(
    KY.delete(`lecture/${LectureID}`).json(),
  );
};

const GenerateTTS = async (params: TGenerateTTSParams) => {
  return await TryCatch<TGenerateTTSResponse, TErrorResponse>(
    KY.post(`lecture/tts/${params.LectureID}`, {
      json: {
        TenantID: params.TenantID,
        ProjectID: params.ProjectID,
        SourceID: params.SourceID,
      },
    }).json(),
  );
};

export const LECTURE = {
  GenerateLectureScript,
  GetLecture,
  GetAllLectures,
  DeleteLecture,
  GenerateTTS,
};
