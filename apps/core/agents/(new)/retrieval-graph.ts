import { RunnableConfig } from '@langchain/core/runnables';
import { Annotation, END, START, StateGraph } from '@langchain/langgraph';
import { OpenAIEmbeddings } from '@langchain/openai';
import { PineconeStore } from '@langchain/pinecone';
import { Pinecone } from '@pinecone-database/pinecone';
import { reduceDocs } from './shared';
import { Document } from '@langchain/core/documents';
import { Embeddings } from '@langchain/core/embeddings';

const InputStateAnnotation = Annotation.Root({
  tenantId: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
  projectId: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
  query: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
});

const AgentStateAnnotation = Annotation.Root({
  ...InputStateAnnotation.spec,
  documents: Annotation<Document[], Document[] | { [key: string]: any }[] | string[] | string | 'delete'>({
    default: () => [],
    reducer: reduceDocs,
  }),
});

async function retrieveDocuments(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const vectorStore = await makeRetriever(state.tenantId, state.projectId);

  // Check if query is defined and not empty
  if (!state.query) {
    console.log('Query is undefined or empty, returning empty documents array');
    return { documents: [] };
  }

  const documents = await vectorStore.similaritySearchWithScore(state.query, 10, {
    tenantId: state.tenantId,
    projectId: state.projectId,
  });
  const filteredResponse = documents
    .filter(
      (doc) => doc?.[0]?.metadata?.tenantId === state.tenantId && doc?.[0]?.metadata?.projectId === state.projectId
    )
    .map((doc) => doc?.[0]);
  return { documents: filteredResponse };
}

async function recheckAndFilter(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  return state;
}

export async function makeRetriever(indexName: string, namespace: string): Promise<PineconeStore> {
  const _indexName = process.env.PINECONE_INDEX_NAME;
  if (!_indexName) {
    throw new Error('PINECONE_INDEX_NAME environment variable is not defined');
  }
  const pinecone = new Pinecone();
  const pineconeIndex = pinecone.Index(_indexName!);
  const embeddingModel = makeTextEmbeddings();

  const vectorStore = await PineconeStore.fromExistingIndex(embeddingModel, {
    pineconeIndex,
    namespace,
  });
  return vectorStore;
}

function makeTextEmbeddings(): Embeddings {
  return new OpenAIEmbeddings({ model: 'text-embedding-3-small' });
}

const builder = new StateGraph({ stateSchema: AgentStateAnnotation })
  .addNode('retrieveDocuments', retrieveDocuments)
  .addNode('recheckAndFilter', recheckAndFilter)
  .addEdge(START, 'retrieveDocuments')
  .addEdge('retrieveDocuments', 'recheckAndFilter')
  .addEdge('recheckAndFilter', END);

export const retrievalGraph = builder.compile().withConfig({ runName: 'RetrievalGraph' });
