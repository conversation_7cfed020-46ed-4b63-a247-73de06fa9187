"use client";
import { API } from "@/apis/api";
import { Content, TContentAlbum } from "@/apis/content-album.api";
import AdvanceEditor from "@workspace/ui/components/editor";
import Orb from "@/components/ui/orb";
import { useProjectContext } from "@/contexts/project-context";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@workspace/ui/components/accordion";
import { Slider } from "@workspace/ui/components/slider";
import { cn } from "@workspace/ui/lib/utils";
import {
  AudioLines,
  Bird,
  Clapperboard,
  Clock8,
  Pause,
  Play,
  ScrollText,
  Squirrel,
  Video,
} from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

export default function ContentLibrary() {
  const { album_id } = useParams<{ album_id: string }>();
  const { selectedProject } = useProjectContext();
  const [album, setAlbum] = useState<TContentAlbum | null>(null);
  const [selectedContent, setSelectedContent] = useState<Content | null>(null);

  const getContentAlbum = async () => {
    const { data, errors } = await API.CONTENT_ALBUM.GetContentAlbum({
      ContentAlbumID: album_id,
      ProjectID: selectedProject?.id!,
    });

    if (errors) {
      errors.forEach((error) => toast.error(error.message));
    }

    if (data) {
      setAlbum(data);
    }
  };

  useEffect(() => {
    if (!selectedProject) return;
    getContentAlbum();
    setSelectedContent(null);
    setAlbum(null);
  }, [selectedProject]);

  function AudioPlayer({
    selectedAudioBook,
  }: {
    selectedAudioBook: Content | null;
  }) {
    const audioRef = useRef<HTMLAudioElement>(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);

    const handlePlayPause = () => {
      if (!audioRef.current) return;
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    };

    const handleTimeUpdate = () => {
      if (!audioRef.current) return;
      setCurrentTime(audioRef.current.currentTime);
    };

    const handleLoadedMetadata = () => {
      if (!audioRef.current) return;
      setDuration(audioRef.current.duration);
    };

    const formatTime = (time: number) => {
      const minutes = Math.floor(time / 60)
        .toString()
        .padStart(2, "0");
      const seconds = Math.floor(time % 60)
        .toString()
        .padStart(2, "0");
      return `${minutes}:${seconds}`;
    };

    const handleSliderChange = (value: number[]) => {
      if (!audioRef.current || !value.length) return;
      const time = value[0];
      audioRef.current.currentTime = time ?? 0;
      setCurrentTime(time ?? 0);
    };

    useEffect(() => {
      if (selectedAudioBook?.S3?.AccessUrl && audioRef.current) {
        audioRef.current.load(); // reload the new URL
        audioRef.current
          .play()
          .then(() => {
            setIsPlaying(true);
          })
          .catch((error) => {
            console.error("Autoplay failed:", error);
            setIsPlaying(false);
          });
        setCurrentTime(0);
        setDuration(0);
      }
      if (selectedAudioBook?.CDN?.URL) {
        handlePlayPause();
      }
    }, [selectedAudioBook?.CDN?.URL]);

    // set playing false when audio is done
    useEffect(() => {
      if (!audioRef.current) return;
      audioRef.current.addEventListener("ended", () => {
        setIsPlaying(false);
      });
    }, [audioRef]);

    return (
      <div className="flex flex-col w-full p-2 space-y-2">
        <div
          style={{ width: "100%", height: "300px", position: "relative" }}
          className="pointer-events-none"
        >
          <Orb
            hoverIntensity={0.5}
            rotateOnHover={false}
            hue={0}
            forceHoverState={isPlaying}
          />
        </div>
        <div className="flex items-center">
          <div className="flex-1 ml-2">
            <p className="text-sm font-medium">
              {selectedAudioBook?.Meta?.Title ?? "No Audio Book Selected"}
            </p>
            <p className="text-xs text-muted-foreground">
              {formatTime(currentTime)} / {formatTime(duration)}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <button onClick={handlePlayPause} className="cursor-pointer">
              {isPlaying ? (
                <Pause className="h-8 w-8" />
              ) : (
                <Play className="h-8 w-8" />
              )}
            </button>
          </div>
        </div>

        {/* Progress Bar */}
        <Slider
          value={[currentTime]}
          max={duration || 1}
          step={0.1}
          onValueChange={handleSliderChange}
          className="w-full mt-4"
        />

        {/* Hidden Audio Element */}
        <audio
          ref={audioRef}
          src={selectedAudioBook?.CDN?.URL ?? ""}
          preload="metadata"
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
        />
      </div>
    );
  }

  const Player = () => {
    const ref = useRef<HTMLVideoElement>(null);
    useEffect(() => {
      if (ref.current) {
        ref.current.play();
      }
    }, [ref]);
    return (
      <div className="bg-accent w-full rounded-lg overflow-hidden lg:max-h-[500px] lg:min-h-[400px] max-h-[400px] min-h-[200px]">
        {selectedContent && selectedContent?.S3?.Type.includes("video") && (
          <video
            ref={ref}
            src={selectedContent?.CDN?.URL}
            controls
            autoPlay
            className="w-full h-full object-contain"
            controlsList="nodownload"
          />
        )}
        {selectedContent && selectedContent?.S3?.Type.includes("audio") && (
          <div className="">
            <AudioPlayer selectedAudioBook={selectedContent} />
          </div>
        )}
        {!selectedContent && (
          <div className="flex flex-col items-center justify-center h-full min-h-[200px] lg:min-h-[400px]">
            <Bird size={100} strokeWidth={1} />
            <h1 className="text-xl font-semibold mt-4">
              Select content to play
            </h1>
          </div>
        )}
      </div>
    );
  };

  const Transcript = () => {
    return (
      <>
        {selectedContent && (
          <div className="flex flex-col gap-2 overflow-y-auto">
            <div className="flex flex-col">
              <h1 className="lg:text-xl text-md font-semibold">
                {selectedContent?.Meta.Title}
              </h1>
              <p className="lg:text-sm text-xs text-muted-foreground">
                {selectedContent?.Meta.Description ?? "..."}
              </p>
            </div>
            <Accordion
              collapsible
              type="single"
              className="bg-accent px-4 py-2 rounded-lg"
              defaultValue="transcript"
            >
              <AccordionItem value="transcript">
                <AccordionTrigger className="hover:no-underline cursor-pointer">
                  <div className="flex items-center gap-2 hover:no-underline">
                    <ScrollText size={16} />
                    Transcript
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <AdvanceEditor
                    viewOnly
                    content={selectedContent?.Meta?.Transcript}
                  />
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        )}
      </>
    );
  };

  return (
    <div className="h-[calc(100vh-64px-16px-16px)] flex flex-col overflow-y-auto lg:flex-row gap-2">
      <div className="flex flex-col lg:gap-4 gap-2 w-full lg:max-h-[calc(100vh-64px-16px-16px)] lg:overflow-y-auto scrollbar scrollbar-none">
        {/* <div className="flex items-center gap-2 justify-between">
          <Button
            variant="outline"
            className="cursor-pointer"
            onClick={() => router.back()}
          >
            <MoveLeft size={16} />
            Back to Content Library
          </Button>
          <div className="flex items-center gap-2 font-bold text-lg text-primary">
            {album?.Name}
          </div>
        </div> */}
        <Player />
        {!selectedContent && (
          <div className="flex flex-col gap-2">
            <h1 className="text-lg font-semibold">{album?.Name}</h1>
            <p className="text-muted-foreground">
              {album?.Description ?? "..."}
            </p>
          </div>
        )}
        <div className="hidden lg:block">
          <Transcript />
        </div>
      </div>

      {/* Side Nav */}
      <div className="bg-accent flex flex-col overflow-y-auto h-[calc(100vh-100px)] rounded-lg px-2 scrollbar-none lg:min-w-[40%] lg:max-w-[20%]">
        <div className="block lg:hidden mt-2">
          <Accordion
            collapsible
            type="single"
            className="rounded-lg px-2 bg-secondary"
            defaultValue="transcript"
          >
            <AccordionItem value="transcript">
              <AccordionTrigger className="hover:no-underline cursor-pointer">
                <div className="flex items-center gap-2 hover:no-underline text-md">
                  <ScrollText size={16} />
                  Transcript
                </div>
              </AccordionTrigger>
              <AccordionContent className="max-h-[350px] overflow-y-auto scrollbar-none">
                {selectedContent && (
                  <AdvanceEditor
                    viewOnly
                    content={selectedContent?.Meta?.Transcript}
                  />
                )}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
        <Accordion
          type="single"
          // defaultValue={album?.ContentGroups?.[0]?.ContentGroupID}
          className="w-full px-2"
          collapsible
        >
          {album?.ContentGroups?.sort(
            // sort by sortOrder
            (a, b) => (a.SortOrder ?? 0) - (b.SortOrder ?? 0)
          )?.map((group) => {
            const contents = album?.Contents?.filter(
              (content) => content.ContentGroupID === group.ContentGroupID
            )?.sort(
              // sort by sortOrder
              (a, b) => (a.SortOrder ?? 0) - (b.SortOrder ?? 0)
            );

            const groupDuration = contents?.reduce(
              (total, content) => total + (content.Duration ?? 0),
              0
            );
            return (
              <AccordionItem
                key={group.ContentGroupID}
                value={group.ContentGroupID}
              >
                <AccordionTrigger className="hover:no-underline cursor-pointer">
                  <div className="flex items-start justify-between w-full">
                    <div className="flex flex-col">
                      <h1 className="text-md font-semibold">{group.Title}</h1>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {group.Description ?? "..."}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <div className="flex items-center gap-2 mt-2">
                          <Clock8 size={16} />
                          <span className="text-xs text-muted-foreground">
                            {Math.floor((groupDuration ?? 0) / 60 / 60) > 0
                              ? Math.floor((groupDuration ?? 0) / 60 / 60) +
                                " hours"
                              : Math.floor((groupDuration ?? 0) / 60) > 0
                                ? Math.floor((groupDuration ?? 0) / 60) +
                                  " minutes"
                                : Math.floor(groupDuration ?? 0) + " seconds"}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 mt-2">
                          <Clapperboard size={16} />
                          <span className="text-xs text-muted-foreground">
                            {contents?.length ?? 0} contents
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>

                <AccordionContent>
                  <div className="flex flex-col gap-2">
                    {contents?.map((content) => (
                      <div
                        key={content.ContentID}
                        className={cn(
                          "flex gap-2 cursor-pointer items-center justify-between py-2 px-4 rounded-lg hover:bg-secondary border border-secondary",
                          content.ContentID === selectedContent?.ContentID &&
                            "bg-primary/20 text-primary"
                        )}
                        onClick={() => {
                          setSelectedContent(content);
                        }}
                      >
                        <div className="flex flex-col">
                          <h1 className="text-md font-semibold line-clamp-2">
                            {content.Meta.Title}
                          </h1>
                          <p className="text-xs text-muted-foreground line-clamp-1">
                            {content.Meta.Description ?? "..."}
                          </p>
                        </div>
                        <div className="flex gap-2 flex-col items-center">
                          <div className="flex items-center gap-2">
                            <Clock8 size={16} />
                            <span className="text-xs text-muted-foreground">
                              {Math.floor((content.Duration ?? 0) / 60 / 60) > 0
                                ? Math.floor(
                                    (content.Duration ?? 0) / 60 / 60
                                  ) + " hours"
                                : Math.floor((content.Duration ?? 0) / 60) > 0
                                  ? Math.floor((content.Duration ?? 0) / 60) +
                                    " minutes"
                                  : Math.floor(content.Duration ?? 0) +
                                    " seconds"}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 bg-accent px-2 py-1 rounded-lg text-xs">
                            {content?.S3?.Type.includes("video") ? (
                              <Video size={16} />
                            ) : (
                              <AudioLines size={16} />
                            )}
                            <span className="text-xs text-muted-foreground">
                              {content?.S3?.Type.includes("video")
                                ? "Video"
                                : "Audio"}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  {contents?.length === 0 && (
                    <div className="flex flex-col items-center justify-center h-full">
                      <Squirrel size={32} strokeWidth={1} />
                      <h1 className="text-sm font-semibold">
                        No contents found
                      </h1>
                    </div>
                  )}
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </div>
    </div>
  );
}
