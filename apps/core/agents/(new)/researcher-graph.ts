import { RunnableConfig } from '@langchain/core/runnables';
import { Annotation, END, MessagesAnnotation, Send, START, StateGraph } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';
import { retrievalGraph } from './retrieval-graph';
import { reduceDocs } from './shared';
import { Document } from '@langchain/core/documents';
import { calculateLLMInvocations, LLMInvocation, LLMOutput } from '../shared/utils';

const PROMPTS = {
  GENERATE_INITIAL_QUERIES: `
   You are an expert research assistant tasked with generating initial search queries to explore a student's question and find relevant documents from the student's course materials and textbooks stored in (pinecone) vector database.
   Your goal is to formulate 2-3 broad queries that will help you understand the main topics and keywords related to the question. Think of these as initial exploratory probes.

   Your should take care of the following steps, including:
   1.  **Identify the main question:** Indetify main questiion asked by the student from the most recent conversation history, but take care of the context and preferences from the conversation history.
   2.  **Identify Sub-Questions:** Break down the main question into smaller, more manageable sub-questions that need to be answered.
   3.  **Define Search Strategies:** For each sub-question, specify the search terms or keywords that will be used to retrieve relevant documents. Consider alternative search strategies to cover different facets of the question.

   **Input:**
   *   **Conversation History:** Student's question and the conversation history.

   **Output:**
   Generate a list of 2-3 broad search queries. Keep the queries short, focused on keywords and general topics related to the question. Each query should be a concise search phrase. Present the queries as a numbered list.

   Example:
   1.  "Photosynthesis definition"
   2.  "Plant energy production"
  `,
  GENERATE_REFINED_QUERIES: `"You are an AI-powered doubt-clearing assistant for students, helping them with academic questions.
  Your task is to create refined search queries for our vector database, leveraging existing information to improve search accuracy.
  The system has already:
  *   Summarized the books in the project.
  *   Chapter wise summaries and topics.

  Now, using the context from:
  *   **Conversation History:** The student's interactions and the AI's responses.
  *   **Books Summary:** Is given below the summary of the books in the project.
  *   **Language of Embedded Documents in the vector database**: Use this to generate the optimized queries in the same language as the documents.

  Frist analyze and understand the question and then generate more refined search queries. These queries should:
  *   Be diverse, covering different aspects of the question and try such that it should not query exact same documents as initial documents.
  *   Be informed by the context, knowledge depth, keywords, and concepts identified in the sample documents.
  *   Follow any prior instructions to focus the scope

  **Input:**
  *   **Conversation History:** {message below this instruction}
  *   **Books Summary:** {booksSummary}
  *   **Language of Embedded Documents in the vector database:** {language}

  These queries will help you to generate a powerful search of the vector database.
  **Output:**
  A numbered list of refined search queries (no more than 3), designed to improve search of the vector database. Please be specific and practical.`,
};

export const InputStateAnnotation = Annotation.Root({
  ...MessagesAnnotation.spec,
  tenantId: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
  projectId: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
  sources: Annotation<
    {
      SourceID: string;
      SourceName: string;
      Chapters: {
        ChapterID: string;
        ChapterNumber: number;
        ChapterName: string;
        Summary: string;
        Topics: {
          Topic: string;
          PageNumber: number;
          SubTopics?: string[];
        }[];
        PageNumber: {
          From: number;
          To: number;
        };
      }[];
      BookSummary: {
        Title: string;
        Summary: string;
        MainTopics: string[];
        ChapterHighlights: {
          ChapterName: string;
          Highlight: string;
        }[];
      };
      Language: string;
    }[]
  >(),
  language: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
});

const AgentStateAnnotation = Annotation.Root({
  ...InputStateAnnotation.spec,
  initialQueries: Annotation<string[]>({
    default: () => [],
    reducer: (a, b) => b,
  }),
  initialDocuments: Annotation<Document[]>({
    default: () => [],
    reducer: reduceDocs,
  }),
  refinedQueries: Annotation<string[]>({
    default: () => [],
    reducer: (a, b) => b,
  }),
  refinedDocuments: Annotation<Document[]>({
    default: () => [],
    reducer: reduceDocs,
  }),
  TokenCount: Annotation<{
    PromptTokens: number;
    CompletionTokens: number;
    TotalTokens: number;
    LLMInvocations: LLMInvocation[];
  }>({
    default: () => ({
      PromptTokens: 0,
      CompletionTokens: 0,
      TotalTokens: 0,
      LLMInvocations: [],
    }),
    reducer: (a, b) => b,
  }),
});

// async function generateInitialQueries(
//   state: typeof AgentStateAnnotation.State,
//   config: RunnableConfig
// ): Promise<typeof AgentStateAnnotation.Update> {
//   const initialQueriesSchema = z
//     .object({
//       queries: z.array(z.string()),
//     })
//     .describe('Generate initial search queries.');

//   const model = new ChatOpenAI({
//     model: 'gpt-4.1-mini',
//     temperature: 0.2,
//   });

//   const messages = [{ role: 'system', content: PROMPTS.GENERATE_INITIAL_QUERIES }, ...state.messages];
//   const response = await model.withStructuredOutput(initialQueriesSchema).invoke(messages);
//   return { initialQueries: response.queries };
// }

// async function retrieveInitialDocuments(
//   state: typeof AgentStateAnnotation.State,
//   config: RunnableConfig
// ): Promise<typeof AgentStateAnnotation.Update> {
//   const retrievalGraphConfig = {
//     tenantId: state.tenantId,
//     projectId: state.projectId,
//     query: state.initialQueries?.[0],
//   };
//   const retrievalGraphResult = await retrievalGraph.invoke(state);
//   return { initialDocuments: retrievalGraphResult.documents, initialQueries: state.initialQueries?.slice(1) };
// }

async function generateRefinedQueries(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.4,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'generate_refined_queries'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const researchPlanSchema = z
    .object({
      queries: z.array(z.string()),
    })
    .describe('Generate more refined search queries.');

  const prompt = PROMPTS.GENERATE_REFINED_QUERIES.replace('{booksSummary}', JSON.stringify(state.sources)).replace(
    '{language}',
    state.language
  );
  const messages = [{ role: 'system', content: prompt }, ...state.messages];

  const response = await model.withStructuredOutput(researchPlanSchema).invoke(messages);
  console.log('response', response);
  return { refinedQueries: response.queries, TokenCount };
}

async function retrieveRefinedDocuments(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const retrievalGraphResult = await retrievalGraph.invoke(state);
  return { refinedDocuments: retrievalGraphResult.documents, refinedQueries: state.refinedQueries?.slice(1) };
}

// function retrieveInitialInParallel(state: typeof AgentStateAnnotation.State): Send[] {
//   return state.initialQueries.map(
//     (query: string) =>
//       new Send('retrieveInitialDocuments', {
//         query: query,
//         tenantId: state.tenantId,
//         projectId: state.projectId,
//       })
//   );
// }

function retrieveRefinedInParallel(state: typeof AgentStateAnnotation.State): Send[] {
  return state.refinedQueries.map(
    (query: string) =>
      new Send('retrieveRefinedDocuments', {
        query: query,
        tenantId: state.tenantId,
        projectId: state.projectId,
      })
  );
}
const builder = new StateGraph({ stateSchema: AgentStateAnnotation })
  .addNode('generateRefinedQueries', generateRefinedQueries)
  .addNode('retrieveRefinedDocuments', retrieveRefinedDocuments, { subgraphs: [retrievalGraph] })
  .addEdge(START, 'generateRefinedQueries')
  .addConditionalEdges('generateRefinedQueries', retrieveRefinedInParallel, ['retrieveRefinedDocuments'])
  .addEdge('retrieveRefinedDocuments', END);

export const researcherGraph = builder.compile().withConfig({ runName: 'ResearcherGraph' });
