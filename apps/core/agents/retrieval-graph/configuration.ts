import { RunnableConfig } from '@langchain/core/runnables';
import { Annotation } from '@langchain/langgraph';
import { BaseConfigurationAnnotation, ensureBaseConfiguration } from '../shared/configuration';
import {
  GENERAL_SYSTEM_PROMPT,
  GENERATE_QUERIES_SYSTEM_PROMPT,
  MORE_INFO_SYSTEM_PROMPT,
  RESEARCH_PLAN_SYSTEM_PROMPT,
  RESPONSE_SYSTEM_PROMPT,
  ROUTER_SYSTEM_PROMPT,
  WRONG_SYLLABUS_RESPONSE_SYSTEM_PROMPT,
} from './prompts';

/**
 * The configuration for the agent.
 */
export const AgentConfigurationAnnotation = Annotation.Root({
  ...BaseConfigurationAnnotation.spec,

  //   models
  /**
   * The language model used for processing and refining queries.
   * Should be in the form: provider/model-name
   */
  queryModel: Annotation<string>,

  /**
   * The language model used for generating responses.
   * Should be in the form: provider/model-name
   */
  responseModel: Annotation<string>,

  //prompts
  /**
   * The system prompt used for classifying user query questions to route them to the correct node.
   */
  routerSystemPrompt: Annotation<string>,

  /**
   * THe system prompt used for asking for more information from the user
   */
  moreInfoSystemPrompt: Annotation<string>,

  /**
   * The system prompt used for repsonding to general questions
   */
  generalQueriesSystemPrompt: Annotation<string>,

  /**
   * The systemm prompt used for generating a research plan based on the user's question
   */
  researchPlanSystemPrmompt: Annotation<string>,

  /**
   * The system prompt used by the researcher to generate queries based on a step in the research plan.
   */
  generateQueriesSystemPrompt: Annotation<string>,

  /**
   * The system prompt used for generating responses.
   */
  responseSystemPrompt: Annotation<string>,

  /**
   * The system prompt used for generating responses to wrong syllabus queries.
   */
  wrongSyllabusResponseSystemPrompt: Annotation<string>,

  /**
   * The tenant ID for scoping document retrieval
   */
  tenantId: Annotation<string>,

  /**
   * The project ID for scoping document retrieval
   */
  projectId: Annotation<string>,
});

/**
 * Create a typeof ConfigurationAnnotation.State instance from a RunnableConfig object
 *
 * @param config - The configuration object to use
 * @returns An instance of typeof ConfigurationAnnotation.State with the specified configuration
 */
export function ensureAgentConfiguration(config: RunnableConfig): typeof AgentConfigurationAnnotation.State {
  const configurable = (config?.configurable || {}) as Partial<typeof AgentConfigurationAnnotation.State>;

  const baseConfig = ensureBaseConfiguration(config);

  // Add tenant and project filtering to the base configuration
  if (configurable.tenantId && configurable.projectId) {
    baseConfig.searchKwargs = {
      ...baseConfig.searchKwargs,
      filter: {
        ...baseConfig.searchKwargs?.filter,
        tenantId: configurable.tenantId,
        projectId: configurable.projectId,
      },
      scoreThreshold: 0.7,
      k: 4,
    };
  }

  return {
    ...baseConfig,
    queryModel: configurable.queryModel || 'openai/gpt-4o-mini',
    responseModel: configurable.responseModel || 'openai/gpt-4o-mini',
    routerSystemPrompt: configurable.routerSystemPrompt || ROUTER_SYSTEM_PROMPT,
    moreInfoSystemPrompt: configurable.moreInfoSystemPrompt || MORE_INFO_SYSTEM_PROMPT,
    generalQueriesSystemPrompt: configurable.generalQueriesSystemPrompt || GENERAL_SYSTEM_PROMPT,
    researchPlanSystemPrmompt: configurable.researchPlanSystemPrmompt || RESEARCH_PLAN_SYSTEM_PROMPT,
    generateQueriesSystemPrompt: configurable.generateQueriesSystemPrompt || GENERATE_QUERIES_SYSTEM_PROMPT,
    responseSystemPrompt: configurable.responseSystemPrompt || RESPONSE_SYSTEM_PROMPT,
    wrongSyllabusResponseSystemPrompt:
      configurable.wrongSyllabusResponseSystemPrompt || WRONG_SYLLABUS_RESPONSE_SYSTEM_PROMPT,
    tenantId: configurable.tenantId || '',
    projectId: configurable.projectId || '',
  };
}
