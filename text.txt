
# Node.js dependencies build stage [Core]
FROM public.ecr.aws/docker/library/node:20-alpine AS node-builder
WORKDIR /build

# Install build tools
RUN npm install -g pnpm @nestjs/cli turbo

# Copy only package files first to leverage caching
COPY ./ ./

# Install all dependencies including dev dependencies for build
RUN pnpm install --frozen-lockfile

# Install TypeScript globally to ensure it's available
RUN npm install -g typescript

# Ensure node_modules/.bin is in PATH for TypeScript and other tools
ENV PATH="/build/node_modules/.bin:${PATH}"

# Build with correct package names - first build common packages then the app
RUN pnpm run build --filter="@package/common-nest" \
    && pnpm run build --filter="academic-lm-core"

# Production stage (final image)
FROM public.ecr.aws/docker/library/python:3.11-slim AS production
WORKDIR /app

# Install Node.js with minimal dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    nodejs npm \
    && rm -rf /var/lib/apt/lists/*


# Copy only what's needed from Node.js build
COPY --from=node-builder /build/node_modules ./node_modules
COPY --from=node-builder /build/packages/common-nest/dist ./packages/common-nest/dist
COPY --from=node-builder /build/packages/common-nest/package.json ./packages/common-nest/package.json
COPY --from=node-builder /build/apps/core ./apps/core
COPY --from=node-builder /build/apps/core/node_modules ./apps/core/node_modules
COPY --from=node-builder /build/apps/core/package.json ./apps/core/package.json


# Copy Python application
COPY apps/agentic-ai/ /app/apps/agentic-ai
RUN pip install poetry
WORKDIR /app/apps/agentic-ai
RUN poetry install

# Copy minimal configuration files
WORKDIR /app
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

# Expose ports for both services
EXPOSE 3001 8000

# Set production environment
ENV NODE_ENV=production

CMD ["/bin/bash", "-c", "cd /app/apps/core && npm run start & cd /app/apps/agentic-ai && poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 & wait"]