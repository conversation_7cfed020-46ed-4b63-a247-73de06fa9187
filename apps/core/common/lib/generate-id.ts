import ShortUniqueId, { ShortUniqueIdOptions } from 'short-unique-id';
import crypto from 'crypto';
class GenerateID {
  static Generate16Hex(prefix: string) {
    const suid = new ShortUniqueId({ length: 12 });
    return prefix + suid.randomUUID();
  }

  static Generate10Hex(prefix: string) {
    const suid = new ShortUniqueId({ length: 10 });
    return prefix + suid.randomUUID();
  }

  static Generate32Hex(prefix: string) {
    const suid = new ShortUniqueId({ length: 32 });
    return prefix + suid.randomUUID();
  }

  static Generate24Hex(prefix: string) {
    const suid = new ShortUniqueId({ length: 24 });
    return prefix + suid.randomUUID();
  }

  /**
   * Generates UUID v4 using crypto.randomUUID()
   * @returns UUID v4
   */
  static GenerateUUIDV4() {
    return crypto.randomUUID();
  }

  static GenerateShortId({ length = 6, dictionary = 'alphanum_upper', prefix = '' }) {
    const su = new ShortUniqueId({ length });
    su.setDictionary(dictionary as ShortUniqueIdOptions['dictionary']);
    return prefix + su.randomUUID();
  }
}

export default GenerateID;
