import { TryCatch } from "@/lib/try-catch";
import { KY, TAckResponse, TErrorResponse } from "./_.index";
import { TProject } from "@/types/project";

type TUser = {
  UserID: string;
  Email: string;
  Name: string;
  FamilyName: string;
  AppType: "Academic" | "Industry";
};

type TTokenCounter = {
  _id: string;
  ProjectID: string;
  TenantID: string;
  TokenCount: {
    InputTokens: number;
    OutputTokens: number;
    TotalTokens: number;
    LLMInvocations: {
      TokenCount: {
        _id: string;
      };
      Node: string;
      Usage: {
        InputTokenDetails: {
          AudioTokens: number;
          CachedTokens: number;
        };
        OutputTokenDetails: {
          AudioTokens: number;
          ReasoningTokens: number;
          AcceptedPredictionTokens: number;
          RejectedPredictionTokens: number;
        };
        InputTokens: number;
        OutputTokens: number;
        TotalTokens: number;
        _id: string;
      };
      LLMInvocationID: string;
    }[];
  };
  ResourceType: "message" | "lecture" | "pop_quiz" | "embedding";
  ResourceIDs: string[];
  UserID: string;
  GraphInvocationID: string;
  createdAt: string;
  updatedAt: string;
};

type TGetTenantTokenUsageResponse = {
  TotalCounts: {
    TotalTokens: number;
    InteractionCount: number;
  };
  Projects: {
    ProjectID: string;
    Project: TProject;
    Resources: {
      ResourceType: "message" | "lecture" | "pop_quiz" | "embedding";
      TotalTokens: number;
      InteractionCount: number;
    }[];
  }[];
};

export type TGetProjectTokenUsageResponse = {
  TotalCounts: {
    TotalTokens: number;
    InteractionCount: number;
  };
  Project: TProject;
  Users: {
    UserID: string;
    User: TUser;
    Resources: {
      ResourceType: "message" | "lecture" | "pop_quiz" | "embedding";
      TotalTokens: number;
      InteractionCount: number;
    }[];
  };
};

export type TGetUserTokenUsageResponse = {
  Project: TProject;
  User: TUser;
  TokenCounter: TTokenCounter[];
};

const GetTenantTokenUsage = async () => {
  return await TryCatch<
    TAckResponse<TGetTenantTokenUsageResponse>,
    TErrorResponse
  >(KY.get(`token-counter/tenant`).json());
};

const GetProjectTokenUsage = async (params: { ProjectID: string }) => {
  return await TryCatch<
    TAckResponse<TGetProjectTokenUsageResponse>,
    TErrorResponse
  >(KY.get(`token-counter/tenant/project/${params.ProjectID}`).json());
};

const GetUserTokenUsage = async (params: {
  ProjectID: string;
  UserID: string;
}) => {
  return await TryCatch<
    TAckResponse<TGetUserTokenUsageResponse>,
    TErrorResponse
  >(
    KY.get(
      `token-counter/tenant/project/${params.ProjectID}/user/${params.UserID}`
    ).json()
  );
};

export const TOKEN_COUNTER = {
  GetTenantTokenUsage,
  GetProjectTokenUsage,
  GetUserTokenUsage,
};
