"use client";

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
  useEffect,
} from "react";
import { API } from "@/apis/api";
import { ThreadDto } from "@/apis/chat.api";
import { toast } from "sonner";
import { useAuth } from "./auth-context";

interface ThreadContextType {
  threads: ThreadDto[];
  isLoading: boolean;
  error: string | null;
  refetchThreads: () => Promise<void>;
  deleteThread: (threadId: string) => Promise<boolean>;
}

const ThreadContext = createContext<ThreadContextType | undefined>(undefined);

export function ThreadProvider({ children }: { children: ReactNode }) {
  const [threads, setThreads] = useState<ThreadDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();

  const fetchThreads = useCallback(async () => {
    if (!isAuthenticated) return;
    setIsLoading(true);
    setError(null);

    try {
      const { data, errors } = await API.CHAT.GetThreads();

      if (errors) {
        const errorMessage = errors[0]?.message || "Failed to fetch threads";
        setError(errorMessage);
        toast.error(errorMessage);
        return;
      }

      if (data) {
        const threadsArray = data.threads || [];
        setThreads(threadsArray);
      } else {
        setThreads([]);
      }
    } catch (err) {
      const errorMessage =
        "An unexpected error occurred while fetching threads";
      setError(errorMessage);
      toast.error(errorMessage);
      console.error("Threads fetch error:", err);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    fetchThreads();
  }, [fetchThreads]);

  const deleteThread = async (threadId: string): Promise<boolean> => {
    try {
      const { errors } = await API.CHAT.DeleteThread(threadId);

      if (errors) {
        toast.error(errors[0]?.message || "Failed to delete conversation");
        return false;
      }

      // Update local state to remove the deleted thread
      setThreads((prev) => prev.filter((t) => t.ThreadID !== threadId));
      toast.success("Conversation deleted successfully");
      return true;
    } catch (err) {
      console.error("Error deleting thread:", err);
      toast.error("An error occurred while deleting the conversation");
      return false;
    }
  };

  return (
    <ThreadContext.Provider
      value={{
        threads,
        isLoading,
        error,
        refetchThreads: fetchThreads,
        deleteThread,
      }}
    >
      {children}
    </ThreadContext.Provider>
  );
}

export const useThreadContext = () => {
  const context = useContext(ThreadContext);
  if (context === undefined) {
    throw new Error("useThreadContext must be used within a ThreadProvider");
  }
  return context;
};
