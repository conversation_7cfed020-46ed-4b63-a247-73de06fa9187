export enum DocumentStatus {
  UPLOADING = "uploading",
  UPLOADED = "uploaded",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
}

export type TProject = {
  Name: string;
  Description: string;
  ProjectID: string;
  TenantID: string;
  createdAt: string;
  updatedAt: string;
};

export type TSource = {
  SourceID: string;
  SourceName: string;
  Chapters: {
    ChapterID: string;
    ChapterName: string;
    ChapterNumber: number;
    ChunkIDs: string[];
    Summary?: string;
    Topics?: {
      Topic: string;
      PageNumber: string;
      SubTopics: string[];
    }[];
    PageNumber: {
      From: number;
      To: number;
    };
    DocumentPageNumber: {
      From: number;
      To: number;
    };
  }[];
  Status: DocumentStatus;
  TenantID: string;
  ProjectID: string;
  AWSKey: string;
  ErrorMessage?: string;
  BookSummary?: {
    Title: string;
    Summary: string;
    MainTopics: string[];
    ChapterHighlights: {
      ChapterName: string;
      Highlight: string;
    }[];
  };
  createdAt: string;
  updatedAt: string;
};

export type TProjectDetails = TProject & {
  Sources: TSource[];
};
