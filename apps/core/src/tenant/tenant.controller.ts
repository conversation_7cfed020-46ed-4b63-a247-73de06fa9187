import { BadRequestException, Body, Controller, Get, Param, Patch, Post } from '@nestjs/common';
import { TenantService } from './tenant.service';
import { CreateTenantReqBodyDto, UpdateTenantReqBodyDto } from './dto/tenant.contoller.dto';
import { UserService } from '../core/user/user.service';
import GenerateID from '../../common/lib/generate-id';
import { TenantID } from '../../common/decorators/core.decorators';
import { AuthGuard, SkipAuth } from '../../common/guards/auth.guard';
import { UseGuards } from '@nestjs/common';

@UseGuards(AuthGuard)
@Controller('tenant')
export class TenantController {
  constructor(
    private readonly TenantService: TenantService,
    private readonly UserService: UserService
  ) {}

  @Post('sign-up')
  async CreateTenant(@Body() Body: CreateTenantReqBodyDto) {
    const user = await this.UserService.GetUserByEmail(Body.AdminUser.Email);
    if (user) {
      throw new BadRequestException('User already exists');
    }

    const TenantID = GenerateID.Generate16Hex('tenant_');
    const createUser = await this.UserService.Create({
      Email: Body.AdminUser.Email,
      Password: Body.AdminUser.Password,
      UserType: 'Admin',
      TenantID,
    });
    const createdTenant = await this.TenantService.CreateTenant({
      ...Body,
      AdminUserID: createUser.UserID,
      TenantID,
    });
    return {
      message: 'Tenant created successfully',
      ack: createdTenant,
    };
  }

  @Get(':TenantID')
  async GetTenant(@Param('TenantID') TenantID: string) {
    const tenant = await this.TenantService.GetTenant(TenantID);
    return tenant;
  }

  @Patch()
  async UpdateTenant(@TenantID() TenantID: string, @Body() Body: UpdateTenantReqBodyDto) {
    const updatedTenant = await this.TenantService.UpdateTenant(TenantID, Body);
    return {
      message: 'Tenant updated successfully',
      ack: updatedTenant,
    };
  }

  @SkipAuth()
  @Get('workspace-domain/:WorkSpaceDomain')
  async GetTenantByWorkspaceDomain(@Param('WorkSpaceDomain') WorkSpaceDomain: string) {
    const tenant = await this.TenantService.GetTenantByWorkspaceDomain(WorkSpaceDomain);
    return tenant;
  }

  @Get()
  async GetMyTenant(@TenantID() TenantID: string) {
    const tenant = await this.TenantService.GetTenant(TenantID);
    return tenant;
  }
}
