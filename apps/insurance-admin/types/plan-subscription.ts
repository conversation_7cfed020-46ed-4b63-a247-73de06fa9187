import { z } from "zod";
import { TUser } from "./user";

const CapTypes = z.enum(["Token", "Message", "Interaction"]);
export type CapType = z.infer<typeof CapTypes>;

export type PricingStructure = {
  Type: string;
  Price: number;
  Duration?: number;
  CreditUsage: {
    CapType: CapType;
    CapQuota: number;
  };
};

export type TPlan = {
  Name: string;
  Subtitle: string;
  Description: string;
  Currency: string;
  Features: { Title: string; HelpText?: string }[];
  ProjectIDs: string[];
  Tier: string;
  TenantID: string;
  PlanID: string;
  createdAt: string;
  updatedAt: string;
  Flags: {
    FlagID: string;
    Properties: Record<string, unknown>;
  }[];
  PricingStructures: PricingStructure[];
  ListAsPublic: boolean;
};

export enum SubscriptionStatus {
  Active = "Active",
  Inactive = "Inactive",
  Cancelled = "Cancelled",
  Paused = "Paused",
  Pending = "Pending",
}

export type TSubscription = {
  UserID: string;
  TenantID: string;
  Price: number;
  Status: SubscriptionStatus;
  StartDate: Date;
  EndDate: Date | null;
  PlanID: string;
  Variant: string;
  VariantDuration: number;
  Plan: TPlan;
  User: TUser;
};
