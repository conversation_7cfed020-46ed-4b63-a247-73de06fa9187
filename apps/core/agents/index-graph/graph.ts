/**
 * This graph processes PDF documents, extracts text, and indexes them with tenant and project scoping.
 */

import { RunnableConfig } from '@langchain/core/runnables';
import { StateGraph, END, START } from '@langchain/langgraph';
import { Document } from '@langchain/core/documents';
import { PDFLoader } from '@langchain/community/document_loaders/fs/pdf';

import { IndexStateAnnotation } from './state';
import { makeRetriever } from '../shared/retrieval';
import { ensureIndexConfiguration, IndexConfigurationAnnotation } from './configuration';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';

async function processPdf(
  state: typeof IndexStateAnnotation.State,
  config?: RunnableConfig
): Promise<typeof IndexStateAnnotation.Update> {
  if (!config) {
    throw new Error('Configuration required to run process_pdf.');
  }

  const configuration = ensureIndexConfiguration(config);
  const { pdfBlob, tenantId, projectId, fileName, pagesOfContent, pageNumberOffset, sourceId, sourceName } =
    configuration;

  // Create a PDFLoader from the blob
  const loader = new PDFLoader(pdfBlob);
  const rawDocs = await loader.load();

  // Split the documents into chunks
  const textSplitter = new RecursiveCharacterTextSplitter({
    chunkSize: 500,
    chunkOverlap: 100,
  });

  const splitDocs = await textSplitter.splitDocuments(rawDocs);

  // Add tenant and project metadata to each document
  const docsWithMetadata = splitDocs.map((doc) => {
    const pageNumber = doc.metadata?.loc?.pageNumber || 0;
    return new Document({
      pageContent: doc.pageContent,
      metadata: {
        ...doc.metadata,
        tenantId,
        projectId,
        fileName,
        sourceId,
        sourceName,
        pageNumber: pageNumber - pageNumberOffset,
        pageNumberOffset: pageNumberOffset,
        originalPageNumber: pageNumber,
        indexPage: pagesOfContent.includes(pageNumber),
      },
    });
  });

  return {
    docs: docsWithMetadata,
    tenantId,
    projectId,
  };
}

async function indexDocs(
  state: typeof IndexStateAnnotation.State,
  config?: RunnableConfig
): Promise<typeof IndexStateAnnotation.Update> {
  if (!config) {
    throw new Error('Configuration required to run index_docs.');
  }

  if (!state.docs.length) {
    throw new Error('No documents to index');
  }

  const retriever = await makeRetriever(config);
  await retriever.addDocuments(state.docs);

  console.log(`Indexed ${state.docs.length} documents for tenant ${state.tenantId} and project ${state.projectId}`);
  return { docs: 'delete' };
}

// Define the graph
const builder = new StateGraph(IndexStateAnnotation, IndexConfigurationAnnotation)
  .addNode('processPdf', processPdf)
  .addNode('indexDocs', indexDocs)
  .addEdge(START, 'processPdf')
  .addEdge('processPdf', 'indexDocs')
  .addEdge('indexDocs', END);

// Compile into a graph object that you can invoke and deploy.
export const graph = builder.compile().withConfig({ runName: 'IndexGraph' });
