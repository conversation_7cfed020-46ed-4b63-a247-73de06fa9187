"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Input } from "@workspace/ui/components/input";
import {
  MessageSquare,
  Search,
  RefreshCw,
  AlertCircle,
  Clock,
  Trash2,
  MoreHorizontal,
} from "lucide-react";
import { useRouter, useParams, usePathname } from "next/navigation";
import { format, isToday, isThisWeek, parseISO } from "date-fns";
import { useProjectContext } from "@/contexts/project-context";
import { useThreadContext } from "@/contexts/thread-context";
import { ThreadDto } from "@/apis/chat.api";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useAuth } from "@/contexts/auth-context";
import { SidebarStore } from "../layouts/auth-layout";
import { useIsMobile } from "@/hooks/use-is-mobile";

interface ThreadListProps {
  onThreadSelect?: (threadId: string) => void;
}

export function ThreadList({ onThreadSelect }: ThreadListProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { threadId } = useParams();
  const { projects, selectedProject } = useProjectContext();
  const { threads, isLoading, error, refetchThreads, deleteThread } =
    useThreadContext();
  const { isAuthenticated } = useAuth();
  const [deleteThreadId, setDeleteThreadId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const { sidebarOpen, setSidebarOpen } = SidebarStore();

  // Listen for route changes to refresh threads
  useEffect(() => {
    refetchThreads();
  }, [pathname, refetchThreads]);

  // Listen for authentication changes
  useEffect(() => {
    if (isAuthenticated) {
      refetchThreads();
    }
  }, [isAuthenticated, refetchThreads]);

  const filteredThreads = threads.filter(
    (thread) =>
      thread.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
      thread.ProjectID === selectedProject?.id
  );

  const isMobile = useIsMobile();

  const handleThreadClick = (thread: ThreadDto) => {
    if (onThreadSelect) {
      onThreadSelect(thread.ThreadID);
    } else {
      isMobile && setSidebarOpen(false);
      router.push(`/thread/${thread.ThreadID}`);
    }
  };

  const handleDeleteClick = (threadId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setDeleteThreadId(threadId);
  };

  const handleDeleteConfirm = async () => {
    if (!deleteThreadId) return;

    setIsDeleting(true);
    try {
      const success = await deleteThread(deleteThreadId);

      if (success) {
        // If on the deleted thread's page, navigate back to home
        if (threadId === deleteThreadId) {
          router.push("/");
        }
      }
    } finally {
      setIsDeleting(false);
      setDeleteThreadId(null);
    }
  };

  const formatDate = (dateString: string | Date) => {
    try {
      const date =
        typeof dateString === "string" ? parseISO(dateString) : dateString;

      if (isToday(date)) {
        return format(date, "h:mm a"); // Show time only
      }

      if (isThisWeek(date)) {
        return format(date, "EEE"); // Show day of week
      }

      // Otherwise show date
      return format(date, "MMM d");
    } catch (error) {
      // Fallback for invalid dates
      console.error(error);
      return "Unknown";
    }
  };

  // Get project name by ID
  const getProjectName = (projectId: string) => {
    const project = projects.find((p) => p.id === projectId);
    return project?.name || "";
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-3 sticky top-0 z-10 bg-background">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search conversations..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {isLoading ? (
        <div className="px-3 py-2 space-y-3">
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center flex-1 p-4 text-center">
          <div className="flex flex-col items-center gap-2 text-destructive mb-4">
            <AlertCircle size={24} />
            <p className="text-sm">Failed to load threads</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetchThreads()}
            className="gap-2"
          >
            <RefreshCw size={14} />
            Retry
          </Button>
        </div>
      ) : filteredThreads.length === 0 ? (
        <div className="flex flex-col items-center justify-center flex-1 p-4 text-center text-muted-foreground">
          <MessageSquare size={24} className="mb-2 opacity-50" />
          {searchQuery ? (
            <p className="text-sm">No matching conversations found</p>
          ) : (
            <p className="text-sm">No conversations yet</p>
          )}
        </div>
      ) : (
        <ScrollArea className="flex-1">
          <div className="px-2 space-y-1 pb-4">
            {filteredThreads.map((thread) => (
              <div
                key={thread.ThreadID}
                className={`rounded-md transition-colors group relative ${
                  thread.ThreadID === threadId
                    ? "bg-secondary"
                    : "hover:bg-muted"
                }`}
              >
                <Button
                  variant="ghost"
                  className={`w-full overflow-hidden justify-start h-auto py-3 px-3 rounded-md ${
                    thread.ThreadID === threadId ? "bg-transparent" : ""
                  } cursor-pointer`}
                  onClick={() => handleThreadClick(thread)}
                >
                  <div className="flex flex-col items-start text-left w-full gap-1">
                    <div className="flex items-center gap-2 w-full justify-between">
                      {/* <MessageSquare
                        size={16}
                        className={
                          thread.ThreadID === threadId
                            ? "text-primary"
                            : "text-muted-foreground"
                        }
                      /> */}
                      <span className="font-medium truncate flex-1">
                        {thread.title?.slice(0, 30) +
                          (thread.title?.length > 30 ? "..." : "") ||
                          "Untitled Thread"}
                      </span>
                      <span className="text-xs flex  items-center gap-1 text-muted-foreground shrink-0">
                        <Clock size={12} />
                        {formatDate(thread.updatedAt || thread.createdAt)}
                      </span>
                    </div>
                    <div className="flex w-full items-center">
                      <span className="text-xs text-muted-foreground truncate flex-1">
                        {new Date(thread.createdAt).toLocaleString("en-GB")}
                      </span>
                      {/* {thread.messageCount > 0 && (
                        <span className="text-xs text-primary-foreground bg-primary rounded-full px-2 py-0.5 ml-2">
                          {thread.messageCount}
                        </span>
                      )} */}
                    </div>
                  </div>
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-[26px] right-2 opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onClick={(e) => handleDeleteClick(thread.ThreadID, e)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete conversation
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ))}
          </div>
        </ScrollArea>
      )}

      <AlertDialog
        open={!!deleteThreadId}
        onOpenChange={(open: boolean) => !open && setDeleteThreadId(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Conversation</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this conversation? This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
