import IsAuthResource from "@/components/is-auth-resource";
import TenantCard from "@/components/tenat-card";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  ArrowRight,
  Briefcase,
  CheckCircle,
  CreditCard,
  LayersIcon,
} from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";

// Meta tags
export const metadata: Metadata = {
  title: "Insurance LM",
  description: "Insurance LM",
};

export default function Page() {
  return (
    <div className="container mx-auto p-10">
      <div className="flex flex-col gap-6">
        <TenantCard />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3">
          <IsAuthResource ResourceID="Projects">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Briefcase className="h-5 w-5 text-primary" />
                  <CardTitle>Policies</CardTitle>
                </div>
                <CardDescription className="pt-2">
                  Create and manage insurance policies with powerful
                  administrative tools
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Organize policy documents and claims</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Track renewals and expirations</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Manage policyholder information</span>
                </div>
              </CardContent>
              <CardFooter>
                <Link href="/projects" className="w-full">
                  <Button variant="outline" className="w-full group">
                    View Policies
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </IsAuthResource>

          <IsAuthResource ResourceID="Plans">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <LayersIcon className="h-5 w-5 text-primary" />
                  <CardTitle>Coverage Plans</CardTitle>
                </div>
                <CardDescription className="pt-2">
                  Design and organize insurance coverage plans with our
                  comprehensive planning tools
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Create premium structures</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Set coverage limits and deductibles</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Monitor risk assessments</span>
                </div>
              </CardContent>
              <CardFooter>
                <Link href="/plans" className="w-full">
                  <Button variant="outline" className="w-full group">
                    View Coverage Plans
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>{" "}
          </IsAuthResource>

          <IsAuthResource ResourceID="Subscriptions">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-primary" />
                  <CardTitle>Subscriptions</CardTitle>
                </div>
                <CardDescription className="pt-2">
                  Process and manage insurance subscriptions with efficient
                  tracking tools
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>View subscriptions history</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Process subscription payments</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Manage subscription plans</span>
                </div>
              </CardContent>
              <CardFooter>
                <Link href="/subscriptions" className="w-full">
                  <Button variant="outline" className="w-full group">
                    Manage Subscriptions
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </IsAuthResource>
        </div>
      </div>
    </div>
  );
}
