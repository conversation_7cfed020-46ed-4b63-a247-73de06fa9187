// Tailored For API FETCH Based On Backend Response
type Success<T> = {
  data: T;
  errors: null;
};

type Failure<E> = {
  data: null;
  errors: E;
};

type Result<T, E = Error> = Success<T> | Failure<E>;

interface ErrorWithResponse extends Error {
  response?: Response;
}

type ErrorResponse = {
  errors: Array<{ message: string }>;
};

// Main wrapper function
export async function TryCatch<T, E = Array<{ message: string }>>(
  promise: Promise<T>,
): Promise<Result<T, E>> {
  try {
    const data = await promise;
    return { data, errors: null };
  } catch (error) {
    try {
      const errorResponse = (error as ErrorWithResponse).response;
      if (errorResponse) {
        const errorJson = (await errorResponse.json()) as ErrorResponse;
        return {
          data: null,
          errors: (errorJson.errors || [
            { message: "An unexpected error occurred" },
          ]) as E,
        };
      }
      return {
        data: null,
        errors: [
          {
            message:
              (error as Error)?.message || "An unexpected error occurred",
          },
        ] as E,
      };
    } catch {
      // If we can't parse the error response, return a generic error
      return {
        data: null,
        errors: [
          {
            message:
              (error as Error)?.message || "An unexpected error occurred",
          },
        ] as E,
      };
    }
  }
}
