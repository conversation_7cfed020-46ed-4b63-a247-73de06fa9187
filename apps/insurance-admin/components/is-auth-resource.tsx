"use client";

import { TResourceType } from "@/apis/user.api";
import { userAuthStore } from "@/store/useAuthStore";

export default function IsAuthResource({
  children,
  ResourceID,
}: {
  children: React.ReactNode;
  ResourceID: TResourceType;
}) {
  const { user } = userAuthStore();

  if (!user) {
    return null;
  }

  if (
    user.UserType !== "Admin" &&
    !user.Access?.Resources.some(
      (resource) => resource.ResourceID === ResourceID
    )
  ) {
    return null;
  }

  return <>{children}</>;
}
