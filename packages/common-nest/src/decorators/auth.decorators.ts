import { createParamDecorator, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { CognitoIdTokenPayload } from 'aws-jwt-verify/jwt-model';

export type AuthUserPayload = CognitoIdTokenPayload;

export const UserPoolId = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  request.UserPoolId = request.UserPoolId;
  return request.UserPoolId;
});

export const AuthUser = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  return request.user as CognitoIdTokenPayload;
});

export const UserId = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  return (request.user as CognitoIdTokenPayload).sub;
});

export const UserEmail = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  return (request.user as CognitoIdTokenPayload)['email'];
});

export const Username = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  if ((request.user as CognitoIdTokenPayload)['cognito:username']) {
    return (request.user as CognitoIdTokenPayload)['cognito:username'];
  }
  throw new UnauthorizedException('Cannot resolve user, please login again');
});

export const AuthContext = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  return request?.Access as Record<string, unknown>;
});

export const IsAdministrator = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  return (request.user as CognitoIdTokenPayload)?.['cognito:groups']?.includes('administrator') ?? false;
});
