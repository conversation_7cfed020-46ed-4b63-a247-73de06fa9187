import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable, SetMetadata } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private configService: ConfigService
  ) {}

  async canActivate(context: ExecutionContext) {
    const skipAuth = this.reflector.get<boolean>('skipAuth', context.getHandler());
    if (skipAuth) {
      return true; // Skip guard execution
    }
    const request = context.switchToHttp().getRequest();
    const token = request?.cookies?.['_session'] ?? request?.headers['authorization']?.split('Bearer ')[1];

    if (!token) {
      const customError = {
        errors: [
          {
            message: 'Not Authenticated, Please login',
          },
        ],
      };
      throw new HttpException(customError, HttpStatus.UNAUTHORIZED);
    }

    const payload = await this.jwtService.verifyAsync(token, {
      secret: this.configService.get<string>('SERVICE_SECRET_KEY'),
    });

    if (payload) {
      request.user = payload;
      return true;
    }

    const customError = {
      errors: [
        {
          message: 'Not Authenticated, Please login',
        },
      ],
    };
    throw new HttpException(customError, HttpStatus.UNAUTHORIZED);
  }
}

@Injectable()
export class AuthAdminGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private configService: ConfigService
  ) {}

  async canActivate(context: ExecutionContext) {
    const skipAuth = this.reflector.get<boolean>('skipAuth', context.getHandler());
    if (skipAuth) {
      return true; // Skip guard execution
    }
    const request = context.switchToHttp().getRequest();
    const token = request?.cookies?.['_session'] ?? request?.headers['authorization']?.split('Bearer ')[1];
    if (!token) {
      const customError = {
        errors: [
          {
            message: 'Not Authenticated, Please login',
          },
        ],
      };
      throw new HttpException(customError, HttpStatus.UNAUTHORIZED);
    }

    const payload = await this.jwtService.verifyAsync(token, {
      secret: this.configService.get<string>('SERVICE_SECRET_KEY'),
    });

    if (payload.UserType !== 'Admin' && payload.UserType !== 'Employee') {
      const customError = {
        errors: [
          {
            message: 'Not Authenticated, Please login',
          },
        ],
      };
      throw new HttpException(customError, HttpStatus.FORBIDDEN);
    }

    if (payload) {
      request.user = payload;
      return true;
    }

    const customError = {
      errors: [
        {
          message: 'Not Authenticated, Please login',
        },
      ],
    };
    throw new HttpException(customError, HttpStatus.UNAUTHORIZED);
  }
}

@Injectable()
export class AuthSubscriberGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private configService: ConfigService
  ) {}

  async canActivate(context: ExecutionContext) {
    const skipAuth = this.reflector.get<boolean>('skipAuth', context.getHandler());
    if (skipAuth) {
      return true; // Skip guard execution
    }
    const request = context.switchToHttp().getRequest();
    const token = request?.cookies?.['_session'] ?? request?.headers['authorization']?.split('Bearer ')[1];

    if (!token) {
      const customError = {
        errors: [
          {
            message: 'Not Authenticated, Please login',
          },
        ],
      };
      throw new HttpException(customError, HttpStatus.FORBIDDEN);
    }

    const payload = await this.jwtService.verifyAsync(token, {
      secret: this.configService.get<string>('SERVICE_SECRET_KEY'),
    });

    if (payload.UserType !== 'Subscriber') {
      const customError = {
        errors: [
          {
            message: 'Not Authenticated, Please login',
          },
        ],
      };
      throw new HttpException(customError, HttpStatus.UNAUTHORIZED);
    }

    if (payload) {
      request.user = payload;
      return true;
    }

    const customError = {
      errors: [
        {
          message: 'Not Authenticated, Please login',
        },
      ],
    };
    throw new HttpException(customError, HttpStatus.UNAUTHORIZED);
  }
}

@Injectable()
export class AuthIndustrySubscriberGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private configService: ConfigService
  ) {}

  async canActivate(context: ExecutionContext) {
    const skipAuth = this.reflector.get<boolean>('skipAuth', context.getHandler());
    if (skipAuth) {
      return true; // Skip guard execution
    }
    const request = context.switchToHttp().getRequest();
    const token = request?.cookies?.['_session'] ?? request?.headers['authorization']?.split('Bearer ')[1];

    if (!token) {
      const customError = {
        errors: [
          {
            message: 'Not Authenticated, Please login',
          },
        ],
      };
      throw new HttpException(customError, HttpStatus.FORBIDDEN);
    }

    const payload = await this.jwtService.verifyAsync(token, {
      secret: this.configService.get<string>('SERVICE_SECRET_KEY'),
    });

    if (payload.UserType !== 'Subscriber' || payload.AppType !== 'Industry') {
      const customError = {
        errors: [
          {
            message: 'Not Authenticated, Please login',
          },
        ],
      };
      throw new HttpException(customError, HttpStatus.UNAUTHORIZED);
    }

    if (payload) {
      request.user = payload;
      return true;
    }

    const customError = {
      errors: [
        {
          message: 'Not Authenticated, Please login',
        },
      ],
    };
    throw new HttpException(customError, HttpStatus.UNAUTHORIZED);
  }
}

@Injectable()
export class AuthIndustryAdminGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private configService: ConfigService
  ) {}

  async canActivate(context: ExecutionContext) {
    const skipAuth = this.reflector.get<boolean>('skipAuth', context.getHandler());
    if (skipAuth) {
      return true; // Skip guard execution
    }
    const request = context.switchToHttp().getRequest();
    const token = request?.cookies?.['_session'] ?? request?.headers['authorization']?.split('Bearer ')[1];
    if (!token) {
      const customError = {
        errors: [
          {
            message: 'Not Authenticated, Please login',
          },
        ],
      };
      throw new HttpException(customError, HttpStatus.UNAUTHORIZED);
    }

    const payload = await this.jwtService.verifyAsync(token, {
      secret: this.configService.get<string>('SERVICE_SECRET_KEY'),
    });

    if (payload.UserType !== 'Admin' || payload.AppType !== 'Industry') {
      const customError = {
        errors: [
          {
            message: 'Not Authenticated, Please login',
          },
        ],
      };
      throw new HttpException(customError, HttpStatus.FORBIDDEN);
    }

    if (payload) {
      request.user = payload;
      return true;
    }

    const customError = {
      errors: [
        {
          message: 'Not Authenticated, Please login',
        },
      ],
    };
    throw new HttpException(customError, HttpStatus.UNAUTHORIZED);
  }
}

export const SkipAuth = () => SetMetadata('skipAuth', true);
