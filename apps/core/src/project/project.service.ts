import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateProjectDto, UpdateProjectDto } from './dto/project.service.dto';
import { ProjectModel } from './schemas/project.schema';
import { SourceModel } from '../embed/schemas/source.schema';
import { AppTypeEnum, TUser } from '../core/user/entities/user.entity';
import { IndSourceModel } from '../embed/schemas/ind-source.schema';

@Injectable()
export class ProjectService {
  constructor(
    @InjectModel(ProjectModel.name) private ProjectModel: Model<ProjectModel>,
    @InjectModel(SourceModel.name) private SourceModel: Model<SourceModel>,
    @InjectModel(IndSourceModel.name) private IndSourceModel: Model<IndSourceModel>
  ) {}

  async CreateProject(CreateProjectDto: CreateProjectDto) {
    const createProject = await this.ProjectModel.create(CreateProjectDto);
    return createProject;
  }

  async ListProjectsByTenantID(TenantID: string, AppType: AppTypeEnum, ProjectIDs?: string[]) {
    const projects = await this.ProjectModel.find({
      TenantID,
      ...(ProjectIDs && { ProjectID: { $in: ProjectIDs } }),
      AppType,
    }).lean();
    return projects;
  }

  async GetProjectByTenantID(TenantID: string, ProjectID: string, AppType: AppTypeEnum) {
    const project = await this.ProjectModel.findOne({ TenantID, ProjectID, AppType }).lean();
    return project;
  }

  async UpdateProject(TenantID: string, ProjectID: string, body: CreateProjectDto) {
    const project = await this.ProjectModel.findOneAndUpdate({ TenantID, ProjectID, AppType: body.AppType }, body, {
      new: true,
    });
    return project;
  }

  async DeleteProject(TenantID: string, ProjectID: string, AppType: AppTypeEnum) {
    const project = await this.ProjectModel.findOneAndDelete({ TenantID, ProjectID, AppType });
    return project;
  }

  async GetProjectDetails(TenantID: string, ProjectID: string, AppType: AppTypeEnum) {
    const project = await this.ProjectModel.findOne({ TenantID, ProjectID, AppType }).lean();
    const sources =
      AppType === AppTypeEnum.Industry
        ? await this.IndSourceModel.find({ TenantID, ProjectID }).lean()
        : await this.SourceModel.find({ TenantID, ProjectID }).lean();
    return {
      ...project,
      Sources: sources,
    };
  }
}
