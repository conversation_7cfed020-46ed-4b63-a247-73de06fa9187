import { useEffect, useState } from "react";
import { toast } from "sonner";
import { AUTH, TUser } from "../api/auth.api.js";

interface UseAuthReturn {
  user: TUser | null;
}

export function useUser(): UseAuthReturn {
  const [user, setUser] = useState<TUser | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchUser = async (): Promise<void> => {
    setIsLoading(true);
    try {
      // Try to get user from localStorage first for immediate UI update
      const storedUser = localStorage.getItem("user");
      if (storedUser) {
        try {
          setUser(JSON.parse(storedUser));
        } catch (error) {
          console.error("Failed to parse user from localStorage", error);
          localStorage.removeItem("user");
        }
      }

      // Then fetch fresh data from API
      const { data, errors } = await AUTH.GetCurrentUser();

      if (errors) {
        errors.forEach((error) => {
          if (error.message) toast.error(error.message);
        });
        setUser(null);
        localStorage.removeItem("user");
        return;
      }

      if (data) {
        setUser(data);
        localStorage.setItem("user", JSON.stringify(data));
      } else {
        setUser(null);
        localStorage.removeItem("user");
      }
    } catch (error) {
      console.error("Auth fetch failed:", error);
      setUser(null);
      localStorage.removeItem("user");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUser();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    user,
  };
}
