import { useState, useEffect } from "react";
import { API } from "@/apis/api";
import { TProject } from "@/apis/subscription.api";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth-context";

interface UseProjectsReturn {
  projects: TProject[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useProjects(): UseProjectsReturn {
  const [projects, setProjects] = useState<TProject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();

  const fetchProjects = async (): Promise<void> => {
    if (!isAuthenticated) return;
    setIsLoading(true);
    setError(null);

    try {
      const { data, errors } = await API.SUBSCRIPTION.GetMyProjects();

      if (errors) {
        const errorMessage = errors[0]?.message || "Failed to fetch projects";
        setError(errorMessage);
        toast.error(errorMessage);
        return;
      }

      if (data) {
        setProjects(data);
      } else {
        setProjects([]);
      }
    } catch (err) {
      const errorMessage =
        "An unexpected error occurred while fetching projects";
      setError(errorMessage);
      toast.error(errorMessage);
      console.error("Project fetch error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  return {
    projects,
    isLoading,
    error,
    refetch: fetchProjects,
  };
}
