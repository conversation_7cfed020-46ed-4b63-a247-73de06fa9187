PORT = 3001
TZ = "Asia/Kolkata"

# DATABASE
MONGODB_URI = "mongodb+srv://nextcampus.0lh8sa3.mongodb.net/?authSource=%24external&authMechanism=MONGODB-X509&retryWrites=true&w=majority"
DB_NAME = "academic-lm"


SERVICE_ID = "NXTP:LM"


#  AMQP
AMQP_URI = "amqp://c2:<EMAIL>:5672"
# AMQP_URI = "amqp://localhost:5672"


# AWS
AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "rdYTzVcpoA0jjZ7tkJaBUUjBv0MFuDDOeVjDs9aV"
AWS_REGION = "ap-south-1"


# S3
S3_NXTCAMPUS_PLATFORM = "nxtcampus-platforms"
S3_NXTCAMPUS_PLATFORM_ADDRESS = "https://nxtcampus-platforms.s3.ap-south-1.amazonaws.com"
CDN_URL = "https://d2t4cidav8427x.cloudfront.net"

SERVICE_SECRET_KEY = "rdYTzVcpoA0jjZ7tkJaBUUjBv0MFuDDOeVjDs9aV"

# Chroma Configuration
CHROMA_URL = "http://localhost:8000"
CHROMA_COLLECTION_NAME = "academic_lm"

PINECONE_API_KEY = "pcsk_5jWUom_NwJSmm1YJREipmbJAEfSWJDxrUY7x4zpXgYJCA2xD93bvYAGkQCzzsAjMcKFeio"
PINECONE_INDEX_NAME = "academic-lm"
INDUSTRY_PINECONE_INDEX_NAME = "industry-lm"


OPENAI_API_KEY = "********************************************************************************************************************************************************************"

ELASTICSEARCH_URL = "https://18ada2050ec34d649b2d12f07a316a9a.us-central1.gcp.cloud.es.io:443"
ELASTICSEARCH_USER = "elastic"
ELASTICSEARCH_PASSWORD = "CLWRhdESSgB2puhFtQQ5ffUu"
ELASTICSEARCH_API_KEY = "cHE3cHZwVUI5S1dDZld5X1NIWmY6Smh6dFVoS1JUbTJjWjA2UlVCLXlIQQ=="

LANGSMITH_TRACING = true
LANGSMITH_ENDPOINT = "https://api.smith.langchain.com"
LANGSMITH_API_KEY = "***************************************************"
LANGSMITH_PROJECT = "academic-lm-dev"

# TTS
GOOGLE_TTS = "AIzaSyD3z7n-BEpNLj1gFIzgozNMeBEPsIKvp_Q"
ELEVEN_LABS = "sk_f5901c56013e03b21b477ac344a751a165b41be10229df5f"


# Payment
RAZORPAY_KEY_ID = rzp_test_uJepf9xXm1mD5f
RAZORPAY_SECRET_KEY = hcN1uWCDZrEa78GUcAtmccbU

AGENTIC_AI_URL = "http://localhost:8000"

GOOGLE_CLIENT_ID="188008143868-1i69a50f00kq27tacr916e6pm2tdnpf1.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
GOOGLE_CALLBACK_URL="http://localhost:3001/auth/google/callback"
