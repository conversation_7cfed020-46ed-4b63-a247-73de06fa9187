from dataclasses import dataclass, field
from typing import Optional
from agents.embedding_agent.academic.state import AcademicEmbeddingState

@dataclass(kw_only=True)
class PageMetadata:
    source_name: str
    page_number: int

@dataclass
class Page:
    page_content: str
    metadata: PageMetadata

@dataclass
class Toc:
    chapter_id: str
    chapter_number: Optional[int] = None
    chapter_name: Optional[str] = None
    page_from: Optional[int] = None
    page_to: Optional[int] = None
    document_page_from: Optional[int] = None
    document_page_to: Optional[int] = None

@dataclass
class ChapterWiseRawPages:
    chapter_id: str
    chapter_number: Optional[int] = None
    chapter_name: Optional[str] = None
    page_from: Optional[int] = None
    page_to: Optional[int] = None
    document_page_from: Optional[int] = None
    document_page_to: Optional[int] = None
    pages: list[Page] = field(default_factory=list)

@dataclass
class Topic:
    chapter_name: str
    topic: str
    page_number: int
    sub_topics: list[str] = field(default_factory=list)

@dataclass
class ChapterWiseTopics:
    chapter_id: str
    topics: list[Topic] = field(default_factory=list)

@dataclass
class SummarizedChunks:
    chunk_id: str
    chunk_number: int
    chapter_id: str
    summary: str
    topic: str
    subtopic: Optional[str] = None

@dataclass
class ChapterSummaries:
    chapter_id: str
    chapter_name: str
    summary: str
    topics: list[str] = field(default_factory=list)

@dataclass
class ChapterHighlight:
    chapter_name: str
    highlight: str

@dataclass
class BookSummary:
    title: str = ""
    summary: str = ""
    main_topics: list[str] = field(default_factory=list)
    chapter_highlights: list[ChapterHighlight] = field(default_factory=list)
    
@dataclass
class ChunkMetadata:
    chunk_id: str
    chunk_number: int
    document_page_number: int
    page_number: int

@dataclass
class SourceMetadata:
    source_id: str
    source_name: str

@dataclass
class ChapterMetaData:
    chapter_id: str
    chapter_number: int
    chapter_name: str
    page_from: int
    page_to: int
    document_page_from: int
    document_page_to: int

@dataclass
class ChunkedPageMetadata:
    page_number: int
    chunk: ChunkMetadata
    source: SourceMetadata
    chapter: ChapterMetaData
    indexPage: bool
    projectId: str
    tenantId: str

@dataclass
class ChunkedPage:
    page_content: str
    metadata: ChunkedPageMetadata


@dataclass(kw_only=True)
class State(AcademicEmbeddingState):
    # Raw documents/pages from PDF parsing
    pages: list[Page] = field(default_factory=list)
    
    # Table of contents information
    toc: list[Toc] = field(default_factory=list)
    
    # Chunked pages for processing
    chunked_pages: list[ChunkedPage] = field(default_factory=list)
    
    # Chapter-wise organized raw pages
    chapter_wise_raw_pages: list[ChapterWiseRawPages] = field(default_factory=list)
    
    # Topics extracted from each chapter
    chapter_wise_topics: list[ChapterWiseTopics] = field(default_factory=list)
    
    # Summarized chunks with metadata
    summarized_chunks: list[SummarizedChunks] = field(default_factory=list)
    
    # Chapter-level summaries
    chapter_summaries: list[ChapterSummaries] = field(default_factory=list)
    
    # Overall book summary
    book_summary: BookSummary = field(default_factory=BookSummary)
    
    # Document IDs from vector store
    document_ids: list[str] = field(default_factory=list)

@dataclass(kw_only=True)
class InputState(AcademicEmbeddingState):
    pass

@dataclass(kw_only=True)
class OutputState(State):
    pass

@dataclass(kw_only=True)
class RawToc:
    chapter_number: int
    chapter_name: str
    page_from: int
    page_to: int

@dataclass(kw_only=True)
class TocOutput:
    tableOfContent: list[RawToc]
    
@dataclass(kw_only=True)
class ChapterSummaryOutput:
    chapter_summaries: list[ChapterSummaries]