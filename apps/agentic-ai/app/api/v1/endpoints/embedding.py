from fastapi import APIRouter, BackgroundTasks, File, UploadFile, Form
from pydantic import BaseModel, HttpUrl
from app.services.embedding_service import process_embed_file, process_academic_embed_file
from app.services.source_service import SourceService
from app.models.enums import DocumentStatus
from app.models.source import Source
from typing import Optional, List
import requests
from fastapi.responses import JSONResponse
import json

router = APIRouter()
source_service = SourceService()


class EmbeddingRequest(BaseModel):
    source_id: str
    source_name: str
    source_type: str
    tenant_id: str
    project_id: str
    user_id: str
    language: str
    page_number_offset: int
    url: Optional[HttpUrl] = None


class AcademicEmbeddingRequest(BaseModel):
    source_id: str
    source_name: str
    source_type: str
    tenant_id: str
    project_id: str
    user_id: str
    language: str
    page_number_offset: int
    toc_pages: List[int]
    url: Optional[HttpUrl] = None


@router.post("/")
async def embedding(
    background_tasks: BackgroundTasks,
    file: Optional[UploadFile] = File(None),
    url: Optional[str] = Form(None),
    source_id: str = Form(...),
    source_name: str = Form(...),
    source_type: str = Form(...),
    tenant_id: str = Form(...),
    project_id: str = Form(...),
    user_id: str = Form(...),
    language: str = Form(...),
    page_number_offset: int = Form(...),
    run_in_background: bool = Form(True),
):
    if not file and not url:
        raise ValueError("Either file or url must be provided")

    # Get source content either from file or URL
    if file:
        source_content = await file.read()
    else:
        response = requests.get(url)
        if response.status_code == 200:
            source_content = response.content 
        else:
            raise ValueError(f"Failed to fetch the PDF: {response.status_code}")

    # Create initial source document
    source = Source(
        SourceID=source_id,
        SourceName=source_name,
        Status=DocumentStatus.UPLOADING,
        TenantID=tenant_id,
        ProjectID=project_id,
        AWSKey="",  # This will be set during processing
    )

    await source_service.create_source(source)

    if run_in_background:
        # Add processing task to background
        print("Background processing started")
        background_tasks.add_task(
            process_embed_file,
            source_content=source_content,
            source_id=source_id,
            source_name=source_name,
            source_type=source_type,
            language=language,
            page_number_offset=page_number_offset,
            user_id=user_id,
            tenant_id=tenant_id,
            project_id=project_id,
        )
        return {"message": "Source uploaded successfully and processing started in background", "source_id": source_id, "status": 200}
    else:
        # Run synchronously
        try:
            print("Synchronous processing started")
            result = await process_embed_file(
                source_content=source_content,
                source_id=source_id,
                source_name=source_name,
                source_type=source_type,
                language=language,
                page_number_offset=page_number_offset,
                user_id=user_id,
                tenant_id=tenant_id,
                project_id=project_id,
            )
            return JSONResponse(
                content={
                    "message": "Source processed successfully",
                    "source_id": source_id,
                    "status": 200,
                    "result": result
                },
                status_code=200
            )
        except Exception as e:
            return JSONResponse(
                content={
                    "message": f"Error processing source: {str(e)}",
                    "source_id": source_id,
                    "status": 500
                },
                status_code=500
            )


@router.post("/academic")
async def academic_embedding(
    background_tasks: BackgroundTasks,
    file: Optional[UploadFile] = File(None),
    url: Optional[str] = Form(None),
    source_id: str = Form(...),
    source_name: str = Form(...),
    source_type: str = Form(...),
    tenant_id: str = Form(...),
    project_id: str = Form(...),
    user_id: str = Form(...),
    language: str = Form(...),
    page_number_offset: int = Form(...),
    toc_pages: str = Form(...),  # JSON string of page numbers
    run_in_background: bool = Form(True),
):
    """
    Academic embedding endpoint for processing educational content with chapter-wise analysis.
    
    Args:
        file: PDF file to process
        url: Alternative URL to fetch PDF
        source_id: Unique identifier for the source
        source_name: Display name for the source
        source_type: MIME type (e.g., application/pdf)
        tenant_id: Tenant identifier
        project_id: Project identifier
        user_id: User identifier
        language: Language code (e.g., 'en', 'hi')
        page_number_offset: Offset for page numbering
        toc_pages: JSON string of page numbers containing table of contents
        run_in_background: Whether to process asynchronously
    """
    if not file and not url:
        raise ValueError("Either file or url must be provided")

    # Parse toc_pages from JSON string
    try:
        toc_pages_list = json.loads(toc_pages)
        if not isinstance(toc_pages_list, list):
            raise ValueError("toc_pages must be a JSON array of page numbers")
    except json.JSONDecodeError:
        raise ValueError("Invalid JSON format for toc_pages")

    # Get source content either from file or URL
    if file:
        source_content = await file.read()
    else:
        response = requests.get(url)
        if response.status_code == 200:
            source_content = response.content 
        else:
            raise ValueError(f"Failed to fetch the PDF: {response.status_code}")

    # # Create initial source document
    # source = Source(
    #     SourceID=source_id,
    #     SourceName=source_name,
    #     Status=DocumentStatus.UPLOADING,
    #     TenantID=tenant_id,
    #     ProjectID=project_id,
    #     AWSKey="",  # This will be set during processing
    # )

    # await source_service.create_source(source)

    if run_in_background:
        # Add academic processing task to background
        print("Academic background processing started")
        background_tasks.add_task(
            process_academic_embed_file,
            source_content=source_content,
            source_id=source_id,
            source_name=source_name,
            source_type=source_type,
            language=language,
            page_number_offset=page_number_offset,
            user_id=user_id,
            tenant_id=tenant_id,
            project_id=project_id,
            toc_pages=toc_pages_list,
        )
        return {
            "message": "Academic source uploaded successfully and processing started in background", 
            "source_id": source_id, 
            "status": 200,
            "processing_type": "academic",
            "toc_pages": toc_pages_list
        }
    else:
        # Run synchronously
        try:
            print("Academic synchronous processing started")
            result = await process_academic_embed_file(
                source_content=source_content,
                source_id=source_id,
                source_name=source_name,
                source_type=source_type,
                language=language,
                page_number_offset=page_number_offset,
                user_id=user_id,
                tenant_id=tenant_id,
                project_id=project_id,
                toc_pages=toc_pages_list,
            )
            return JSONResponse(
                content={
                    "message": "Academic source processed successfully",
                    "source_id": source_id,
                    "status": 200,
                    "processing_type": "academic",
                    "result": result
                },
                status_code=200
            )
        except Exception as e:
            return JSONResponse(
                content={
                    "message": f"Error processing academic source: {str(e)}",
                    "source_id": source_id,
                    "status": 500,
                    "processing_type": "academic"
                },
                status_code=500
            )