import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateLectureDto } from './dto/create-lecture.dto';
import { UpdateLectureDto } from './dto/update-lecture.dto';
import { Model } from 'mongoose';
import { SourceModel } from '../embed/schemas/source.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Chunk } from '../embed/schemas/chunk.schema';
import { graph as academicGraph } from '../../agents/(teaching)/graph';
import { graph as insuranceGraph } from '../../agents/(insurance)/(teaching)/graph';
import { Lecture } from './schemas/lecture.schema';
import { TTSService } from '../tts/tts.service';
import { TokenCounterService } from '../token-counter/token-counter.service';
import { IndChunkModel } from '../embed/schemas/ind-chunk.schema';
import { IndSourceModel } from '../embed/schemas/ind-source.schema';
import { AppTypeEnum } from '../core/user/entities/user.entity';

@Injectable()
export class LectureService {
  constructor(
    @InjectModel(Chunk.name) private chunkModel: Model<Chunk>,
    @InjectModel(SourceModel.name) private sourceChunkModel: Model<SourceModel>,
    @InjectModel(Lecture.name) private lectureModel: Model<Lecture>,
    @InjectModel(IndChunkModel.name) private indChunkModel: Model<IndChunkModel>,
    @InjectModel(IndSourceModel.name) private indSourceModel: Model<IndSourceModel>,
    private readonly ttsService: TTSService,
    private readonly tokenCounterService: TokenCounterService
  ) {}

  async create(createLectureDto: CreateLectureDto, UserID: string, AppType: AppTypeEnum) {
    const { TenantID, ProjectID, SourceID, ChapterID, TeachingStyle, AgeGroup, CreativityLevel } = createLectureDto;

    const Lectures = await this.lectureModel
      .find({ TenantID, ProjectID, SourceID, ChapterID })
      .select('LectureID')
      .lean();
    if (Lectures.length > 0) {
      await this.lectureModel.deleteMany({ LectureID: { $in: Lectures.map((l) => l.LectureID) } });
    }

    if (AppType === AppTypeEnum.Academic) {
      const source = await this.sourceChunkModel.findOne({ SourceID, TenantID, ProjectID });
      if (!source) {
        throw new NotFoundException('Source not found');
      }

      const Chunks = await this.chunkModel
        .find({ SourceID, TenantID, ProjectID, ChapterID })
        .select('Topic SubTopic Summary PageNumber DocumentPageNumber ChunkNumber ChunkID');

      const CurrentChapterSummary = source.Chapters.find((chapter) => chapter.ChapterID === ChapterID);

      const response = await academicGraph.invoke({
        TeachingStyle: TeachingStyle,
        AgeGroup,
        CreativityLevel,
        BookSummary: source.BookSummary,
        OtherChaptersSummary: source.Chapters.filter((chapter) => chapter.ChapterID !== ChapterID).map((chapter) => ({
          ChapterID: chapter.ChapterID,
          ChapterName: chapter.ChapterName,
          ChapterNumber: chapter.ChapterNumber,
          Summary: chapter.Summary,
          Topics: chapter.Topics,
          PageNumber: chapter.PageNumber,
        })),
        CurrentChapterSummary: {
          ChapterID: CurrentChapterSummary.ChapterID,
          ChapterName: CurrentChapterSummary.ChapterName,
          ChapterNumber: CurrentChapterSummary.ChapterNumber,
          Summary: CurrentChapterSummary.Summary,
          Topics: CurrentChapterSummary.Topics,
          PageNumber: CurrentChapterSummary.PageNumber,
        },
        Chunks: Chunks.map((chunk) => ({
          Topic: chunk.Topic,
          SubTopic: chunk.SubTopic,
          Summary: chunk.Summary,
          PageNumber: chunk.PageNumber,
          ChunkNumber: chunk.ChunkNumber,
          ChunkID: chunk.ChunkID,
        })),
        Language: source.Language,
      });

      const lecture = await this.lectureModel.create({
        TenantID,
        ProjectID,
        SourceID,
        ChapterID,
        LectureTitle: CurrentChapterSummary.ChapterName || '',
        LectureDescription: `${source.BookSummary.Title} | ${CurrentChapterSummary.ChapterName}`,
        LectureSummary: CurrentChapterSummary.Summary || '',
        TeachingStyle,
        AgeGroup,
        CreativityLevel,
        LectureScript: response.LectureScript,
        SSMLScript: response.SSMLScript,
        TeachingPlan: response.TeachingPlan,
        Lectures: response.Lectures,
      });

      const tokenCounter = await this.tokenCounterService.create({
        TenantID,
        ProjectID,
        ResourceIDs: [lecture.LectureID],
        ResourceType: 'lecture',
        TokenCount: response.TokenCount,
        UserID,
        AppType: AppType,
      });

      return {
        success: true,
        message: 'Lecture created successfully',
        data: lecture,
        tokenCounter,
      };
    } else {
      const source = await this.indSourceModel.findOne({ SourceID, TenantID, ProjectID });
      if (!source) {
        throw new NotFoundException('Source not found');
      }

      const Chunks = await this.indChunkModel.find({ SourceID, TenantID, ProjectID, 'Sections.SectionID': ChapterID });
      const CurrentSectionSummary = source.Sections.find((section) => section.SectionID === ChapterID);
      const response = await insuranceGraph.invoke({
        TeachingStyle: TeachingStyle,
        AgeGroup,
        CreativityLevel,
        SourceSummary: source.SourceSummary,
        OtherSectionsSummary: source.Sections.filter((section) => section.SectionID !== ChapterID).map((section) => ({
          SectionID: section.SectionID,
          SectionName: section.SectionName,
          Description: section.Description,
          PageNumber: section.PageNumber,
          DocumentPageNumber: section.DocumentPageNumber,
          SubSections: section.SubSections,
        })),
        CurrentSectionSummary: {
          SectionID: CurrentSectionSummary.SectionID,
          SectionName: CurrentSectionSummary.SectionName,
          Description: CurrentSectionSummary.Description,
          Summary: CurrentSectionSummary.Description,
          PageNumber: CurrentSectionSummary.PageNumber,
          DocumentPageNumber: CurrentSectionSummary.DocumentPageNumber,
          SubSections: CurrentSectionSummary.SubSections,
        },
        Chunks: Chunks.map((chunk) => ({
          ChunkID: chunk.ChunkID,
          ChunkNumber: chunk.ChunkNumber,
          PageNumber: chunk.PageNumber,
          DocumentPageNumber: chunk.DocumentPageNumber,
          Summary: chunk.Summary,
          Content: chunk.Content,
          Sections: chunk.Sections.map((section) => ({
            SectionID: section.SectionID,
            SectionName: section.SectionName,
            SubSections: section.SubSections,
          })),
        })),
      });

      const lecture = await this.lectureModel.create({
        TenantID,
        ProjectID,
        SourceID,
        ChapterID,
        LectureTitle: CurrentSectionSummary.SectionName || '',
        LectureDescription: `${source.SourceSummary.Title} | ${CurrentSectionSummary.SectionName}`,
        LectureSummary: CurrentSectionSummary.Description || '',
        TeachingStyle,
        AgeGroup,
        CreativityLevel,
        LectureScript: response.LectureScript,
        SSMLScript: response.SSMLScript,
        TeachingPlan: response.TeachingPlan,
        Lectures: response.Lectures,
      });

      const tokenCounter = await this.tokenCounterService.create({
        TenantID,
        ProjectID,
        ResourceIDs: [lecture.LectureID],
        ResourceType: 'lecture',
        TokenCount: response.TokenCount,
        UserID,
        AppType: AppType,
      });

      return {
        success: true,
        message: 'Lecture created successfully',
        data: lecture,
        tokenCounter,
      };
    }
  }

  async findAll(TenantID: string, ProjectID: string, SourceID: string, ChapterID?: string) {
    const query: any = { TenantID, ProjectID, SourceID };
    if (ChapterID) {
      query.ChapterID = ChapterID;
    }
    return await this.lectureModel.find(query);
  }

  async findOne(LectureID: string) {
    return await this.lectureModel.findOne({ LectureID });
  }

  async update(LectureID: string, updateLectureDto: UpdateLectureDto) {
    return await this.lectureModel.findByIdAndUpdate({ LectureID }, updateLectureDto, { new: true });
  }

  async remove(LectureID: string) {
    return await this.lectureModel.findByIdAndDelete({ LectureID });
  }

  async generateTTS(LectureID: string, TenantID: string, ProjectID: string, SourceID: string) {
    const lecture = await this.lectureModel.findOne({ LectureID, TenantID, ProjectID, SourceID });
    if (!lecture) {
      throw new NotFoundException('Lecture not found');
    }

    const process = await this.ttsService.NewProcess({
      Name: lecture.LectureTitle,
      Summary: lecture.LectureSummary,
      SpeechText: lecture.LectureScript,
      Provider: 'Google',
      AudioConfig: {
        SpeechRate: 0.85,
        Pitch: 0,
        Gender: 'MALE',
        VoiceModel: 'en-IN-Standard-B',
        Language: 'en-IN',
        SampleRateHertz: 24000,
        VolumeGainDecibels: 0,
      },
      Scopes: {
        TenantID,
        ProjectID,
        SourceID,
      },
      TTSParams: {},
      TenantID,
    });

    return {
      success: true,
      message: 'TTS process created successfully',
      data: process,
    };
  }
}
