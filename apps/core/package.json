{"name": "academic-lm-core", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "cross-env NODE_ENV=prod pm2 start dist/src/main.js --name zuma-core --attach", "dev": "cross-env NODE_ENV=dev nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "cross-env NODE_ENV=prod node dist/src/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.779.0", "@aws-sdk/s3-request-presigner": "^3.779.0", "@elastic/elasticsearch": "^8.17.1", "@google-cloud/text-to-speech": "^6.0.1", "@langchain/anthropic": "^0.3.16", "@langchain/cohere": "^0.3.2", "@langchain/community": "^0.3.39", "@langchain/core": "^0.3.43", "@langchain/langgraph": "^0.2.62", "@langchain/mongodb": "^0.1.0", "@langchain/openai": "^0.5.2", "@langchain/pinecone": "^0.2.0", "@langchain/textsplitters": "^0.1.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.4.1", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/devtools-integration": "^0.1.6", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/microservices": "^11.1.1", "@nestjs/mongoose": "^10.0.10", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.4.15", "@nestjs/schedule": "^4.1.1", "@nextcampus/common": "^1.0.29", "@package/common-nest": "workspace:*", "@pinecone-database/pinecone": "^5.1.1", "@supercharge/promise-pool": "^3.2.0", "@types/body-parser": "^1.19.5", "amqplib": "^0.10.4", "argon2": "^0.41.1", "axios": "^1.8.4", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cache-manager": "^6.4.3", "chromadb": "^2.2.0", "chromadb-default-embed": "^2.14.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "elevenlabs": "^1.57.0", "form-data": "^4.0.2", "fs": "0.0.1-security", "ky": "^1.8.0", "mongodb": "^6.15.0", "mongoose": "^8.5.3", "multer": "1.4.5-lts.2", "music-metadata": "^7.14.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pdf-parse": "^1.1.1", "pm2": "^6.0.6", "promise": "link:@types/@supercharge/promise", "radash": "^12.1.0", "redis": "^5.5.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "short-unique-id": "^5.2.0", "ts-mixer": "^6.0.4", "uuid": "^11.1.0", "which-browser": "^0.7.1", "zod": "^3.24.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@package/utils": "workspace:*", "@tooling/eslint-config": "workspace:tooling", "@tooling/prettier": "workspace:tooling", "@tooling/typescript-config": "workspace:tooling", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.7", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/pdf-parse": "^1.1.5", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}