import { Body, Controller, Delete, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { TenantID } from '../../common/decorators/core.decorators';
import { AuthAdminGuard } from '../../common/guards/auth.guard';
import { ListProcessesQueryDto, NewProcessRequestDto } from './dto/tts.controller.dto';
import { TTSService } from './tts.service';

@UseGuards(AuthAdminGuard)
@Controller('tts')
export class TTSController {
  constructor(private readonly TTSService: TTSService) {}

  @Get('voices')
  async ListVoice() {
    return this.TTSService.ListVoices();
  }

  @Post('process')
  async NewProcess(@Body() Params: NewProcessRequestDto, @TenantID() TenantID: string) {
    const process = await this.TTSService.NewProcess({ ...Params, TenantID });
    return {
      message: 'Process created',
      ack: process,
    };
  }

  @Get('process')
  async ListProcesses(@Query() Params: ListProcessesQueryDto, @TenantID() TenantID: string) {
    const processes = await this.TTSService.ListProcesses({ ...Params, TenantID });
    return processes;
  }

  @Get('process/:ProcessID')
  async GetProcess(@Param('ProcessID') ProcessID: string, @TenantID() TenantID: string) {
    const process = await this.TTSService.GetProcess({ ProcessID, TenantID });
    return process;
  }

  @Delete('process/:ProcessID')
  async DeleteProcess(@Param('ProcessID') ProcessID: string, @TenantID() TenantID: string) {
    const process = await this.TTSService.DeleteProcess({ ProcessID, TenantID });
    return {
      message: 'Process deleted',
      ack: process,
    };
  }
}
