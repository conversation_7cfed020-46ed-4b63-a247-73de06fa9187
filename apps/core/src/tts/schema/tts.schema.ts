import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { AudioFormat, Status, TTSParams, TTSProviders } from '../entities/tt.entity';
import mongoose from 'mongoose';

@Schema({
  _id: null,
})
export class S3Model {
  @Prop({
    type: String,
  })
  Location: string;

  @Prop({
    type: String,
  })
  Key: string;

  @Prop({
    type: String,
    enum: AudioFormat.options,
  })
  AudioFormat: AudioFormat;

  @Prop({
    type: Number,
  })
  FileSize: number;

  @Prop({
    type: Date,
  })
  Expiration: Date;

  @Prop({
    type: String,
  })
  AccessUrl: string;
}

@Schema({
  _id: null,
})
export class CDNModel {
  @Prop()
  URL: string;
}

@Schema({ _id: null })
export class CreditUsageModel {
  @Prop({
    type: Number,
  })
  Cost: number;

  @Prop({
    type: String,
  })
  Currency: string;

  @Prop({
    type: Number,
  })
  Credits: number | null;

  @Prop({
    type: Number,
  })
  PerCharacter: number;
}

@Schema({ _id: null })
export class AudioConfigModel {
  @Prop()
  SpeechRate: number;

  @Prop({
    type: Number,
  })
  Pitch: number;

  @Prop({
    type: String,
  })
  Gender: 'MALE' | 'FEMALE';

  @Prop({
    type: String,
  })
  VoiceModel: string;

  @Prop({
    type: String,
  })
  Language: string;

  @Prop({
    type: Number,
  })
  SampleRateHertz: number;

  @Prop({
    type: Number,
  })
  VolumeGainDecibels: number;
}

@Schema({ _id: null })
export class EventModel {
  @Prop({
    type: Date,
  })
  StartedAt: Date;

  @Prop({
    type: Date,
  })
  FinishedAt: Date;

  @Prop({
    type: String,
  })
  FailedReason?: string;
}

@Schema({
  timestamps: true,
  collection: 'tts',
  versionKey: false,
})
export class TTSModel {
  @Prop({
    type: String,
  })
  Name: string;

  @Prop({
    type: String,
  })
  Summary: string;

  @Prop({
    type: AudioConfigModel,
  })
  AudioConfig: AudioConfigModel;

  @Prop({
    type: String,
  })
  SpeechText: string;

  @Prop({
    type: Number,
  })
  CharacterCount: number;

  @Prop({
    type: Number,
  })
  Duration: number; // In Seconds

  @Prop({
    type: String,
    enum: TTSProviders.options,
  })
  Provider: TTSProviders;

  @Prop({
    type: mongoose.Schema.Types.Mixed,
  })
  TTSParams: TTSParams;

  @Prop({
    type: String,
    enum: Status.options,
  })
  Status: Status;

  @Prop({
    type: mongoose.Schema.Types.Mixed,
  })
  Scopes: Record<string, string>;

  @Prop({
    type: String,
    required: true,
  })
  TenantID: string;

  @Prop({
    type: S3Model,
  })
  S3?: S3Model;

  @Prop({
    type: CDNModel,
  })
  CDN?: CDNModel;

  @Prop({
    type: CreditUsageModel,
  })
  CreditUsage: CreditUsageModel;

  @Prop({
    type: EventModel,
  })
  Event?: EventModel;
}

export const TTSSchema = SchemaFactory.createForClass(TTSModel);
