"use client";

import { API } from "@/apis/api";
import { TSubscription } from "@/apis/subscription.api";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import {
  AlertCircle,
  CheckIcon,
  CreditCard,
  MousePointerClick,
  Squircle,
} from "lucide-react";
import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { RazorpayOrderOptions, useRazorpay } from "react-razorpay";
import { toast } from "sonner";
import { useAuth } from "./auth-context";
import { PricingStructure, TPlan, TTierGroup } from "@/apis/plan-&-tier.api";
import { <PERSON>, CardFooter, CardHeader } from "@workspace/ui/components/card";
import { cn } from "@workspace/ui/lib/utils";
import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from "@workspace/ui/components/scroll-area";
import { useIsMobile } from "@/hooks/use-is-mobile";

type TAccess = {
  chat: {
    access: boolean;
    properties: Record<string, string>;
  };
  story: {
    access: boolean;
    properties: Record<string, string>;
  };
  popQuiz: {
    access: boolean;
    properties: Record<string, string>;
  };
};

interface PaywallContextType {
  subscriptions: TSubscription[];
  fetchSubscriptions: () => Promise<void>;
  refreshSubscription: (SubID: string) => Promise<void>;
  access: TAccess;
  setAccessByProject: (projectID: string) => void;
  inViewSubscription: TSubscription | null;
}

export const PaywallContext = createContext<PaywallContextType>({
  subscriptions: [],
  fetchSubscriptions: () => Promise.resolve(),
  refreshSubscription: () => Promise.resolve(),
  access: {
    chat: {
      access: false,
      properties: {},
    },
    story: {
      access: false,
      properties: {},
    },
    popQuiz: {
      access: false,
      properties: {},
    },
  },
  setAccessByProject: () => {},
  inViewSubscription: null,
});

export const PaywallProvider = ({ children }: { children: ReactNode }) => {
  const [subscriptions, setSubscriptions] = useState<TSubscription[]>([]);
  const [inViewSubscription, setInViewSubscription] =
    useState<TSubscription | null>(null);
  const [access, setAccess] = useState<TAccess>({
    chat: {
      access: false,
      properties: {},
    },
    story: {
      access: false,
      properties: {},
    },
    popQuiz: {
      access: false,
      properties: {},
    },
  });
  const [tierGroups, setTierGroups] = useState<TTierGroup[]>([]);

  const { Razorpay } = useRazorpay();
  const { user, isAuthenticated, tenant } = useAuth();

  const fetchSubscriptions = async () => {
    const { data, errors } = await API.SUBSCRIPTION.GetMySubscriptions();

    if (errors) {
      errors.forEach((error) => toast.error(error.message));
      return;
    }

    if (data) {
      setSubscriptions(data);
    }
  };

  const verifyPayment = async (params: {
    OrderID: string;
    PaymentID: string;
    Signature: string;
  }) => {
    const { data, errors } = await API.SUBSCRIPTION.VerifyPayment(params);
    if (errors) {
      errors.forEach((error) => toast.error(error.message));
      return;
    }
    toast.success("Payment Successful!");
    await fetchSubscriptions();
  };

  const handlePayment = async () => {
    const options: RazorpayOrderOptions = {
      key: process.env.NEXT_PUBLIC_RAZORPAY_KEY || "",
      amount: 0, // Amount in paise
      currency: "INR",
      name: isSubPaymentRequired()?.Plan?.Tier || "",
      description: isSubPaymentRequired()?.Plan?.Name || "",
      order_id: "", // Generate order_id on server
      handler: async (response) => {
        await verifyPayment({
          OrderID: response.razorpay_order_id,
          PaymentID: response.razorpay_payment_id,
          Signature: response.razorpay_signature,
        });
      },
      prefill: {
        name: user?.Name,
        email: user?.Email,
        contact: user?.PhoneNumber,
      },
      theme: {
        color: "#F37254",
      },
    };

    const { data, errors } = await API.SUBSCRIPTION.PayForSubscription(
      isSubPaymentRequired()?._id || ""
    );

    if (errors) {
      errors.forEach((error) => toast.error(error.message));
      fetchSubscriptions();
      return;
    }
    options.order_id = data.ack.OrderID;
    options.amount = data.ack.Amount;
    // for handling when upgrade subscription is free
    if (!options.order_id) {
      toast.success(data.message);
      await fetchSubscriptions();
      return;
    }

    const razorpayInstance = new Razorpay(options);
    razorpayInstance.open();
  };

  const handlePaymentForUpgrade = async (params: {
    SubscriptionID: string;
    Variant: string;
    ForceCancel: boolean;
    PlanID: string;
  }) => {
    if (!params.Variant.length) {
      toast.error("Please select a variant");
      return;
    }
    const { data, errors } = await API.SUBSCRIPTION.UpgradeSubscription({
      SubscriptionID: params.SubscriptionID,
      Variant: params.Variant,
      ForceCancel: params.ForceCancel,
      PlanID: params.PlanID,
    });

    if (errors) {
      errors.forEach((error) => toast.error(error.message));
      fetchSubscriptions();
      return;
    }
    const options: RazorpayOrderOptions = {
      key: process.env.NEXT_PUBLIC_RAZORPAY_KEY || "",
      amount: 0, // Amount in paise
      currency: "INR",
      name: isSubPaymentRequired()?.Plan?.Tier || "",
      description: isSubPaymentRequired()?.Plan?.Name || "",
      order_id: "", // Generate order_id on server
      handler: async (response) => {
        await verifyPayment({
          OrderID: response.razorpay_order_id,
          PaymentID: response.razorpay_payment_id,
          Signature: response.razorpay_signature,
        });
      },
      prefill: {
        name: user?.Name,
        email: user?.Email,
        contact: user?.PhoneNumber,
      },
      theme: {
        color: "#F37254",
      },
    };
    options.order_id = data.ack.OrderID;
    options.amount = data.ack.Amount;
    // for handling when upgrade subscription is free
    if (!options.order_id) {
      toast.success(data.message);
      await fetchSubscriptions();
      return;
    }
    const razorpayInstance = new Razorpay(options);
    razorpayInstance.open();
  };

  const refreshSubscription = async (SubID: string) => {
    const { data, errors } = await API.SUBSCRIPTION.GetMySubscriptions();

    if (errors) {
      errors.forEach((error) => toast.error(error.message));
      return;
    }

    if (data) {
      const findSub = data.find(
        (sub) => sub._id === SubID && sub.Status !== "Cancelled"
      );
      if (!findSub) {
        return;
      }
      setInViewSubscription(findSub);
      setSubscriptions(data);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchSubscriptions();
    }
  }, [isAuthenticated]);

  const setAccessByProject = (projectID: string) => {
    const findSub = subscriptions.find(
      (sub) =>
        sub.Plan.ProjectIDs.includes(projectID) && sub.Status !== "Cancelled"
    );
    if (!findSub) {
      return;
    }
    const Flags = findSub.Plan.Flags;
    const projectAccess = {
      chat: {
        access: Flags?.some((flag) => flag.FlagID === "CHAT") || false,
        properties:
          Flags?.find((flag) => flag.FlagID === "CHAT")?.Properties || {},
      },
      story: {
        access: Flags?.some((flag) => flag.FlagID === "STORY") || false,
        properties:
          Flags?.find((flag) => flag.FlagID === "STORY")?.Properties || {},
      },
      popQuiz: {
        access: Flags?.some((flag) => flag.FlagID === "POP-QUIZ") || false,
        properties:
          Flags?.find((flag) => flag.FlagID === "POP-QUIZ")?.Properties || {},
      },
    };
    setAccess(projectAccess);
    setInViewSubscription(findSub);
  };

  const value = {
    subscriptions,
    fetchSubscriptions,
    access,
    setAccessByProject,
    inViewSubscription,
    refreshSubscription,
  };

  const fetchTierGroups = async () => {
    const { data, errors } = await API.PLAN_TIER_API.GetTierGroups({
      PlanIDs: subscriptions.map((sub) => sub.PlanID),
      SubDomain: tenant?.WorkSpaceDomain,
    });
    if (errors) {
      errors.forEach((error) => toast.error(error.message));
      return;
    }
    if (data) {
      setTierGroups(data);
    }
  };

  const isSubPaymentRequired = useCallback(() => {
    return (
      subscriptions.find((subscription) => subscription.Status === "Pending") ||
      subscriptions
        .filter((sub) => sub.Status !== "Cancelled")
        .find(
          (subs) =>
            (subs?.Price > 0 &&
              subs?.EndDate &&
              new Date(subs.EndDate).getTime() < new Date().getTime()) ||
            (subs?.Plan?.MarkAsTrailPlan &&
              new Date(subs.EndDate).getTime() < new Date().getTime()) ||
            (subs?.Plan?.MarkAsTrailPlan &&
              subs.Credits.reduce((a, b) => a + b.RemainingCredit, 0) < 1)
        )
    );
  }, [subscriptions, inViewSubscription]);

  const isTrailPlanExpired = useCallback(() => {
    return isSubPaymentRequired()?.Plan?.MarkAsTrailPlan;
  }, [isSubPaymentRequired]);

  useEffect(() => {
    if (!isAuthenticated) return;
    fetchTierGroups();
  }, [isTrailPlanExpired]);

  const PayForSubscriptionDialog = () => {
    const [selectedPlan, setSelectedPlan] = useState<
      | (TPlan & {
          PricingStructure: PricingStructure;
        })
      | null
    >(null);
    const DurationByValue = {
      1: "Daily",
      7: "Weekly",
      30: "Monthly",
      365: "Yearly",
    };
    const ValueByDuration = {
      Daily: 1,
      Weekly: 7,
      Monthly: 30,
      Yearly: 365,
    };
    const allPlans = useCallback(() => {
      return tierGroups
        ?.map((tg) =>
          tg.Plans?.map((plan) =>
            plan.PricingStructures?.map((ps) => {
              return {
                ...plan,
                PricingStructure: {
                  ...ps,
                  Duration:
                    ps.Duration ??
                    ValueByDuration?.[ps.Type as keyof typeof ValueByDuration],
                },
              };
            })
          ).flat()
        )
        .flat()
        .filter((pl) => !pl.MarkAsTrailPlan);
    }, [tierGroups]);

    const uniqueVariants = useCallback(() => {
      // After sorting by duration then all other variants
      const SortOrder = ["Daily", "Weekly", "Monthly", "Yearly"];
      const vars = [
        ...new Set(allPlans().map((plan) => plan.PricingStructure.Duration)),
      ].sort((a, b) => (b ?? 0) - (a ?? 0));
      return vars;
    }, [allPlans]);
    const [variant, setVariant] = useState(uniqueVariants()[0]);
    const [selectedPeriod, setSelectedPeriod] = useState(uniqueVariants()[0]);
    const [selectedVariant, setSelectedVariant] = useState<string | null>(null);
    const isMobile = useIsMobile();

    return (
      <Dialog open={!!isSubPaymentRequired()} modal>
        <DialogContent className="sm:max-w-[625px]">
          <DialogHeader>
            <DialogTitle>
              <div className="flex items-center gap-2">
                <AlertCircle color="orange" />
                <h2 className="text-lg font-semibold">
                  {isSubPaymentRequired()?.Plan?.MarkAsTrailPlan
                    ? "Your Trail Plan is expired"
                    : isSubPaymentRequired()?.Status === "Pending"
                      ? "Payment Pending"
                      : "Subscription Renewal Pending"}
                </h2>
              </div>
            </DialogTitle>
            <DialogDescription>
              {isSubPaymentRequired()?.Plan?.MarkAsTrailPlan
                ? "Upgrade your plan to continue using the service."
                : isSubPaymentRequired()?.Status === "Pending"
                  ? "Please complete your subscription payment to continue using the service."
                  : "Please renew your subscription to continue using the service."}
            </DialogDescription>
          </DialogHeader>
          {!isTrailPlanExpired() && (
            <div className="flex flex-col items-center gap-2 p-6 text-center">
              <Badge className="text-md">
                {isSubPaymentRequired()?.Plan?.Tier}
              </Badge>
              <h2 className="text-lg font-semibold">
                {isSubPaymentRequired()?.Plan?.Name}
              </h2>
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                {isSubPaymentRequired()?.Plan?.Description}
              </p>
              <div className="flex items-center gap-2">
                <span className="text-4xl font-bold text-zinc-900 dark:text-zinc-100">
                  {new Intl.NumberFormat("en-IN", {
                    style: "currency",
                    currency: "INR",
                  }).format((isSubPaymentRequired()?.Price ?? 0) / 100)}
                </span>
                <span className="text-md text-zinc-500 dark:text-zinc-400">
                  /{" "}
                  {DurationByValue?.[
                    isSubPaymentRequired()
                      ?.VariantDuration as keyof typeof DurationByValue
                  ] ?? isSubPaymentRequired()?.VariantDuration}
                </span>
              </div>
            </div>
          )}
          {isTrailPlanExpired() && (
            <div className="grid grid-cols-1 gap-4 w-full min-h-[200px] lg:grid-cols-2">
              <div className="flex flex-col gap-2">
                <div className="flex gap-2 flex-wrap">
                  {uniqueVariants().map((period) => (
                    <Badge
                      key={period}
                      variant={
                        period === selectedPeriod ? "default" : "outline"
                      }
                      onClick={() => setSelectedPeriod(period)}
                      className="cursor-pointer text-sm"
                    >
                      {DurationByValue?.[
                        period as keyof typeof DurationByValue
                      ] || `${period} Days`}
                    </Badge>
                  ))}
                </div>
                <ScrollArea className="max-h-96 w-full rounded-md border flex flex-col">
                  {allPlans()
                    .filter((pl) => !pl.MarkAsTrailPlan)
                    .map((plan) => (
                      <Card
                        key={plan._id}
                        className={cn(
                          "w-full mb-2 cursor-pointer",
                          selectedPlan?.PlanID === plan.PlanID &&
                            selectedVariant === plan.PricingStructure.Type
                            ? "border-primary bg-primary/20"
                            : ""
                        )}
                        onClick={() => {
                          setSelectedPlan(plan);
                          setSelectedVariant(plan.PricingStructure.Type);
                        }}
                      >
                        <CardHeader>
                          <div className="flex items-center justify-between gap-2">
                            <h2 className="text-lg font-semibold">
                              {plan.Name}{" "}
                            </h2>
                            <span className="text-sm bg-muted px-2 py-1 rounded max-w-fit">
                              {plan.Tier}
                            </span>
                          </div>
                          <p className="text-sm text-zinc-500 dark:text-zinc-400">
                            {plan.Description}
                          </p>
                          <div className="mb-4">
                            <div className="flex items-baseline gap-2">
                              <span className="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
                                {new Intl.NumberFormat("en-IN", {
                                  style: "currency",
                                  currency: "INR",
                                }).format(plan.PricingStructure.Price / 100)}
                              </span>
                              <span className="text-md text-zinc-500 dark:text-zinc-400">
                                /{" "}
                                {DurationByValue?.[
                                  plan.PricingStructure
                                    .Duration as keyof typeof DurationByValue
                                ] ?? `${plan.PricingStructure.Duration} Days`}
                              </span>
                            </div>
                          </div>
                          <Button
                            variant={
                              plan.PlanID === selectedPlan?.PlanID &&
                              selectedVariant === plan.PricingStructure.Type
                                ? "default"
                                : "outline"
                            }
                            className="w-full"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedPlan(plan);
                              setSelectedVariant(plan.PricingStructure.Type);
                            }}
                            size="sm"
                          >
                            {plan.PlanID === selectedPlan?.PlanID &&
                            selectedVariant === plan.PricingStructure.Type
                              ? "Selected"
                              : "Select"}
                          </Button>
                        </CardHeader>
                      </Card>
                    ))}
                  <ScrollBar orientation="horizontal" />
                </ScrollArea>
              </div>
              <div className="flex flex-col items-center gap-2 text-center h-full bg-accent rounded-lg">
                {!selectedPlan && (
                  <div className="flex flex-col items-center justify-center gap-2 text-center h-full">
                    <MousePointerClick size={48} strokeWidth={1} />
                    <p className="text-md text-zinc-500 dark:text-zinc-400">
                      Choose a plan to continue
                    </p>
                  </div>
                )}
                {selectedPlan && (
                  <div className="p-6 flex-1 text-start">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-md font-semibold text-zinc-900 dark:text-zinc-100">
                        {selectedPlan.Name}
                      </h3>
                      <div
                        className={cn(
                          "p-1 px-2 rounded-sm text-xs",
                          "bg-zinc-100/20 text-white"
                        )}
                      >
                        {selectedPlan.Tier}
                      </div>
                    </div>
                    <div className="flex items-center justify-between mb-4">
                      <Badge variant="outline">
                        {selectedPlan.PricingStructure.CreditUsage.CapQuota}{" "}
                        {selectedPlan.PricingStructure.CreditUsage.CapType}
                      </Badge>
                    </div>

                    <div className="mb-4">
                      <div className="flex items-baseline gap-2">
                        <span className="text-xl font-bold text-zinc-900 dark:text-zinc-100">
                          {new Intl.NumberFormat("en-IN", {
                            style: "currency",
                            currency: "INR",
                          }).format(selectedPlan.PricingStructure.Price / 100)}
                        </span>
                        <span className="text-md text-zinc-500 dark:text-zinc-400">
                          /{" "}
                          {DurationByValue[
                            selectedPlan.PricingStructure
                              .Duration as keyof typeof DurationByValue
                          ] ?? `${selectedPlan.PricingStructure.Duration} Days`}
                        </span>
                      </div>
                      <p className="mt-2 text-sm text-zinc-600 dark:text-zinc-400">
                        {selectedPlan.Description}
                      </p>
                    </div>

                    <div className="space-y-4 max-h-[300px] overflow-y-auto scrollbar-thin">
                      {selectedPlan.Features.map((feature) => (
                        <div
                          key={feature.Title}
                          className="flex gap-4 text-left"
                        >
                          <div
                            className={cn(
                              "mt-1 p-0.5 rounded-full transition-colors duration-200",
                              "text-emerald-600 dark:text-emerald-400"
                            )}
                          >
                            <CheckIcon className="w-4 h-4" />
                          </div>
                          <div>
                            <div className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                              {feature.Title}
                            </div>
                            <div className="text-xs text-zinc-500 dark:text-zinc-400">
                              {feature.HelpText}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="default"
              className="w-full"
              onClick={() =>
                isTrailPlanExpired()
                  ? handlePaymentForUpgrade({
                      SubscriptionID: isSubPaymentRequired()?._id || "",
                      Variant: selectedVariant || "",
                      ForceCancel: true,
                      PlanID: selectedPlan?.PlanID || "",
                    })
                  : handlePayment()
              }
            >
              <CreditCard className="h-4 w-4" />
              {isTrailPlanExpired()
                ? "Upgrade"
                : isSubPaymentRequired()?.Status === "Pending"
                  ? "Pay"
                  : "Pay & Renew"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <PaywallContext.Provider value={value}>
      {isSubPaymentRequired() && <PayForSubscriptionDialog />}
      {children}
    </PaywallContext.Provider>
  );
};

export const usePaywallContext = () => useContext(PaywallContext);
