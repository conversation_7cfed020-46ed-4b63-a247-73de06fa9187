import { IsEnum, IsNotEmpty, IsNumber, IsString } from 'class-validator';

export enum TeachingStyle {
  SCIENTIFIC = 'Scientific',
  HISTORICAL = 'Historical',
  TECHNICAL = 'Technical',
  STORYTELLING = 'Storytelling',
  SOCRATIC = 'Socratic',
  ACADEMIC = 'Academic',
  CONVERSATIONAL = 'Conversational',
  GENERAL = 'General',
  INTERACTIVE = 'Interactive',
}

export enum AgeGroup {
  ELEMENTARY = 'Elementary',
  MIDDLE_SCHOOL = 'Middle School',
  HIGH_SCHOOL = 'High School',
  UNDERGRADUATE = 'Undergraduate',
  GRADUATE = 'Graduate',
  ADULT = 'Adult',
}

export class CreateLectureDto {
  @IsNotEmpty()
  @IsString()
  TenantID: string;

  @IsNotEmpty()
  @IsString()
  ProjectID: string;

  @IsNotEmpty()
  @IsString()
  SourceID: string;

  @IsNotEmpty()
  @IsString()
  ChapterID: string;

  @IsNotEmpty()
  @IsEnum(TeachingStyle)
  TeachingStyle: TeachingStyle;

  @IsNotEmpty()
  @IsEnum(AgeGroup)
  AgeGroup: AgeGroup;

  @IsNotEmpty()
  @IsNumber()
  CreativityLevel: number;
}
