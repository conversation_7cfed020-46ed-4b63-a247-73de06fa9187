import { Test, TestingModule } from '@nestjs/testing';
import { TokenCounterController } from './token-counter.controller';
import { TokenCounterService } from './token-counter.service';

describe('TokenCounterController', () => {
  let controller: TokenCounterController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TokenCounterController],
      providers: [TokenCounterService],
    }).compile();

    controller = module.get<TokenCounterController>(TokenCounterController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
