import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Sse } from '@nestjs/common';
import { TestService } from './test.service';
import { UpdateTestDto } from './dto/update-test.dto';
import { interval } from 'rxjs';
import { map } from 'rxjs/operators';

@Controller('test')
export class TestController {
  constructor(private readonly testService: TestService) {}

  @Sse('stream')
  test() {
    return interval(1000).pipe(map((_) => ({ data: 'Hello, world!', x: 0 })));
  }

  @Get()
  findAll() {
    return this.testService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.testService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateTestDto: UpdateTestDto) {
    return this.testService.update(+id, updateTestDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.testService.remove(+id);
  }
}
