"use client";

import { useAuth } from "@/contexts/auth-context";
import { Button } from "@workspace/ui/components/button";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Skeleton } from "@workspace/ui/components/skeleton";

export function UserProfile() {
  const { user, isLoading, isAuthenticated, signOut } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-4">
        <Skeleton className="h-12 w-12 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-[200px]" />
          <Skeleton className="h-4 w-[160px]" />
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  const initials = (user.GivenName?.[0] || "") + (user.FamilyName?.[0] || "");
  const displayName =
    user.GivenName && user.FamilyName
      ? `${user.GivenName} ${user.FamilyName}`
      : user.Name || user.Email;

  return (
    <div className="flex flex-col items-center space-y-4 p-4">
      <Avatar className="h-24 w-24">
        <AvatarImage src={user.ProfilePicture || ""} alt={displayName} />
        <AvatarFallback>
          {initials || user.Email?.[0]?.toUpperCase() || "U"}
        </AvatarFallback>
      </Avatar>

      <div className="text-center">
        <h3 className="text-lg font-medium">{displayName}</h3>
        <p className="text-sm text-muted-foreground">{user.Email}</p>
      </div>

      <Button variant="outline" size="sm" onClick={signOut}>
        Sign Out
      </Button>
    </div>
  );
}
