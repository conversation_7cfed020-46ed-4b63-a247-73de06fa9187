"use client";
import { API } from "@/apis/api";
import { TContentAlbum } from "@/apis/content-album.api";
import { ThemeToggle } from "@/components/theme-toggle";
import { ThreadList } from "@/components/thread/thread-list";
import { useAuth } from "@/contexts/auth-context";
import { useModeContext } from "@/contexts/mode-context";
import { usePaywallContext } from "@/contexts/paywall-context";
import { useProjectContext } from "@/contexts/project-context";
import { useIsMobile } from "@/hooks/use-is-mobile";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Button } from "@workspace/ui/components/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@workspace/ui/components/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Separator } from "@workspace/ui/components/separator";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Tabs, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import { cn } from "@workspace/ui/lib/utils";
import {
  AlignLeft,
  BookOpen,
  Boxes,
  ChevronLeft,
  Clapperboard,
  Clock8,
  FileQuestion,
  MessageCircle,
  RotateCcw,
  ShieldQuestion,
  Squirrel,
  X,
} from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import AccessMode from "../auth/mode-access";
import PopQuizList from "../pop-quiz/pop-quiz-list";
import { create } from "zustand";
import Image from "next/image";
import { Progress } from "@workspace/ui/components/progress";
import { toast } from "sonner";

export const SidebarStore = create<{
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}>((set) => ({
  sidebarOpen: true,
  setSidebarOpen: (open: boolean) => set({ sidebarOpen: open }),
}));

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, isLoading, isAuthenticated, signOut, tenant } = useAuth();
  const {
    access,
    setAccessByProject,
    inViewSubscription,
    refreshSubscription,
  } = usePaywallContext();
  const { sidebarOpen, setSidebarOpen } = SidebarStore();
  const [currentMode, setCurrentMode] = useState<"doubt" | "quiz" | "story">(
    "doubt"
  );
  const {
    projects,
    isLoading: projectsLoading,
    selectedProject,
    setSelectedProject,
  } = useProjectContext();
  const { mode, setMode } = useModeContext();
  const isDoubtMode = mode === "doubt";
  const isStoryMode = mode === "story";
  const isQuizMode = mode === "quiz";

  const [contentAlbums, setContentAlbums] = useState<TContentAlbum[]>([]);

  const getContentAlbums = async () => {
    if (!isStoryMode) return;
    const { data, errors } = await API.CONTENT_ALBUM.GetContentAlbums({
      ProjectID: selectedProject?.id!,
    });

    if (!errors) {
      setContentAlbums(data);
    }
  };

  const isMobile = useIsMobile();

  useEffect(() => {
    if (!selectedProject) return;
    setAccessByProject(selectedProject.id);
    if (isStoryMode) {
      router.push(`/${selectedProject.id}/content`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedProject, isAuthenticated]);

  useEffect(() => {
    const isAuthRoute = pathname.startsWith("/sign-in");

    if (isAuthenticated) {
      // User is authenticated
      // If on an auth route (like sign-in), redirect to home
      if (isAuthRoute) {
        router.push("/");
      }
    } else if (!isLoading) {
      // User is not authenticated and not in loading state
      // If not on an auth route, redirect to sign-in
      if (!isAuthRoute && pathname !== "/sign-up") {
        router.push("/sign-in");
      }
    }
  }, [isAuthenticated, isLoading, pathname, router]);

  const accessibleRedirect = (): string => {
    if (access.story.access) {
      return `/${selectedProject?.id}/content`;
    }
    if (access.chat.access) {
      return "/";
    }
    if (access.popQuiz.access) {
      return `/${selectedProject?.id}/quiz`;
    }
    return "/";
  };

  useEffect(() => {
    if (pathname.includes("/content") && !access.story.access) {
      router.push(accessibleRedirect());
    }
    if (pathname.includes("/quiz") && !access.popQuiz.access) {
      router.push(accessibleRedirect());
    }
    if (pathname.includes("/thread") && !access.chat.access) {
      router.push(accessibleRedirect());
    }
  }, [pathname]);

  useEffect(() => {
    if (pathname.includes("/thread") || pathname === "/") {
      setCurrentMode("doubt");
    } else if (pathname.includes("/quiz")) {
      setCurrentMode("quiz");
    } else if (pathname.includes("/content")) {
      setCurrentMode("story");
      getContentAlbums();
    }
  }, [pathname]);

  const handleStartNewConversation = () => {
    router.push("/");
  };

  const ContentLibrary = () => {
    const selectedContentAlbum = usePathname();
    return (
      <div className="flex flex-col px-2 gap-2">
        {contentAlbums.length > 0 ? (
          contentAlbums.map((album) => (
            <div
              className={
                "cursor-pointer rounded-lg overflow-hidden flex flex-col gap-2 hover:shadow-lg p-2 border-1" +
                (selectedContentAlbum?.includes(album.ContentAlbumID)
                  ? " bg-accent"
                  : "")
              }
              key={album.ContentAlbumID}
              onClick={() => {
                isMobile && setSidebarOpen(false);
                router.push(
                  `/${album.ProjectID}/content/${album.ContentAlbumID}`
                );
              }}
            >
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2 max-w-20 max-h-16">
                  <img
                    src={album.CoverImage}
                    alt={album.Name}
                    className="object-cover rounded-sm overflow-hidden"
                  />
                </div>
                <div>
                  <h2 className="text-lg font-semibold">{album.Name}</h2>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {album.Description ?? "..."}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3 text-sm">
                <span className="flex items-center gap-1">
                  <Clock8 size={16} />
                  <span className="text-xs text-muted-foreground  ">
                    {Math.floor((album.ContentDuration ?? 0) / 60 / 60) > 0
                      ? Math.floor((album.ContentDuration ?? 0) / 60 / 60) +
                        " hours"
                      : Math.floor((album.ContentDuration ?? 0) / 60) +
                        " minutes"}
                  </span>
                </span>
                <span className="flex items-center gap-1">
                  <Clapperboard size={16} />
                  <span className="text-xs text-muted-foreground">
                    {album.ContentCount ?? 0} contents
                  </span>
                </span>
                <span className="flex items-center gap-1">
                  <Boxes size={16} />
                  <span className="text-xs text-muted-foreground">
                    {album.ContentGroups.length} modules
                  </span>
                </span>
              </div>
            </div>
          ))
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-muted-foreground mt-2 gap-2">
            <Squirrel size={48} strokeWidth={1} />
            <p className=" text-md">No content albums available</p>
          </div>
        )}
      </div>
    );
  };

  const NavContent = () => {
    const ActiveCredits = useCallback(() => {
      return inViewSubscription?.Credits?.filter(
        (c) =>
          (c.ExpiryDate && new Date(c.ExpiryDate) > new Date()) ||
          c.ExpiryDate === null
      )[0];
    }, [inViewSubscription, selectedProject]);
    return (
      <>
        <div className="px-4 py-3 flex flex-col gap-3">
          {isDoubtMode && (
            <>
              {!!ActiveCredits() && (
                <div className="bg-muted p-2 rounded-lg">
                  <div className="flex items-center gap-2">
                    <p className="text-primary font-semibold">
                      {(ActiveCredits()?.Credit as number) -
                        (ActiveCredits()?.RemainingCredit as number)}
                    </p>
                    <span className="text-muted-foreground">/</span>
                    <p className="font-semibold">
                      {ActiveCredits()?.Credit ?? 0}
                    </p>
                    <p className="text-sm">{ActiveCredits()?.CapType}</p>
                    <RotateCcw
                      className="cursor-pointer ml-auto hover:text-primary"
                      onClick={() => {
                        refreshSubscription(inViewSubscription?._id!);
                        toast.success("Credits refreshed");
                      }}
                      size={16}
                    />
                  </div>
                  <Progress
                    className="mt-2"
                    value={
                      (((ActiveCredits()?.Credit as number) -
                        (ActiveCredits()?.RemainingCredit as number)) /
                        (ActiveCredits()?.Credit as number)) *
                      100
                    }
                  />
                  <p className="text-xs text-muted-foreground mt-2">
                    Expires on{" "}
                    {new Date(
                      ActiveCredits()?.ExpiryDate ?? ""
                    ).toLocaleDateString("en-GB")}
                  </p>
                </div>
              )}
              <Button
                variant="outline"
                className="w-full justify-start gap-2 hover:bg-primary/10 cursor-pointer hover:border-gray-400"
                onClick={handleStartNewConversation}
                size="default"
              >
                <MessageCircle className="text-primary" />
                New Chat
              </Button>
            </>
          )}
          {isQuizMode && (
            <Button
              variant="outline"
              className="w-full justify-start gap-2 hover:bg-primary/10 cursor-pointer hover:border-gray-400"
              size="default"
              onClick={() => {
                router.push(`/${selectedProject?.id}/quiz`);
              }}
            >
              <ShieldQuestion className="text-primary" />
              Trigger New Quiz
            </Button>
          )}
          <Select
            value={selectedProject?.id || ""}
            onValueChange={(value) => {
              const project = projects.find((p) => p.id === value) || null;
              setSelectedProject(project);
            }}
            disabled={projectsLoading}
          >
            <SelectTrigger className="w-full">
              {projectsLoading ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
                  <span>Loading projects...</span>
                </div>
              ) : (
                <SelectValue placeholder="Select a Project" />
              )}
            </SelectTrigger>
            <SelectContent>
              {projectsLoading ? (
                <div className="p-4 space-y-2">
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-5 w-full" />
                </div>
              ) : projects.length === 0 ? (
                <div className="p-2 text-center text-muted-foreground">
                  No projects available
                </div>
              ) : (
                projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Thread List */}
        {mode === "doubt" && (
          <AccessMode isChat>
            <ThreadList />
          </AccessMode>
        )}
        {mode === "story" && (
          <AccessMode isStory>
            <ContentLibrary />
          </AccessMode>
        )}
        {mode === "quiz" && (
          <AccessMode isPopQuiz>
            <PopQuizList />
          </AccessMode>
        )}
      </>
    );
  };

  // Show loading state or render children based on authentication status
  return (
    <div>
      {isLoading ? (
        <div className="flex min-h-svh items-center justify-center">
          <div className="h-6 w-6 animate-spin round border-b-2 border-t-2 border-primary"></div>
        </div>
      ) : isAuthenticated ? (
        <div className="flex h-screen bg-background">
          {/* Sidebar with thread list */}
          <div
            className={cn(
              "border-r bg-muted/40 transition-all duration-200 hidden md:block overflow-x-hidden scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-none",
              sidebarOpen ? "max-w-80 min-w-80" : "w-0",
              mode === "quiz" ? "overflow-y-hidden" : ""
            )}
          >
            <div className="flex h-full flex-col">
              <div className="p-4 border-b flex justify-between items-center h-16">
                <h2 className="text-xl font-semibold">
                  {isDoubtMode && "Conversations"}
                  {isStoryMode && "Content Library"}
                  {isQuizMode && "Quiz"}
                </h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(false)}
                  className="md:hidden"
                >
                  <ChevronLeft className="h-5 w-5" />
                </Button>
              </div>
              <NavContent />
            </div>
          </div>

          <Drawer open={sidebarOpen && isMobile} onOpenChange={setSidebarOpen}>
            <DrawerContent className="min-h-[90vh]">
              <div className="mx-auto w-full max-w-sm">
                <DrawerHeader>
                  <DrawerTitle className="text-center">
                    {isDoubtMode && "Conversations"}
                    {isStoryMode && "Content Library"}
                    {isQuizMode && "Quiz"}
                  </DrawerTitle>
                </DrawerHeader>
                <ScrollArea className="h-[70vh] scrollbar-thin scrollbar-thumb-secondary">
                  <NavContent />
                </ScrollArea>
                <DrawerFooter>
                  <DrawerClose asChild>
                    <Button variant="outline">
                      <X className="h-4 w-4" />
                      Close
                    </Button>
                  </DrawerClose>
                </DrawerFooter>
              </div>
            </DrawerContent>
          </Drawer>

          {/* Main Content */}
          <div className="flex flex-col overflow-y-auto w-full h-[100vh]">
            {/* Top Bar */}
            <header className="min-h-16 h-16 border-b flex items-center justify-between px-4 bg-background">
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                >
                  {sidebarOpen ? (
                    <ChevronLeft className="h-5 w-5" />
                  ) : (
                    <AlignLeft className="h-5 w-5" />
                  )}
                </Button>
                <div className="flex items-center space-x-2">
                  {tenant?.Personalization?.ShowTenantLogo && (
                    <Image
                      src={tenant?.Logo || ""}
                      alt={tenant?.Name || ""}
                      width={24}
                      height={24}
                      unoptimized
                    />
                  )}
                  {tenant?.Personalization?.ShowTenantName && (
                    <h1 className="text-lg font-bold hidden md:block">
                      {tenant?.Name}
                    </h1>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {/* Mode Switcher */}
                <Tabs
                  defaultValue={mode ?? "doubt"}
                  onValueChange={(value) => {
                    setMode(value as "doubt" | "story" | "quiz");
                  }}
                >
                  <TabsList>
                    {access.chat.access && (
                      <TabsTrigger
                        value="doubt"
                        className="flex items-center gap-2 hover:bg-primary/10 cursor-pointer"
                      >
                        <MessageCircle size={16} />
                        <span>Chat</span>
                      </TabsTrigger>
                    )}
                    {access.story.access && (
                      <TabsTrigger
                        value="story"
                        className="flex items-center gap-2 hover:bg-primary/10 cursor-pointer"
                      >
                        <BookOpen size={16} />
                        <span>Story</span>
                      </TabsTrigger>
                    )}
                    {access.popQuiz.access && (
                      <TabsTrigger
                        value="quiz"
                        className="flex items-center gap-2 hover:bg-primary/10 cursor-pointer"
                      >
                        <FileQuestion size={16} />
                        <span>Quiz</span>
                      </TabsTrigger>
                    )}
                  </TabsList>
                </Tabs>
              </div>

              {user && (
                <div className="flex items-center space-x-3">
                  <ThemeToggle className="text-muted-foreground hover:text-foreground transition-colors" />
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="rounded-full"
                      >
                        <Avatar>
                          <AvatarImage src={user.ProfilePicture || ""} />
                          <AvatarFallback>
                            {user.Email?.[0]?.toUpperCase() || "U"}
                          </AvatarFallback>
                        </Avatar>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>Profile</DropdownMenuItem>
                      <DropdownMenuItem>Settings</DropdownMenuItem>
                      <Separator className="my-1" />
                      <DropdownMenuItem onClick={signOut}>
                        Sign Out
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </header>

            {/* Chat Content */}
            <main className="h-[calc(100vh-64px)] overflow-y-auto bg-background/95 flex flex-col p-4">
              {children}
            </main>
          </div>
        </div>
      ) : (
        children
      )}
    </div>
  );
}
