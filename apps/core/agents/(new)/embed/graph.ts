import { PDFLoader } from '@langchain/community/document_loaders/fs/pdf';
import { Document } from '@langchain/core/documents';
import { RunnableConfig } from '@langchain/core/runnables';
import { Annotation, END, START, StateGraph } from '@langchain/langgraph';
import { PROMPTS } from './prompts';
import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { makeRetriever } from '../retrieval-graph';
import GenerateID from '../../../common/lib/generate-id';
import { LLMInvocation, LLMOutput, calculateLLMInvocations } from '../../shared/utils';

type ChapterWiseRawDocsType = {
  chapter_id: string;
  chapter_number?: number;
  chapter_name?: string;
  page_from?: number;
  page_to?: number;
  document_page_from?: number;
  document_page_to?: number;
  docs: Document[];
};

const TableOfContentSchema = z.array(
  z.object({
    chapter_number: z.number().nullable(),
    chapter_name: z.string(),
    page_from: z.number(),
    page_to: z.number().nullable(),
  })
);

const TopicSchema = z.object({
  topics: z.array(
    z.object({ chapterName: z.string(), topic: z.string(), pageNumber: z.number(), subTopics: z.array(z.string()) })
  ),
});

const ChunkSummarySchema = z.object({
  summary: z.string(),
  topic: z.string(),
  subtopic: z.string().optional(),
});

const ChapterSummarySchema = z.object({
  chapter_id: z.string(),
  chapter_name: z.string(),
  summary: z.string(),
  topics: z.array(z.string()),
});

const BookSummarySchema = z.object({
  title: z.string(),
  summary: z.string(),
  main_topics: z.array(z.string()),
  chapter_highlights: z.array(
    z.object({
      chapter_name: z.string(),
      highlight: z.string(),
    })
  ),
});

export const InputAnnotation = Annotation.Root({
  pdfBlob: Annotation<Blob>({
    default: () => new Blob(),
    value: (old, _new) => _new,
  }),

  tenantId: Annotation<string>({
    default: () => '',
    value: (old, _new) => _new || old,
  }),

  projectId: Annotation<string>({
    default: () => '',
    value: (old, _new) => _new || old,
  }),

  sourceId: Annotation<string>({
    default: () => '',
    value: (old, _new) => _new || old,
  }),

  sourceName: Annotation<string>({
    default: () => '',
    value: (old, _new) => _new || old,
  }),

  tableOfContentPages: Annotation<number[]>({
    default: () => [],
    value: (old, _new) => _new,
  }),

  pageNumberOffset: Annotation<number>({
    default: () => 0,
    value: (old, _new) => _new,
  }),
});

export const EmbedStateAnnotation = Annotation.Root({
  ...InputAnnotation.spec,
  rawDocs: Annotation<Document[]>({
    default: () => [],
    value: (old, _new) => _new,
  }),
  tableOfContent: Annotation<
    {
      chapter_id: string;
      chapter_number?: number;
      chapter_name?: string;
      page_from?: number;
      page_to?: number;
      document_page_from?: number;
      document_page_to?: number;
    }[]
  >({
    default: () => [],
    value: (old, _new) => _new,
  }),
  chunkedDocs: Annotation<Document[]>({
    value: (old, _new) => _new,
  }),
  chapterWiseRawDocs: Annotation<ChapterWiseRawDocsType[]>({
    value: (old, _new) => _new,
  }),
  chapterWiseTopics: Annotation<
    {
      chapter_id: string;
      topics: z.infer<typeof TopicSchema>['topics'];
    }[]
  >({
    default: () => [],
    value: (old, _new) => _new,
  }),
  summarizedChunks: Annotation<
    {
      chunk_id: string;
      chunk_number: number;
      chapter_id: string;
      summary: string;
      topic: string;
      subtopic?: string;
    }[]
  >({
    default: () => [],
    value: (old, _new) => _new,
  }),
  chapterSummaries: Annotation<
    {
      chapter_id: string;
      chapter_name: string;
      summary: string;
      topics: string[];
    }[]
  >({
    default: () => [],
    value: (old, _new) => _new,
  }),
  bookSummary: Annotation<{
    title: string;
    summary: string;
    main_topics: string[];
    chapter_highlights: {
      chapter_name: string;
      highlight: string;
    }[];
  }>({
    default: () => ({
      title: '',
      summary: '',
      main_topics: [],
      chapter_highlights: [],
    }),
    value: (old, _new) => _new,
  }),
  documentIds: Annotation<string[]>({
    value: (old, _new) => _new,
  }),
  TokenCount: Annotation<{
    PromptTokens: number;
    CompletionTokens: number;
    TotalTokens: number;
    LLMInvocations: LLMInvocation[];
  }>({
    default: () => ({
      PromptTokens: 0,
      CompletionTokens: 0,
      TotalTokens: 0,
      LLMInvocations: [],
    }),
    reducer: (acc, curr) => ({
      PromptTokens: acc.PromptTokens + curr.PromptTokens,
      CompletionTokens: acc.CompletionTokens + curr.CompletionTokens,
      TotalTokens: acc.TotalTokens + curr.TotalTokens,
      LLMInvocations: [...acc.LLMInvocations, ...curr.LLMInvocations],
    }),
  }),
});

async function parsePdf(
  state: typeof EmbedStateAnnotation.State,
  config?: RunnableConfig
): Promise<typeof EmbedStateAnnotation.Update> {
  const { pdfBlob } = state;
  if (!pdfBlob) throw new Error('pdfBlob is required');
  const rawDocs = await new PDFLoader(pdfBlob).load();
  return {
    ...state,
    rawDocs,
  };
}

async function deriveTableOFContents(
  state: typeof EmbedStateAnnotation.State,
  config?: RunnableConfig
): Promise<typeof EmbedStateAnnotation.Update> {
  const { rawDocs, tableOfContentPages, pageNumberOffset } = state;
  const tableOfContentsDocs = rawDocs.filter((doc) => tableOfContentPages.includes(doc.metadata.loc.pageNumber));

  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'derive_table_of_contents'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });
  const prompt = PROMPTS.DERIVE_CONTENTS.replace('{documents}', JSON.stringify(tableOfContentsDocs)).replace(
    '{total_pages}',
    rawDocs.length.toString()
  );

  const response = await model.withStructuredOutput(z.object({ tableOfContent: TableOfContentSchema })).invoke(prompt);

  const tableOfContent = response.tableOfContent.map((chapter) => ({
    ...chapter,
    document_page_from: chapter.page_from + pageNumberOffset,
    document_page_to: chapter.page_to + pageNumberOffset,
    chapter_id: GenerateID.Generate16Hex('chap_'),
  }));

  const ChapterWiseRawDocsMap = tableOfContent.map((chapter) => {
    const filteredDocs = rawDocs.filter(
      (doc) =>
        doc.metadata.loc.pageNumber >= chapter.document_page_from &&
        doc.metadata.loc.pageNumber <= chapter.document_page_to
    );

    const chapterDocs = filteredDocs.reduce(
      (acc, doc) => {
        if (!acc[chapter.chapter_name]) {
          acc[chapter.chapter_name] = {
            ...chapter,
            docs: [],
          };
        }

        acc[chapter.chapter_name].docs.push({
          ...doc,
          metadata: {
            ...doc.metadata,
            chapter,
          },
        });

        return acc;
      },
      {} as {
        [chapter_name: string]: ChapterWiseRawDocsType;
      }
    );

    return chapterDocs;
  });

  const ChapterWiseRawDocs = ChapterWiseRawDocsMap.map((chapter) => Object.values(chapter)).flat();

  const topicPrompt = PROMPTS.DERIVE_TABLE_OF_CONTENTS();

  const ChapterWiseTopics: {
    chapter_id: string;
    topics: z.infer<typeof TopicSchema>['topics'];
  }[] = [];

  for (const chapter of ChapterWiseRawDocs) {
    const fTopicPrompt = topicPrompt.replace('{chapter}', JSON.stringify(chapter));
    const response = await model.withStructuredOutput(TopicSchema).invoke(fTopicPrompt);

    ChapterWiseTopics.push({
      chapter_id: chapter.chapter_id,
      topics: response.topics,
    });
  }

  return {
    ...state,
    tableOfContent,
    chapterWiseRawDocs: ChapterWiseRawDocs,
    chapterWiseTopics: ChapterWiseTopics,
    TokenCount,
  };
}

async function generateChunks(
  state: typeof EmbedStateAnnotation.State,
  config?: RunnableConfig
): Promise<typeof EmbedStateAnnotation.Update> {
  const { pageNumberOffset, sourceId, sourceName, chapterWiseRawDocs: ChapterWiseRawDocs } = state;

  const textSplitter = new RecursiveCharacterTextSplitter({
    chunkSize: 500,
    chunkOverlap: 100,
    separators: ['\n\n', '\n', '.'],
  });

  const chunkedDocs = (
    await Promise.all(
      ChapterWiseRawDocs.map(async (chapter) => {
        return await textSplitter.splitDocuments(chapter.docs);
      })
    )
  ).flat();

  return {
    ...state,
    chunkedDocs: chunkedDocs.map((doc, i) => ({
      ...doc,
      metadata: {
        ...doc.metadata,
        chunk: {
          chunk_id: GenerateID.Generate16Hex('cnk_'),
          chunk_number: i + 1,
          document_page_number: doc.metadata.loc.pageNumber,
          page_number: doc.metadata.loc.pageNumber - pageNumberOffset,
        },
        source: {
          source_id: sourceId,
          source_name: sourceName,
        },
        indexPage: state.tableOfContentPages.includes(doc.metadata.loc.pageNumber),
        projectId: state.projectId,
        tenantId: state.tenantId,
      },
    })),
  };
}

async function summarizeChunks(
  state: typeof EmbedStateAnnotation.State,
  config?: RunnableConfig
): Promise<typeof EmbedStateAnnotation.Update> {
  const { chunkedDocs, chapterWiseTopics } = state;
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'summarize_chunks'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  // Process chunks in batches to avoid rate limits
  const batchSize = 10;
  const summarizedChunks = [];

  for (let i = 0; i < chunkedDocs.length; i += batchSize) {
    const batch = chunkedDocs.slice(i, i + batchSize);
    const batchPromises = batch.map(async (doc) => {
      const topicsInChapter = chapterWiseTopics.find(
        (chapter) => chapter.chapter_id === doc.metadata.chapter.chapter_id
      );

      const prompt = PROMPTS.SUMMARIZE_CHUNKS(doc.pageContent, topicsInChapter);

      try {
        const response = await model.withStructuredOutput(ChunkSummarySchema).invoke(prompt);
        return {
          chunk_id: doc.metadata.chunk.chunk_id,
          chunk_number: doc.metadata.chunk.chunk_number,
          chapter_id: doc.metadata.chapter.chapter_id,
          summary: response.summary,
          topic: response.topic,
          subtopic: response.subtopic || '',
        };
      } catch (error) {
        console.error(`Error summarizing chunk ${doc.metadata.chunk.chunk_id}:`, error);
        return {
          chunk_id: doc.metadata.chunk.chunk_id,
          chunk_number: doc.metadata.chunk.chunk_number,
          chapter_id: doc.metadata.chapter.chapter_id,
          summary: 'Failed to generate summary',
          topic: 'Unknown',
          subtopic: '',
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    summarizedChunks.push(...batchResults);
  }

  return {
    ...state,
    summarizedChunks,
    TokenCount,
  };
}

async function generateChapterSummaries(
  state: typeof EmbedStateAnnotation.State,
  config?: RunnableConfig
): Promise<typeof EmbedStateAnnotation.Update> {
  const { summarizedChunks, tableOfContent } = state;
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'generate_chapter_summaries'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  // Group summaries by chapter
  const chapterSummariesMap = new Map();

  // Initialize with all chapters from table of contents
  tableOfContent.forEach((chapter) => {
    chapterSummariesMap.set(chapter.chapter_id, {
      chapter_id: chapter.chapter_id,
      chapter_name: chapter.chapter_name || '',
      chunk_summaries: [],
      topics: new Set(),
    });
  });

  // Group chunk summaries by chapter
  summarizedChunks.forEach((chunk) => {
    const chapterData = chapterSummariesMap.get(chunk.chapter_id);
    if (chapterData) {
      chapterData.chunk_summaries.push(chunk.summary);
      chapterData.topics.add(chunk.topic);
    }
  });

  // Generate a summary for each chapter based on its chunk summaries
  const chapterSummaries = await Promise.all(
    Array.from(chapterSummariesMap.values()).map(async (chapterData) => {
      if (chapterData.chunk_summaries.length === 0) {
        return {
          chapter_id: chapterData.chapter_id,
          chapter_name: chapterData.chapter_name,
          summary: 'No content available for this chapter',
          topics: [] as string[],
        };
      }

      const prompt = PROMPTS.GENERATE_CHAPTER_SUMMARIES(chapterData);

      try {
        const response = await model.withStructuredOutput(ChapterSummarySchema).invoke(prompt);
        return {
          chapter_id: response.chapter_id,
          chapter_name: response.chapter_name,
          summary: response.summary,
          topics: response.topics,
        };
      } catch (error) {
        console.error(`Error generating summary for chapter ${chapterData.chapter_id}:`, error);
        return {
          chapter_id: chapterData.chapter_id,
          chapter_name: chapterData.chapter_name,
          summary: 'Failed to generate chapter summary',
          topics: Array.from(chapterData.topics) as string[],
        };
      }
    })
  );

  return {
    ...state,
    chapterSummaries,
    TokenCount,
  };
}

async function generateBookSummary(
  state: typeof EmbedStateAnnotation.State,
  config?: RunnableConfig
): Promise<typeof EmbedStateAnnotation.Update> {
  const { chapterSummaries, sourceName } = state;
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'generate_book_summary'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  // Skip if there are no chapter summaries
  if (!chapterSummaries || chapterSummaries.length === 0) {
    return {
      ...state,
      bookSummary: {
        title: sourceName,
        summary: 'No content available for generating a summary',
        main_topics: [],
        chapter_highlights: [],
      },
    };
  }

  const chapterSummariesText = chapterSummaries
    .map(
      (chapter) => `Chapter: ${chapter.chapter_name}\nSummary: ${chapter.summary}\nTopics: ${chapter.topics.join(', ')}`
    )
    .join('\n\n');

  const prompt = PROMPTS.GENERATE_BOOK_SUMMARY(sourceName, chapterSummariesText);

  try {
    const response = await model.withStructuredOutput(BookSummarySchema).invoke(prompt);
    return {
      ...state,
      bookSummary: {
        title: response.title,
        summary: response.summary,
        main_topics: response.main_topics,
        chapter_highlights: response.chapter_highlights.map((highlight) => ({
          chapter_name: highlight.chapter_name,
          highlight: highlight.highlight,
        })),
      },
      TokenCount,
    };
  } catch (error) {
    console.error('Error generating book summary:', error);
    return {
      ...state,
      bookSummary: {
        title: sourceName,
        summary: 'Failed to generate book summary',
        main_topics: [],
        chapter_highlights: chapterSummaries.map((chapter) => ({
          chapter_name: chapter.chapter_name,
          highlight: chapter.summary.split('.')[0] + '.', // Use first sentence as highlight
        })),
      },
      TokenCount,
    };
  }
}

async function embedContent(
  state: typeof EmbedStateAnnotation.State,
  config?: RunnableConfig
): Promise<typeof EmbedStateAnnotation.Update> {
  const { chunkedDocs, tableOfContent, projectId, tenantId, summarizedChunks, chapterSummaries, bookSummary } = state;

  // Enhance the chunks with summary information
  const enhancedChunks = chunkedDocs.map((doc) => {
    const chunkSummary = summarizedChunks.find((summary) => summary.chunk_id === doc.metadata.chunk.chunk_id);

    if (chunkSummary) {
      return {
        ...doc,
        metadata: {
          ...doc.metadata,
          summary: {
            text: chunkSummary.summary,
            topic: chunkSummary.topic,
            subtopic: chunkSummary.subtopic,
          },
          pdf: null,
        },
      };
    }
    return doc;
  });

  const retriever = await makeRetriever(tenantId, projectId);
  const documentIds = await retriever.addDocuments(enhancedChunks, {
    namespace: projectId,
    ids: enhancedChunks.map((doc) => doc.metadata.chunk.chunk_id),
  });

  return {
    chunkedDocs: enhancedChunks,
    tableOfContent,
    documentIds,
    summarizedChunks,
    chapterSummaries,
    bookSummary,
  };
}

const builder = new StateGraph(EmbedStateAnnotation)
  .addNode('parse_pdf', parsePdf)
  .addNode('derive_contents', deriveTableOFContents)
  .addNode('generate_chunks', generateChunks)
  .addNode('summarize_chunks', summarizeChunks)
  .addNode('generate_chapter_summaries', generateChapterSummaries)
  .addNode('generate_book_summary', generateBookSummary)
  .addNode('embed_content', embedContent)
  .addEdge(START, 'parse_pdf')
  .addEdge('parse_pdf', 'derive_contents')
  .addEdge('derive_contents', 'generate_chunks')
  .addEdge('generate_chunks', 'summarize_chunks')
  .addEdge('summarize_chunks', 'generate_chapter_summaries')
  .addEdge('generate_chapter_summaries', 'generate_book_summary')
  .addEdge('generate_book_summary', 'embed_content')
  .addEdge('embed_content', END);

export const graph = builder.compile().withConfig({ runName: 'EmbedGraph' });
