import { Module } from '@nestjs/common';
import { ContentAlbumService } from './content-album.service';
import { ContentAlbumController } from './content-album.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { ContentAlbumSchema } from './schemas/content-album.schema';
import { ContentAlbumModel } from './schemas/content-album.schema';
import { IndSourceModel } from '../embed/schemas/ind-source.schema';
import { IndSourceSchema } from '../embed/schemas/ind-source.schema';
import { LectureModule } from '../lecture/lecture.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ContentAlbumProcessorService } from './content-album-processor.service';
import { SourceModel, SourceSchema } from '../embed/schemas/source.schema';

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    MongooseModule.forFeature([
      { name: ContentAlbumModel.name, schema: ContentAlbumSchema },
      { name: SourceModel.name, schema: SourceSchema },
      { name: IndSourceModel.name, schema: IndSourceSchema },
    ]),
    LectureModule,
  ],
  controllers: [ContentAlbumController],
  providers: [ContentAlbumService, ContentAlbumProcessorService],
  exports: [ContentAlbumService],
})
export class ContentAlbumModule {}
