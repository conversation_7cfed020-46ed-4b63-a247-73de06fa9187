# Academic Doubt Clearing Chatbot API

This API provides endpoints for interacting with the doubt clearing chatbot. The chatbot uses project-specific and tenant-specific document repositories to answer student questions. All endpoints are protected by authentication and organized by conversation threads with strict ownership validation.

## Authentication

All chat endpoints require authentication. The API uses JWT authentication with the AuthGuard middleware. Include your JWT token in the Authorization header of all requests:

```
Authorization: Bearer <your_jwt_token>
```

The JWT token contains user information including:

- `UserID`: Unique identifier for the user
- `TenantID`: Tenant identifier for the user

## Security Model

The API implements a robust security model with the following features:

1. **User Authentication**: All endpoints require a valid JWT token
2. **Thread Ownership**: Users can only access threads they own
3. **Thread Isolation**: Chat histories are isolated by thread ID
4. **Tenant Scoping**: All data is scoped to the user's tenant
5. **Project Validation**: Operations verify that projects exist and users have access
6. **Comprehensive Validation**: Input validation at both controller and service layers

## Data Persistence

All chat and thread data is stored in MongoDB, with the following collections:

1. **threads**: Stores conversation threads with proper user, tenant, and project scoping
2. **chat_messages**: Stores all messages with references to their respective threads

This ensures data persistence across server restarts and enables scaling of the application.

## API Endpoints

### Send Message

Sends a message to the chatbot and receives a complete response.

**URL**: `/chat/send`

**Method**: `POST`

**Request Body**:

```json
{
  "message": "What is Newton's third law?",
  "ProjectID": "physics101",
  "ThreadID": "thread123" // Optional - will create a new thread if not provided
}
```

**Response**:

```json
{
  "message": "Newton's third law states that for every action, there is an equal and opposite reaction. This means that when one object exerts a force on another object, the second object exerts an equal force in the opposite direction on the first object.",
  "logic": "This question is about a physics concept that's part of the syllabus.",
  "ThreadID": "thread123",
  "isComplete": true
}
```

**Security Notes**:

- If no ThreadID is provided, a new thread is created
- If ThreadID is provided, ownership is verified
- The user must have access to the specified project
- The thread must belong to the specified project

### Stream Message

Sends a message to the chatbot and receives the response as a real-time stream of text chunks.

**URL**: `/chat/stream`

**Method**: `POST`

**Request Body**:

```json
{
  "message": "What is Newton's third law?",
  "ProjectID": "physics101",
  "ThreadID": "thread123" // Optional - will create a new thread if not provided
}
```

**Response**: Server-Sent Events (SSE) stream with the following format for each chunk:

```
data: {"ThreadID":"thread123","message":"","isComplete":false,"chunkIndex":0}

data: {"message":"Newton's","isComplete":false,"chunkIndex":1,"ThreadID":"thread123"}

data: {"message":" third","isComplete":false,"chunkIndex":2,"ThreadID":"thread123"}

data: {"message":" law","isComplete":false,"chunkIndex":3,"ThreadID":"thread123"}

// ... more chunks ...

data: {"message":"","logic":"This question is about a physics concept that's part of the syllabus.","isComplete":true,"chunkIndex":42,"ThreadID":"thread123"}
```

**Security Notes**:

- Same ownership validation as the send message endpoint
- Error messages are formatted as SSE events
- Stream is closed immediately if validation fails

### Get Chat History

Retrieves the conversation history for a specific thread.

**URL**: `/chat/history/:ThreadID`

**Method**: `GET`

**Response**:

```json
{
  "messages": [
    {
      "role": "user",
      "content": "What is Newton's third law?",
      "timestamp": "2023-07-10T15:23:45.123Z"
    },
    {
      "role": "assistant",
      "content": "Newton's third law states that for every action, there is an equal and opposite reaction. This means that when one object exerts a force on another object, the second object exerts an equal force in the opposite direction on the first object.",
      "timestamp": "2023-07-10T15:23:48.456Z"
    }
  ],
  "ThreadID": "thread123",
  "TenantID": "tenant123",
  "ProjectID": "physics101",
  "UserID": "user456",
  "title": "Physics Discussion"
}
```

**Security Notes**:

- Only returns history for threads owned by the authenticated user
- Returns 401 Unauthorized if the thread belongs to another user
- Returns 404 Not Found if the thread doesn't exist

### Clear Chat History

Clears the conversation history for a specific thread.

**URL**: `/chat/history/:ThreadID`

**Method**: `DELETE`

**Response**:

```json
{
  "success": true
}
```

**Security Notes**:

- Only clears history for threads owned by the authenticated user
- Returns 401 Unauthorized if the thread belongs to another user
- Returns 404 Not Found if the thread doesn't exist

### Thread Management

#### List Threads

Retrieves all threads for the authenticated user.

**URL**: `/chat/threads`

**Method**: `GET`

**Query Parameters**:

- `ProjectID`: (Optional) Filter threads by project ID

**Response**:

```json
{
  "threads": [
    {
      "ThreadID": "thread123",
      "ProjectID": "physics101",
      "title": "Physics Discussion",
      "UserID": "user456",
      "TenantID": "tenant123",
      "createdAt": "2023-07-10T15:20:00.000Z",
      "updatedAt": "2023-07-10T15:25:00.000Z",
      "messageCount": 2
    },
    {
      "ThreadID": "thread124",
      "ProjectID": "chemistry101",
      "title": "Chemistry Questions",
      "UserID": "user456",
      "TenantID": "tenant123",
      "createdAt": "2023-07-11T10:00:00.000Z",
      "updatedAt": "2023-07-11T10:15:00.000Z",
      "messageCount": 4
    }
  ],
  "count": 2
}
```

**Security Notes**:

- Only returns threads owned by the authenticated user
- Automatically filters by UserID
- Can be further filtered by ProjectID

#### Create Thread

Creates a new conversation thread.

**URL**: `/chat/threads`

**Method**: `POST`

**Request Body**:

```json
{
  "ProjectID": "physics101",
  "title": "Physics Discussion" // Optional - will use timestamp if not provided
}
```

**Response**:

```json
{
  "ThreadID": "thread123",
  "ProjectID": "physics101",
  "title": "Physics Discussion",
  "UserID": "user456",
  "TenantID": "tenant123",
  "createdAt": "2023-07-10T15:20:00.000Z",
  "updatedAt": "2023-07-10T15:20:00.000Z",
  "messageCount": 0
}
```

**Security Notes**:

- Thread is automatically associated with the authenticated user
- Thread is scoped to the user's tenant
- User must have access to the specified project

#### Get Thread

Retrieves a specific thread by ID.

**URL**: `/chat/threads/:ThreadID`

**Method**: `GET`

**Response**:

```json
{
  "ThreadID": "thread123",
  "ProjectID": "physics101",
  "title": "Physics Discussion",
  "UserID": "user456",
  "TenantID": "tenant123",
  "createdAt": "2023-07-10T15:20:00.000Z",
  "updatedAt": "2023-07-10T15:25:00.000Z",
  "messageCount": 2
}
```

**Security Notes**:

- Only returns threads owned by the authenticated user
- Returns 401 Unauthorized if the thread belongs to another user
- Returns 404 Not Found if the thread doesn't exist

#### Update Thread Title

Updates the title of a thread.

**URL**: `/chat/threads/:ThreadID`

**Method**: `PATCH`

**Request Body**:

```json
{
  "title": "Newton's Laws Discussion"
}
```

**Response**:

```json
{
  "ThreadID": "thread123",
  "ProjectID": "physics101",
  "title": "Newton's Laws Discussion",
  "UserID": "user456",
  "TenantID": "tenant123",
  "createdAt": "2023-07-10T15:20:00.000Z",
  "updatedAt": "2023-07-10T15:30:00.000Z",
  "messageCount": 2
}
```

**Security Notes**:

- Only updates threads owned by the authenticated user
- Returns 401 Unauthorized if the thread belongs to another user
- Returns 404 Not Found if the thread doesn't exist

#### Delete Thread

Deletes a thread and its conversation history.

**URL**: `/chat/threads/:ThreadID`

**Method**: `DELETE`

**Response**:

```json
{
  "success": true
}
```

**Security Notes**:

- Only deletes threads owned by the authenticated user
- Returns 401 Unauthorized if the thread belongs to another user
- Returns 404 Not Found if the thread doesn't exist
- Permanently removes all associated conversation history

## Client-side Example (JavaScript)

```javascript
// Function to start a chat with a new or existing thread
async function startChat(message, ProjectID, ThreadID = null) {
  // If no ThreadID, create a new thread first
  if (!ThreadID) {
    const response = await fetch('/chat/threads', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + jwtToken,
      },
      body: JSON.stringify({
        ProjectID,
        title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
      }),
    });

    const thread = await response.json();
    ThreadID = thread.ThreadID;
  }

  // Now stream the message in this thread
  const eventSource = new EventSource(`/chat/stream`, {
    headers: {
      Authorization: 'Bearer ' + jwtToken,
    },
  });

  // Send the message as a POST request to the stream endpoint
  fetch('/chat/stream', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: 'Bearer ' + jwtToken,
    },
    body: JSON.stringify({
      message,
      ProjectID,
      ThreadID,
    }),
  });

  let fullMessage = '';

  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);

    if (!data.isComplete) {
      // Append the new chunk to the full message
      fullMessage += data.message;

      // Update the UI with the current state of the message
      document.getElementById('response').innerText = fullMessage;
    } else {
      // The stream is complete
      console.log('Logic:', data.logic);
      console.log('Thread ID:', data.ThreadID);
      eventSource.close();
    }
  };

  eventSource.onerror = (error) => {
    console.error('Error:', error);
    eventSource.close();
  };
}
```

## Error Handling

The API implements comprehensive error handling:

1. **Authentication Errors**: 401 Unauthorized for invalid or missing tokens
2. **Authorization Errors**: 401 Unauthorized for attempting to access another user's data
3. **Not Found Errors**: 404 Not Found for non-existent resources
4. **Validation Errors**: 400 Bad Request for invalid input data
5. **Server Errors**: 500 Internal Server Error for unexpected issues

All error responses include descriptive messages to aid debugging.

## Data Security

1. **Thread Isolation**: Each thread's conversation history is isolated and accessible only to its owner
2. **Tenant Isolation**: All data is scoped to specific tenants
3. **User Validation**: All requests verify the authenticated user's identity
4. **Project Access Control**: Users can only access projects they have permissions for
5. **Input Validation**: All inputs are validated to prevent injection attacks

## MongoDB Schema Design

### Thread Collection Schema

```typescript
@Schema({ collection: 'threads', timestamps: true, versionKey: false })
export class Thread {
  @Prop({
    required: true,
    default: () => GenerateID.Generate16Hex('thread_'),
  })
  ThreadID: string;

  @Prop({ required: true })
  ProjectID: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  UserID: string;

  @Prop({ required: true })
  TenantID: string;

  @Prop({ default: 0 })
  messageCount: number;

  @Prop()
  createdAt: Date;

  @Prop()
  updatedAt: Date;
}
```

### Chat Message Collection Schema

```typescript
@Schema({ collection: 'chat_messages', timestamps: true, versionKey: false })
export class ChatMessage {
  @Prop({
    required: true,
    default: () => GenerateID.Generate16Hex('msg_'),
  })
  MessageId: string;

  @Prop({ required: true })
  ThreadID: string;

  @Prop({ required: true, enum: ['user', 'assistant', 'system'] })
  role: 'user' | 'assistant' | 'system';

  @Prop({ required: true })
  content: string;

  @Prop({ required: true })
  timestamp: Date;

  @Prop({ required: true })
  UserID: string;

  @Prop({ required: true })
  TenantID: string;

  @Prop({ required: true })
  ProjectID: string;
}
```

## Thread-based Conversation Management

The thread system works by:

1. Creating a new thread or selecting an existing thread for a conversation
2. Associating messages with a specific ThreadID
3. Retrieving the conversation history for that thread when processing new messages
4. Maintaining user ownership of threads to ensure data separation and privacy
5. Organizing threads by projects to keep subject matter separate
6. Implementing strict ownership validation for all operations
