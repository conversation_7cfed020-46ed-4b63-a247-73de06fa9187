import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

import { Req } from '@nestjs/common';
import { Request as ExpressRequest } from 'express';
import { ConfigService } from '@nestjs/config';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly configService: ConfigService
  ) {}

  @Get()
  async getHello(@Req() req: ExpressRequest): Promise<any> {
    await this.appService.getHello();
    const date = new Date();
    // timezone of the date
    const timeZone = Intl.DateTimeFormat('en-US', { timeZoneName: 'long' }).resolvedOptions().timeZone;
    const timeZoneOffset = -date.getTimezoneOffset() / 60;
    return {
      message: 'Academic LM API',
      timestamp: new Date(),
      timeZone,
      timeZoneOffsetInHours: timeZoneOffset,
    };
  }

  @Get('agentic-ai')
  async getAgenticAi(@Req() req: ExpressRequest): Promise<any> {
    const resp = await fetch(this.configService.get('AGENTIC_AI_URL') + '/health');
    const data = await resp.json();
    return data;
  }
}
