# Use consistent base images to avoid GLIBC mismatch
FROM public.ecr.aws/docker/library/python:3.11-slim-bullseye AS builder

# Set environment variables
ENV PYTHONFAULTHANDLER=1 \
    PYTHONHASHSEED=random \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=100 \
    POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_CREATE=true \
    POETRY_VIRTUALENVS_IN_PROJECT=true \
    POETRY_VERSION=1.8.2

# Install system dependencies and Poetry in a single layer
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    libmagic-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/* \
    && pip install poetry==$POETRY_VERSION

# Set working directory
WORKDIR /app

# Copy only dependency files first to leverage Docker cache
COPY pyproject.toml poetry.lock* ./

# Install dependencies in virtual environment
RUN poetry install --only=main --no-root && \
    # Clean up to reduce size
    find .venv -name "*.pyc" -delete && \
    find .venv -name "__pycache__" -type d -exec rm -rf {} + || true

# Second stage: runtime - USE SAME BASE IMAGE
FROM public.ecr.aws/docker/library/python:3.11-slim-bullseye AS runtime

# Set environment variables
ENV PYTHONFAULTHANDLER=1 \
    PYTHONHASHSEED=random \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PORT=8000 \
    ENV=production \
    PATH="/app/.venv/bin:$PATH" \
    PYTHONPATH="/app:/app/app"

# Install only required runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libmagic1 \
    poppler-utils \
    tesseract-ocr \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy virtual environment from builder
COPY --from=builder /app/.venv /app/.venv

# Copy ALL application code (including agents directory)
COPY . .

# Remove unnecessary files to keep image clean
RUN rm -rf .git .pytest_cache __pycache__ \
    && find . -name "*.pyc" -delete \
    && find . -name "__pycache__" -type d -exec rm -rf {} + || true

# Expose the port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]