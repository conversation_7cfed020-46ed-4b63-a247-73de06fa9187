import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ collection: 'chunks', timestamps: true, versionKey: false })
export class Chunk {
  @Prop({ required: true })
  ChunkID: string;

  @Prop({ required: true })
  SourceID: string;

  @Prop({ required: true })
  ChapterID: string;

  @Prop({ required: true })
  ChapterName: string;

  @Prop({ type: Number, required: true })
  ChunkNumber: number;

  @Prop({ type: Number, required: true })
  PageNumber: number;

  @Prop({ type: Number, required: true })
  DocumentPageNumber: number;

  @Prop({ type: String })
  Summary?: string;

  @Prop({ type: String })
  Topic?: string;

  @Prop({ type: String })
  SubTopic?: string;

  @Prop({ type: String, required: true })
  Content: string;

  @Prop({ type: String, required: true })
  TenantID: string;

  @Prop({ type: String, required: true })
  ProjectID: string;
}

export const ChunkSchema = SchemaFactory.createForClass(Chunk);

// Add indexes for better query performance
ChunkSchema.index({ SourceID: 1 });
ChunkSchema.index({ ChunkID: 1 }, { unique: true });
ChunkSchema.index({ ChapterID: 1 });
ChunkSchema.index({ TenantID: 1, ProjectID: 1 });
ChunkSchema.index({ Topic: 1 });
