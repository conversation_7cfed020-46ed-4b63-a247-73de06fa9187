import { PartialType } from '@nestjs/mapped-types';
import { Transform } from 'class-transformer';
import { MinLength } from 'class-validator';

export class CreateTierGroupDto {
  Name: string;
  Description?: string;
  PlanIDs: string[];
  TenantID: string;
  ListAsPublic?: boolean;
}

export class UpdateTierGroupDto extends PartialType(CreateTierGroupDto) {
  TierGroupID: string;
  TenantID: string;
}

export class ListTierGroupsDto {
  @Transform(({ value }) => value.split(','))
  TierGroupIDs?: string[];
  @Transform(({ value }) => value.split(','))
  PlanIDs?: string[];
  TenantID?: string;
  ListAsPublic?: boolean;
}

export class ListTierGroupsParamsDto {
  @Transform(({ value }) => value.split(','))
  TierGroupIDs?: string[];
  @Transform(({ value }) => value.split(','))
  PlanIDs?: string[];
  TenantID?: string;
  ListAsPublic?: boolean;
  @MinLength(3, { message: 'Invalid SubDomain' })
  SubDomain: string;
}
