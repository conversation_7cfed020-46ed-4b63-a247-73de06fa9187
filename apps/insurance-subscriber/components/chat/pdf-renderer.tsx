import { Button } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>it<PERSON>,
  SheetDescription,
  SheetTrigger,
} from "@workspace/ui/components/sheet";
import { cn } from "@workspace/ui/lib/utils";
import { StickyNote } from "lucide-react";
import { useEffect, useState } from "react";

interface PDFViewerProps {
  url: string;
  pageNumber?: number;
  documentPageNumber?: number;
  scale?: number;
}

export default function PDFViewer({
  url,
  pageNumber = 1,
  scale = 1.0,
}: PDFViewerProps) {
  const [pdfUrl, setPdfUrl] = useState("");

  useEffect(() => {
    if (url) {
      setPdfUrl(
        `${url}#toolbar=0&navpanes=0&scrollbar=0&view=FitH&zoom=${scale * 100}&page=${pageNumber}`,
      );
    }
  }, [url, scale, pageNumber]);

  const iframeStyle = {
    width: "100%",
    height: "100%",
    border: "none",
    borderRadius: "8px",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
  };

  const containerStyle = {
    width: "100%",
    height: "800px",
    padding: "16px",
    backgroundColor: "#f5f5f5",
    borderRadius: "12px",
    position: "relative" as const,
  };

  return (
    <div style={containerStyle}>
      <object
        key={`pdf-object-${pageNumber}`}
        data={pdfUrl}
        type="application/pdf"
        style={iframeStyle}
      >
        <iframe
          key={`pdf-iframe-${pageNumber}`}
          src={pdfUrl}
          style={iframeStyle}
          title="PDF Viewer"
          loading="lazy"
        />
      </object>
    </div>
  );
}

interface TableOfCitationsProps {
  tableOfCitations: {
    url: string;
    title: string;
    page: number;
    document_page_number: number;
  }[];
}

export function PDFViewerSheet({
  url,
  pageNumber = 1,
  documentPageNumber = 1,
  scale = 1.0,
  title = "Reference",
  description,
  isOpen = false,
  onClose = () => {},
  tableOfCitations = [],
  setPDFViewerContext,
}: PDFViewerProps & {
  title: string;
  description?: string;
  isOpen: boolean;
  onClose: () => void;
  tableOfCitations: {
    url: string;
    title: string;
    page: number;
    document_page_number: number;
  }[];
  setPDFViewerContext: React.Dispatch<
    React.SetStateAction<{
      url: string;
      title: string;
      page: number;
      document_page_number: number;
      isOpen: boolean;
    }>
  >;
}) {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="min-w-[600px]" side="right">
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          {description && <SheetDescription>{description}</SheetDescription>}
          <span className="text-xs text-muted-foreground">
            Current Page {pageNumber}
          </span>
        </SheetHeader>

        <div className="px-4 py-1">
          <PDFViewer url={url} pageNumber={documentPageNumber} scale={scale} />
        </div>

        <div className="px-4 py-1 overflow-y-auto">
          <div className="flex flex-wrap gap-2">
            {tableOfCitations
              .sort((a, b) => a.document_page_number - b.document_page_number)
              .map((citation) => (
                <div
                  key={citation.document_page_number + "_" + citation.title}
                  className={cn(
                    "flex items-center gap-2 text-xs text-muted-foreground bg-muted-foreground/10 rounded px-2 py-1 cursor-pointer",
                    citation.document_page_number === documentPageNumber &&
                      "bg-primary text-primary-foreground",
                  )}
                  onClick={() => {
                    setPDFViewerContext({
                      url: citation.url,
                      title: citation.title,
                      page: citation.page,
                      document_page_number: citation.document_page_number,
                      isOpen: true,
                    });
                  }}
                >
                  <span className="">{citation.title}</span>
                  <span className="">Page {citation.page}</span>
                </div>
              ))}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
