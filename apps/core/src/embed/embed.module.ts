import { Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { EmbedService } from './embed.service';
import { EmbedController } from './embed.controller';
import { EmbedProcessorService } from './embed-processor.service';
import { SourceModel, SourceSchema } from './schemas/source.schema';
import { Chunk, ChunkSchema } from './schemas/chunk.schema';
import { TokenCounterModule } from '../token-counter/token-counter.module';
import { IndChunkModel, IndChunkSchema } from './schemas/ind-chunk.schema';
import { IndSourceModel, IndSourceSchema } from './schemas/ind-source.schema';
import { PopQuizModule } from '../pop-quiz/pop-quiz.module';
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule.forRoot(),
    TokenCounterModule,
    PopQuizModule,
    MulterModule.register({
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB limit
      },
    }),
    MongooseModule.forFeature([
      { name: SourceModel.name, schema: SourceSchema },
      { name: Chunk.name, schema: ChunkSchema },
      { name: IndSourceModel.name, schema: IndSourceSchema },
      { name: IndChunkModel.name, schema: IndChunkSchema },
    ]),
  ],
  controllers: [EmbedController],
  providers: [EmbedService, EmbedProcessorService],
})
export class EmbedModule {}
