import { Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as argon2 from 'argon2';
import { Model } from 'mongoose';
import { MONGODB_URI } from '../config/env';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './schemas/user.model';
import { AppTypeEnum, TUserType } from './entities/user.entity';
import { UpdateUserAccessDto } from './dto/update-user-acess.dto';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  async HashPassword(password: string) {
    return await argon2.hash(password);
  }
  async ComparePassword(password: string, hash: string) {
    return await argon2.verify(hash, password);
  }

  async Create(createUserDto: CreateUserDto) {
    const passwordHash = await this.HashPassword(createUserDto.Password);
    const createdUser = new this.userModel({
      ...createUserDto,
      PasswordHash: passwordHash,
    });
    return await createdUser.save();
  }

  async Update(UserID: string, TenantID: string, updateUserDto: UpdateUserDto) {
    return await this.userModel.findOneAndUpdate(
      { UserID, TenantID },
      {
        $set: {
          ...updateUserDto,
          ...((updateUserDto?.Password || updateUserDto?.NewPassword) && {
            PasswordHash: await this.HashPassword(updateUserDto.Password),
          }),
        },
      },
      { new: true }
    );
  }

  async GetUserById(UserID: string, TenantID: string) {
    const cachedUser = await this.cacheManager.get<User>(`user:${UserID}`);
    if (cachedUser) {
      return cachedUser;
    }
    const user = await this.userModel
      .findOne({
        UserID,
        TenantID,
      })
      .lean();
    await this.cacheManager.set(`user:${UserID}`, user);
    return user;
  }

  async GetUserByEmail(email: string) {
    return await this.userModel.findOne({ Email: email }).lean();
  }

  async GetUserByTenantID(TenantID: string) {
    return await this.userModel.find({ TenantID }).lean();
  }

  async ListUsers(Params: { TenantID: string; UserType: TUserType; AppType: AppTypeEnum }) {
    return await this.userModel
      .find({ TenantID: Params.TenantID, UserType: Params.UserType, AppType: Params.AppType })
      .lean();
  }

  async UpdateUserAccess(updateUserAccessDto: UpdateUserAccessDto) {
    await this.cacheManager.del(`user:${updateUserAccessDto.UserID}`);
    return await this.userModel.findOneAndUpdate(
      { UserID: updateUserAccessDto.UserID, TenantID: updateUserAccessDto.TenantID },
      {
        $set: {
          Access: updateUserAccessDto,
        },
      },
      { new: true }
    );
  }
}
