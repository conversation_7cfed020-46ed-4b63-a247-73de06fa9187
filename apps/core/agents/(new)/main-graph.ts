import { RunnableConfig } from '@langchain/core/runnables';
import { Annotation, END, MessagesAnnotation, START, StateGraph } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';
import { researcherGraph } from './researcher-graph';
import { reduceDocs } from './shared';
import { Document } from '@langchain/core/documents';
import { PROMPTS } from './prompts';
import { calculateLLMInvocations, LLMOutput, LLMInvocation } from '../shared/utils';

const OutputSchema = z.object({
  response: z.string(),
  citations: z.array(
    z.object({
      id: z.string(),
      source_title: z.string(),
      source_id: z.string().nullable(),
      page_number: z.number(),
      document_page_number: z.number(),
      line_from: z.number().nullable(),
      line_to: z.number().nullable(),
    })
  ),
  output_language: z
    .enum(['us', 'gb', 'hi', 'mr', 'romanized-hi', 'romanized-mr', 'ja', 'es', 'fr', 'it', 'pt', 'cn', 'other'])
    .describe('The language of the output'),
});

export const InputStateAnnotation = Annotation.Root({
  ...MessagesAnnotation.spec,
  tenantId: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
  projectId: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
  Sources: Annotation<
    {
      SourceID: string;
      SourceName: string;
      Chapters: {
        ChapterID: string;
        ChapterNumber: number;
        ChapterName: string;
        Summary: string;
        Topics: {
          Topic: string;
          PageNumber: number;
          SubTopics?: string[];
        }[];
        PageNumber: {
          From: number;
          To: number;
        };
      }[];
      BookSummary: {
        Title: string;
        Summary: string;
        MainTopics: string[];
        ChapterHighlights: {
          ChapterName: string;
          Highlight: string;
        }[];
      };
      Language: string;
    }[]
  >(),
  Language: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
});

const AgentStateAnnotation = Annotation.Root({
  ...InputStateAnnotation.spec,
  summarizedQuestion: Annotation<string>(),
  router: Annotation<{
    type: 'more-info' | 'general-conversation' | 'academic' | 'syllabus-enquiry';
    logic: string;
  }>(),
  initialDocuments: Annotation<Document[]>({
    default: () => [],
    reducer: reduceDocs,
  }),
  initialQueries: Annotation<string[]>(),
  refinedQueries: Annotation<string[]>(),
  refinedDocuments: Annotation<Document[]>({
    default: () => [],
    reducer: reduceDocs,
  }),
  TokenCount: Annotation<{
    PromptTokens: number;
    CompletionTokens: number;
    TotalTokens: number;
    LLMInvocations: LLMInvocation[];
  }>({
    default: () => ({
      PromptTokens: 0,
      CompletionTokens: 0,
      TotalTokens: 0,
      LLMInvocations: [],
    }),
    reducer: (acc, curr) => ({
      PromptTokens: acc.PromptTokens + curr.PromptTokens,
      CompletionTokens: acc.CompletionTokens + curr.CompletionTokens,
      TotalTokens: acc.TotalTokens + curr.TotalTokens,
      LLMInvocations: [...acc.LLMInvocations, ...curr.LLMInvocations],
    }),
  }),
});

async function analyzeAndRouteQuery(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'analyze_and_route_query'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const messages = [
    {
      role: 'system',
      content: PROMPTS.ANALYZE_AND_ROUTE_QUERY,
    },
    ...state.messages,
  ];

  const responseSchema = z
    .object({
      type: z.enum(['more-info', 'general-conversation', 'academic', 'syllabus-enquiry']),
      logic: z.string(),
    })
    .describe('Classify users query');

  const response = await model.withStructuredOutput(responseSchema).invoke(messages);

  return {
    router: {
      type: response.type,
      logic: response.logic,
    },
    TokenCount,
  };
}

async function askForMoreInfo(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.2,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'ask_for_more_info'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const systemPrompt = PROMPTS.MORE_INFO_PROMPT.replace('{logic}', state.router.logic).replace(
    '{booksSummary}',
    JSON.stringify(state.Sources?.map((s) => ({ BookSummary: s.BookSummary.Summary })))
  );
  const messages = [
    {
      role: 'system',
      content: systemPrompt,
    },
    ...state.messages,
  ];

  const response = await model.withStructuredOutput(OutputSchema).invoke(messages);

  return {
    messages: [JSON.stringify(response)],
    TokenCount,
  };
}

async function respondToGeneralConversation(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.7,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'respond_to_general_conversation'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const systemPrompt = PROMPTS.GENERAL_CONVERSATION_PROMPT.replace('{logic}', state.router.logic).replace(
    '{booksSummary}',
    JSON.stringify(state.Sources?.map((s) => ({ BookSummary: s.BookSummary.Summary })))
  );
  const messages = [
    {
      role: 'system',
      content: systemPrompt,
    },
    ...state.messages,
  ];

  const response = await model.withStructuredOutput(OutputSchema).invoke(messages);

  return {
    messages: [JSON.stringify(response)],
    TokenCount,
  };
}

async function conductResearch(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const researcherGraphConfig = {
    ...config.configurable,
    tenantId: state.tenantId,
    projectId: state.projectId,
    messages: state.messages,
    sources: state.Sources,
    language: state.Language,
  };
  const researcherGraphResult = await researcherGraph.invoke(researcherGraphConfig);
  return {
    initialDocuments: researcherGraphResult.initialDocuments,
    initialQueries: researcherGraphResult.initialQueries,
    refinedDocuments: researcherGraphResult.refinedDocuments,
    refinedQueries: researcherGraphResult.refinedQueries,
    TokenCount: researcherGraphResult.TokenCount,
  };
}

async function analyzeAndConstructAnswer(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'analyze_and_construct_answer'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const uniqueIds = new Set();
  const documents = [...state.initialDocuments, ...state.refinedDocuments]
    .map((d) => {
      if (uniqueIds.has(d.id)) {
        return null;
      }
      uniqueIds.add(d.id);

      return {
        content: d.pageContent,
        metadata: {
          documentPageNumber: d.metadata?.[`chunk.document_page_number`] || 'Unknown',
          pageNumber: d.metadata?.['chunk.page_number'] || 'Unknown',
          sourceTitle: d.metadata?.['source.source_name'] || 'Unknown',
          fromLine: d.metadata['loc.lines.from'] || null,
          toLine: d.metadata['loc.lines.to'] || null,
          sourceId: d.metadata?.['source.source_id'] || null,
          summarizedText: d.metadata['summary.text'],
          chunkTopic: d.metadata['summary.topic'],
          chapter: d.metadata['chapter.chapter_name'],
          chunkId: d.metadata['chunk.chunk_id'],
        },
      };
    })
    .filter((d) => d !== null);

  const { sourceIds, chapterIds } = documents.reduce(
    (acc, d) => {
      if (d.metadata?.['source.source_id']) {
        acc.sourceIds.add(d.metadata['source.source_id']);
      }
      if (d.metadata?.['chapter.chapter_id']) {
        acc.chapterIds.add(d.metadata['chapter.chapter_id']);
      }
      return acc;
    },
    { sourceIds: new Set(), chapterIds: new Set() }
  );

  const sources = state.Sources?.filter((s) => sourceIds.has(s.SourceID)).map((s) => ({
    SourceID: s.SourceID,
    SourceName: s.SourceName,
    Chapters: s.Chapters.map((c) => ({
      ChapterID: c.ChapterID,
      ChapterNumber: c.ChapterNumber,
      ChapterName: c.ChapterName,
      Summary: chapterIds.has(c.ChapterID) ? c.Summary : '',
      Topics: chapterIds.has(c.ChapterID)
        ? c.Topics
        : c.Topics.map((topic) => ({ Topic: topic.Topic, PageNumber: topic.PageNumber })),
      PageNumber: {
        From: c.PageNumber?.From,
        To: c.PageNumber?.To,
      },
    })),
    BookSummary: {
      Title: s.BookSummary.Title,
      Summary: s.BookSummary.Summary,
      MainTopics: s.BookSummary.MainTopics,
      ChapterHighlights: s.BookSummary.ChapterHighlights,
    },
  }));

  const systemPrompt = PROMPTS.ANALYZE_AND_CONSTRUCT_ANSWER.replace('{documents}', JSON.stringify(documents)).replace(
    '{chapters}',
    JSON.stringify(sources)
  );

  const messages = [
    {
      role: 'system',
      content: systemPrompt,
    },
    ...state.messages,
  ];

  const response = await model.withStructuredOutput(OutputSchema).invoke(messages);

  return {
    messages: [JSON.stringify(response)],
    TokenCount,
  };
}

async function respondToSyllabusEnquiry(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'respond_to_syllabus_enquiry'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const response = await model.withStructuredOutput(OutputSchema).invoke([
    {
      role: 'system',
      content: PROMPTS.SYLLABUS_ENQUIRY_PROMPT.replace('{sources}', JSON.stringify(state.Sources)),
    },
    {
      role: 'user',
      content: state.messages[state.messages.length - 1].content,
    },
  ]);

  return {
    messages: [JSON.stringify(response)],
    TokenCount,
  };
}

function queryRouter(
  state: typeof AgentStateAnnotation.State
): 'askForMoreInfo' | 'respondToGeneralConversation' | 'conductResearch' | 'respondToSyllabusEnquiry' {
  switch (state.router.type) {
    case 'more-info':
      return 'askForMoreInfo';
    case 'general-conversation':
      return 'respondToGeneralConversation';
    case 'academic':
      return 'conductResearch';
    case 'syllabus-enquiry':
      return 'respondToSyllabusEnquiry';
    default:
      throw new Error(`Unknown router type: ${state.router.type}`);
  }
}

const builder = new StateGraph({
  stateSchema: AgentStateAnnotation,
  input: InputStateAnnotation,
})
  .addNode('analyzeAndRouteQuery', analyzeAndRouteQuery)
  .addNode('askForMoreInfo', askForMoreInfo)
  .addNode('respondToGeneralConversation', respondToGeneralConversation)
  .addNode('respondToSyllabusEnquiry', respondToSyllabusEnquiry)
  .addNode('conductResearch', conductResearch, { subgraphs: [researcherGraph] })
  .addNode('analyzeAndConstructAnswer', analyzeAndConstructAnswer)
  .addEdge(START, 'analyzeAndRouteQuery')
  .addConditionalEdges('analyzeAndRouteQuery', queryRouter, [
    'askForMoreInfo',
    'respondToGeneralConversation',
    'respondToSyllabusEnquiry',
    'conductResearch',
  ])
  .addEdge('conductResearch', 'analyzeAndConstructAnswer')
  .addEdge('askForMoreInfo', END)
  .addEdge('respondToGeneralConversation', END)
  .addEdge('respondToSyllabusEnquiry', END)
  .addEdge('analyzeAndConstructAnswer', END);

export const graph = builder.compile();

export async function streamAcademicGraph(input: {
  messages: { role: string; content: string }[];
  Sources: {
    SourceID: string;
    SourceName: string;
    Chapters: {
      ChapterID: string;
      ChapterNumber: number;
      ChapterName: string;
      Summary: string;
      Topics: {
        Topic: string;
        PageNumber: number;
        SubTopics?: string[];
      }[];
      PageNumber: {
        From: number;
        To: number;
      };
    }[];
    BookSummary: {
      Title: string;
      Summary: string;
      MainTopics: string[];
      ChapterHighlights: {
        ChapterName: string;
        Highlight: string;
      }[];
    };
    Language: string;
  }[];
  tenantId: string;
  projectId: string;
  Language: string;
}) {
  let TokenCount = {
    PromptTokens: 0,
    CompletionTokens: 0,
    TotalTokens: 0,
    LLMInvocations: [],
  };
  const stream = await graph.stream(
    {
      tenantId: input.tenantId,
      projectId: input.projectId,
      messages: input.messages,
      Sources: input.Sources,
      Language: input.Language,
    },
    {
      streamMode: 'messages',
      callbacks: [
        {
          handleLLMEnd(output: LLMOutput) {
            const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
              output,
              'message_stream'
            );
            const tokenCount = {
              PromptTokens,
              CompletionTokens,
              TotalTokens,
              LLMInvocations,
            };
            TokenCount = {
              PromptTokens: TokenCount.PromptTokens + tokenCount.PromptTokens,
              CompletionTokens: TokenCount.CompletionTokens + tokenCount.CompletionTokens,
              TotalTokens: TokenCount.TotalTokens + tokenCount.TotalTokens,
              LLMInvocations: [...TokenCount.LLMInvocations, ...tokenCount.LLMInvocations],
            };
          },
        },
      ],
    }
  );

  return new ReadableStream({
    async start(controller) {
      for await (const data of stream) {
        if (data[1].langgraph_node === 'analyzeAndRouteQuery') controller.enqueue('$$%%##analyzeAndRouteQuery##%%$$');
        else if (data[1].langgraph_node === 'conductResearch') controller.enqueue('$$%%##conductResearch##%%$$');
        else if (
          data[1].langgraph_node === 'askForMoreInfo' ||
          data[1].langgraph_node === 'respondToGeneralConversation' ||
          data[1].langgraph_node === 'analyzeAndConstructAnswer' ||
          data[1].langgraph_node === 'respondToSyllabusEnquiry'
        ) {
          if (data[1].langgraph_node === 'respondToGeneralConversation')
            controller.enqueue('$$%%##respondToGeneralConversation##%%$$');
          controller.enqueue(data[0].content);
        }
      }
      controller.enqueue('$$%%##TokenCount##%%$$');
      controller.enqueue(JSON.stringify(TokenCount));
      controller.close();
    },
  });
}
