import { PartialType } from '@nestjs/mapped-types';
import { Mixin } from 'ts-mixer';

export type TGender = 'Male' | 'Female' | 'Other';
export type TUserType = 'Admin' | 'Subscriber' | 'Employee';

class Resource {
  ResourceID: string;
  Properties: Record<string, any>;
}

export class Access {
  Resources: Resource[];
}

export enum AppTypeEnum {
  Academic = 'Academic',
  Industry = 'Industry',
}

export class Integration {}

export class RequiredAttributes {
  Email: string;
  TenantID: string;
}

export class OptionalAttributes {
  Name: string;
  PhoneNumber: string;
  Gender: TGender;
  DateOfBirth: string;
  Address: string;
  ProfilePicture: string;
  ZoneInfo: string;
  Locale: string;
  GivenName: string;
  FamilyName: string;
  MiddleName: string;
  Website: string;
  UserID: string;
  UserType: TUserType;
  Integrations: Integration[];
  AppType: AppTypeEnum;
  Access: Access;
}

export class OAuth {
  Google: {
    Connected: boolean;
    UserID: string;
  };
}

export class TUser extends Mixin(RequiredAttributes, PartialType(OptionalAttributes), PartialType(OAuth)) {}
