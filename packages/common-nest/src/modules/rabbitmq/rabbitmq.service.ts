import amqp from 'amqplib';
import { RabbitMQ } from '@nextcampus/common';

class channelProfile extends RabbitMQ {
  private _client: amqp.Connection | undefined;
  private AMQP_URI: string;

  constructor(AMQP_URI: string) {
    super();
    this.AMQP_URI = AMQP_URI;
  }

  async link() {
    this._client = await this.connect(this.AMQP_URI);
    return this._client;
  }
}

export type TChannelProfile = channelProfile;
export type TChannel = amqp.Channel;

export default channelProfile;
