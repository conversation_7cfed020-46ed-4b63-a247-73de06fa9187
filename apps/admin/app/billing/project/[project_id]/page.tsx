"use client";
import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { TOKEN_COUNTER } from "@/apis/token-counter";
import { formatNumber } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import type { TGetProjectTokenUsageResponse } from "@/apis/token-counter";

export default function ProjectBillingPage() {
  const { project_id } = useParams();
  const router = useRouter();
  const [data, setData] = useState<TGetProjectTokenUsageResponse | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const { data: responseData } = await TOKEN_COUNTER.GetProjectTokenUsage({
        ProjectID: project_id as string,
      });
      if (responseData)
        setData(responseData as unknown as TGetProjectTokenUsageResponse);
      setLoading(false);
    };
    if (project_id) fetchData();
  }, [project_id]);

  if (loading || !data) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const { Project, TotalCounts, Users } = data;
  const usersArray = Array.isArray(Users) ? Users : [];

  return (
    <div className="w-full h-full bg-muted/50 min-h-screen">
      <div className="flex flex-col gap-6 p-6 md:p-8">
        {/* Breadcrumbs */}
        <nav className="text-sm mb-2" aria-label="Breadcrumb">
          <ol className="flex items-center gap-2 text-muted-foreground">
            <li>
              <button
                onClick={() => router.push("/")}
                className="hover:underline"
              >
                Home
              </button>
            </li>
            <li>/</li>
            <li>
              <button
                onClick={() => router.push("/billing")}
                className="hover:underline"
              >
                Billing
              </button>
            </li>
            <li>/</li>
            <li className="font-semibold text-foreground">Project</li>
          </ol>
        </nav>

        {/* Project Overview */}
        <Card className="shadow-md rounded-xl">
          <CardHeader>
            <CardTitle className="text-xl font-semibold">
              {Project.Name}
            </CardTitle>
            <p className="text-muted-foreground mt-1 text-sm">
              {Project.Description}
            </p>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-6 mt-2">
              <div>
                <span className="text-xs text-muted-foreground">
                  Project ID
                </span>
                <div className="font-mono text-sm">{Project.ProjectID}</div>
              </div>
              <div>
                <span className="text-xs text-muted-foreground">Tenant ID</span>
                <div className="font-mono text-sm">{Project.TenantID}</div>
              </div>
              <div>
                <span className="text-xs text-muted-foreground">Created</span>
                <div className="text-sm">
                  {new Date(Project.createdAt).toLocaleString()}
                </div>
              </div>
              <div>
                <span className="text-xs text-muted-foreground">Updated</span>
                <div className="text-sm">
                  {new Date(Project.updatedAt).toLocaleString()}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card className="shadow-md rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Tokens
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(TotalCounts.TotalTokens)}
              </div>
            </CardContent>
          </Card>
          <Card className="shadow-md rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Interactions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(TotalCounts.InteractionCount)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Users Table */}
        <Card className="shadow-md rounded-xl">
          <CardHeader>
            <CardTitle>User Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table className="min-w-full border rounded-lg">
                <TableHeader>
                  <TableRow className="bg-muted/70">
                    <TableHead className="border text-left">
                      User Name
                    </TableHead>
                    <TableHead className="border text-left">Email</TableHead>
                    <TableHead className="border text-center" colSpan={2}>
                      Messages
                    </TableHead>
                    <TableHead className="border text-center" colSpan={2}>
                      Lectures
                    </TableHead>
                    <TableHead className="border text-center" colSpan={2}>
                      Pop Quizzes
                    </TableHead>
                    <TableHead className="border text-center" colSpan={2}>
                      Embeddings
                    </TableHead>
                    <TableHead className="border text-center" colSpan={2}>
                      Total
                    </TableHead>
                  </TableRow>
                  <TableRow className="bg-muted/40">
                    <TableHead className="border"></TableHead>
                    <TableHead className="border"></TableHead>
                    <TableHead className="border text-center">Tokens</TableHead>
                    <TableHead className="border text-center">
                      Interactions
                    </TableHead>
                    <TableHead className="border text-center">Tokens</TableHead>
                    <TableHead className="border text-center">
                      Interactions
                    </TableHead>
                    <TableHead className="border text-center">Tokens</TableHead>
                    <TableHead className="border text-center">
                      Interactions
                    </TableHead>
                    <TableHead className="border text-center">Tokens</TableHead>
                    <TableHead className="border text-center">
                      Interactions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {usersArray.map((user: (typeof usersArray)[0]) => {
                    const resourceMap = user.Resources.reduce(
                      (
                        acc: Record<string, (typeof user.Resources)[0]>,
                        resource: (typeof user.Resources)[0]
                      ) => {
                        acc[resource.ResourceType] = resource;
                        return acc;
                      },
                      {}
                    );
                    const totalTokens = user.Resources.reduce(
                      (sum: number, r: (typeof user.Resources)[0]) =>
                        sum + r.TotalTokens,
                      0
                    );
                    const totalInteractions = user.Resources.reduce(
                      (sum: number, r: (typeof user.Resources)[0]) =>
                        sum + r.InteractionCount,
                      0
                    );
                    const userName =
                      [user.User.Name, user.User.FamilyName]
                        .filter(Boolean)
                        .join(" ") || "NA";
                    const userEmail = user.User.Email || "NA";
                    return (
                      <TableRow
                        key={user.UserID}
                        className="cursor-pointer transition-colors hover:bg-primary/10 border-b border-muted/60"
                        onClick={() =>
                          router.push(
                            `/billing/project/${project_id}/user/${user.UserID}`
                          )
                        }
                      >
                        <TableCell className="font-medium border text-left">
                          {userName}
                        </TableCell>
                        <TableCell className="border text-left">
                          {userEmail}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(resourceMap.message?.TotalTokens || 0)}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(
                            resourceMap.message?.InteractionCount || 0
                          )}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(resourceMap.lecture?.TotalTokens || 0)}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(
                            resourceMap.lecture?.InteractionCount || 0
                          )}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(resourceMap.pop_quiz?.TotalTokens || 0)}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(
                            resourceMap.pop_quiz?.InteractionCount || 0
                          )}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(
                            resourceMap.embedding?.TotalTokens || 0
                          )}
                        </TableCell>
                        <TableCell className="border text-center">
                          {formatNumber(
                            resourceMap.embedding?.InteractionCount || 0
                          )}
                        </TableCell>
                        <TableCell className="font-medium border text-center">
                          {formatNumber(totalTokens)}
                        </TableCell>
                        <TableCell className="font-medium border text-center">
                          {formatNumber(totalInteractions)}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
