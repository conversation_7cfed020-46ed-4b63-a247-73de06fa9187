import { z } from 'zod';
import { IsBoolean, IsEnum, MinLength } from 'class-validator';

export const QuestionTypes = z.enum(['SINGLE_CHOICE']);

export type TQuestionType = z.infer<typeof QuestionTypes>;

export class Options {
  @MinLength(3)
  Title: string;

  @IsBoolean()
  IsCorrect: boolean;

  OptionID: string;
}

export class PopQuizQuestion {
  @MinLength(3)
  Title: string;

  @MinLength(3)
  QuestionText: string;

  @IsEnum(QuestionTypes.options)
  Type: TQuestionType;

  Options: Options[];

  QuestionID: string;

  Tags: string[];

  Scopes: Record<string, string>; // Additional scopes for chunk-id, chapter-id, etc

  @MinLength(10)
  SourceID: string;

  @MinLength(10)
  TenantID: string;

  @MinLength(10)
  ProjectID: string;
}
