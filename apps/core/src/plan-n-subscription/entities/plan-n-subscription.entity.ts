export enum CapInterval {
  Daily = 'Daily',
  Weekly = 'Weekly',
  Monthly = 'Monthly',
  Unlimited = 'Unlimited',
}

export class Flag {
  FlagID: 'CHAT' | 'STORY' | 'POP-QUIZ';
  Properties: Record<string, unknown>;
}

export enum PricingType {
  Daily = 'Daily', // 1 day
  Weekly = 'Weekly', // 7 days
  Monthly = 'Monthly', // 30 days
  Yearly = 'Yearly', // 365 days
  Custom = 'Custom', // Custom duration
}

export enum CapType {
  Token = 'Token',
  Message = 'Message',
  Interaction = 'Interaction',
}

export class CreditUsageProperties {
  CapType: CapType;
  CapQuota: number; // 0 for unlimited
}

export class PricingStructure {
  Type: PricingType | string;
  Price: number;
  Duration?: number; // in days
  CreditUsage: CreditUsageProperties;
}

export class Plan {
  PlanID: string;
  Name: string;
  Subtitle: string;
  Description: string;
  Currency: string;
  PricingStructures: PricingStructure[];
  Features: { Title: string; HelpText?: string }[];
  ProjectIDs: string[];
  Tier: string;
  TenantID: string;
  Flags: Flag[];
  ListAsPublic: boolean;
  MarkAsTrailPlan: boolean;
}

export class TierGroup {
  Name: string;
  Description?: string;
  PlanIDs: string[];
  InviteCode: string;
}

export enum SubscriptionStatus {
  Active = 'Active',
  Inactive = 'Inactive',
  Cancelled = 'Cancelled',
  Paused = 'Paused', // Paused, Renewal Pending
  Pending = 'Pending', // Queued for confirmation
}

export enum RenewalStatus {
  Paid = 'Paid',
  Failed = 'Failed',
  Pending = 'Pending',
  AwaitingConfirmation = 'AwaitingConfirmation',
}

export class Renewal {
  FromDate: Date;
  ToDate: Date;
  OrderID: string;
  Price: number;
  Type: PricingType;
  Status: RenewalStatus;
}

export class Subscription {
  _id: string;
  UserID: string;
  TenantID: string;
  Price: number;
  Type: PricingType;
  Status: SubscriptionStatus;
  StartDate: Date;
  EndDate: Date; // Will be pushed every time the subscription is renewed
  PlanID: string;
  Plan: Plan;
  AutoRenew: boolean;
  RenewalLogs: Renewal[];
}
