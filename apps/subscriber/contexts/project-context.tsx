"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { API } from "@/apis/api";
import { toast } from "sonner";
import { TProject } from "@/apis/subscription.api";
import { useAuth } from "./auth-context";

export interface Project {
  id: string;
  name: string;
  description?: string;
}

interface ProjectContextType {
  projects: Project[];
  isLoading: boolean;
  error: string | null;
  selectedProject: Project | null;
  setSelectedProject: (project: Project | null) => void;
  refetchProjects: () => Promise<void>;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export function ProjectProvider({ children }: { children: ReactNode }) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const { isAuthenticated } = useAuth();

  const fetchProjects = async () => {
    if (!isAuthenticated) return;
    setIsLoading(true);
    setError(null);

    try {
      const { data, errors } = await API.SUBSCRIPTION.GetMyProjects();

      if (errors) {
        setError(errors[0]?.message || "Failed to fetch projects");
        toast.error("Failed to load your projects");
        setIsLoading(false);
        return;
      }

      if (data) {
        // Map API response to our Project interface
        const projectsList = data.map((project: TProject) => ({
          id: project.ProjectID,
          name: project.Name,
          description: project.Description,
        }));

        const uniqueProjectIDs = [
          ...new Set(projectsList.map((project) => project.id)),
        ];

        const projects = uniqueProjectIDs.map((id) => {
          const find = projectsList.find((project) => project.id === id);
          return {
            id: find?.id!,
            name: find?.name!,
            description: find?.description,
          };
        });

        setProjects(projects);

        // Set first project as selected if one exists and none is selected
        if (projectsList.length > 0 && !selectedProject) {
          setSelectedProject(projectsList[0] || null);
        }
      }
    } catch (err) {
      console.error("Error fetching projects:", err);
      setError("An unexpected error occurred");
      toast.error("Failed to load projects");
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    if (!isAuthenticated) return;
    fetchProjects();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  // If the selected project is removed from the projects list, select the first available one
  useEffect(() => {
    if (selectedProject && !projects.some((p) => p.id === selectedProject.id)) {
      if (projects.length > 0) {
        setSelectedProject(projects[0] || null);
      } else {
        setSelectedProject(null);
      }
    }
  }, [projects, selectedProject]);

  return (
    <ProjectContext.Provider
      value={{
        projects,
        isLoading,
        error,
        selectedProject,
        setSelectedProject,
        refetchProjects: fetchProjects,
      }}
    >
      {children}
    </ProjectContext.Provider>
  );
}

export const useProjectContext = () => {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error("useProjectContext must be used within a ProjectProvider");
  }
  return context;
};
