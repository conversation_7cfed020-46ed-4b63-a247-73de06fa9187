import { TryCatch } from "@/lib/try-catch";
import { KY, TAckResponse, TErrorResponse } from "./_.index";

export interface Options {
  OptionID: string;
  Title: string;
  IsCorrect: boolean;
}

export interface TCitation {
  ID: string;
  SourceTitle: string;
  SourceID: string;
  PageNumber: number;
  DocumentPageNumber: number;
}

export interface TAnswer {
  Explnation: string;
  Citations: TCitation[];
}

export interface TQuestion {
  Title: string;
  QuestionText: string;
  Type: "SINGLE_CHOICE";
  QuestionID: string;
  SourceID: string;
  Tags: string[];
  Options: Options[];
  Answer: TAnswer;
}
export interface TPopQuiz {
  Title: string;
  Description: string;
  Status: "Draft" | "Active";
  Questions: string[];
  QuestionCount: number;
  ProjectID: string;
  TenantID: string;
  Subscribers?: {
    UserID: string;
    Status: "Pending" | "Submitted";
  }[];
  FloatingSubscribers: boolean;
  PopQuizID: string;
  createdAt: string;
  updatedAt: string;
  QuestionDetails: TQuestion[];
  IsSelfQuiz: boolean;
}

export interface TCreateResponseAnswer {
  QuestionID: string;
  OptionIDs: string[];
}

export interface TCreateResponse {
  PopQuizID: string;
  Answers: TCreateResponseAnswer[];
}

export interface TResponseQuestion {
  QuestionID: string;
  IsCorrect: boolean;
  OptionIDs: string[];
}

export interface TResponseResult {
  CorrectAnswers: number;
  IncorrectAnswers: number;
  TotalAnswers: number;
  Score: number;
}

export interface TResponse {
  PopQuizID: string;
  UserID: string;
  ResponseID: string;
  Questions: TResponseQuestion[];
  Result: TResponseResult;
  TenantID: string;
  createdAt: string;
  updatedAt: string;
  QuestionDetails: TQuestion[];
  PopQuiz: TPopQuiz;
}

export interface TGetPopQuizListParams {
  ProjectID: string;
}

export interface TGetMyResponsesParams {
  ProjectID: string;
  PopQuizID?: string;
}

export interface TTriggerSelfQuizParams {
  ProjectID: string;
  NumberOfQuestions: number;
}

const PreFlightPopQuiz = async () => {
  return await TryCatch<TPopQuiz[], TErrorResponse>(
    KY.get("pop-quiz/response/pre-flight").json(),
  );
};

const GetMyPopQuizResponses = async () => {
  return await TryCatch<TResponse[], TErrorResponse>(
    KY.get("pop-quiz/response/responses").json(),
  );
};

const GetPopQuizList = async (Params: TGetPopQuizListParams) => {
  const searchParams = new URLSearchParams();
  searchParams.set("ProjectID", Params.ProjectID);
  return await TryCatch<TPopQuiz[], TErrorResponse>(
    KY.get("pop-quiz/response/list-quizes", {
      searchParams: searchParams.toString(),
    }).json(),
  );
};

const GetResponse = async (ResponseID: string) => {
  return await TryCatch<TResponse, TErrorResponse>(
    KY.get(`pop-quiz/response/${ResponseID}`).json(),
  );
};

const GetMyResponses = async (Params: TGetMyResponsesParams) => {
  const searchParams = new URLSearchParams();
  searchParams.set("ProjectID", Params.ProjectID);
  if (Params.PopQuizID) {
    searchParams.set("PopQuizID", Params.PopQuizID);
  }
  return await TryCatch<TResponse[], TErrorResponse>(
    KY.get("pop-quiz/response", {
      searchParams: searchParams.toString(),
    }).json(),
  );
};

const CreateResponse = async (Params: TCreateResponse) => {
  return await TryCatch<TAckResponse<TResponse>, TErrorResponse>(
    KY.post("pop-quiz/response", { json: Params }).json(),
  );
};

const TriggerSelfQuiz = async (Params: TTriggerSelfQuizParams) => {
  return await TryCatch<TPopQuiz, TErrorResponse>(
    KY.post("pop-quiz/response/generate-self-quiz", {
      json: {
        ProjectID: Params.ProjectID,
        QuestionCount: Params.NumberOfQuestions,
      },
    }).json(),
  );
};

export const POP_QUIZ = {
  PreFlightPopQuiz,
  GetMyPopQuizResponses,
  GetResponse,
  CreateResponse,
  GetPopQuizList,
  GetMyResponses,
  TriggerSelfQuiz,
};
