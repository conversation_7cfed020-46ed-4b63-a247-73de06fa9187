import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import PlanTable from "./plan-table";
import TierTable from "./tier-table";

export default function Page() {
  return (
    <div className="container mx-auto p-6 md:p-10">
      <Tabs defaultValue="plans">
        <TabsList>
          <TabsTrigger value="plans">Plans</TabsTrigger>
          <TabsTrigger value="tier-groups">Tier Groups</TabsTrigger>
        </TabsList>
        <TabsContent value="plans">
          <PlanTable />
        </TabsContent>
        <TabsContent value="tier-groups">
          <TierTable />
        </TabsContent>
      </Tabs>
    </div>
  );
}
