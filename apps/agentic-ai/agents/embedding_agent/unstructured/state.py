# from dataclasses import dataclass, field
# from agents.embedding_agent.state import EmbeddingState
# from unstructured_client.models import shared


# @dataclass(kw_only=True)
# class UnstructuredState(EmbeddingState):
#     strategy: shared.Strategy = field(
#         default=shared.Strategy.VLM,
#         metadata={
#             "description": "The strategy to use for parsing the document. Options are fast, hi_res, auto, vlm. Default: vlm"
#         },
#     )
#     vlm_model: shared.VLMModel = field(
#         default=shared.VLMModel.GPT_4O,
#         metadata={
#             "description": "The VLM model to use. Options are claude-3-5-sonnet-20241022, claude-3-7-sonnet-20250219, gpt-4o, gemini-1.5-pro, us.amazon.nova-pro-v1:0, us.amazon.nova-lite-v1:0, us.anthropic.claude-3-5-sonnet-20241022-v2:0, us.anthropic.claude-3-opus-20240229-v1:0, us.anthropic.claude-3-haiku-20240307-v1:0, us.anthropic.claude-3-sonnet-20240229-v1:0, us.meta.llama3-2-90b-instruct-v1:0. Default: gpt-4o"
#         },
#     )
#     vlm_model_provider: shared.VLMModelProvider = field(
#         default=shared.VLMModelProvider.OPENAI,
#         metadata={
#             "description": "The VLM model provider. Options are openai, anthropic, meta. Default: openai"
#         },
#     )
#     languages: list[str] = field(
#         metadata={
#             "description": "The list of languages to use for parsing the document. Options are eng, hin, mar. Default: eng"
#         },
#     )
#     split_pdf_page: bool = field(
#         default=False,
#         metadata={
#             "description": "This parameter determines if the PDF file should be split on the client side. It's an internal parameter for the Python client and is not sent to the backend. Default: False"
#         },
#     )
#     split_pdf_allow_failed: bool = field(
#         default=False,
#         metadata={
#             "description": "This parameter determines if the PDF file should be split on the client side. It's an internal parameter for the Python client and is not sent to the backend. Default: False"
#         },
#     )
#     split_pdf_concurrency_level: int = field(default=15)
#     combine_under_n_chars: int = field(
#         default=1000,
#         metadata={
#             "description": "If chunking strategy is set, combine elements until a section reaches a length of n chars. Default: 1000"
#         },
#     )
#     extract_image_block_types: list[str] = field()
#     starting_page_number: int = field(default=1)
#     unique_element_ids: bool = field(default=False)
#     chunking_strategy: str = field(default="by_title")
#     max_characters: int = field(default=1000)
#     multipage_sections: bool = field(default=False)
#     coordinates: bool = field(default=False)
#     hi_res_model_name: str
