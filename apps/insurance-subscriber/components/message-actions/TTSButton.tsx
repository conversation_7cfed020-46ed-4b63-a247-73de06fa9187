"use client";

import React, { useState, useEffect } from "react";
import { VolumeX, Volume2, Pause, Play } from "lucide-react";
import { useTTS } from "@/contexts/tts-context";
import { Button } from "@workspace/ui/components/button";
import { <PERSON>lider } from "@workspace/ui/components/slider";

interface TTSButtonProps {
  text: string;
  messageId: string;
}

export default function TTSButton({ text, messageId }: TTSButtonProps) {
  const { isPlaying, currentMessageId, progress, play, pause, resume, stop } =
    useTTS();

  const [isHovering, setIsHovering] = useState(false);
  const [isTTSSupported, setIsTTSSupported] = useState(true);

  // Check if TTS is supported
  useEffect(() => {
    if (typeof window !== "undefined") {
      setIsTTSSupported("speechSynthesis" in window);
    }
  }, []);

  // Determine if this message is active
  const isActiveMessage = currentMessageId === messageId;

  const isAtEnd = isActiveMessage && progress >= 0.999;

  // Handle play button click
  const handlePlayClick = () => {
    if (!isTTSSupported) return;

    try {
      // If this message is already playing and not at the end
      if (isActiveMessage && isPlaying) {
        pause();
      }
      // If this message is paused
      else if (isActiveMessage && !isPlaying && !isAtEnd) {
        resume();
      }
      // If this is a new message or we're at the end
      else {
        play(messageId, text);
      }
    } catch (error) {
      console.error("Error handling play button:", error);
      // Try to recover by stopping everything
      stop();
    }
  };

  // Handle stop button click
  const handleStopClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isActiveMessage) {
      stop();
    }
  };

  // Handle seekbar change
  const handleSeek = (newValue: number[]) => {
    if (
      isActiveMessage &&
      newValue &&
      newValue.length > 0 &&
      typeof newValue[0] === "number"
    ) {
    }
  };

  // If TTS is not supported, don't render the button
  if (!isTTSSupported) return null;

  return (
    <div
      className="flex items-center space-x-1 text-gray-500"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        onClick={handlePlayClick}
        aria-label={isActiveMessage && isPlaying ? "Pause" : "Play"}
      >
        {isActiveMessage && isPlaying ? (
          <Pause className="h-4 w-4" />
        ) : (
          <Play className="h-4 w-4" />
        )}
      </Button>

      {isActiveMessage && (
        <>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={handleStopClick}
            aria-label="Stop"
          >
            <VolumeX className="h-4 w-4" />
          </Button>

          <div className="w-24 px-1">
            <Slider
              value={[progress]}
              min={0}
              max={1}
              step={0.01}
              onValueChange={handleSeek}
              aria-label="Playback progress"
            />
          </div>
        </>
      )}

      {!isActiveMessage && (isHovering || isActiveMessage) && (
        <Volume2 className="h-4 w-4 mr-1" />
      )}
    </div>
  );
}
