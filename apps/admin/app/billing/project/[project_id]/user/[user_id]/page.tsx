"use client";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { TOKEN_COUNTER } from "@/apis/token-counter";
import { formatNumber } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { ChevronDown, ChevronUp } from "lucide-react";
import type { TGetUserTokenUsageResponse } from "@/apis/token-counter";
import { useRouter } from "next/navigation";

export default function UserBillingPage() {
  const { project_id, user_id } = useParams();
  const [data, setData] = useState<TGetUserTokenUsageResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const { data: responseData } = await TOKEN_COUNTER.GetUserTokenUsage({
        ProjectID: project_id as string,
        UserID: user_id as string,
      });
      if (responseData)
        setData(responseData as unknown as TGetUserTokenUsageResponse);
      setLoading(false);
    };
    if (project_id && user_id) fetchData();
  }, [project_id, user_id]);

  if (loading || !data) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const { Project, User, TokenCounter } = data;

  // Calculate totals
  const totalTokens = TokenCounter.reduce(
    (sum: number, t: (typeof TokenCounter)[0]) =>
      sum + (t.TokenCount?.TotalTokens || 0),
    0
  );
  const totalInputTokens = TokenCounter.reduce(
    (sum: number, t: (typeof TokenCounter)[0]) =>
      sum + (t.TokenCount?.InputTokens || 0),
    0
  );
  const totalOutputTokens = TokenCounter.reduce(
    (sum: number, t: (typeof TokenCounter)[0]) =>
      sum + (t.TokenCount?.OutputTokens || 0),
    0
  );
  const totalInteractions = TokenCounter.length;

  return (
    <div className="w-full h-full bg-muted/50 min-h-screen">
      <div className="flex flex-col gap-6 p-6 md:p-8">
        {/* Breadcrumbs */}
        <nav className="text-sm mb-2" aria-label="Breadcrumb">
          <ol className="flex items-center gap-2 text-muted-foreground">
            <li>
              <button
                onClick={() => router.push("/")}
                className="hover:underline"
              >
                Home
              </button>
            </li>
            <li>/</li>
            <li>
              <button
                onClick={() => router.push("/billing")}
                className="hover:underline"
              >
                Billing
              </button>
            </li>
            <li>/</li>
            <li>
              <button
                onClick={() => router.push(`/billing/project/${project_id}`)}
                className="hover:underline"
              >
                Project
              </button>
            </li>
            <li>/</li>
            <li className="font-semibold text-foreground">User</li>
          </ol>
        </nav>

        {/* User Overview - horizontal card with larger name/email */}
        <Card className="shadow-md rounded-xl p-0">
          <div className="flex flex-col md:flex-row items-center md:items-start gap-6 p-6">
            <div className="flex-1 min-w-0">
              <div className="text-2xl font-bold mb-1">
                {User.Name} {User.FamilyName}
              </div>
              <div className="text-base text-muted-foreground mb-2">
                {User.Email}
              </div>
              <div className="flex flex-wrap gap-6 mt-2">
                <div>
                  <span className="text-xs text-muted-foreground">User ID</span>
                  <div className="font-mono text-sm break-all">
                    {User.UserID}
                  </div>
                </div>
                <div>
                  <span className="text-xs text-muted-foreground">
                    App Type
                  </span>
                  <div className="text-sm">{User.AppType}</div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="shadow-md rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Tokens
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(totalTokens)}
              </div>
            </CardContent>
          </Card>
          <Card className="shadow-md rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Input Tokens
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(totalInputTokens)}
              </div>
            </CardContent>
          </Card>
          <Card className="shadow-md rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Output Tokens
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(totalOutputTokens)}
              </div>
            </CardContent>
          </Card>
          <Card className="shadow-md rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Interactions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(totalInteractions)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* TokenCounter Table */}
        <Card className="shadow-md rounded-xl">
          <CardHeader>
            <CardTitle>Interactions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table className="min-w-full border rounded-lg">
                <TableHeader>
                  <TableRow className="bg-muted/70">
                    <TableHead className="border text-left">
                      Graph Invocation ID
                    </TableHead>
                    <TableHead className="border text-left">Date</TableHead>
                    <TableHead className="border text-left">
                      Resource Type
                    </TableHead>
                    <TableHead className="border text-left">
                      Resource IDs
                    </TableHead>
                    <TableHead className="border text-left">
                      Project ID
                    </TableHead>
                    <TableHead className="border text-center">
                      Input Tokens
                    </TableHead>
                    <TableHead className="border text-center">
                      Output Tokens
                    </TableHead>
                    <TableHead className="border text-center">
                      Total Tokens
                    </TableHead>
                    <TableHead className="border text-center">
                      LLM Invocations
                    </TableHead>
                    <TableHead className="border text-center">Expand</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {TokenCounter.map((interaction: (typeof TokenCounter)[0]) => {
                    const isExpanded = expanded === interaction._id;
                    return (
                      <>
                        <TableRow
                          key={interaction._id}
                          className="cursor-pointer transition-colors hover:bg-primary/10 border-b border-muted/60"
                          onClick={() =>
                            setExpanded(isExpanded ? null : interaction._id)
                          }
                        >
                          <TableCell className="border text-left">
                            {interaction.GraphInvocationID}
                          </TableCell>
                          <TableCell className="border text-left">
                            {new Date(interaction.createdAt).toLocaleString()}
                          </TableCell>
                          <TableCell className="border text-left">
                            {interaction.ResourceType}
                          </TableCell>
                          <TableCell className="border text-left">
                            {interaction.ResourceIDs?.join(", ")}
                          </TableCell>
                          <TableCell className="border text-left">
                            {interaction.ProjectID}
                          </TableCell>
                          <TableCell className="border text-center">
                            {formatNumber(
                              interaction.TokenCount?.InputTokens || 0
                            )}
                          </TableCell>
                          <TableCell className="border text-center">
                            {formatNumber(
                              interaction.TokenCount?.OutputTokens || 0
                            )}
                          </TableCell>
                          <TableCell className="border text-center">
                            {formatNumber(
                              interaction.TokenCount?.TotalTokens || 0
                            )}
                          </TableCell>
                          <TableCell className="border text-center">
                            {interaction.TokenCount?.LLMInvocations?.length ||
                              0}
                          </TableCell>
                          <TableCell className="border text-center">
                            {isExpanded ? (
                              <ChevronUp size={18} />
                            ) : (
                              <ChevronDown size={18} />
                            )}
                          </TableCell>
                        </TableRow>
                        {isExpanded && (
                          <TableRow className="bg-muted/40">
                            <TableCell colSpan={12} className="p-0">
                              <div className="p-4">
                                <div className="font-semibold mb-2">
                                  LLM Invocations
                                </div>
                                <Table className="min-w-full border rounded-lg">
                                  <TableHeader>
                                    <TableRow className="bg-muted/70">
                                      <TableHead className="border text-left">
                                        LLM Invocation ID
                                      </TableHead>
                                      <TableHead className="border text-left">
                                        Node
                                      </TableHead>
                                      <TableHead className="border text-left">
                                        Model Name
                                      </TableHead>

                                      <TableHead className="border text-center">
                                        Input Tokens
                                      </TableHead>
                                      <TableHead className="border text-center">
                                        Output Tokens
                                      </TableHead>
                                      <TableHead className="border text-center">
                                        Total Tokens
                                      </TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {interaction.TokenCount?.LLMInvocations?.map(
                                      (inv: any) => (
                                        <TableRow key={inv.LLMInvocationID}>
                                          <TableCell className="border text-left">
                                            {inv.LLMInvocationID}
                                          </TableCell>
                                          <TableCell className="border text-left">
                                            {inv.Node}
                                          </TableCell>
                                          <TableCell className="border text-left">
                                            {inv.Model || "-"}
                                          </TableCell>
                                          <TableCell className="border text-center">
                                            {formatNumber(
                                              inv.Usage?.InputTokens || 0
                                            )}
                                          </TableCell>
                                          <TableCell className="border text-center">
                                            {formatNumber(
                                              inv.Usage?.OutputTokens || 0
                                            )}
                                          </TableCell>
                                          <TableCell className="border text-center">
                                            {formatNumber(
                                              inv.Usage?.TotalTokens || 0
                                            )}
                                          </TableCell>
                                        </TableRow>
                                      )
                                    )}
                                  </TableBody>
                                </Table>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
