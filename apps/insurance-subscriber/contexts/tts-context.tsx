"use client";

import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useCallback,
  ReactNode,
  useEffect,
} from "react";

// Define types
interface TTSState {
  isPlaying: boolean;
  currentMessageId: string | null;
  progress: number;
}

interface TTSContextType {
  isPlaying: boolean;
  currentMessageId: string | null;
  progress: number;
  play: (messageId: string, text: string) => void;
  pause: () => void;
  resume: () => void;
  stop: () => void;
  autoRead: boolean;
  toggleAutoRead: () => void;
}

// Create context
const TTSContext = createContext<TTSContextType | undefined>(undefined);

// Maximum length for each speech chunk to prevent timeouts
const MAX_CHUNK_LENGTH = 200; // Characters per chunk

export function TTSProvider({ children }: { children: ReactNode }) {
  // Core TTS state
  const [ttsState, setTTSState] = useState<TTSState>({
    isPlaying: false,
    currentMessageId: null,
    progress: 0,
  });

  // Auto-read preference (default to false, retrieve from localStorage if available)
  const [autoRead, setAutoRead] = useState<boolean>(false);

  // Refs for managing speech
  const synthesisRef = useRef<SpeechSynthesis | null>(null);
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  const progressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);
  const durationRef = useRef<number>(0);
  const operationLockRef = useRef<boolean>(false);

  // Refs for chunked text handling
  const textChunksRef = useRef<string[]>([]);
  const currentChunkIndexRef = useRef<number>(0);
  const totalChunksRef = useRef<number>(0);
  const speechTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const originalTextRef = useRef<string>("");
  const chunkPlayingRef = useRef<boolean>(false);

  // Initialize speech synthesis and load saved preferences
  useEffect(() => {
    if (typeof window !== "undefined") {
      synthesisRef.current = window.speechSynthesis;

      // Load auto-read preference from localStorage
      try {
        const savedAutoRead = localStorage.getItem("tts-auto-read");
        if (savedAutoRead !== null) {
          setAutoRead(savedAutoRead === "true");
        }
      } catch (error) {
        console.error("Failed to load TTS preferences:", error);
      }

      // Add event listener to handle synthesis interruptions
      window.addEventListener("visibilitychange", handleVisibilityChange);
    }

    // Clean up on unmount
    return () => {
      window.removeEventListener("visibilitychange", handleVisibilityChange);
      cleanup();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle visibility change (browser tab switching) to prevent synthesis interruption
  const handleVisibilityChange = useCallback(() => {
    if (document.visibilityState === "visible") {
      if (
        ttsState.isPlaying &&
        chunkPlayingRef.current &&
        synthesisRef.current
      ) {
        // If text was playing when tab lost visibility, try to resume playback
        synthesisRef.current.resume();
      }
    } else if (document.visibilityState === "hidden") {
      // Store state when tab loses visibility
      if (ttsState.isPlaying && synthesisRef.current) {
        // Pause speech when tab is not visible to prevent timeouts
        synthesisRef.current.pause();
      }
    }
  }, [ttsState.isPlaying]);

  // Save auto-read preference to localStorage when it changes
  useEffect(() => {
    try {
      localStorage.setItem("tts-auto-read", autoRead.toString());
    } catch (error) {
      console.error("Failed to save TTS preferences:", error);
    }
  }, [autoRead]);

  // Toggle auto-read function
  const toggleAutoRead = useCallback(() => {
    setAutoRead((prev) => !prev);
  }, []);

  // Cleanup function to stop speech and clear timers
  const cleanup = useCallback(() => {
    // Clear progress timer
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
      progressTimerRef.current = null;
    }

    // Clear speech timeout
    if (speechTimeoutRef.current) {
      clearTimeout(speechTimeoutRef.current);
      speechTimeoutRef.current = null;
    }

    // Cancel speech synthesis
    try {
      if (synthesisRef.current) {
        synthesisRef.current.cancel();
      }
    } catch (error) {
      console.error("Error during speech cleanup:", error);
    }

    // Clear utterance reference and chunk state
    utteranceRef.current = null;
    textChunksRef.current = [];
    currentChunkIndexRef.current = 0;
    totalChunksRef.current = 0;
    chunkPlayingRef.current = false;
    originalTextRef.current = "";
  }, []);

  // Stop TTS completely
  const stop = useCallback(() => {
    if (operationLockRef.current) return;
    operationLockRef.current = true;

    try {
      cleanup();
      setTTSState({
        isPlaying: false,
        currentMessageId: null,
        progress: 0,
      });
    } catch (error) {
      console.error("Error stopping speech:", error);
    } finally {
      setTimeout(() => {
        operationLockRef.current = false;
      }, 100);
    }
  }, [cleanup]);

  // Pause ongoing speech
  const pause = useCallback(() => {
    if (
      operationLockRef.current ||
      !ttsState.isPlaying ||
      !synthesisRef.current
    )
      return;
    operationLockRef.current = true;

    try {
      // Pause speech synthesis
      synthesisRef.current.pause();

      // Stop progress updates
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
        progressTimerRef.current = null;
      }

      // Pause chunk playing state
      chunkPlayingRef.current = false;

      // Clear speech timeout if any
      if (speechTimeoutRef.current) {
        clearTimeout(speechTimeoutRef.current);
        speechTimeoutRef.current = null;
      }

      // Update state to reflect paused status
      setTTSState((prev) => ({
        ...prev,
        isPlaying: false,
      }));
    } catch (error) {
      console.error("Error pausing speech:", error);
      stop(); // Fall back to stopping if pause fails
    } finally {
      setTimeout(() => {
        operationLockRef.current = false;
      }, 100);
    }
  }, [stop, ttsState.isPlaying]);

  // Resume paused speech
  const resume = useCallback(() => {
    if (
      operationLockRef.current ||
      ttsState.isPlaying ||
      !ttsState.currentMessageId ||
      !synthesisRef.current
    )
      return;

    operationLockRef.current = true;

    try {
      synthesisRef.current.resume();
      chunkPlayingRef.current = true;

      // Calculate elapsed time and update start time reference
      const elapsedTime = ttsState.progress * durationRef.current;
      startTimeRef.current = Date.now() - elapsedTime;

      // Restart progress tracking
      startProgressTracking();

      // Update state to reflect playing status
      setTTSState((prev) => ({
        ...prev,
        isPlaying: true,
      }));
    } catch (error) {
      console.error("Error resuming speech:", error);
      stop(); // Fall back to stopping if resume fails
    } finally {
      setTimeout(() => {
        operationLockRef.current = false;
      }, 100);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stop, ttsState.currentMessageId, ttsState.isPlaying, ttsState.progress]);

  // Start tracking progress
  const startProgressTracking = useCallback(() => {
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
    }

    progressTimerRef.current = setInterval(() => {
      const elapsed = Date.now() - startTimeRef.current;
      const newProgress = Math.min(elapsed / durationRef.current, 1);

      setTTSState((prev) => ({
        ...prev,
        progress: newProgress,
      }));

      // If we've reached the end and speech has stopped
      if (newProgress >= 1) {
        cleanup();
        setTTSState({
          isPlaying: false,
          currentMessageId: null,
          progress: 0,
        });
      }
    }, 50);
  }, [cleanup]);

  // Split text into chunks for more reliable TTS
  const splitIntoChunks = useCallback((text: string): string[] => {
    // First, split by sentences to try to keep natural breaks
    const sentences = text.match(/[^.!?]+[.!?]+/g) || [];
    const chunks: string[] = [];

    if (sentences.length === 0) {
      // If no sentence boundaries found, split by character count
      let i = 0;
      while (i < text.length) {
        chunks.push(text.slice(i, i + MAX_CHUNK_LENGTH));
        i += MAX_CHUNK_LENGTH;
      }
    } else {
      // Group sentences into chunks of reasonable size
      let currentChunk = "";
      for (const sentence of sentences) {
        // If adding this sentence would make the chunk too long, save current chunk and start a new one
        if (
          currentChunk.length + sentence.length > MAX_CHUNK_LENGTH &&
          currentChunk.length > 0
        ) {
          chunks.push(currentChunk);
          currentChunk = sentence;
        } else {
          currentChunk += sentence;
        }
      }

      // Add the last chunk if it's not empty
      if (currentChunk.length > 0) {
        chunks.push(currentChunk);
      }
    }

    return chunks;
  }, []);

  // Estimate speech duration for the entire text
  const estimateDuration = useCallback((text: string): number => {
    // Average speaking rate (words per minute)
    const wpm = 150;
    const wordCount = text.split(/\s+/).filter((w) => w.length > 0).length;
    const minutesRequired = wordCount / wpm;

    // Base duration in milliseconds
    const baseDuration = minutesRequired * 60 * 1000;

    // Add time for pauses
    const sentenceCount = (text.match(/[.!?]+/g) || []).length;
    const pauseDuration = sentenceCount * 300; // 300ms pause per sentence

    return Math.max(2000, baseDuration + pauseDuration);
  }, []);

  // Process text for better TTS reading (handle citations, code blocks, etc.)
  const processTextForSpeech = useCallback((text: string): string => {
    return text
      .replace(/```[\s\S]*?```/g, "code block omitted") // Remove code blocks
      .replace(/`([^`]+)`/g, "$1") // Remove inline code formatting
      .replace(/#{1,6}\s/g, "") // Remove heading markers
      .replace(/\*\*([^*]+)\*\*/g, "$1") // Remove bold
      .replace(/\*([^*]+)\*/g, "$1") // Remove italic
      .replace(/\[([^\]]+)\]\([^)]+\)/g, "$1") // Replace links with just text
      .replace(/>/g, "") // Remove blockquote markers
      .replace(/\n\n/g, ". ") // Convert paragraph breaks to pauses
      .replace(/\n/g, " ") // Replace newlines with spaces
      .replace(/\s+/g, " ") // Normalize whitespace
      .trim();
  }, []);

  // Get the best available voice
  const getBestVoice = useCallback((): SpeechSynthesisVoice | null => {
    if (!synthesisRef.current) return null;

    // Get all available voices
    const voices = synthesisRef.current.getVoices();
    if (!voices || voices.length === 0) return null;

    // Try to find a good English voice in this order:
    // 1. Google UK English Female (high quality)
    // 2. Any en-GB or en-US voice
    // 3. Default voice

    // First choice: Google UK English Female
    const googleUKFemale = voices.find(
      (voice) =>
        voice.name === "Google UK English Female" ||
        (voice.name.includes("Google") &&
          voice.name.includes("Female") &&
          voice.lang.startsWith("en")),
    );
    if (googleUKFemale) return googleUKFemale;

    // Second choice: Any en-GB voice
    const enGBVoice = voices.find((voice) => voice.lang === "en-GB");
    if (enGBVoice) return enGBVoice;

    // Third choice: Any en-US voice
    const enUSVoice = voices.find((voice) => voice.lang === "en-US");
    if (enUSVoice) return enUSVoice;

    // Fallback: Default system voice
    return voices.find((voice) => voice.default) || voices[0] || null;
  }, []);

  // Speak the next chunk in the queue
  const speakNextChunk = useCallback(() => {
    if (
      !synthesisRef.current ||
      currentChunkIndexRef.current >= textChunksRef.current.length
    ) {
      return;
    }

    chunkPlayingRef.current = true;

    try {
      // Get the next chunk
      const chunk = textChunksRef.current[currentChunkIndexRef.current];

      // Create utterance for this chunk
      const utterance = new SpeechSynthesisUtterance(chunk);
      utteranceRef.current = utterance;

      // Set voice
      const voice = getBestVoice();
      if (voice) {
        utterance.voice = voice;
        utterance.rate = 1.0;
        utterance.pitch = 1.0;
      }

      // Set up event handlers
      utterance.onend = () => {
        // Move to next chunk when this one ends
        currentChunkIndexRef.current++;

        // If there are more chunks, speak the next one
        if (currentChunkIndexRef.current < textChunksRef.current.length) {
          // Small delay between chunks to prevent browser throttling
          speechTimeoutRef.current = setTimeout(() => {
            speakNextChunk();
          }, 50);
        } else {
          // All chunks complete
          if (ttsState.currentMessageId) {
            chunkPlayingRef.current = false;
          }
        }
      };

      utterance.onerror = (event) => {
        console.error("Speech synthesis error:", event);

        // Try to recover by moving to the next chunk
        currentChunkIndexRef.current++;

        if (currentChunkIndexRef.current < textChunksRef.current.length) {
          speechTimeoutRef.current = setTimeout(() => {
            speakNextChunk();
          }, 100);
        } else {
          chunkPlayingRef.current = false;

          // If we can't recover, stop TTS entirely
          if (ttsState.isPlaying) {
            stop();
          }
        }
      };

      // Speak the chunk
      synthesisRef.current.speak(utterance);

      // Set a backup timeout to detect if speech synthesis hangs
      speechTimeoutRef.current = setTimeout(
        () => {
          // If we're still on the same chunk after a while, it might have failed silently
          if (
            currentChunkIndexRef.current < textChunksRef.current.length &&
            chunk &&
            textChunksRef.current[currentChunkIndexRef.current] === chunk
          ) {
            console.warn(
              "Speech synthesis chunk may have stalled, trying next chunk",
            );
            currentChunkIndexRef.current++;

            if (currentChunkIndexRef.current < textChunksRef.current.length) {
              // Try the next chunk
              speakNextChunk();
            } else {
              // We've tried all chunks
              chunkPlayingRef.current = false;
            }
          }
        },
        Math.max(5000, chunk ? chunk.length * 50 : 5000),
      ); // Timeout based on chunk length
    } catch (error) {
      console.error("Error speaking chunk:", error);

      // Try to recover by moving to next chunk
      currentChunkIndexRef.current++;
      if (currentChunkIndexRef.current < textChunksRef.current.length) {
        speechTimeoutRef.current = setTimeout(() => {
          speakNextChunk();
        }, 100);
      } else {
        chunkPlayingRef.current = false;
      }
    }
  }, [getBestVoice, stop, ttsState.currentMessageId, ttsState.isPlaying]);

  // Play TTS for a message using chunking for reliability
  const play = useCallback(
    (messageId: string, text: string) => {
      if (!text || !synthesisRef.current || operationLockRef.current) return;
      operationLockRef.current = true;

      try {
        // If already playing something, stop it first
        if (ttsState.isPlaying || ttsState.currentMessageId) {
          cleanup();
        }

        // Short delay to ensure proper cleanup
        setTimeout(() => {
          try {
            if (!synthesisRef.current) return;

            // Process text for better TTS reading
            const processedText = processTextForSpeech(text);
            originalTextRef.current = processedText;

            // Split the text into manageable chunks
            textChunksRef.current = splitIntoChunks(processedText);
            totalChunksRef.current = textChunksRef.current.length;
            currentChunkIndexRef.current = 0;

            // Calculate estimated duration for the entire text
            durationRef.current = estimateDuration(processedText);
            startTimeRef.current = Date.now();

            // Update state
            setTTSState({
              isPlaying: true,
              currentMessageId: messageId,
              progress: 0,
            });

            // Start progress tracking
            startProgressTracking();

            // Start speaking the first chunk
            speakNextChunk();
          } catch (error) {
            console.error("Error starting speech:", error);
            cleanup();
            setTTSState({
              isPlaying: false,
              currentMessageId: null,
              progress: 0,
            });
          } finally {
            operationLockRef.current = false;
          }
        }, 100);
      } catch (error) {
        console.error("Error in play function:", error);
        operationLockRef.current = false;
      }
    },
    [
      cleanup,
      estimateDuration,
      processTextForSpeech,
      speakNextChunk,
      splitIntoChunks,
      startProgressTracking,
      ttsState.currentMessageId,
      ttsState.isPlaying,
    ],
  );

  // Provide the context
  return (
    <TTSContext.Provider
      value={{
        isPlaying: ttsState.isPlaying,
        currentMessageId: ttsState.currentMessageId,
        progress: ttsState.progress,
        play,
        pause,
        resume,
        stop,
        autoRead,
        toggleAutoRead,
      }}
    >
      {children}
    </TTSContext.Provider>
  );
}

// Hook to use the TTS context
export const useTTS = () => {
  const context = useContext(TTSContext);
  if (context === undefined) {
    throw new Error("useTTS must be used within a TTSProvider");
  }
  return context;
};
