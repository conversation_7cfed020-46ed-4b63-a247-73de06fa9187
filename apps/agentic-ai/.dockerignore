# Exclude everything by default, then include what we need
**

# Include essential application files
!app/
!agents/
!pyproject.toml
!poetry.lock
!.env

# Exclude from included directories
app/__pycache__/
app/**/__pycache__/
agents/__pycache__/
agents/**/__pycache__/

# Python
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.venv/
venv/
env/
ENV/

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
.vim/
.emacs.d/

# OS generated
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Tests and development
test/
tests/
.pytest_cache/
.coverage
htmlcov/
.tox/
.mypy_cache/
.ruff_cache/

# Documentation
docs/
*.md
LICENSE
CHANGELOG

# Development configs
.env.local
.env.development
.env.test
.flaskenv

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
Jenkinsfile
.circleci/

# Build artifacts
build.sh
Makefile

# Large files
*.zip
*.tar.gz
*.rar
*.7z

# Data files (adjust based on your needs)