import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { CapType } from '../entities/plan-n-subscription.entity';
import mongoose from 'mongoose';

@Schema({
  timestamps: true,
  collection: 'credit_usage',
  versionKey: false,
})
export class CreditUsageModel {
  @Prop()
  UserID: string;

  @Prop({
    type: String,
    enum: ['Subscription', 'TopUp'],
    default: 'Subscription',
  })
  Type: 'Subscription' | 'TopUp';

  @Prop({
    type: String,
    enum: ['Token', 'Message', 'Interaction'],
    default: 'Token',
  })
  CapType: CapType;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subscription',
  })
  SubscriptionID: string;

  @Prop({
    type: String,
    ref: 'Renewal',
  })
  RenewalID?: string;

  @Prop({
    type: Number,
    default: 0,
  })
  Credit: number;

  @Prop({
    type: Number,
    default: 0,
  })
  RemainingCredit: number;

  @Prop({
    type: Date,
    default: null,
  })
  ExpiryDate: Date | null;

  @Prop({
    type: Date,
    default: Date.now,
  })
  LastSyncDate: Date;
}

export const CreditUsageSchema = SchemaFactory.createForClass(CreditUsageModel);
