"use client";

import { API } from "@/apis/api";
import { USER } from "@/apis/user.api";
import { TUser } from "@/types/user";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@workspace/ui/components/alert-dialog";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@workspace/ui/components/pagination";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@workspace/ui/components/sheet";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { format } from "date-fns";
import { Plus, Redo, RotateCcw, SlidersHorizontal } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

export default function UserTable() {
  const [users, setUsers] = useState<TUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<TUser[]>([]);
  const [selectedUser, setSelectedUser] = useState<TUser | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchField, setSearchField] = useState("all");
  const [filters, setFilters] = useState({
    hasPhone: false,
    hasDob: false,
    sortBy: "name", // name, email, dob
    sortOrder: "asc", // asc, desc
  });

  const router = useRouter();

  const schema = z.object({
    Name: z.string().min(1, "Name is required"),
    Email: z.string().email("Invalid email"),
    Password: z.string().min(6, "Password must be at least 6 characters"),
    UserType: z.enum(["Subscriber", "User"]),
    PhoneNumber: z.string().optional(),
    Gender: z.enum(["Male", "Female", "Other"]),
    DateOfBirth: z.string().optional(),
    Address: z.string().optional(),
    GivenName: z.string().optional(),
    FamilyName: z.string().optional(),
    MiddleName: z.string().optional(),
    Website: z.string().optional(),
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    getValues,
    formState: { errors },
  } = useForm({
    defaultValues: {
      Name: "",
      Email: "",
      Password: "",
      UserType: "User",
      PhoneNumber: "",
      Gender: "Other",
      DateOfBirth: "",
      Address: "",
      GivenName: "",
      FamilyName: "",
      MiddleName: "",
      Website: "",
    },
    resolver: zodResolver(schema),
  });

  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentUsers = filteredUsers.slice(startIndex, endIndex);
  const UserType = "Employee";

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const { data, errors } = await USER.ListUsers({ UserType });
      if (data) {
        setUsers(data);
      }
    } catch (error) {
      toast.error("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    let filtered = users.filter((user) => {
      const searchLower = searchQuery.toLowerCase();

      // Apply search
      if (searchQuery !== "") {
        switch (searchField) {
          case "name":
            return user.Name?.toLowerCase().includes(searchLower);
          case "lastName":
            return user.FamilyName?.toLowerCase().includes(searchLower);
          case "email":
            return user.Email.toLowerCase().includes(searchLower);
          case "phone":
            return user.PhoneNumber?.toLowerCase().includes(searchLower);
          case "all":
          default:
            return (
              user.Name?.toLowerCase().includes(searchLower) ||
              user.FamilyName?.toLowerCase().includes(searchLower) ||
              user.Email.toLowerCase().includes(searchLower) ||
              user.PhoneNumber?.toLowerCase().includes(searchLower)
            );
        }
      }

      return true;
    });

    // Apply filters
    if (filters.hasPhone) {
      filtered = filtered.filter(
        (user) => user.PhoneNumber && user.PhoneNumber.length > 0
      );
    }

    if (filters.hasDob) {
      filtered = filtered.filter(
        (user) => user.DateOfBirth && user.DateOfBirth.length > 0
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let compareA, compareB;

      switch (filters.sortBy) {
        case "name":
          compareA = a.Name || "";
          compareB = b.Name || "";
          break;
        case "email":
          compareA = a.Email || "";
          compareB = b.Email || "";
          break;
        case "dob":
          compareA = a.DateOfBirth || "";
          compareB = b.DateOfBirth || "";
          break;
        default:
          compareA = a.Name || "";
          compareB = b.Name || "";
      }

      if (filters.sortOrder === "asc") {
        return compareA.localeCompare(compareB);
      } else {
        return compareB.localeCompare(compareA);
      }
    });

    setFilteredUsers(filtered);
  }, [searchQuery, searchField, filters, users]);

  const handleCreateUser = async (data: any) => {
    try {
      setLoading(true);
      const { data: userData, errors } = await USER.CreateUser({
        ...data,
        UserType,
      });
      if (userData) {
        setUsers([...users, userData.ack]);
        reset();
      }
      if (errors) {
        errors.forEach((err) => toast.error(err.message));
      }
    } catch (error) {
      toast.error("Failed to create user");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateUser = async (data: any) => {
    if (!selectedUser) return;
    try {
      setLoading(true);
      const { data: userData, errors } = await USER.UpdateUser({
        UserID: selectedUser.UserID || "",
        ...data,
        UserType: "Subscriber",
        ProfilePicture: selectedUser.ProfilePicture || "",
        ZoneInfo: selectedUser.ZoneInfo || "",
        Locale: selectedUser.Locale || "",
      });
      if (userData) {
        setUsers(
          users.map((user) =>
            user.UserID === selectedUser.UserID ? userData.ack : user
          )
        );
        setSelectedUser(null);
        reset();
      }
      if (errors) {
        errors.forEach((err) => toast.error(err.message));
      }
    } catch (error) {
      console.error("Error updating user:", error);
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: any) => {
    // parse errors
    const parse = schema.safeParse(data);
    if (!parse.success) {
      parse.error.errors.forEach((err) => toast.error(err.message));
      return;
    }
    if (selectedUser) {
      await handleUpdateUser(data);
    } else {
      await handleCreateUser(data);
    }
    setOpen(false);
    setSelectedUser(null);
  };

  const UserForm = () => (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col gap-4 flex-1"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="givenName">First Name</Label>
          <Input
            id="givenName"
            {...register("Name", { required: "First name is required" })}
            className={errors.Name ? "border-destructive" : ""}
            required
          />
          {errors.Name && (
            <p className="text-sm text-destructive">{errors.Name.message}</p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="familyName">Last Name</Label>
          <Input
            id="familyName"
            {...register("FamilyName")}
            className={errors.FamilyName ? "border-destructive" : ""}
          />
          {errors.FamilyName && (
            <p className="text-sm text-destructive">
              {errors.FamilyName.message}
            </p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          {...register("Email", { required: "Email is required" })}
          className={errors.Email ? "border-destructive" : ""}
        />
        {errors.Email && (
          <p className="text-sm text-destructive">{errors.Email.message}</p>
        )}
      </div>

      {!selectedUser && (
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            {...register("Password", { required: "Password is required" })}
            className={errors.Password ? "border-destructive" : ""}
          />
          {errors.Password && (
            <p className="text-sm text-destructive">
              {errors.Password.message}
            </p>
          )}
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="phone">Phone Number</Label>
        <Input
          id="phone"
          type="tel"
          {...register("PhoneNumber")}
          className={errors.PhoneNumber ? "border-destructive" : ""}
        />
        {errors.PhoneNumber && (
          <p className="text-sm text-destructive">
            {errors.PhoneNumber.message}
          </p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="address">Address</Label>
        <Input
          id="address"
          {...register("Address")}
          className={errors.Address ? "border-destructive" : ""}
        />
        {errors.Address && (
          <p className="text-sm text-destructive">{errors.Address?.message}</p>
        )}
      </div>
      <Button type="submit" className="mt-auto">
        {selectedUser ? "Update User" : "Create User"}
      </Button>
    </form>
  );

  const [resetPasswordOpen, setResetPasswordOpen] = useState(false);

  const ResetUserPasswordAlertDialog = ({
    open,
    onOpenChange,
    selectedUser,
  }: {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    selectedUser: TUser | null;
  }) => {
    if (!selectedUser) return;

    const handleResetPassword = async () => {
      const { data, errors } = await API.USER.ResetUserPassword({
        UserID: selectedUser.UserID as string,
        Password: getValues("Password"),
      });
      if (data) {
        toast.success("Password reset successfully");
        onOpenChange(false);
      }
      if (errors) {
        errors.forEach((err) => toast.error(err.message));
      }
      reset();
    };
    return (
      <AlertDialog open={open} onOpenChange={onOpenChange}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reset Password</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to reset the password of this user?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="flex flex-col gap-2">
            <Input
              type="password"
              placeholder="New Password"
              {...register("Password")}
            />
            {errors.Password && (
              <p className="text-sm text-destructive">
                {errors.Password?.message}
              </p>
            )}
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleResetPassword}>
              Reset
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  return (
    <div className="space-y-4 p-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4 justify-between w-full py-2">
          <div className="flex gap-2">
            <Select value={searchField} onValueChange={setSearchField}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Search in..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Fields</SelectItem>
                <SelectItem value="name">First Name</SelectItem>
                <SelectItem value="lastName">Last Name</SelectItem>
                <SelectItem value="email">Email</SelectItem>
                <SelectItem value="phone">Phone</SelectItem>
              </SelectContent>
            </Select>
            <Input
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-[300px]"
            />
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <SlidersHorizontal className="h-4 w-4" />
                  Filters
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium leading-none">Filters</h4>
                    <p className="text-sm text-muted-foreground">
                      Select filters to apply to the user list
                    </p>
                  </div>
                  <div className="grid gap-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="hasPhone"
                        checked={filters.hasPhone}
                        onCheckedChange={(checked) =>
                          setFilters({
                            ...filters,
                            hasPhone: checked as boolean,
                          })
                        }
                      />
                      <label
                        htmlFor="hasPhone"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Has phone number
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="hasDob"
                        checked={filters.hasDob}
                        onCheckedChange={(checked) =>
                          setFilters({ ...filters, hasDob: checked as boolean })
                        }
                      />
                      <label
                        htmlFor="hasDob"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Has date of birth
                      </label>
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <Label>Sort by</Label>
                    <Select
                      value={filters.sortBy}
                      onValueChange={(value) =>
                        setFilters({ ...filters, sortBy: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="name">Name</SelectItem>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="dob">Date of Birth</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant={
                          filters.sortOrder === "asc" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() =>
                          setFilters({ ...filters, sortOrder: "asc" })
                        }
                      >
                        Ascending
                      </Button>
                      <Button
                        variant={
                          filters.sortOrder === "desc" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() =>
                          setFilters({ ...filters, sortOrder: "desc" })
                        }
                      >
                        Descending
                      </Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button
                onClick={() => {
                  setOpen(true);
                  setSelectedUser(null);
                  setValue("Name", "");
                  setValue("Email", "");
                  setValue("Password", "");
                  setValue("UserType", "Subscriber");
                  setValue("PhoneNumber", "");
                  setValue("Gender", "Other");
                  setValue("DateOfBirth", "");
                  setValue("Address", "");
                  setValue("GivenName", "");
                  setValue("FamilyName", "");
                  setValue("MiddleName", "");
                  reset();
                }}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add User
              </Button>
            </SheetTrigger>
            <SheetContent className="w-full sm:max-w-lg p-4">
              <SheetHeader>
                <SheetTitle>Create New User</SheetTitle>
                <SheetDescription>
                  Add a new user to the system
                </SheetDescription>
              </SheetHeader>
              <UserForm />
            </SheetContent>
          </Sheet>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Name</TableHead>
              <TableHead className="hidden md:table-cell">Last Name</TableHead>
              <TableHead className="hidden md:table-cell">Email</TableHead>
              <TableHead className="hidden md:table-cell">Phone</TableHead>
              <TableHead className="hidden lg:table-cell">
                Date of Birth
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentUsers.map((user) => (
              <TableRow key={user.UserID}>
                <TableCell
                  className="font-medium cursor-pointer"
                  onClick={() => {
                    router.push(`/users/${user.UserID}`);
                  }}
                >
                  <div>{user.Name}</div>
                  <div className="text-sm text-muted-foreground md:hidden">
                    {user.Email}
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  {user.FamilyName}
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  {user.Email}
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  {user.PhoneNumber || "-"}
                </TableCell>
                <TableCell className="hidden lg:table-cell">
                  {user.DateOfBirth
                    ? format(new Date(user.DateOfBirth), "PP")
                    : "-"}
                </TableCell>
                <TableCell className="text-right max-w-30">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedUser(user);
                        setResetPasswordOpen(true);
                      }}
                    >
                      <RotateCcw className="mr-2 h-4 w-4" />
                      Reset Pass
                    </Button>
                    <Sheet
                      open={open && selectedUser !== null}
                      onOpenChange={setOpen}
                    >
                      <SheetTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-[70px]"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedUser(user);
                            setValue("Name", user.Name || "");
                            setValue("Email", user.Email);
                            setValue("Password", "");
                            setValue("PhoneNumber", user.PhoneNumber || "");
                            setValue("Gender", user.Gender || "Other");
                            setValue("Address", user.Address || "");
                            setValue("GivenName", user.GivenName || "");
                            setValue("FamilyName", user.FamilyName || "");
                            setValue("MiddleName", user.MiddleName || "");
                            setValue("Website", user.Website || "");
                            setOpen(true);
                          }}
                        >
                          Edit
                        </Button>
                      </SheetTrigger>
                      <SheetContent
                        className="w-full sm:max-w-lg p-4"
                        onInteractOutside={(e) => {
                          // Prevent closing when typing in inputs
                          if (
                            e.target instanceof HTMLInputElement ||
                            e.target instanceof HTMLSelectElement ||
                            e.target instanceof HTMLTextAreaElement
                          ) {
                            e.preventDefault();
                          }
                        }}
                      >
                        <SheetHeader>
                          <SheetTitle>Edit User</SheetTitle>
                          <SheetDescription>
                            Update user information
                          </SheetDescription>
                        </SheetHeader>
                        <UserForm />
                      </SheetContent>
                    </Sheet>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                />
              </PaginationItem>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={currentPage === page}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                )
              )}
              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    setCurrentPage((p) => Math.min(totalPages, p + 1))
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
      <ResetUserPasswordAlertDialog
        open={resetPasswordOpen}
        onOpenChange={setResetPasswordOpen}
        selectedUser={selectedUser}
      />
    </div>
  );
}
