import { OmitType, PartialType } from '@nestjs/mapped-types';
import { PopQuizQuestion } from '../entities/question.entity';
import { PopQuiz } from '../entities/pop-quiz.entity';

// [Question]
export class CreateQuestionDto extends OmitType(PopQuizQuestion, ['QuestionID']) {}
export class UpdateQuestionDto extends PartialType(CreateQuestionDto) {
  QuestionID: string;
}

export class ListQuestionsDto {
  QuestionIDs?: string[];
  SourceIDs?: string[];
  Tags?: string[];
  ProjectID: string;
  TenantID: string;
}

export class CommonQuestionDto {
  QuestionID: string;
  ProjectID: string;
  TenantID: string;
  SourceID?: string;
}

// [PopQuiz]
export class CreatePopQuizDto extends OmitType(PopQuiz, ['PopQuizID']) {}

export class GeneratePopQuizRandomQuestionsDto {
  QuestionCount: number;
  ProjectID: string;
  TenantID: string;
  Scopes: Record<string, string>;
  SubscriberIDs?: string[];
}

export class UpdatePopQuizDto extends PartialType(CreatePopQuizDto) {
  PopQuizID: string;
}

export class ListPopQuizDto {
  PopQuizIDs?: string[];
  ProjectID: string;
  TenantID: string;
  SubscriberIDs?: string[];
}

export class GetPopQuizDto {
  PopQuizID: string;
  TenantID: string;
}

export class CommonPopQuizDto {
  PopQuizID: string;
  ProjectID: string;
  TenantID: string;
}

// [Response]

class Answer {
  QuestionID: string;
  OptionIDs: string[]; // For Now it'll be single option
}
export class CreatePopQuizResponseDto {
  PopQuizID: string;
  Answers: Answer[];
  UserID: string;
  TenantID: string;
}

export class PreFlightPopQuizDto {
  UserID: string;
  TenantID: string;
}

export class GetMyPopQuizResponsesDto {
  UserID: string;
  TenantID: string;
  ProjectID?: string;
  PopQuizID?: string;
}

export class GetResponseDto {
  ResponseID: string;
  UserID: string;
  TenantID: string;
}

export class GetResponsesForPopQuizDto {
  PopQuizID: string;
  TenantID: string;
}

export class ListMyQuizesDto {
  UserID: string;
  TenantID: string;
  ProjectID?: string;
}

export class GenerateSelfQuizDto {
  UserID: string;
  QuestionCount: number;
  ProjectID: string;
  TenantID: string;
}
