import { TextToSpeechClient } from '@google-cloud/text-to-speech';
import { google } from '@google-cloud/text-to-speech/build/protos/protos';
import TTSBlueprint, { IListVoicesResponse, ISynthesizeSpeechParams } from './_.blueprint';
import { AudioFormat } from '../entities/tt.entity';
import { ConfigService } from '@nestjs/config';

//  White Spaces are Considered as Characters
//  SSML Tags except <mark/> is counted as characters

const SupportVoices = [
  {
    LanguageCode: 'en-IN',
    Accent: 'Indian',
    Name: 'en-IN-Neural2-A',
    Gender: 'FEMALE',
    VoiceModel: 'Neural2',
    PricePerCharacter: 0.000016, // 16usd per 1m characters
  },
  {
    LanguageCode: 'en-IN',
    Accent: 'Indian',
    Name: 'en-IN-Neural2-B',
    Gender: 'MALE',
    VoiceModel: 'Neural2',
    PricePerCharacter: 0.000016, // 16usd per 1m characters
  },
  {
    LanguageCode: 'en-US',
    Accent: 'American',
    Name: 'en-US-Neural2-A',
    Gender: 'FEMALE',
    VoiceModel: 'Neural2',
    PricePerCharacter: 0.000016, // 16usd per 1m characters
  },
  {
    LanguageCode: 'en-US',
    Accent: 'American',
    Name: 'en-US-Neural2-B',
    Gender: 'MALE',
    VoiceModel: 'Neural2',
    PricePerCharacter: 0.000016, // 16usd per 1m characters
  },
  {
    LanguageCode: 'en-IN',
    Accent: 'Indian',
    Name: 'en-IN-Standard-A',
    Gender: 'FEMALE',
    VoiceModel: 'Standard',
    PricePerCharacter: 0.000004, // 4usd per 1m characters
  },
  {
    LanguageCode: 'en-IN',
    Accent: 'Indian',
    Name: 'en-IN-Standard-B',
    Gender: 'MALE',
    VoiceModel: 'Standard',
    PricePerCharacter: 0.000004, // 4usd per 1m characters
  },
  {
    LanguageCode: 'en-US',
    Accent: 'Indian',
    Name: 'en-IN-Wavenet-E',
    Gender: 'FEMALE',
    VoiceModel: 'Wavenet',
    PricePerCharacter: 0.000016, // 16usd per 1m characters
  },
  {
    LanguageCode: 'en-US',
    Accent: 'Indian',
    Name: 'en-IN-Wavenet-F',
    Gender: 'MALE',
    VoiceModel: 'Wavenet',
    PricePerCharacter: 0.000016, // 16usd per 1m characters
  },
];

export interface ISynthesizeSpeechGoogleParams extends ISynthesizeSpeechParams {
  Encoding: AudioFormat;
}

export type CostEstimationParams = {
  Text: string;
  ModelName: string;
};
export type CostEstimationType = {
  Cost: number;
  Currency: string;
  PerCharacter: number;
};

export type ISynthesizeSpeechResponse = {
  AudioContent: Buffer;
  Cost: CostEstimationType;
};

export class GoogleCloudTTSAdapter
  implements
    TTSBlueprint<
      IListVoicesResponse[],
      object,
      ISynthesizeSpeechResponse,
      ISynthesizeSpeechGoogleParams,
      CostEstimationType,
      CostEstimationParams
    >
{
  Client: TextToSpeechClient;

  private MAXIMUM_BYTE_LENGTH = 1000;

  constructor(private readonly ConfigService: ConfigService) {
    this.Client = new TextToSpeechClient({
      apiKey: this.ConfigService.get('GOOGLE_TTS'),
    });
  }

  async ListVoices() {
    return SupportVoices;
  }

  CountCharacter(Text: string) {
    // ignore <mark> tags
    Text = Text?.replace(/<mark[^>]*>.*?<\/mark>/g, '');
    return Text.length;
  }

  CountBytes(Text: string) {
    return Buffer.from(Text).length;
  }

  DetectSSML(Text: string) {
    return Text?.includes('<speak>') && Text?.includes('</speak>');
  }

  SplitTextIntoChunks(Text: string, MaxChunkSize = this.MAXIMUM_BYTE_LENGTH) {
    // chunk text

    return Text.match(new RegExp(`.{1,${MaxChunkSize}}`, 'g')) || [];
  }

  SplitSSMLIntoChunks(Text: string) {
    const chunks: string[] = [];
    let currentChunk = '';

    // Split SSML by <p>...</p> or <s>...</s> or by simple tags
    const parts = Text.replace(/<\/speak>/gi, '') // remove outer </speak> if present
      .replace(/<speak[^>]*>/gi, '') // remove outer <speak> if present
      .split(/(?=<p>|<s>)/i); // split at <p> or <s> tags

    for (const part of parts) {
      if (currentChunk.length + part.length > this.MAXIMUM_BYTE_LENGTH) {
        // Wrap the chunk
        chunks.push(`<speak>${currentChunk.trim()}</speak>`);
        currentChunk = part;
      } else {
        currentChunk += part;
      }
    }

    if (currentChunk.trim().length > 0) {
      chunks.push(`<speak>${currentChunk.trim()}</speak>`);
    }

    return chunks;
  }

  async SynthesizeSpeech(params: ISynthesizeSpeechGoogleParams): Promise<ISynthesizeSpeechResponse> {
    console.log(`🔊 [GoogleCloudTTS] Synthesizing with Model ${params.Voice}`);

    // if text is more than 1000 characters, enable audio stitching
    const EnableAudioStitching = this.CountBytes(params.Text) > this.MAXIMUM_BYTE_LENGTH;
    const SSML = this.DetectSSML(params.Text);

    if (!EnableAudioStitching) {
      const [response] = await this.Client.synthesizeSpeech({
        input: {
          ...(SSML ? { ssml: params.Text } : { text: params.Text }),
        },
        voice: { languageCode: params.LanguageCode, name: params.Voice },
        audioConfig: {
          audioEncoding: params.Encoding as unknown as google.cloud.texttospeech.v1.AudioEncoding,
          pitch: params.Pitch, // How high or low the voice is
          speakingRate: params.SpeechRate, // How fast or slow the voice is
          volumeGainDb: params.VolumeGainDb, // How loud or quiet the voice is
        },
      });
      return {
        AudioContent: Buffer.from(response.audioContent),
        Cost: await this.GenerateCostEstimation({ Text: params.Text, ModelName: params.Voice }),
      };
    }

    const Chunks = SSML ? this.SplitSSMLIntoChunks(params.Text) : this.SplitTextIntoChunks(params.Text);
    const audioBuffers: Buffer[] = [];
    for (const Chunk of Chunks) {
      console.log(`🔊 [GoogleCloudTTS] Chunk size: ${this.CountBytes(Chunk)}`);
      const [response] = await this.Client.synthesizeSpeech({
        input: {
          ...(SSML ? { ssml: Chunk } : { text: Chunk }),
        },
        voice: { languageCode: params.LanguageCode, name: params.Voice },
        audioConfig: {
          audioEncoding: params.Encoding as unknown as google.cloud.texttospeech.v1.AudioEncoding,
          pitch: params.Pitch, // How high or low the voice is
          speakingRate: params.SpeechRate, // How fast or slow the voice is
          volumeGainDb: params.VolumeGainDb, // How loud or quiet the voice is
        },
      });
      audioBuffers.push(Buffer.from(response.audioContent));
    }
    const AudioContent = Buffer.concat(audioBuffers);
    return {
      AudioContent,
      Cost: await this.GenerateCostEstimation({ Text: params.Text, ModelName: params.Voice }),
    };
  }

  async GenerateCostEstimation(params: CostEstimationParams): Promise<CostEstimationType> {
    const Voice = SupportVoices.find((Voice) => Voice.Name === params.ModelName);
    if (!Voice) {
      throw new Error('Voice not found');
    }
    const CharacterCount = this.CountCharacter(params.Text);
    const Price = Voice.PricePerCharacter * CharacterCount;
    console.log(`🔊 [GoogleCloudTTS] Generated cost estimation: ${Price} for ${CharacterCount} characters`);
    return {
      Cost: Price,
      Currency: 'USD',
      PerCharacter: Voice.PricePerCharacter,
    };
  }
}
