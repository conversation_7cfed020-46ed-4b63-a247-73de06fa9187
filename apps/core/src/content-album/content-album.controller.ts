import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { TenantID, User } from '../../common/decorators/core.decorators';
import { AuthAdminGuard } from '../../common/guards/auth.guard';
import { ContentAlbumService } from './content-album.service';
import {
  CreateContentAlbumRequestDto,
  GenerateContentAlbumRequestDto,
  UpdateContentAlbumRequestDto,
  UpdateContentMetaRequestDto,
  UploadContentRequestDto,
} from './dto/content-album.controller.dto';
import { TContent } from './entities/content-album.entity';
import { TUser } from '../core/user/entities/user.entity';

@UseGuards(AuthAdminGuard)
@Controller('content-album')
export class ContentAlbumController {
  constructor(private readonly contentAlbumService: ContentAlbumService) {}

  @Post()
  async CreateContentAlbum(@Body() Params: CreateContentAlbumRequestDto, @TenantID() TenantID: string) {
    const createdContentAlbum = await this.contentAlbumService.CreateContentAlbum({ ...Params, TenantID });
    return {
      message: 'Content album created successfully',
      ack: createdContentAlbum,
    };
  }

  @Patch(':ContentAlbumID')
  async UpdateContentAlbum(
    @Body() Params: UpdateContentAlbumRequestDto,
    @Param('ContentAlbumID') ContentAlbumID: string,
    @TenantID() TenantID: string
  ) {
    const updatedContentAlbum = await this.contentAlbumService.UpdateContentAlbum({
      ...Params,
      ContentAlbumID,
      TenantID,
    });
    return {
      message: 'Content album updated successfully',
      ack: updatedContentAlbum,
    };
  }

  @Delete(':ContentAlbumID')
  async DeleteContentAlbum(@Param('ContentAlbumID') ContentAlbumID: string, @TenantID() TenantID: string) {
    const deletedContentAlbum = await this.contentAlbumService.DeleteContentAlbum({ ContentAlbumID, TenantID });
    return {
      message: 'Content album deleted successfully',
      ack: deletedContentAlbum,
    };
  }

  @Get()
  async ListContentAlbums(@TenantID() TenantID: string, @Query('ProjectID') ProjectID?: string) {
    const contentAlbums = await this.contentAlbumService.ListContentAlbums({ TenantID, ProjectID });
    return contentAlbums;
  }

  @Get(':ContentAlbumID')
  async GetContentAlbum(@Param('ContentAlbumID') ContentAlbumID: string, @TenantID() TenantID: string) {
    const contentAlbum = await this.contentAlbumService.GetContentAlbum({ ContentAlbumID, TenantID });
    return contentAlbum;
  }

  /**
   * Create Raw Content
   * @param Params
   * @param ContentAlbumID
   * @param TenantID
   * @returns
   */
  @Post(':ContentAlbumID/content')
  async CreateContent(
    @Body() Params: TContent,
    @Param('ContentAlbumID') ContentAlbumID: string,
    @TenantID() TenantID: string
  ) {
    const createdContent = await this.contentAlbumService.CreateContentRaw({
      ...Params,
      ContentAlbumID,
      TenantID,
    });
    return {
      message: 'Content created successfully',
      ack: createdContent,
    };
  }

  @Put(':ContentAlbumID/content')
  @UseInterceptors(FileInterceptor('Content'))
  async UploadContent(
    @Body() Params: UploadContentRequestDto,
    @Param('ContentAlbumID') ContentAlbumID: string,
    @TenantID() TenantID: string,
    @UploadedFile() file: Express.Multer.File
  ) {
    const createdContent = await this.contentAlbumService.CreateContent({
      ...Params,
      Meta: {
        Title: Params.Title,
        Description: Params?.Description,
        Transcript: Params?.Transcript,
        Attachments: Params?.Attachments,
      },
      TenantID,
      Content: file,
      ContentAlbumID,
    });
    return {
      message: 'Content created successfully',
      ack: createdContent,
    };
  }

  @Put(':ContentAlbumID/content/:ContentID')
  @UseInterceptors(FileInterceptor('Content'))
  async UploadContentMedia(
    @Body() Params: UploadContentRequestDto,
    @Param('ContentAlbumID') ContentAlbumID: string,
    @Param('ContentID') ContentID: string,
    @TenantID() TenantID: string,
    @UploadedFile() file: Express.Multer.File
  ) {
    const updatedContent = await this.contentAlbumService.UploadContentMedia({
      ...Params,
      TenantID,
      ContentAlbumID,
      ContentID,
      Content: file,
    });
    return {
      message: 'Content updated successfully',
      ack: updatedContent,
    };
  }

  @Patch(':ContentAlbumID/content/:ContentID')
  async UpdateContentMeta(
    @Body() Params: UpdateContentMetaRequestDto,
    @Param('ContentAlbumID') ContentAlbumID: string,
    @Param('ContentID') ContentID: string,
    @TenantID() TenantID: string
  ) {
    const updatedContent = await this.contentAlbumService.UpdateContentMeta({
      ...Params,
      TenantID,
      ContentAlbumID,
      ContentID,
    });
    return {
      message: 'Content updated successfully',
      ack: updatedContent,
    };
  }

  @Get(':ContentAlbumID/content/:ContentID')
  async GetContent(
    @Param('ContentAlbumID') ContentAlbumID: string,
    @Param('ContentID') ContentID: string,
    @TenantID() TenantID: string
  ) {
    const content = await this.contentAlbumService.GetContent({ ContentAlbumID, ContentID, TenantID });
    return content;
  }

  @Delete(':ContentAlbumID/content/:ContentID')
  async DeleteContent(
    @Param('ContentAlbumID') ContentAlbumID: string,
    @Param('ContentID') ContentID: string,
    @TenantID() TenantID: string
  ) {
    const deletedContent = await this.contentAlbumService.DeleteContent({ ContentAlbumID, ContentID, TenantID });
    return {
      message: 'Content deleted successfully',
      ack: deletedContent,
    };
  }

  @Post('/generate')
  async GenerateContentAlbum(
    @Body() Params: GenerateContentAlbumRequestDto,
    @TenantID() TenantID: string,
    @User() User: TUser
  ) {
    const generatedContentAlbum = await this.contentAlbumService.GenerateContentAlbum(
      { ...Params, TenantID },
      User.UserID,
      User.AppType
    );
    return generatedContentAlbum;
  }
}
