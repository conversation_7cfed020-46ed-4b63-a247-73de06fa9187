import { DeleteObjectCommand, GetObjectCommand, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import * as fs from 'fs';
import { Model } from 'mongoose';
import * as mm from 'music-metadata';
import * as path from 'path';
import * as util from 'util';
import { omit } from 'radash';
import GenerateID from '../../common/lib/generate-id';
import {
  BaseProcessParams,
  GenerateCostEstimationDto,
  ListProcessesDto,
  NewProcessDto,
  QuickCreditEstimationDto,
} from './dto/tts.service.dto';
import { AudioFormat } from './entities/tt.entity';
import TTSAdapter from './provider/tts.adapter';
import { TTSModel } from './schema/tts.schema';

@Injectable()
export class TTSService {
  private readonly TTSAdapter = new TTSAdapter(this.configService);

  private readonly S3_EXPIRATION = 604800; // 1 week
  private readonly AUDIO_ENCODING = AudioFormat.Enum.MP3;
  private s3Client: S3Client;

  constructor(
    @InjectModel(TTSModel.name) private readonly TTSModel: Model<TTSModel>,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2
  ) {
    this.s3Client = new S3Client({
      region: this.configService.get('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      },
    });
    this.TTSAdapter = new TTSAdapter(this.configService);
  }

  async ListVoices() {
    // List Voices Across All Providers
    return {
      Google: await this.TTSAdapter.ListVoices({ Provider: 'Google' }),
      ElevenLabs: await this.TTSAdapter.ListVoices({ Provider: 'ElevenLabs' }),
    };
  }

  async GenerateCostEstimation(Params: GenerateCostEstimationDto) {
    return this.TTSAdapter.GenerateCostEstimation({
      Provider: Params.Provider,
      Text: Params.Text,
      ModelName: Params.ModelName,
    });
  }

  CountCharacter(Text: string) {
    // Remove white space
    return Text.replace(/\s/g, '').length;
  }

  async NewProcess(Params: NewProcessDto) {
    // Get Relevant Provider
    const CostEstimation = await this.TTSAdapter.GenerateCostEstimation({
      ...Params.TTSParams,
      Provider: Params.Provider,
      Text: Params.SpeechText,
      ModelName: Params.AudioConfig.VoiceModel,
    });
    // Schedule Process
    const createNewProcess = new this.TTSModel({
      Name: Params.Name,
      Summary: Params.Summary,
      SpeechText: Params.SpeechText,
      AudioConfig: Params.AudioConfig,
      Provider: Params.Provider,
      TTSParams: {
        ...Params.TTSParams,
        ...Params.AudioConfig,
        Language: Params.AudioConfig.Language,
        Encoding: this.AUDIO_ENCODING,
      },
      Status: 'Queued',
      CharacterCount: this.CountCharacter(Params.SpeechText),
      TenantID: Params.TenantID,
      Scopes: Params.Scopes,
      CreditUsage: {
        Cost: CostEstimation.Cost,
        Currency: CostEstimation.Currency,
        PerCharacter: CostEstimation.PerCharacter,
        Credits: null,
      },
      Duration: 0,
    } as TTSModel);
    await createNewProcess.save();

    // Emit Event
    this.eventEmitter.emit('TTS.Process', {
      ProcessID: createNewProcess._id.toString(),
      TenantID: Params.TenantID,
    });

    // Schedule Process
    return createNewProcess;
  }

  @OnEvent('TTS.Process')
  async Process(Params: BaseProcessParams) {
    console.log(`🔊 [TTSService] Processing Process ${Params.ProcessID}`);
    const process = await this.TTSModel.findById(Params.ProcessID);
    if (!process) {
      throw new Error('Process not found');
    }
    // Update Process
    process.Status = 'Processing';
    process.Event = {
      StartedAt: new Date(),
      FinishedAt: null,
    };
    await process.save();

    try {
      console.log(`🧠 [TTSService] Synthesizing Speech`);
      // Synthesize Speech
      const { AudioContent } = await this.TTSAdapter.SynthesizeSpeech({
        ...process.TTSParams, // Load Provider Params
        Provider: process.Provider,
        Text: process.SpeechText,
        Voice: process.AudioConfig.VoiceModel,
        LanguageCode: process.AudioConfig.Language,
        Pitch: process.AudioConfig.Pitch,
        SpeechRate: process.AudioConfig.SpeechRate,
        VolumeGainDb: process.AudioConfig.VolumeGainDecibels,
        Encoding: this.AUDIO_ENCODING,
      });
      console.log(`✅ [TTSService] Synthesized`);

      // [Fut] Compress & HSL Streaming Optimization

      // Save to S3
      const AudioKey = `academic-lm/tts/${process.TenantID}/${process._id.toString()}.mp3`;
      const { SignedUrl, Location, Key } = await this.SaveToS3({
        AudioContent,
        Key: AudioKey,
      });

      console.log(`💾 [TTSService] Saved to S3`);

      // Distribute CDN
      console.log(`📡 [TTSService] Distributing CDN`);

      // Update Process
      process.Status = 'Completed';
      process.Event = {
        StartedAt: process.Event.StartedAt,
        FinishedAt: new Date(),
      };
      const { FileSize, Duration } = await this.AudioStats({ AudioContent: AudioContent });
      console.log(`💽 [TTSService] Audio Stats ${FileSize / 1024} KB, ${Duration} seconds`);
      process.S3 = {
        Location: Location,
        Key: Key,
        AudioFormat: this.AUDIO_ENCODING,
        FileSize: FileSize / 1024, // in KB
        Expiration: new Date(Date.now() + this.S3_EXPIRATION * 1000),
        AccessUrl: SignedUrl,
      };
      process.Duration = Duration;
      await process.save();
    } catch (error) {
      console.log(`❌ [TTSService] Error: ${error.message}`);
      process.Status = 'Failed';
      process.Event = {
        StartedAt: process.Event.StartedAt,
        FinishedAt: new Date(),
        FailedReason: error.message,
      };
      await process.save();
    }
  }

  /**
   * Analyze Audio
   * @param Params
   * @returns Size, Duration
   */
  async AudioStats(Params: { AudioContent: Buffer }) {
    // Save Audio File To Local
    const tempPath = path.join(__dirname, 'tts-audio');
    if (!fs.existsSync(tempPath)) {
      fs.mkdirSync(tempPath);
    }
    const filePath = `${tempPath}/${GenerateID.Generate10Hex('aud')}.mp3`;
    const writeFile = util.promisify(fs.writeFile);
    await writeFile(filePath, Params.AudioContent, 'binary');

    // Get Audio Stats
    const metadata = await mm.parseFile(filePath);
    const fileSize = fs.statSync(filePath).size;
    const durationInSeconds = metadata.format.duration;
    // Delete Temp File
    fs.unlinkSync(filePath);
    return {
      FileSize: fileSize,
      Duration: durationInSeconds,
    };
  }

  async SaveToS3(Params: { AudioContent: Buffer; Key: string }) {
    const Bucket = this.configService.get('S3_NXTCAMPUS_PLATFORM');
    const upload = await this.s3Client.send(
      new PutObjectCommand({
        Bucket,
        Key: Params.Key,
        Body: Params.AudioContent,
        ContentType: 'audio/mpeg',
      })
    );

    // Generate presigned URL
    const command = new GetObjectCommand({
      Bucket,
      Key: Params.Key,
    });
    const presignedUrl = await getSignedUrl(this.s3Client, command, { expiresIn: this.S3_EXPIRATION });

    const location = `https://${Bucket}.s3.${this.configService.get('AWS_REGION')}.amazonaws.com/${Params.Key}`;

    return {
      SignedUrl: presignedUrl,
      Location: location,
      Key: Params.Key,
    };
  }

  async ListProcesses(Params: ListProcessesDto) {
    // list all and renew access url if expired

    const ScopeKeys = omit(Params, ['TenantID', 'Status', 'Provider']);
    const ScopeFilters = Object.keys(ScopeKeys).reduce(
      (acc, key) => {
        acc[`Scopes.${key}`] = ScopeKeys[key];
        return acc;
      },
      {} as Record<string, string | number | boolean>
    );
    const processes = await this.TTSModel.find({
      TenantID: Params.TenantID,
      ...(Params.Status ? { Status: Params.Status } : {}),
      ...(Params.Provider ? { Provider: Params.Provider } : {}),
      ...ScopeFilters,
    }).select('Name Summary Status Duration CharacterCount Provider createdAt updatedAt Event');

    return processes;
  }

  async GetProcess(Params: BaseProcessParams) {
    const process = await this.TTSModel.findOne({ _id: Params.ProcessID, TenantID: Params.TenantID });
    if (!process) {
      throw new Error('Process not found');
    }

    // Renew Access Url if expired
    if (process?.S3 && process.S3.Expiration < new Date()) {
      const getObjectCommand = new GetObjectCommand({
        Bucket: this.configService.get('S3_NXTCAMPUS_PLATFORM'),
        Key: process.S3.Key,
      });
      const presignedUrl = await getSignedUrl(this.s3Client, getObjectCommand, { expiresIn: this.S3_EXPIRATION });
      process.S3.AccessUrl = presignedUrl;
      process.S3.Expiration = new Date(Date.now() + this.S3_EXPIRATION * 1000); // 1 week
      await process.save();
    }
    return process;
  }

  async DistributeCDN() {}

  async GetCreditUsage() {}

  async QuickCreditEstimation(Params: QuickCreditEstimationDto) {
    return this.TTSAdapter.GenerateCostEstimation({
      ...Params,
      Provider: Params.Provider,
      Text: Params.Text,
      ModelName: Params.ModelName,
    });
  }

  async DeleteProcess(Params: BaseProcessParams) {
    const process = await this.TTSModel.findOneAndDelete({ _id: Params.ProcessID, TenantID: Params.TenantID });

    // Delete from S3
    if (process?.S3) {
      const deleteObject = new DeleteObjectCommand({
        Bucket: this.configService.get('S3_NXTCAMPUS_PLATFORM'),
        Key: process.S3.Key,
      });
      await this.s3Client.send(deleteObject);
    }

    if (!process) {
      throw new Error('Process not found');
    }
    return process;
  }
}
