"use client";
import React, { useEffect, useState, useC<PERSON>back } from "react";
import { useRouter } from "next/navigation";
import { useProjectContext } from "@/contexts/project-context";
import { API } from "@/apis/api";
import { TPopQuiz } from "@/apis/pop-quiz.api";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";
import {
  ShieldQuestion,
  Clock,
  Calendar,
  CheckCircle2,
  RefreshCw,
} from "lucide-react";
import { toast } from "sonner";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { cn } from "@workspace/ui/lib/utils";
import { format } from "date-fns";
import { useAuth } from "@/contexts/auth-context";
import { create } from "zustand";
import { useIsMobile } from "@/hooks/use-is-mobile";
import { SidebarStore } from "../layouts/auth-layout";

// Create a store for quiz list state management
interface QuizStore {
  quizzes: TPopQuiz[];
  isLoading: boolean;
  fetchQuizzes: (projectId: string) => Promise<void>;
  setQuizzes: (quizzes: TPopQuiz[]) => void;
  lastRefreshed: number;
  triggerRefresh: () => void;
}

export const useQuizStore = create<QuizStore>((set, get) => ({
  quizzes: [],
  isLoading: false,
  lastRefreshed: Date.now(),

  setQuizzes: (quizzes) => set({ quizzes }),

  triggerRefresh: () => set({ lastRefreshed: Date.now() }),

  fetchQuizzes: async (projectId) => {
    if (!projectId) return;

    set({ isLoading: true });
    try {
      const { data, errors } = await API.POP_QUIZ.GetPopQuizList({
        ProjectID: projectId,
      });

      if (errors) {
        errors.forEach((error) => {
          toast.error(error.message || "Failed to fetch quizzes");
        });
        set({ quizzes: [], isLoading: false });
      } else if (data) {
        set({ quizzes: data, isLoading: false });
      }
    } catch (error) {
      console.error("Error fetching quizzes:", error);
      toast.error("Failed to fetch quizzes");
      set({ isLoading: false });
    }
  },
}));

const PopQuizList = () => {
  const router = useRouter();
  const { user } = useAuth();
  const { selectedProject } = useProjectContext();
  const { quizzes, isLoading, fetchQuizzes, lastRefreshed } = useQuizStore();
  const { sidebarOpen, setSidebarOpen } = SidebarStore();

  // Separate quizzes into pending and completed
  const pendingQuizzes = quizzes.filter((quiz) => {
    const userSubscription = quiz.Subscribers?.find(
      (sub) => sub.UserID === user?.UserID
    );
    return (
      (quiz.FloatingSubscribers && !userSubscription) ||
      userSubscription?.Status === "Pending"
    );
  });

  const completedQuizzes = quizzes.filter((quiz) => {
    const userSubscription = quiz.Subscribers?.find(
      (sub) => sub.UserID === user?.UserID
    );
    return userSubscription?.Status === "Submitted";
  });

  // Fetch quizzes when selected project changes or refresh is triggered
  useEffect(() => {
    if (selectedProject?.id) {
      fetchQuizzes(selectedProject.id);
    }
  }, [selectedProject?.id, fetchQuizzes, lastRefreshed]);

  const handleTriggerNewQuiz = () => {
    if (selectedProject?.id) {
      router.push(`/${selectedProject.id}/quiz`);
    }
  };

  const isMobile = useIsMobile();

  const handleQuizClick = (quizId: string) => {
    if (selectedProject?.id) {
      isMobile && setSidebarOpen(false);
      router.push(`/${selectedProject.id}/quiz/${quizId}`);
    }
  };

  const handleManualRefresh = () => {
    if (selectedProject?.id) {
      fetchQuizzes(selectedProject.id);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-2 p-4">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-8 w-full mt-4" />
        <Skeleton className="h-24 w-full" />
      </div>
    );
  }

  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-6">
        {/* Pending Quizzes Section */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            Pending ({pendingQuizzes.length})
          </h3>

          {pendingQuizzes.length === 0 ? (
            <p className="text-xs text-muted-foreground p-2">
              No pending quizzes
            </p>
          ) : (
            <div className="space-y-2">
              {pendingQuizzes.map((quiz) => (
                <div
                  key={quiz.PopQuizID}
                  className="border rounded-lg p-3 cursor-pointer hover:bg-muted/20"
                  onClick={() => handleQuizClick(quiz.PopQuizID)}
                >
                  <div className="flex justify-between items-start">
                    <h4 className="text-sm font-medium line-clamp-1">
                      {quiz.Title}
                    </h4>
                    <Badge
                      variant={quiz.IsSelfQuiz ? "outline" : "secondary"}
                      className="text-xs"
                    >
                      {quiz.IsSelfQuiz ? "Self Quiz" : "Admin Quiz"}
                    </Badge>
                  </div>

                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {quiz.Description ||
                      "Take this quiz to test your knowledge"}
                  </p>

                  <div className="flex items-center mt-2 text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3 mr-1" />
                    {quiz.createdAt
                      ? format(new Date(quiz.createdAt), "MMM d, yyyy")
                      : "Recently added"}
                    <span className="mx-2">•</span>
                    <span>{quiz.QuestionCount} questions</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Completed Quizzes Section */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground flex items-center">
            <CheckCircle2 className="h-4 w-4 mr-2" />
            Completed ({completedQuizzes.length})
          </h3>

          {completedQuizzes.length === 0 ? (
            <p className="text-xs text-muted-foreground p-2">
              No completed quizzes
            </p>
          ) : (
            <div className="space-y-2">
              {completedQuizzes.map((quiz) => (
                <div
                  key={quiz.PopQuizID}
                  className={cn(
                    "border rounded-lg p-3 cursor-pointer hover:bg-muted/20",
                    "bg-muted/10"
                  )}
                  onClick={() => handleQuizClick(quiz.PopQuizID)}
                >
                  <div className="flex justify-between items-start">
                    <h4 className="text-sm font-medium line-clamp-1">
                      {quiz.Title}
                    </h4>
                    <Badge
                      variant={quiz.IsSelfQuiz ? "outline" : "secondary"}
                      className="text-xs"
                    >
                      {quiz.IsSelfQuiz ? "Self Quiz" : "Admin Quiz"}
                    </Badge>
                  </div>

                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {quiz.Description || "Quiz completed"}
                  </p>

                  <div className="flex items-center mt-2 text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3 mr-1" />
                    {quiz.updatedAt
                      ? format(new Date(quiz.updatedAt), "MMM d, yyyy")
                      : "Recently completed"}
                    <span className="mx-2">•</span>
                    <span>{quiz.QuestionCount} questions</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </ScrollArea>
  );
};

export default PopQuizList;
