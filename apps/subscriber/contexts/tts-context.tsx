"use client";

import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useCallback,
  useEffect,
} from "react";

export type TTSProvider = "browser" | "lemonfox" | "openai" | "elevenlabs";

export interface TTSState {
  currentMessageId: string | null;
  isPlaying: boolean;
  isPaused: boolean;
  isLoading: boolean;
  provider: TTSProvider;
  progress: number; // 0-100 percentage
  duration: number; // total duration in seconds
  currentTime: number; // current time in seconds
}

export interface Citation {
  id: string;
  source_title: string;
  pdf_url: string | null;
  page: number;
  line_from: number | null;
  line_to: number | null;
  document_page_number: number;
}

export interface TTSContextType {
  // State
  ttsState: TTSState;

  // Actions
  playMessage: (
    messageId: string,
    text: string,
    citations?: Citation[],
    language?: string
  ) => Promise<void>;
  autoReadMessage: (
    messageId: string,
    text: string,
    citations?: Citation[],
    language?: string
  ) => Promise<void>;
  pauseMessage: () => void;
  resumeMessage: () => void;
  stopMessage: () => void;

  // Provider management
  setProvider: (provider: TTSProvider) => void;

  // Voice settings
  voice: string | null;
  setVoice: (voice: string) => void;
  getAvailableVoices: () => {
    name: string;
    label: string;
    provider: TTSProvider;
  }[];

  // Rate and pitch (for browser TTS)
  rate: number;
  setRate: (rate: number) => void;
  pitch: number;
  setPitch: (pitch: number) => void;
}

// Constants
const DEFAULT_RATE = 1.0;
const DEFAULT_PITCH = 1.0;
const DEFAULT_PROVIDER: TTSProvider = "lemonfox";
const PROGRESS_UPDATE_INTERVAL = 100; // ms
const ESTIMATED_CHARS_PER_SECOND = 10;
const MIN_ESTIMATED_DURATION = 2; // seconds

// Language mapping constants
const LANGUAGE_MAP: Record<string, string> = {
  // English variants
  us: "en-us",
  gb: "en-gb",

  // Hindi and Marathi variants (all use Hindi TTS)
  hi: "hi",
  mr: "hi", // Marathi uses Hindi TTS
  "romanized-hi": "hi", // Romanized Hindi uses Hindi TTS
  "romanized-mr": "hi", // Romanized Marathi uses Hindi TTS

  // Other languages
  ja: "ja", // Japanese
  es: "es", // Spanish
  fr: "fr", // French
  it: "it", // Italian
  pt: "pt-br", // Portuguese
  cn: "zh", // Chinese

  // Fallback for "other"
  other: "en-us",

  // Legacy mappings for backwards compatibility
  english: "en-us",
  en: "en-us",
  "en-us": "en-us",
  "en-gb": "en-gb",
  british: "en-gb",
  japanese: "ja",
  hindi: "hi",
  marathi: "hi",
  chinese: "zh",
  mandarin: "zh",
  zh: "zh",
  spanish: "es",
  french: "fr",
  italian: "it",
  portuguese: "pt-br",
  "pt-br": "pt-br",
};

// Default voices for each language
const DEFAULT_VOICES: Record<string, string> = {
  "en-us": "sarah",
  "en-gb": "alice",
  ja: "sakura",
  hi: "alpha",
  zh: "xiaobei",
  es: "dora",
  fr: "siwis",
  it: "sara",
  "pt-br": "clara",
};

// LemonFox voices data
const LEMONFOX_VOICES = [
  // US English (en-us)
  { name: "heart", label: "Heart (Male, US English)" },
  { name: "bella", label: "Bella (Female, US English)" },
  { name: "michael", label: "Michael (Male, US English)" },
  { name: "alloy", label: "Alloy (Female, US English)" },
  { name: "aoede", label: "Aoede (Female, US English)" },
  { name: "kore", label: "Kore (Female, US English)" },
  { name: "jessica", label: "Jessica (Female, US English)" },
  { name: "nicole", label: "Nicole (Female, US English)" },
  { name: "nova", label: "Nova (Female, US English)" },
  { name: "river", label: "River (Male, US English)" },
  { name: "sarah", label: "Sarah (Female, US English)" },
  { name: "sky", label: "Sky (Female, US English)" },
  { name: "echo", label: "Echo (Male, US English)" },
  { name: "eric", label: "Eric (Male, US English)" },
  { name: "fenrir", label: "Fenrir (Male, US English)" },
  { name: "liam", label: "Liam (Male, US English)" },
  { name: "onyx", label: "Onyx (Male, US English)" },
  { name: "puck", label: "Puck (Male, US English)" },
  { name: "adam", label: "Adam (Male, US English)" },
  { name: "santa", label: "Santa (Male, US English)" },

  // GB English (en-gb)
  { name: "alice", label: "Alice (Female, British English)" },
  { name: "emma", label: "Emma (Female, British English)" },
  { name: "isabella", label: "Isabella (Female, British English)" },
  { name: "lily", label: "Lily (Female, British English)" },
  { name: "daniel", label: "Daniel (Male, British English)" },
  { name: "fable", label: "Fable (Male, British English)" },
  { name: "george", label: "George (Male, British English)" },
  { name: "lewis", label: "Lewis (Male, British English)" },

  // Japanese (ja)
  { name: "sakura", label: "Sakura (Female, Japanese)" },
  { name: "gongitsune", label: "Gongitsune (Female, Japanese)" },
  { name: "nezumi", label: "Nezumi (Female, Japanese)" },
  { name: "tebukuro", label: "Tebukuro (Female, Japanese)" },
  { name: "kumo", label: "Kumo (Male, Japanese)" },

  // Hindi (hi)
  { name: "alpha", label: "Alpha (Female, Hindi)" },
  { name: "beta", label: "Beta (Female, Hindi)" },
  { name: "omega", label: "Omega (Male, Hindi)" },
  { name: "psi", label: "Psi (Male, Hindi)" },

  // Mandarin Chinese (zh)
  { name: "xiaobei", label: "Xiaobei (Female, Mandarin Chinese)" },

  // Spanish (es)
  { name: "dora", label: "Dora (Female, Spanish)" },

  // French (fr)
  { name: "siwis", label: "Siwis (Female, French)" },

  // Italian (it)
  { name: "sara", label: "Sara (Female, Italian)" },

  // Portuguese Brazil (pt-br)
  { name: "clara", label: "Clara (Female, Portuguese Brazil)" },
] as const;

/**
 * Maps backend OutputLanguage values to TTS-compatible language codes
 * Special handling: Hindi/Marathi variants all map to "hi" for consistent pronunciation
 */
const mapOutputLanguageToTTSLanguage = (outputLanguage?: string): string => {
  if (!outputLanguage) return "en-us";
  return LANGUAGE_MAP[outputLanguage] || "en-us";
};

/**
 * Selects the default voice for a given language
 * Used for automatic voice selection when no user preference is set
 */
const selectVoiceForLanguage = (language: string): string => {
  return DEFAULT_VOICES[language] || "sarah";
};

// Context
const TTSContext = createContext<TTSContextType | undefined>(undefined);

export const useTTS = () => {
  const context = useContext(TTSContext);
  if (!context) {
    throw new Error("useTTS must be used within a TTSProvider");
  }
  return context;
};

// Provider Component
export function TTSProvider({ children }: { children: React.ReactNode }) {
  // State
  const [ttsState, setTTSState] = useState<TTSState>({
    currentMessageId: null,
    isPlaying: false,
    isPaused: false,
    isLoading: false,
    provider: DEFAULT_PROVIDER,
    progress: 0,
    duration: 0,
    currentTime: 0,
  });

  const [voice, setVoice] = useState<string | null>(null);
  const [rate, setRate] = useState(DEFAULT_RATE);
  const [pitch, setPitch] = useState(DEFAULT_PITCH);

  // Refs for browser TTS
  const speechSynthesisRef = useRef<SpeechSynthesis | null>(null);
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

  // Refs for API TTS
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Refs for duplicate prevention and cleanup
  const currentRequestRef = useRef<string | null>(null);
  const isProcessingRef = useRef<boolean>(false);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize speech synthesis and set default voice
  useEffect(() => {
    if (typeof window === "undefined" || !("speechSynthesis" in window)) {
      return;
    }

    speechSynthesisRef.current = window.speechSynthesis;

    const setDefaultVoice = () => {
      if (ttsState.provider === "lemonfox") {
        // LemonFox default voice
        setVoice("sarah");
        return;
      }

      // Browser TTS: Look for Indian English voices first
      const voices = speechSynthesisRef.current?.getVoices() || [];

      const indianVoice = voices.find(
        (voice) =>
          voice.lang.includes("en-IN") ||
          voice.name.toLowerCase().includes("indian") ||
          voice.name.toLowerCase().includes("india")
      );

      if (indianVoice) {
        setVoice(indianVoice.name);
        return;
      }

      // Fallback to default English voice
      const englishVoice = voices.find(
        (voice) => voice.lang.startsWith("en-") && voice.default
      );

      if (englishVoice) {
        setVoice(englishVoice.name);
      }
    };

    // Set voice immediately if available, otherwise wait for voices to load
    if (speechSynthesisRef.current.getVoices().length > 0) {
      setDefaultVoice();
    } else {
      speechSynthesisRef.current.addEventListener(
        "voiceschanged",
        setDefaultVoice
      );
    }

    // Cleanup on unmount
    return () => {
      if (speechSynthesisRef.current) {
        speechSynthesisRef.current.removeEventListener(
          "voiceschanged",
          setDefaultVoice
        );
        speechSynthesisRef.current.cancel();
      }
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, [ttsState.provider]);

  /**
   * Cleans text by removing markdown formatting and other TTS-unfriendly content
   */
  const cleanTextForTTS = useCallback((text: string): string => {
    return (
      text
        // Remove markdown formatting
        .replace(/^#+\s+/gm, "") // Headers
        .replace(/\*\*(.*?)\*\*/g, "$1") // Bold
        .replace(/\*(.*?)\*/g, "$1") // Italic
        .replace(/```[\s\S]*?```/g, "") // Code blocks
        .replace(/`([^`]+)`/g, "$1") // Inline code
        .replace(/\[([^\]]+)\]\([^)]+\)/g, "$1") // Links
        .replace(/\[\[([^\]]+)\]\]/g, "") // Citation markers
        .replace(/\s+/g, " ") // Extra whitespace
        .trim()
    );
  }, []);

  /**
   * Processes text content and replaces citation markers with readable text
   * Combines citation information with content for better TTS experience
   */
  const processContentWithCitations = useCallback(
    (text: string, citations?: Citation[]): string => {
      if (!text || !citations?.length) {
        return cleanTextForTTS(text);
      }

      // Replace citation markers with readable text
      const citationRegex = /\[\[([^\]]+)\]\]/g;
      const processedText = text.replace(citationRegex, (match, citationId) => {
        const citation = citations.find((c) => c.id === citationId);
        if (citation) {
          return ` (Source: ${citation.source_title}, Page: ${citation.page}) `;
        }
        return "";
      });

      return cleanTextForTTS(processedText);
    },
    [cleanTextForTTS]
  );

  const clearProgressInterval = useCallback(() => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  }, []);

  const resetTTSState = useCallback((keepProvider = true) => {
    setTTSState((prev) => ({
      currentMessageId: null,
      isPlaying: false,
      isPaused: false,
      isLoading: false,
      provider: keepProvider ? prev.provider : DEFAULT_PROVIDER,
      progress: 0,
      duration: 0,
      currentTime: 0,
    }));
  }, []);

  // Core TTS functions
  const stopMessage = useCallback(() => {
    // Clear processing locks
    isProcessingRef.current = false;
    currentRequestRef.current = null;

    // Clear progress tracking
    clearProgressInterval();

    // Stop browser TTS
    if (speechSynthesisRef.current) {
      speechSynthesisRef.current.cancel();
    }

    // Stop and cleanup API TTS
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current = null;
    }

    // Clear utterance reference
    utteranceRef.current = null;

    // Reset state
    resetTTSState();
  }, [clearProgressInterval, resetTTSState]);

  const pauseMessage = useCallback(() => {
    if (ttsState.provider === "browser" && speechSynthesisRef.current) {
      speechSynthesisRef.current.pause();
    } else if (audioRef.current) {
      audioRef.current.pause();
    }

    clearProgressInterval();

    setTTSState((prev) => ({
      ...prev,
      isPlaying: false,
      isPaused: true,
    }));
  }, [ttsState.provider, clearProgressInterval]);

  const resumeMessage = useCallback(() => {
    if (ttsState.provider === "browser" && speechSynthesisRef.current) {
      speechSynthesisRef.current.resume();

      // Restart progress tracking for browser TTS
      if (utteranceRef.current) {
        const startTime = Date.now() - ttsState.currentTime * 1000;
        const estimatedDuration = ttsState.duration;

        progressIntervalRef.current = setInterval(() => {
          const elapsed = (Date.now() - startTime) / 1000;
          const progress = Math.min(95, (elapsed / estimatedDuration) * 100);

          setTTSState((prev) => ({
            ...prev,
            progress,
            currentTime: elapsed,
          }));
        }, PROGRESS_UPDATE_INTERVAL);
      }
    } else if (audioRef.current) {
      audioRef.current.play().catch(console.error);
    }

    setTTSState((prev) => ({
      ...prev,
      isPlaying: true,
      isPaused: false,
    }));
  }, [ttsState.provider, ttsState.currentTime, ttsState.duration]);

  const playBrowserTTS = useCallback(
    async (text: string, language?: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        if (!speechSynthesisRef.current) {
          reject(new Error("Speech synthesis not supported"));
          return;
        }

        const utterance = new SpeechSynthesisUtterance(text);
        utteranceRef.current = utterance;

        // Configure utterance
        const voices = speechSynthesisRef.current.getVoices();

        // Priority: Language-specific voice selection over user-selected voice for Hindi variants
        if (language === "hi") {
          // For Hindi language (including romanized variants), prioritize Hindi voices
          const hindiVoice =
            voices.find((v) => v.lang.startsWith("hi")) ||
            voices.find((v) => v.lang.includes("Hindi")) ||
            voices.find((v) => v.lang.startsWith("hi-IN")) ||
            voices.find((v) => v.lang.toLowerCase().includes("hindi")) ||
            voices.find((v) => v.lang.includes("IN")) ||
            voices.find((v) => v.name.toLowerCase().includes("hindi")) ||
            voices.find((v) => v.name.toLowerCase().includes("indian")) ||
            // Fallback to user's selected voice if no Hindi found
            (voice ? voices.find((v) => v.name === voice) : null);

          if (hindiVoice) {
            utterance.voice = hindiVoice;
          }
          // Always set language to Hindi even if no Hindi voice is found
          // This is crucial for romanized content
          utterance.lang = "hi";
        } else if (voice) {
          // Use user-selected voice for non-Hindi languages
          const selectedVoice = voices.find((v) => v.name === voice);
          if (selectedVoice) {
            utterance.voice = selectedVoice;
          }
        } else if (language) {
          // Auto-select a voice for other languages if no specific voice is set
          const langCode = language as string;
          const baseLang = langCode.split("-")[0];
          const languageVoice =
            voices.find((v) => v.lang.startsWith(langCode)) ||
            voices.find((v) => v.lang.startsWith(baseLang || ""));
          if (languageVoice) {
            utterance.voice = languageVoice;
          }
        }

        // Set language if provided
        if (language) {
          utterance.lang = language;
        }

        utterance.rate = rate;
        utterance.pitch = pitch;

        let isResolved = false;

        // Start event
        utterance.onstart = () => {
          const estimatedDuration = Math.max(
            MIN_ESTIMATED_DURATION,
            text.length / ESTIMATED_CHARS_PER_SECOND
          );
          const startTime = Date.now();

          setTTSState((prev) => ({
            ...prev,
            isPlaying: true,
            isLoading: false,
            progress: 0,
            currentTime: 0,
            duration: estimatedDuration,
          }));

          // Start progress tracking
          progressIntervalRef.current = setInterval(() => {
            const elapsed = (Date.now() - startTime) / 1000;
            const progress = Math.min(95, (elapsed / estimatedDuration) * 100);

            setTTSState((prev) => ({
              ...prev,
              progress,
              currentTime: elapsed,
            }));
          }, PROGRESS_UPDATE_INTERVAL);
        };

        // End event
        utterance.onend = () => {
          if (!isResolved) {
            isResolved = true;
            clearProgressInterval();

            if (utteranceRef.current === utterance) {
              setTTSState((prev) => ({
                ...prev,
                currentMessageId: null,
                isPlaying: false,
                isPaused: false,
                progress: 100,
                currentTime: prev.duration,
              }));
            }
            resolve();
          }
        };

        // Error event
        utterance.onerror = (event) => {
          if (!isResolved) {
            isResolved = true;
            clearProgressInterval();

            if (utteranceRef.current === utterance) {
              resetTTSState();
            }

            // Don't reject on intentional cancellations
            if (event.error === "canceled" || event.error === "interrupted") {
              resolve();
            } else {
              reject(new Error(`Speech synthesis error: ${event.error}`));
            }
          }
        };

        speechSynthesisRef.current.speak(utterance);
      });
    },
    [voice, rate, pitch, clearProgressInterval, resetTTSState]
  );

  const playLemonFoxTTS = useCallback(
    async (
      text: string,
      language?: string,
      voiceOverride?: string
    ): Promise<void> => {
      return new Promise(async (resolve, reject) => {
        try {
          // Stop any existing audio
          if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
            audioRef.current = null;
          }

          const apiKey = process.env.NEXT_PUBLIC_LEMON_FOX_API_KEY;
          if (!apiKey) {
            throw new Error("LemonFox API key not configured");
          }

          const selectedVoice = voiceOverride || (voice ?? "sarah");

          // API call
          const response = await fetch(
            "https://api.lemonfox.ai/v1/audio/speech",
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${apiKey}`,
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                input: text,
                voice: selectedVoice,
                response_format: "mp3",
                speed: rate,
                language: language || "en-us",
              }),
            }
          );

          if (!response.ok) {
            throw new Error(
              `LemonFox API error: ${response.status} ${response.statusText}`
            );
          }

          const audioBlob = await response.blob();
          const audioUrl = URL.createObjectURL(audioBlob);
          const audio = new Audio(audioUrl);
          audioRef.current = audio;

          let isResolved = false;

          // Audio event handlers
          audio.onloadstart = () => {
            setTTSState((prev) => ({
              ...prev,
              isPlaying: true,
              isLoading: false,
              progress: 0,
              currentTime: 0,
              duration: 0,
            }));
          };

          audio.onloadedmetadata = () => {
            setTTSState((prev) => ({
              ...prev,
              duration: audio.duration || 0,
            }));
          };

          audio.onplay = () => {
            setTTSState((prev) => ({
              ...prev,
              isPlaying: true,
              isLoading: false,
            }));

            // Start progress tracking
            progressIntervalRef.current = setInterval(() => {
              if (audio.duration && !audio.paused) {
                const progress = (audio.currentTime / audio.duration) * 100;
                setTTSState((prev) => ({
                  ...prev,
                  progress: Math.min(100, progress),
                  currentTime: audio.currentTime,
                  duration: audio.duration,
                }));
              }
            }, PROGRESS_UPDATE_INTERVAL);
          };

          audio.onpause = () => {
            clearProgressInterval();
          };

          audio.onended = () => {
            if (!isResolved) {
              isResolved = true;
              clearProgressInterval();
              URL.revokeObjectURL(audioUrl);

              if (audioRef.current === audio) {
                audioRef.current = null;
                setTTSState((prev) => ({
                  ...prev,
                  currentMessageId: null,
                  isPlaying: false,
                  isPaused: false,
                  progress: 100,
                  currentTime: prev.duration,
                }));
              }
              resolve();
            }
          };

          audio.onerror = () => {
            if (!isResolved) {
              isResolved = true;
              clearProgressInterval();
              URL.revokeObjectURL(audioUrl);

              if (audioRef.current === audio) {
                audioRef.current = null;
                resetTTSState();
              }
              reject(new Error("Audio playback failed"));
            }
          };

          // Start playback
          await audio.play();
        } catch (error) {
          reject(error);
        }
      });
    },
    [voice, rate, clearProgressInterval, resetTTSState]
  );

  // Main play function with duplicate prevention
  const playMessage = useCallback(
    async (
      messageId: string,
      text: string,
      citations?: Citation[],
      language?: string
    ) => {
      if (!text || text === "typing-indicator") return;

      // Prevent duplicate requests
      if (currentRequestRef.current === messageId || isProcessingRef.current) {
        return;
      }

      // Lock processing
      isProcessingRef.current = true;
      currentRequestRef.current = messageId;

      try {
        // Stop any current playback
        stopMessage();

        // Process text
        const cleanedText = processContentWithCitations(text, citations);
        if (!cleanedText.trim()) {
          throw new Error("No text content to speak");
        }

        // Map output language to TTS language
        const ttsLanguage = mapOutputLanguageToTTSLanguage(language);

        // Auto-select appropriate voice for the language if using LemonFox
        let selectedVoice: string | undefined = voice || undefined;
        if (ttsState.provider === "lemonfox") {
          selectedVoice = selectVoiceForLanguage(ttsLanguage);
        }

        // Set loading state
        setTTSState((prev) => ({
          ...prev,
          currentMessageId: messageId,
          isLoading: true,
          isPlaying: false,
          isPaused: false,
          progress: 0,
          currentTime: 0,
          duration: 0,
        }));

        // Play based on user's selected provider
        if (ttsState.provider === "lemonfox") {
          await playLemonFoxTTS(cleanedText, ttsLanguage, selectedVoice);
        } else {
          await playBrowserTTS(cleanedText, ttsLanguage);
        }
      } catch (error) {
        console.error("TTS playback failed:", error);
        resetTTSState();
        throw error;
      } finally {
        // Always unlock
        isProcessingRef.current = false;
        currentRequestRef.current = null;
      }
    },
    [
      ttsState.provider,
      processContentWithCitations,
      stopMessage,
      playLemonFoxTTS,
      playBrowserTTS,
      resetTTSState,
      voice,
    ]
  );

  // Auto-read function for new messages
  const autoReadMessage = useCallback(
    async (
      messageId: string,
      text: string,
      citations?: Citation[],
      language?: string
    ) => {
      if (!text || text === "typing-indicator") return;

      // Prevent duplicate calls for the same message
      if (ttsState.currentMessageId === messageId) return;

      // Use main play function (which handles stopping current playback)
      await playMessage(messageId, text, citations, language);
    },
    [playMessage, ttsState.currentMessageId]
  );

  const setProvider = useCallback(
    (provider: TTSProvider) => {
      stopMessage();
      setTTSState((prev) => ({ ...prev, provider }));
    },
    [stopMessage]
  );

  const getAvailableVoices = useCallback(() => {
    const voices: { name: string; label: string; provider: TTSProvider }[] = [];

    // LemonFox voices organized by language
    LEMONFOX_VOICES.forEach((voice) => {
      voices.push({
        name: voice.name,
        label: `${voice.label} (LemonFox)`,
        provider: "lemonfox",
      });
    });

    // Browser voices
    if (speechSynthesisRef.current) {
      const browserVoices = speechSynthesisRef.current.getVoices();
      browserVoices.forEach((voice) => {
        voices.push({
          name: voice.name,
          label: `${voice.name} (${voice.lang}) (Browser)`,
          provider: "browser",
        });
      });
    }

    return voices;
  }, []);

  // Context value
  const contextValue: TTSContextType = {
    ttsState,
    playMessage,
    autoReadMessage,
    pauseMessage,
    resumeMessage,
    stopMessage,
    setProvider,
    voice,
    setVoice,
    getAvailableVoices,
    rate,
    setRate,
    pitch,
    setPitch,
  };

  return (
    <TTSContext.Provider value={contextValue}>{children}</TTSContext.Provider>
  );
}
