import json
import uuid
from langgraph.graph import StateGraph, START, END
from agents.embedding_agent.academic.py_mu_pdf.state import (
    State, 
    InputState, 
    OutputState,
    Page,
    Toc,
    ChapterWiseRawPages,
    ChapterWiseTopics,
    Topic,
    SummarizedChunks,
    ChapterSummaries,
    BookSummary,
    ChapterHighlight,
    PageMetadata,
    TocOutput,
    ChunkedPage,
    ChunkMetadata,
    SourceMetadata,
    ChunkedPageMetadata,
    ChapterMetaData,
)
from agents.embedding_agent.academic.py_mu_pdf.prompts import (
    DERIVE_CONTENTS_PROMPT,
    DERIVE_TABLE_OF_CONTENTS_PROMPT,
    SUMMARIZE_CHUNKS_PROMPT,
    GENERATE_CHAPTER_SUMMARIES_PROMPT,
    GENERATE_BOOK_SUMMARY_PROMPT
)
from agents.common.utils import get_llm, get_vector_store
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
import fitz

def parse_pdf(state: State) -> State:
    """Parse PDF content and extract pages with metadata"""
    print("Parsing PDF")
    try:
        # Create a PDF document from bytes
        dev_to_eng = str.maketrans("१२३४५६७८९०", "1234567890")
        doc = fitz.open(stream=state.source_content, filetype="pdf")
        
        pages = []
        for page_num in range(len(doc)):
            page = doc[page_num]
            text = page.get_text()
            
            page_obj = Page(
                page_content=text.translate(dev_to_eng),
                metadata=PageMetadata(
                    source_name=state.source_name,
                    page_number=page_num + 1  # 1-indexed
                )
            )
            pages.append(page_obj)
        
        doc.close()
        state.pages = pages
        return state
        
    except Exception as e:
        print(f"Error parsing PDF: {e}")
        state.pages = []
        return state


def derive_table_of_contents(state: State) -> State:
    """Derive table of contents from specified pages"""
    print("Deriving table of contents")
    try:
        # Filter pages that contain table of contents
        toc_pages = [page for page in state.pages 
                    if page.metadata.page_number in state.toc_pages]
        
        if not toc_pages:
            state.toc = []
            state.chapter_wise_raw_pages = []
            state.chapter_wise_topics = []
            return state
        
        # Prepare documents for LLM
        toc_documents = []
        for page in toc_pages:
            toc_documents.append({
                "pageContent": page.page_content,
                "metadata": {"page_number": page.metadata.page_number}
            })
        
        llm = get_llm()
        
        # # Format prompt
        prompt = DERIVE_CONTENTS_PROMPT.format(
            documents=json.dumps(toc_documents),
            total_pages=len(state.pages),
            language=state.language
        )
        # Get structured output for table of contents
        response = llm.with_structured_output(TocOutput).invoke(prompt)
        toc_data = response["tableOfContent"]
        toc = []
        # Create table of contents objects
        chapter_wise_raw_pages = []
        
        for chapter_data in toc_data:
            chapter_id = f"chap_{uuid.uuid4().hex[:16]}"
            
            toc_entry = Toc(
                chapter_id=chapter_id,
                chapter_number=chapter_data.get("chapter_number"),
                chapter_name=chapter_data.get("chapter_name"),
                page_from=chapter_data.get("page_from"),
                page_to=chapter_data.get("page_to"),
                document_page_from=chapter_data.get("page_from", 0) + state.page_number_offset,
                document_page_to=chapter_data.get("page_to", 0) + state.page_number_offset
            )
            toc.append(toc_entry)
            
            # Get pages for this chapter
            chapter_pages = [
                page for page in state.pages
                if (toc_entry.document_page_from <= page.metadata.page_number <= 
                    toc_entry.document_page_to)
            ]
            
            chapter_wise_pages = ChapterWiseRawPages(
                chapter_id=chapter_id,
                chapter_number=toc_entry.chapter_number,
                chapter_name=toc_entry.chapter_name,
                page_from=toc_entry.page_from,
                page_to=toc_entry.page_to,
                document_page_from=toc_entry.document_page_from,
                document_page_to=toc_entry.document_page_to,
                pages=chapter_pages
            )
            chapter_wise_raw_pages.append(chapter_wise_pages)
        
        state.toc = toc
        state.chapter_wise_raw_pages = chapter_wise_raw_pages
        
        # Derive topics for each chapter
        chapter_wise_topics = []
        for chapter in chapter_wise_raw_pages:
            try:
                chapter_json = json.dumps({
                        "chapter_id": chapter.chapter_id,
                        "chapter_name": chapter.chapter_name,
                        "docs": [
                            {
                                "pageContent": page.page_content,
                                "metadata": {"page_number": page.metadata.page_number}
                            }
                            for page in chapter.pages
                        ]
                    },ensure_ascii=False)
                
                chapter_prompt = DERIVE_TABLE_OF_CONTENTS_PROMPT.format(
                    chapter=chapter_json,
                    language=state.language
                )
                
                topic_response = llm.with_structured_output(ChapterWiseTopics).invoke(chapter_prompt)
                topic_data = topic_response
                topics = []
                for topic_item in topic_data.get("topics", []):
                    topic = Topic(
                        chapter_name=topic_item.get("chapterName", ""),
                        topic=topic_item.get("topic", ""),
                        page_number=topic_item.get("pageNumber", 0),
                        sub_topics=topic_item.get("subTopics", [])
                    )
                    topics.append(topic)
                
                chapter_topics = ChapterWiseTopics(
                    chapter_id=chapter.chapter_id,
                    topics=topics
                )
                chapter_wise_topics.append(chapter_topics)
                
            except Exception as e:
                print(f"Error deriving topics for chapter {chapter.chapter_id}: {e}")
                # Add empty topics for this chapter
                chapter_topics = ChapterWiseTopics(
                    chapter_id=chapter.chapter_id,
                    topics=[]
                )
                chapter_wise_topics.append(chapter_topics)
        
        state.chapter_wise_topics = chapter_wise_topics
        return state
        
    except Exception as e:
        print(f"Error deriving table of contents: {e}")
        state.toc = []
        state.chapter_wise_raw_pages = []
        state.chapter_wise_topics = []
        return state


def generate_chunks(state: State) -> State:
    """Generate chunks from chapter pages"""
    print("Generating chunks")
    try:
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=700,
            chunk_overlap=100,
            separators=['\n\n', '\n', '.']
        )
        
        all_chunked_pages = []
        
        for chapter in state.chapter_wise_raw_pages:
            # Split into chunks
            chunked_documents = text_splitter.split_documents([Document(page_content=page.page_content, metadata={"page_number": page.metadata.page_number}) for page in chapter.pages])
            
            for i, doc in enumerate(chunked_documents):
                chunk_id = f"cnk_{uuid.uuid4().hex[:16]}"
                chunked_page = ChunkedPage(
                    page_content=doc.page_content,
                    metadata=ChunkedPageMetadata(
                        page_number=doc.metadata.get("page_number", 0),
                        chunk=ChunkMetadata(
                            chunk_id=chunk_id,
                            chunk_number=i + 1,
                            document_page_number=doc.metadata.get("page_number", 0),
                            page_number=doc.metadata.get("page_number", 0) - state.page_number_offset
                        ),
                        source=SourceMetadata(
                            source_id=state.source_id,
                            source_name=state.source_name
                        ),
                        chapter=ChapterMetaData(
                            chapter_id=chapter.chapter_id,
                            chapter_number=chapter.chapter_number,
                            chapter_name=chapter.chapter_name,
                            page_from=chapter.page_from,
                            page_to=chapter.page_to,
                            document_page_from=chapter.document_page_from,
                            document_page_to=chapter.document_page_to
                        ),
                        indexPage=doc.metadata.get("page_number", 0) in state.toc_pages,
                        projectId=state.project_id,
                        tenantId=state.tenant_id
                    )
                )
                
                all_chunked_pages.append(chunked_page)
        
        state.chunked_pages = all_chunked_pages
        return state
        
    except Exception as e:
        print(f"Error generating chunks: {e}")
        state.chunked_pages = []
        return state


def summarize_chunks(state: State) -> State:
    """Summarize individual chunks"""
    print("Summarizing chunks")
    try:
        llm = get_llm()
        summarized_chunks = []
        
        # Process chunks in batches to avoid rate limits
        batch_size = 10
        
        for i in range(0, len(state.chunked_pages), batch_size):
            batch = state.chunked_pages[i:i + batch_size]
            
            for chunk in batch:
                print(i, chunk.metadata.page_number)
                try:
                    # Find topics for this chunk's chapter
                    chapter_topics = next(
                        (ct for ct in state.chapter_wise_topics 
                         if ct.chapter_id == chunk.metadata.chapter.chapter_id),
                        None
                    )
                    
                    topics_json = json.dumps({
                        "chapter_id": chapter_topics.chapter_id if chapter_topics else "",
                        "topics": [
                            {
                                "chapterName": topic.chapter_name,
                                "topic": topic.topic,
                                "pageNumber": topic.page_number,
                                "subTopics": topic.sub_topics
                            }
                            for topic in (chapter_topics.topics if chapter_topics else [])
                        ]
                    })
                    
                    prompt = SUMMARIZE_CHUNKS_PROMPT.format(
                        page_content=chunk.page_content,
                        topics_in_chapter=topics_json,
                        language=state.language
                    )
                    
                    response = llm.invoke(prompt)
                    summary_data = json.loads(response.content if hasattr(response, 'content') else str(response))
                    
                    summarized_chunk = SummarizedChunks(
                        chunk_id=chunk.metadata.chunk.chunk_id,
                        chunk_number=chunk.metadata.chunk.chunk_number,
                        chapter_id=chunk.metadata.chapter.chapter_id,
                        summary=summary_data.get("summary", "Failed to generate summary"),
                        topic=summary_data.get("topic", "Unknown"),
                        subtopic=summary_data.get("subtopic")
                    )
                    
                    summarized_chunks.append(summarized_chunk)
                    
                except Exception as e:
                    print(f"Error summarizing chunk: {e}")
                    # Add a fallback summarized chunk
                    summarized_chunk = SummarizedChunks(
                        chunk_id=chunk.metadata.chunk.chunk_id,
                        chunk_number=chunk.metadata.chunk.chunk_number,
                        chapter_id=chunk.metadata.chapter.chapter_id,
                        summary="Failed to generate summary",
                        topic="Unknown",
                        subtopic=None
                    )
                    summarized_chunks.append(summarized_chunk)
        
        state.summarized_chunks = summarized_chunks
        return state
        
    except Exception as e:
        print(f"Error summarizing chunks: {e}")
        state.summarized_chunks = []
        return state


def generate_chapter_summaries(state: State) -> State:
    """Generate summaries for each chapter"""
    print("Generating chapter summaries")
    try:
        llm = get_llm()
        chapter_summaries = []
        
        # Group summaries by chapter
        chapters_map = {}
        
        # Initialize with all chapters from table of contents
        for toc_entry in state.toc:
            chapters_map[toc_entry.chapter_id] = {
                "chapter_id": toc_entry.chapter_id,
                "chapter_name": toc_entry.chapter_name or "",
                "chunk_summaries": [],
                "topics": set()
            }
        
        # Group chunk summaries by chapter
        for chunk in state.summarized_chunks:
            if chunk.chapter_id in chapters_map:
                chapters_map[chunk.chapter_id]["chunk_summaries"].append(chunk.summary)
                chapters_map[chunk.chapter_id]["topics"].add(chunk.topic)
        
        # Generate summary for each chapter
        for chapter_data in chapters_map.values():
            try:
                if not chapter_data["chunk_summaries"]:
                    summary_obj = ChapterSummaries(
                        chapter_id=chapter_data["chapter_id"],
                        chapter_name=chapter_data["chapter_name"],
                        summary="No content available for this chapter",
                        topics=[]
                    )
                    chapter_summaries.append(summary_obj)
                    continue
                
                prompt = GENERATE_CHAPTER_SUMMARIES_PROMPT.format(
                    chapter_id=chapter_data["chapter_id"],
                    chapter_name=chapter_data["chapter_name"],
                    chunk_summaries="\n\n".join(chapter_data["chunk_summaries"]),
                    language=state.language
                )
                
                summary_data = llm.with_structured_output(ChapterSummaries).invoke(prompt)
                
                summary_obj = ChapterSummaries(
                    chapter_id=summary_data['chapter_id'],
                    chapter_name=summary_data['chapter_name'],
                    summary=summary_data['summary'],
                    topics=summary_data['topics']
                )
                
                chapter_summaries.append(summary_obj)
                
            except Exception as e:
                print(f"Error generating summary for chapter {chapter_data['chapter_id']}: {e}")
                # Add fallback summary
                summary_obj = ChapterSummaries(
                    chapter_id=chapter_data["chapter_id"],
                    chapter_name=chapter_data["chapter_name"],
                    summary="Failed to generate chapter summary",
                    topics=list(chapter_data["topics"])
                )
                chapter_summaries.append(summary_obj)
        
        state.chapter_summaries = chapter_summaries
        return state
        
    except Exception as e:
        print(f"Error generating chapter summaries: {e}")
        state.chapter_summaries = []
        return state


def generate_book_summary(state: State) -> State:
    """Generate overall book summary"""
    print("Generating book summary")
    try:
        llm = get_llm()
        
        if not state.chapter_summaries:
            state.book_summary = BookSummary(
                title=state.source_name,
                summary="No content available for generating a summary",
                main_topics=[],
                chapter_highlights=[]
            )
            return state
        
        chapter_summaries_text = "\n\n".join([
            f"Chapter: {chapter.chapter_name}\nSummary: {chapter.summary}\nTopics: {', '.join(chapter.topics)}"
            for chapter in state.chapter_summaries
        ])
        
        prompt = GENERATE_BOOK_SUMMARY_PROMPT.format(
            source_name=state.source_name,
            chapter_summaries_text=chapter_summaries_text,
            language=state.language
        )
        
        summary_data = llm.with_structured_output(BookSummary).invoke(prompt)
        
        chapter_highlights = [
            ChapterHighlight(
                chapter_name=highlight['chapter_name'],
                highlight=highlight['highlight']
            )
            for highlight in summary_data['chapter_highlights']
        ]
        
        state.book_summary = BookSummary(
            title=summary_data['title'],
            summary=summary_data['summary'],
            main_topics=summary_data['main_topics'],
            chapter_highlights=chapter_highlights
        )
        
        return state
        
    except Exception as e:
        print(f"Error generating book summary: {e}")
        # Create fallback summary
        fallback_highlights = [
            ChapterHighlight(
                chapter_name=chapter.chapter_name,
                highlight=chapter.summary.split('.')[0] + '.' if chapter.summary else ""
            )
            for chapter in state.chapter_summaries
        ]
        
        state.book_summary = BookSummary(
            title=state.source_name,
            summary="Failed to generate book summary",
            main_topics=[],
            chapter_highlights=fallback_highlights
        )
        return state


def embed_content(state: State) -> State:
    """Embed chunks into vector store"""
    print("Embedding content")
    try:
        vector_store = get_vector_store(namespace=state.project_id, index_name="academic-lm", dimension=1536)
        
        # Enhance chunks with summary information
        enhanced_docs = []
        chunk_ids = []
        
        for chunk in state.chunked_pages:
            # Find corresponding summary
            chunk_summary = next(
                (summary for summary in state.summarized_chunks 
                 if summary.chunk_id == chunk.metadata.chunk.chunk_id),
                None
            )
            
            # Create document for vector store
            doc = Document(
                page_content=chunk.page_content,
                metadata={
                    "blobType": state.source_type,
                    "chapter.chapter_id":chunk.metadata.chapter.chapter_id,
                    "chapter.chapter_name":chunk.metadata.chapter.chapter_name,
                    "chapter.chapter_number":chunk.metadata.chapter.chapter_number,
                    "chapter.document_page_from":chunk.metadata.chapter.document_page_from,
                    "chapter.document_page_to":chunk.metadata.chapter.document_page_to,
                    "chapter.page_from":chunk.metadata.chapter.page_from,
                    "chapter.page_to":chunk.metadata.chapter.page_to,
                    "chunk.chunk_id":chunk.metadata.chunk.chunk_id,
                    "chunk.chunk_number":chunk.metadata.chunk.chunk_number,
                    "chunk.document_page_number":chunk.metadata.chunk.document_page_number,
                    "chunk.page_number":chunk.metadata.chunk.page_number,
                    "indexPage":chunk.metadata.indexPage,
                    "loc.lines.from":"NA",
                    "loc.lines.to":"NA",
                    "loc.pageNumber":chunk.metadata.chunk.document_page_number,
                    "projectId":state.project_id,
                    "source.source_id":chunk.metadata.source.source_id,
                    "source.source_name":chunk.metadata.source.source_name,
                    "summary.text": chunk_summary.summary if chunk_summary else "",
                    "summary.topic": chunk_summary.topic if chunk_summary else "",
                    "summary.subtopic": chunk_summary.subtopic if chunk_summary else "",
                    "tenantId":state.tenant_id,
                }
            )
            enhanced_docs.append(doc)
            chunk_ids.append(chunk.metadata.chunk.chunk_id)
        
        # Add documents to vector store in batches to avoid size limits
        batch_size = 50  # Process 50 documents at a time to stay under 2MB limit
        all_document_ids = []
        
        for i in range(0, len(enhanced_docs), batch_size):
            batch_docs = enhanced_docs[i:i + batch_size]
            batch_ids = chunk_ids[i:i + batch_size]
            
            print(f"Processing batch {i//batch_size + 1}/{(len(enhanced_docs) + batch_size - 1)//batch_size}")
            
            try:
                batch_document_ids = vector_store.add_documents(
                    documents=batch_docs,
                    ids=batch_ids
                )
                
                if batch_document_ids:
                    all_document_ids.extend(batch_document_ids)
                else:
                    all_document_ids.extend(batch_ids)
                    
            except Exception as batch_error:
                print(f"Error processing batch {i//batch_size + 1}: {batch_error}")
                # Continue with next batch instead of failing completely
                continue
        
        state.document_ids = all_document_ids
        print(f"Successfully embedded {len(all_document_ids)} documents")
        return state
        
    except Exception as e:
        print(f"Error embedding content: {e}")
        state.document_ids = []
        return state


# Create the graph builder
builder = StateGraph(State, input=InputState, output=OutputState)

# Add nodes
builder.add_node("parse_pdf", parse_pdf)
builder.add_node("derive_table_of_contents", derive_table_of_contents)
builder.add_node("generate_chunks", generate_chunks)
builder.add_node("summarize_chunks", summarize_chunks)
builder.add_node("generate_chapter_summaries", generate_chapter_summaries)
builder.add_node("generate_book_summary", generate_book_summary)
builder.add_node("embed_content", embed_content)

# Connect the nodes
builder.add_edge(START, "parse_pdf")
builder.add_edge("parse_pdf", "derive_table_of_contents")
builder.add_edge("derive_table_of_contents", "generate_chunks")
builder.add_edge("generate_chunks", "summarize_chunks")
builder.add_edge("summarize_chunks", "generate_chapter_summaries")
builder.add_edge("generate_chapter_summaries", "generate_book_summary")
builder.add_edge("generate_book_summary", "embed_content")
builder.add_edge("embed_content", END)

# Compile the graph
graph = builder.compile()

if __name__ == "__main__":
    # Test the graph
    with open("../../../sample_data/Hindi.pdf", "rb") as file:
        file_content = file.read()
        
        input_state = InputState(
            source_content=file_content,
            source_id="test_123",
            source_name="Hindi Textbook",
            source_type="application/pdf",
            tenant_id="tenant_123",
            project_id="project_123",
            user_id="user_123",
            language="hi",
            page_number_offset=0,
            toc_pages=[10]
        )
        
        result = graph.invoke(input_state)
        print(f"Processed document: {result.source_name}")
        print(f"Generated {len(result.chunked_pages)} chunks")
        print(f"Found {len(result.toc)} chapters")
        print(f"Embedded {len(result.document_ids)} documents")