import { Metada<PERSON> } from "next";
import Link from "next/link";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
  CardDescription,
  Card<PERSON>ontent,
  CardFooter,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import {
  ArrowRight,
  Briefcase,
  LayersIcon,
  CreditCard,
  CheckCircle,
} from "lucide-react";
import TenantCard from "@/components/tenat-card";
import IsAuthResource from "@/components/is-auth-resource";

// Meta tags
export const metadata: Metadata = {
  title: "Zuma LM",
  description: "Zuma LM",
};

export default function Page() {
  return (
    <div className="container mx-auto p-10">
      <div className="flex flex-col gap-6">
        <TenantCard />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <IsAuthResource ResourceID="Projects">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Briefcase className="h-5 w-5 text-primary" />
                  <CardTitle>Projects</CardTitle>
                </div>
                <CardDescription className="pt-2">
                  Create and manage your academic projects with powerful
                  collaboration tools
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Organize assignments and research</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Track progress and deadlines</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Collaborate with team members</span>
                </div>
              </CardContent>
              <CardFooter>
                <Link href="/projects" className="w-full">
                  <Button variant="outline" className="w-full group">
                    View Projects
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </IsAuthResource>
          <IsAuthResource ResourceID="Plans">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <LayersIcon className="h-5 w-5 text-primary" />
                  <CardTitle>Plans</CardTitle>
                </div>
                <CardDescription className="pt-2">
                  Plan and organize your academic journey with our comprehensive
                  planning tools
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Create study schedules</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Set academic milestones</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Monitor learning progress</span>
                </div>
              </CardContent>
              <CardFooter className="mt-auto">
                <Link href="/plans" className="w-full">
                  <Button variant="outline" className="w-full group">
                    View Plans
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </IsAuthResource>

          <IsAuthResource ResourceID="Subscriptions">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-primary" />
                  <CardTitle>Subscription</CardTitle>
                </div>
                <CardDescription className="pt-2">
                  Access premium features and manage your subscription settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>View billing history</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Upgrade subscription plan</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>Manage payment methods</span>
                </div>
              </CardContent>
              <CardFooter className="mt-auto">
                <Link href="/subscriptions" className="w-full">
                  <Button variant="outline" className="w-full group">
                    Manage Subscription
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </IsAuthResource>
        </div>
      </div>
    </div>
  );
}
