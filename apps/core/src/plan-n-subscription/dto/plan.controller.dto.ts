import { OmitType } from '@nestjs/mapped-types';
import { Create<PERSON>lanDto, ListPlansDto, UpdatePlanDto } from './plan.service.dto';

export class CreatePlanReqBodyDto extends OmitType(CreatePlanDto, ['TenantID'] as const) {}

export class UpdatePlanReqBodyDto extends OmitType(UpdatePlanDto, ['TenantID'] as const) {}

export class ListPlansReqBodyDto extends OmitType(ListPlansDto, ['TenantID'] as const) {}
