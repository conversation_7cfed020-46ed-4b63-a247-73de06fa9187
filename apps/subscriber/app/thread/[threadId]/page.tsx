"use client";

import { API } from "@/apis/api";
import { ChatResponseDto } from "@/apis/chat.api";
import { TAudioBook, TAudioBookMeta } from "@/apis/subscription.api";
import SpeechToText from "@workspace/ui/components/speech-to-text";
import { ChatMessage } from "@/components/thread/chat-message";
import { useProjectContext } from "@/contexts/project-context";
import { useThreadContext } from "@/contexts/thread-context";
import { AutoReadToggle } from "@/components/message-actions/AutoReadToggle";
import { useAutoRead } from "@/contexts/auto-read-context";
import { useTTS } from "@/contexts/tts-context";
import { Button } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/sheet";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Slider } from "@workspace/ui/components/slider";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  AudioLines,
  Disc3,
  Pause,
  Play,
  RefreshCw,
  SendHorizontal,
} from "lucide-react";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import ChatInput from "@/components/chat/chat-input";

interface Message {
  id: string;
  content: string;
  timestamp: string;
  isUser: boolean;
  output_language?: string;
  status?:
    | "sending"
    | "sent"
    | "error"
    | "analyzing"
    | "researching"
    | "processing"
    | "completed"
    | "replying"
    | undefined;
  isStreaming?: boolean;
  isExistingMessage?: boolean;
  citations: {
    id: string;
    source_title: string;
    pdf_url: string | null;
    page: number;
    line_from: number | null;
    line_to: number | null;
    document_page_number: number;
  }[];
}

export default function ThreadPage() {
  const { threadId } = useParams();
  const { selectedProject } = useProjectContext();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [audioBooks, setAudioBooks] = useState<TAudioBookMeta[]>([]);
  const [selectedAudioBook, setSelectedAudioBook] = useState<TAudioBook | null>(
    null
  );
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { refetchThreads } = useThreadContext();
  const { isAutoReadEnabled } = useAutoRead();
  const { autoReadMessage } = useTTS();

  // Auto-read management
  const autoReadTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const processedMessagesRef = useRef<Set<string>>(new Set());

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    const message = searchParams.get("message");
    if (message) {
      setNewMessage(message);
      const params = new URLSearchParams(searchParams);
      params.delete("message");
      router.push(`${pathname}?${params.toString()}`);
    } else {
      handleSendStreamMessage(newMessage);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  // Fetch thread messages
  useEffect(() => {
    if (!threadId || threadId === "new-thread") return;

    const fetchMessages = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const { data, errors } = await API.CHAT.GetChatHistory(
          threadId as string
        );

        if (errors) {
          setError(errors[0]?.message || "Failed to load conversation");
          toast.error("Failed to load conversation");
          setIsLoading(false);
          return;
        }

        if (data) {
          // Convert API messages to our format
          const formattedMessages = data.messages.map((msg, index) => ({
            id: `msg-${threadId}-${index}`,
            content: msg.content,
            timestamp: new Date(msg.timestamp).toISOString(),
            isUser: msg.role === "user",
            output_language: msg.output_language,
            isExistingMessage: true,
            citations: msg.citations.map((citation) => ({
              id: citation.id,
              source_title: citation.source_title,
              pdf_url: citation.pdf_url,
              page: citation.page_number,
              line_from: citation.line_from,
              line_to: citation.line_to,
              document_page_number: citation.document_page_number,
            })),
          }));

          console.log(formattedMessages);

          setMessages(formattedMessages);
        }

        setIsLoading(false);
      } catch (err) {
        console.error("Failed to load messages:", err);
        setError("Failed to load messages");
        toast.error("An error occurred while loading the conversation");
        setIsLoading(false);
      }
    };

    fetchMessages();
  }, [threadId]);

  useEffect(() => {
    const fetchAudioBooks = async () => {
      try {
        const ProjectID = selectedProject?.id || "";

        const { data: audioBooks, errors: audioBooksErrors } =
          await API.SUBSCRIPTION.GetAudioBooks(ProjectID);

        if (!audioBooksErrors && audioBooks.length > 0) {
          setAudioBooks(audioBooks);
        }
      } catch (err) {
        console.error("Failed to load audio books:", err);
      }
    };

    fetchAudioBooks();
  }, [selectedProject]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleAudioBookSelect = async (audioBookID: string) => {
    const { data, errors } = await API.SUBSCRIPTION.GetAudioBook(
      audioBookID,
      selectedProject?.id || ""
    );

    if (!errors && data) {
      setSelectedAudioBook(data);
    }
  };

  const handleSendStreamMessage = async (newMessage: string) => {
    if (!newMessage.trim() || !threadId || !selectedProject) return;

    const userMessageId = `user-${threadId}-${Date.now()}`;
    const aiResponseId = `ai-${threadId}-${Date.now()}`;

    const userMessage: Message = {
      id: userMessageId,
      content: newMessage,
      timestamp: new Date().toISOString(),
      isUser: true,
      status: "sent",
      isExistingMessage: false,
      citations: [],
    };

    setMessages((prev) => [...prev, userMessage]);

    const aiMessage: Message = {
      id: aiResponseId,
      content: "typing-indicator",
      timestamp: new Date().toISOString(),
      isUser: false,
      status: "sending",
      isExistingMessage: false,
      citations: [],
      isStreaming: true,
    };

    setMessages((prev) => [...prev, aiMessage]);

    let newThreadId = "";

    try {
      const onChunk = (chunk: ChatResponseDto) => {
        newThreadId = chunk.ThreadID || "";
        setMessages((prevMessages) => {
          return prevMessages.map((msg) => {
            if (msg.id === userMessageId) {
              return {
                ...msg,
                status: "sent",
              };
            }
            if (msg.id === aiResponseId) {
              const updatedMessage = {
                ...msg,
                content: chunk.message,
                output_language: chunk.output_language,
                citations:
                  chunk.citations?.map((citation) => ({
                    id: citation.id,
                    source_title: citation.source_title,
                    pdf_url: citation.pdf_url,
                    page: citation.page_number,
                    line_from: citation.line_from,
                    line_to: citation.line_to,
                    document_page_number: citation.document_page_number,
                  })) || [],
                status: chunk.status,
                isStreaming: !chunk.isComplete,
              };

              // Handle auto-read when message is complete
              if (
                chunk.isComplete &&
                isAutoReadEnabled &&
                chunk.message &&
                chunk.message !== "typing-indicator" &&
                !processedMessagesRef.current.has(aiResponseId)
              ) {
                // Mark as processed to prevent duplicates
                processedMessagesRef.current.add(aiResponseId);

                // Clear any existing timeout
                if (autoReadTimeoutRef.current) {
                  clearTimeout(autoReadTimeoutRef.current);
                }

                // Debounced auto-read call
                autoReadTimeoutRef.current = setTimeout(() => {
                  autoReadMessage(
                    aiResponseId,
                    chunk.message,
                    updatedMessage.citations,
                    chunk.output_language
                  ).catch((error) => {
                    console.error("Auto-read failed:", error);
                  });
                }, 300); // 300ms debounce for stability
              }

              return updatedMessage;
            }
            return msg;
          });
        });
      };

      const onComplete = () => {
        if (newThreadId !== threadId) router.push(`/thread/${newThreadId}`);
      };

      const onError = (error: Error) => {
        throw new Error(error?.message || "Failed to send message");
      };

      API.CHAT.StreamMessage(
        {
          message: newMessage.trim(),
          ProjectID: selectedProject?.id || "",
          ...(threadId !== "new-thread" && { ThreadID: threadId as string }),
        },
        onChunk,
        onComplete,
        onError
      );

      setNewMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  const handleRetry = () => {
    window.location.reload();
  };

  function AudioPlayer({
    selectedAudioBook,
  }: {
    selectedAudioBook: TAudioBook | null;
  }) {
    const audioRef = useRef<HTMLAudioElement>(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);

    const handlePlayPause = () => {
      if (!audioRef.current) return;
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    };

    const handleTimeUpdate = () => {
      if (!audioRef.current) return;
      setCurrentTime(audioRef.current.currentTime);
    };

    const handleLoadedMetadata = () => {
      if (!audioRef.current) return;
      setDuration(audioRef.current.duration);
    };

    const formatTime = (time: number) => {
      const minutes = Math.floor(time / 60)
        .toString()
        .padStart(2, "0");
      const seconds = Math.floor(time % 60)
        .toString()
        .padStart(2, "0");
      return `${minutes}:${seconds}`;
    };

    const handleSliderChange = (value: number[]) => {
      if (!audioRef.current || !value.length) return;
      const time = value[0];
      audioRef.current.currentTime = time ?? 0;
      setCurrentTime(time ?? 0);
    };

    useEffect(() => {
      if (selectedAudioBook?.S3?.AccessUrl && audioRef.current) {
        audioRef.current.load(); // reload the new URL
        audioRef.current
          .play()
          .then(() => {
            setIsPlaying(true);
          })
          .catch((error) => {
            console.error("Autoplay failed:", error);
            setIsPlaying(false);
          });
        setCurrentTime(0);
        setDuration(0);
      }
      if (selectedAudioBook?.S3?.AccessUrl) {
        handlePlayPause();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedAudioBook?.S3?.AccessUrl]);

    return (
      <div className="flex flex-col w-full p-2 space-y-2">
        <div className="flex items-center">
          <div className="h-20 w-20 rounded-lg bg-gray-800 flex items-center justify-center">
            <Disc3 className="h-18 w-18" />
          </div>

          <div className="flex-1 ml-2">
            <p className="text-sm font-medium">
              {selectedAudioBook?.Name ?? "No Audio Book Selected"}
            </p>
            <p className="text-xs text-muted-foreground">
              {formatTime(currentTime)} / {formatTime(duration)}
            </p>
            <p className="text-xs text-muted-foreground">
              {selectedAudioBook?.Summary ?? ""}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <button onClick={handlePlayPause} className="cursor-pointer">
              {isPlaying ? (
                <Pause className="h-8 w-8" />
              ) : (
                <Play className="h-8 w-8" />
              )}
            </button>
          </div>
        </div>
        {/* Progress Bar */}
        <Slider
          value={[currentTime]}
          max={duration || 1}
          step={0.1}
          onValueChange={handleSliderChange}
          className="w-full mt-4"
        />
        {/* Hidden Audio Element */}
        <audio
          ref={audioRef}
          src={selectedAudioBook?.S3?.AccessUrl ?? ""}
          preload="metadata"
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
        />
      </div>
    );
  }

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (autoReadTimeoutRef.current) {
        clearTimeout(autoReadTimeoutRef.current);
      }
    };
  }, []);

  const AudioBookSheet = () => {
    return (
      <Sheet>
        <SheetTrigger asChild>
          <div className="text-sm border-1 border-gray-600 rounded-2xl px-2 py-1 flex items-center gap-2 cursor-pointer place">
            <AudioLines size={18} className="animate-pulse" />
            <span className="text-xs">Audio Books Available</span>
          </div>
        </SheetTrigger>
        <SheetContent className="min-w-[600px]">
          <SheetHeader>
            <SheetTitle>Audio Books</SheetTitle>
            <SheetDescription>
              Listen to your generated audio books
            </SheetDescription>
          </SheetHeader>
          <div className="flex flex-col gap-6 p-4">
            {/* Main Player */}

            <AudioPlayer selectedAudioBook={selectedAudioBook} />

            {/* Audio List */}
            <div className="space-y-2 m-2">
              <ScrollArea className="h-[480px] w-full rounded-md border-1">
                {audioBooks.map((audioBook) => (
                  <div
                    key={audioBook._id}
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-accent/50 transition-color cursor-pointer"
                    onClick={() => handleAudioBookSelect(audioBook._id)}
                  >
                    <div className="flex items-center justify-between gap-3 w-full">
                      <div className="flex items-center gap-2">
                        <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                          <AudioLines size={16} className="text-primary" />
                        </div>
                        <div>
                          <p className="font-medium line-clamp-1">
                            {audioBook.Name}
                          </p>
                          <p className="text-xs text-muted-foreground line-clamp-1 ">
                            {audioBook.Summary}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <p className="text-xs text-muted-foreground line-clamp-1 ">
                          {audioBook.Duration > 60
                            ? `${Math.round(audioBook.Duration / 60)} Mins`
                            : `${Math.round(audioBook.Duration)} Secs`}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </ScrollArea>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  };

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Messages area */}
      <div className="flex-1 overflow-y-auto p-4">
        {isLoading ? (
          // Loading skeletons
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div
                key={i}
                className={`flex gap-3 ${i % 2 === 0 ? "" : "justify-end"}`}
              >
                <Skeleton className="h-10 w-10 rounded-full flex-shrink-0" />
                <div className={`${i % 2 === 0 ? "" : "text-right"} space-y-2`}>
                  <Skeleton className="h-4 w-20" />
                  <Skeleton
                    className={`h-24 ${i % 2 === 0 ? "w-[280px]" : "w-[260px] ml-auto"} rounded-lg`}
                  />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          // Error state
          <div className="flex justify-center p-4">
            <Card className="p-4 text-center text-destructive max-w-md">
              <p>{error}</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={handleRetry}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            </Card>
          </div>
        ) : messages.length === 0 ? (
          // Empty state
          <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground py-12">
            <p>No messages yet</p>
            <p className="text-sm mt-1">
              Start the conversation by sending a message
            </p>
          </div>
        ) : (
          // Messages
          messages.map((message) => {
            return (
              <ChatMessage
                key={message.id}
                id={message.id}
                content={message.content}
                timestamp={message.timestamp}
                isUser={message.isUser}
                output_language={message.output_language}
                status={message.status}
                isStreaming={message.isStreaming}
                isExistingMessage={message.isExistingMessage}
                citations={message.citations}
              />
            );
          })
        )}

        {/* This div is for auto-scrolling to the bottom */}
        <div ref={messagesEndRef} />
      </div>

      <div className="bg-accent rounded-2xl border-1 border-gray-600 shadow-2xl relative">
        <div className="pointer-events-none absolute inset-0 opacity-0 transition duration-200 ease-in-out bg-gray-900/20 group-hover:opacity-100" />
        <ChatInput
          placeholder="Ask anything related to your project"
          onSubmit={(message) => handleSendStreamMessage(message)}
          onMessageChange={(message) => setNewMessage(message)}
        />
      </div>
    </div>
  );
}
