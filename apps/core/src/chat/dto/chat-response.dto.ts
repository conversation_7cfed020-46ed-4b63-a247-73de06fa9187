export class CitationDto {
  id: string;
  source_title: string;
  pdf_url: string | null;
  page: number;
  line_from: number | null;
  line_to: number | null;
}

export class ChatResponseDto {
  message: string;
  logic?: string;
  isComplete: boolean;
  chunkIndex?: number;
  ThreadID?: string;
  isResponseStarted?: boolean;
  isResponseEnded?: boolean;
  citations?: CitationDto[];
  output_language?: string;
  status?: 'analyzing' | 'researching' | 'processing' | 'completed' | 'replying' | 'error';
}

export class ChatStreamCompleteDto {
  message: string;
  logic?: string;
  isComplete: boolean;
  chunkIndex?: number;
  ThreadID?: string;
}
