import { Test, TestingModule } from '@nestjs/testing';
import { TokenCounterService } from './token-counter.service';

describe('TokenCounterService', () => {
  let service: TokenCounterService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TokenCounterService],
    }).compile();

    service = module.get<TokenCounterService>(TokenCounterService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
