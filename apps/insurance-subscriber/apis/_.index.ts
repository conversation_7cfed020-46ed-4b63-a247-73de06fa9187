import ky from "ky";

export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
export const KY = ky.extend({
  prefixUrl: API_BASE_URL,
  // credentials: "include",
  timeout: 30000, // Default 30 second timeout for all requests
  hooks: {
    beforeRequest: [
      (options) => {
        // get cookie
        const cookie = document.cookie.split("_session=")[1]?.split(";")[0];
        if (cookie) {
          options.headers.set("Authorization", `Bearer ${cookie}`);
        }
      },
    ],
  },
});

export type TSuccessResponse = {
  message: string;
};

export type TAckResponse<T> = {
  message: string;
  ack: T;
};

export type TErrorResponse = { message: string }[];
