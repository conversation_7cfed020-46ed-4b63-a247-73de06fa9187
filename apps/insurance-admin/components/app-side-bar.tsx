"use client";
import React, { use<PERSON><PERSON>back, useEffect, useMemo } from "react";
import { AUTH } from "@/apis/auth.api";
import { ResourcesTypes } from "@/apis/user.api";
import { userAuthStore } from "@/store/useAuthStore";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from "@workspace/ui/components/sidebar";
import { cn } from "@workspace/ui/lib/utils";
import {
  Calendar,
  ChevronsUpDown,
  CreditCard,
  FolderKanban,
  Key,
  LogOut,
  Settings,
  UserRoundCog,
  Users,
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { toast } from "sonner";
import { ThemeToggle } from "./theme-toggle";

export function NavUser({
  user,
}: {
  user: {
    name: string;
    email: string;
    avatar: string;
  };
}) {
  const { isMobile } = useSidebar();

  const router = useRouter();
  const { clearUser } = userAuthStore();

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="rounded-lg">
                  {user.name.charAt(0).toUpperCase() ??
                    user.email.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{user.name}</span>
                <span className="truncate text-xs">{user.email}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="rounded-lg">
                    {user.name.charAt(0).toUpperCase() ??
                      user.email.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{user.name}</span>
                  <span className="truncate text-xs">{user.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Button variant="ghost" size="sm">
                <Key />
                Change Password
              </Button>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Button
                variant="ghost"
                size="sm"
                onClick={async () => {
                  await AUTH.SignOut();
                  toast.success("Signing out...");
                  router.push("/sign-in");
                  clearUser();
                }}
              >
                <LogOut />
                Logout
              </Button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}

const SideBarItems = {
  groups: [
    {
      name: "Workspace",
      items: [
        {
          name: "Dashboard",
          url: "/",
          icon: Users,
        },
        {
          name: "Projects",
          url: "/projects",
          icon: FolderKanban,
          ResourceID: ResourcesTypes.Enum.Projects,
        },
        {
          name: "Plan & Tiers",
          url: "/plans",
          icon: Calendar,
          ResourceID: ResourcesTypes.Enum.Plans,
        },
      ],
    },
    {
      name: "Customers",
      items: [
        {
          name: "Subscribers",
          url: "/subscribers",
          icon: Users,
          ResourceID: ResourcesTypes.Enum.Subscribers,
        },
        {
          name: "Subscriptions",
          url: "/subscriptions",
          icon: CreditCard,
          ResourceID: ResourcesTypes.Enum.Subscriptions,
        },
      ],
    },
    {
      name: "Organization",
      items: [
        {
          name: "Users & Access",
          url: "/users",
          icon: UserRoundCog,
          ResourceID: ResourcesTypes.Enum.Users,
        },
        {
          name: "Billing & Usage",
          url: "/billing",
          icon: CreditCard,
          ResourceID: ResourcesTypes.Enum.Billing,
        },
        {
          name: "Settings",
          url: "/settings",
          icon: Settings,
          ResourceID: ResourcesTypes.Enum.Settings,
        },
      ],
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = userAuthStore();
  const path = usePathname();
  const router = useRouter();

  const IgnoreRoutesNames = ["Dashboard"];
  const AuthItems = useCallback(() => {
    return user?.UserType === "Admin"
      ? SideBarItems
      : {
          groups: SideBarItems.groups
            .map((grp) => {
              return {
                ...grp,
                items: grp.items.filter((item) => {
                  if (IgnoreRoutesNames.includes(item.name)) {
                    return item;
                  }
                  return user?.Access?.Resources.some((resource) => {
                    return resource.ResourceID === item.ResourceID;
                  });
                }),
              };
            })
            .filter((grp) => grp.items.length > 0),
        };
  }, [user]);

  const flatRoutes = useMemo(() => {
    return AuthItems().groups.flatMap((grp) => {
      return grp.items;
    });
  }, [AuthItems]);

  // validate route access
  const validateRouteAccess = () => {
    const route = flatRoutes.find((item) => {
      return path.includes(item.url);
    });
    if (!route) {
      router.push("/");
    }
  };

  useEffect(() => {
    if (user && user?.UserType !== "Admin") validateRouteAccess();
  }, [path]);

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="p-0 py-2">
        <NavUser
          user={{
            name: user?.Name ?? "",
            email: user?.Email ?? "",
            avatar: user?.ProfilePicture ?? "",
          }}
        />
      </SidebarHeader>
      <SidebarContent>
        {AuthItems().groups.map((grp) => (
          <SidebarGroup key={grp.name} className="p-0">
            <SidebarGroupLabel>{grp.name}</SidebarGroupLabel>
            <SidebarMenu>
              {grp.items.map((item) => (
                <SidebarMenuItem key={item.name}>
                  <SidebarMenuButton
                    asChild
                    className={cn(
                      (path === "/" && item.url === "/") ||
                        (path.includes(item.url) && item.url !== "/")
                        ? "bg-primary/20 text-primary hover:bg-primary/20 hover:text-primary"
                        : ""
                    )}
                  >
                    <Link href={item.url}>
                      <item.icon />
                      <span>{item.name}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroup>
        ))}
      </SidebarContent>
      <SidebarFooter>
        <div className="flex items-center justify-between px-4 py-2 border-t border-border">
          <ThemeToggle className="text-muted-foreground hover:text-foreground hover:bg-muted transition-colors rounded-full" />
          <SidebarTrigger className="text-muted-foreground hover:text-foreground hover:bg-muted transition-colors rounded-full p-1" />
        </div>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
