import { Embeddings } from '@langchain/core/embeddings';
import { RunnableConfig } from '@langchain/core/runnables';
import { Annotation, END, MessagesAnnotation, Send, START, StateGraph } from '@langchain/langgraph';
import { ChatOpenAI, OpenAIEmbeddings } from '@langchain/openai';
import { PineconeStore } from '@langchain/pinecone';
import { Pinecone } from '@pinecone-database/pinecone';
import { z } from 'zod';
import { retrievalGraph } from './retrieval-graph';
import { reduceDocs } from './shared';
import { Document } from '@langchain/core/documents';
import { calculateLLMInvocations, LLMInvocation, LLMOutput } from '../shared/utils';

const PROMPTS = {
  GENERATE_INITIAL_QUERIES: `
You are an expert research assistant tasked with generating initial search queries to explore a user's \`industry_name\`-related question and find relevant documents from the \`industry_name\` policies, product documents, and guidelines stored in the vector database (e.g., Pinecone).

Your goal is to formulate 2-3 broad queries that help you understand the main topics and keywords related to the user's question. Think of these as initial exploratory probes.

You should take care of the following steps, including:
1. **Identify the main question:** Identify the main question asked by the user from the most recent conversation history, considering context and preferences from the conversation history.
2. **Identify Sub-Questions:** Break down the main question into smaller, more manageable sub-questions that need to be answered.
3. **Define Search Strategies:** For each sub-question, specify the search terms or keywords that will be used to retrieve relevant \`industry_name\` documents. Consider alternative search strategies to cover different facets of the question.

# Current Industry (industry_name): 'insurance'
**Input:**
* **Conversation History:** User's question and the conversation history.

**Output:**
Generate a list of 2-3 broad search queries. Keep the queries short, focused on keywords and general topics related to the \`industry_name\` question. Each query should be a concise search phrase. Present the queries as a numbered list.

Example:
1. "insurance claim process"
2. "policy coverage exclusions"
3. "product liability guidelines"
`,
  GENERATE_REFINED_QUERIES: `
You are an AI-powered assistant helping users with \`industry_name\`-related questions. Your task is to create refined search queries for the vector database (e.g., Pinecone), leveraging existing information to improve search accuracy.

The system has already:
* Summarized the \`industry_name\` policy documents.
* Provided section-wise summaries and key topics.

Each document in the vector database follows this format:
- "SECTION: <Section Name>, CONTENT: <complete content>"
- The SECTION name exactly matches the ChapterName in the Policies Summary provided in the input.
- If a SECTION spans multiple pages or documents, the same SECTION name appears in multiple documents.
- To retrieve all relevant content for a topic, queries should primarily consider SECTION names to target related documents, but should also include general keywords and concepts to capture documents that may not be strictly categorized under those SECTION names.

Now, using the context from:
* **Conversation History:** The user's previous interactions and your responses.
* **Sections Summary:** A summary of the \`industry_name\` document sections (SECTION names).

Analyze and understand the user's question, then generate refined search queries that:

* Primarily incorporate relevant SECTION names when applicable, to ensure retrieval of all documents related to those sections.
* Also include broad keywords or phrases related to the question to capture any relevant documents not categorized under those SECTION names.
* Are diverse, covering different aspects of the user's question, and avoid redundant retrieval.
* Aim for a balanced search strategy that maximizes recall and precision considering both SECTION-based and keyword-based document indexing.

# Current Industry (industry_name): 'insurance'
**Input:**
* **Conversation History:** {message below this instruction}
* **Sections Summary:** {sectionsSummary}

These queries will help generate an optimized search in the vector database that considers SECTION-based document structure without losing relevant uncategorized content.

**Output:**
A numbered list of refined search queries (no more than 3), designed to comprehensively retrieve relevant documents from all related sections and general content in the vector database. Please be specific and practical.
`,
};

export const InputStateAnnotation = Annotation.Root({
  ...MessagesAnnotation.spec,
  tenantId: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
  projectId: Annotation<string>({
    default: () => '',
    reducer: (a, b) => b,
  }),
  Sources: Annotation<
    {
      SourceID: string;
      SourceName: string;
      Sections: {
        SectionID: string;
        SectionName: string;
        Description: string;
        SubSections: {
          SubSectionName: string;
          PageNumber: number;
        }[];
      }[];
      SourceSummary: {
        Title: string;
        Summary: string;
        MainTopics: string[];
        SectionHighlights: {
          SectionName: string;
          Highlight: string;
        }[];
      };
    }[]
  >(),
});

const AgentStateAnnotation = Annotation.Root({
  ...InputStateAnnotation.spec,
  initialQueries: Annotation<string[]>({
    default: () => [],
    reducer: (a, b) => b,
  }),
  initialDocuments: Annotation<Document[]>({
    default: () => [],
    reducer: reduceDocs,
  }),
  refinedQueries: Annotation<string[]>({
    default: () => [],
    reducer: (a, b) => b,
  }),
  refinedDocuments: Annotation<Document[]>({
    default: () => [],
    reducer: reduceDocs,
  }),
  TokenCount: Annotation<{
    PromptTokens: number;
    CompletionTokens: number;
    TotalTokens: number;
    LLMInvocations: LLMInvocation[];
  }>({
    default: () => ({
      PromptTokens: 0,
      CompletionTokens: 0,
      TotalTokens: 0,
      LLMInvocations: [],
    }),
    reducer: (a, b) => b,
  }),
});

// async function generateInitialQueries(
//   state: typeof AgentStateAnnotation.State,
//   config: RunnableConfig
// ): Promise<typeof AgentStateAnnotation.Update> {
//   const initialQueriesSchema = z
//     .object({
//       queries: z.array(z.string()),
//     })
//     .describe('Generate initial search queries.');

//   const model = new ChatOpenAI({
//     model: 'gpt-4.1-mini',
//     temperature: 0.2,
//   });

//   const messages = [{ role: 'system', content: PROMPTS.GENERATE_INITIAL_QUERIES }, ...state.messages];
//   const response = await model.withStructuredOutput(initialQueriesSchema).invoke(messages);
//   return { initialQueries: response.queries };
// }

// async function retrieveInitialDocuments(
//   state: typeof AgentStateAnnotation.State,
//   config: RunnableConfig
// ): Promise<typeof AgentStateAnnotation.Update> {
//   const retrievalGraphConfig = {
//     tenantId: state.tenantId,
//     projectId: state.projectId,
//     query: state.initialQueries?.[0],
//   };
//   const retrievalGraphResult = await retrievalGraph.invoke(state);
//   return { initialDocuments: retrievalGraphResult.documents, initialQueries: state.initialQueries?.slice(1) };
// }

async function generateRefinedQueries(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  let TokenCount = state.TokenCount;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.4,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'generate_refined_queries'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });
  const researchPlanSchema = z
    .object({
      queries: z.array(z.string()),
    })
    .describe('Generate more refined search queries.');

  const prompt = PROMPTS.GENERATE_REFINED_QUERIES.replace('{sourcesSummary}', JSON.stringify(state.Sources));
  const messages = [{ role: 'system', content: prompt }, ...state.messages];

  const response = await model.withStructuredOutput(researchPlanSchema).invoke(messages);
  return { refinedQueries: response.queries, TokenCount };
}

async function retrieveRefinedDocuments(
  state: typeof AgentStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof AgentStateAnnotation.Update> {
  const retrievalGraphResult = await retrievalGraph.invoke(state);
  return { refinedDocuments: retrievalGraphResult.documents, refinedQueries: state.refinedQueries?.slice(1) };
}

function retrieveInitialInParallel(state: typeof AgentStateAnnotation.State): Send[] {
  return state.initialQueries.map(
    (query: string) =>
      new Send('retrieveInitialDocuments', {
        query: query,
        tenantId: state.tenantId,
        projectId: state.projectId,
      })
  );
}

function retrieveRefinedInParallel(state: typeof AgentStateAnnotation.State): Send[] {
  return state.refinedQueries.map(
    (query: string) =>
      new Send('retrieveRefinedDocuments', {
        query: query,
        tenantId: state.tenantId,
        projectId: state.projectId,
      })
  );
}
const builder = new StateGraph({ stateSchema: AgentStateAnnotation })
  .addNode('generateRefinedQueries', generateRefinedQueries)
  .addNode('retrieveRefinedDocuments', retrieveRefinedDocuments, { subgraphs: [retrievalGraph] })
  .addEdge(START, 'generateRefinedQueries')
  .addConditionalEdges('generateRefinedQueries', retrieveRefinedInParallel, ['retrieveRefinedDocuments'])
  .addEdge('retrieveRefinedDocuments', END);

export const researcherGraph = builder.compile().withConfig({ runName: 'ResearcherGraph' });
