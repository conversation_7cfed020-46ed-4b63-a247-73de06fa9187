import { TryCatch } from "@/lib/try-catch";
import { KY, TAckResponse } from "./_.index";
import { TErrorResponse } from "./_.index";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export interface ChatCitationDto {
  id: string;
  source_title: string;
  pdf_url: string | null;
  line_from: number | null;
  line_to: number | null;
  source_id: string;
  document_page_number: number;
  page_number: number;
}
export interface ChatRequestDto {
  message: string;
  ProjectID: string;
  ThreadID?: string;
}

export interface CitationDto {
  id: string;
  source_title: string;
  source_id: string | null;
  page_number: number;
  document_page_number: number;
  pdf_url: string | null;
  line_from: number | null;
  line_to: number | null;
}

export interface ChatResponseDto {
  message: string;
  logic?: string;
  isComplete: boolean;
  chunkIndex?: number;
  ThreadID?: string;
  isResponseStarted?: boolean;
  isResponseEnded?: boolean;
  citations?: CitationDto[];
  status?:
    | "analyzing"
    | "researching"
    | "processing"
    | "completed"
    | "replying"
    | "error";
}

export interface ChatHistoryDto {
  messages: ChatMessageDto[];
  ThreadID: string;
  tenantId: string;
  ProjectID: string;
  userId: string;
  title: string;
}

export interface ChatMessageDto {
  role: string;
  content: string;
  timestamp: Date;
  citations: ChatCitationDto[];
}

export interface ThreadDto {
  ThreadID: string;
  title: string;
  ProjectID: string;
  userId: string;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
}

export interface ThreadsResponseDto {
  threads: ThreadDto[];
  count: number;
}

const SendMessage = async (data: ChatRequestDto) => {
  return await TryCatch<ChatResponseDto, TErrorResponse>(
    KY.post("chat/send", {
      json: data,
      timeout: 60000, // Increasing timeout to 60 seconds
    }).json(),
  );
};

/**
 * Stream a chat message using Server-Sent Events (SSE)
 * @param data The chat request data
 * @param onChunk Callback for each message chunk received
 * @param onComplete Callback when streaming is complete
 * @param onError Callback for any errors
 * @returns A function to abort the connection
 */
const StreamMessage = (
  data: ChatRequestDto,
  onChunk: (chunk: ChatResponseDto) => void,
  onComplete?: (finalMessage: string) => void,
  onError?: (error: Error) => void,
) => {
  // Create AbortController to allow canceling the stream
  const controller = new AbortController();
  const { signal } = controller;

  // Start the streaming connection
  (async () => {
    try {
      const cookie = document.cookie.split("_session=")[1]?.split(";")[0];
      const response = await fetch(`${API_BASE_URL}/chat/stream`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${cookie}`,
        },
        body: JSON.stringify({ ...data, AgentType: "insurance-agent" }),
        signal,
      });

      if (!response.ok) {
        throw new Error(`Failed to connect to stream: ${response.status}`);
      }

      // Set up event source reader
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("Response body is not readable");
      }

      const decoder = new TextDecoder();
      let buffer = "";
      let completeMessage = "";

      // Read chunks from the stream
      while (true) {
        const { value, done } = await reader.read();

        if (done) {
          break;
        }

        // Decode and process the chunk
        const text = decoder.decode(value, { stream: true });
        buffer += text;

        // Process each line in the buffer (SSE format sends data: prefix)
        const lines = buffer.split("\n\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.substring(6));

              // Add message chunk to the complete message
              completeMessage += data.message || "";

              // Call the chunk callback
              onChunk(data);

              // If this is the last chunk, call the complete callback
              if (data.isComplete && onComplete) {
                onComplete(completeMessage);
                return;
              }
            } catch (e) {
              console.error("Error parsing SSE data:", e);
            }
          }
        }
      }

      // If we get here without seeing isComplete=true, call complete anyway
      if (onComplete) {
        onComplete(completeMessage);
      }
    } catch (error: unknown) {
      if (error instanceof Error && error.name !== "AbortError" && onError) {
        onError(error);
      }
    }
  })();

  // Return abort function
  return () => controller.abort();
};

const GetChatHistory = async (threadId: string) => {
  return await TryCatch<ChatHistoryDto, TErrorResponse>(
    KY.get(`chat/history/${threadId}`).json(),
  );
};

const GetThreads = async () => {
  return await TryCatch<ThreadsResponseDto, TErrorResponse>(
    KY.get("chat/threads").json(),
  );
};

const CreateThread = async (data: { ProjectID: string; title: string }) => {
  return await TryCatch<{ ThreadID: string }, TErrorResponse>(
    KY.post("chat/threads", { json: data }).json(),
  );
};

const DeleteThread = async (threadId: string) => {
  return await TryCatch<TAckResponse<null>, TErrorResponse>(
    KY.delete(`chat/threads/${threadId}`).json(),
  );
};

export const CHAT = {
  SendMessage,
  StreamMessage,
  GetChatHistory,
  GetThreads,
  CreateThread,
  DeleteThread,
};
