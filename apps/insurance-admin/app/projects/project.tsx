"use client";

import { API } from "@/apis/api";
import { TProject } from "@/types/project";
import {
  Sheet,
  Sheet<PERSON>ontent,
  <PERSON>etDescription,
  She<PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
  Sheet<PERSON>lose,
} from "@workspace/ui/components/sheet";
import { useEffect, useState, useRef } from "react";
import { toast } from "sonner";

import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import { format } from "date-fns";
import {
  Edit,
  PlusCircle,
  Search,
  LayoutGrid,
  Table as TableIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Badge } from "@workspace/ui/components/badge";
import { userAuthStore } from "@/store/useAuthStore";
import { ResourcesTypes } from "@/apis/user.api";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@workspace/ui/components/pagination";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";

export default function Projects() {
  const [projects, setProjects] = useState<TProject[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [newProject, setNewProject] = useState({ Name: "", Description: "" });
  const [editingProject, setEditingProject] = useState<TProject | null>(null);
  const [viewMode, setViewMode] = useState<"cards" | "table">("cards");
  const projectsPerPage = 9;
  const sheetCloseRef = useRef<HTMLButtonElement>(null);
  const editSheetCloseRef = useRef<HTMLButtonElement>(null);
  const router = useRouter();
  const { user } = userAuthStore();

  useEffect(() => {
    fetchProjects();
  }, [user]);

  const fetchProjects = async () => {
    const { data, errors } = await API.PROJECT.ListProjects();
    if (data) {
      if (user?.UserType === "Admin") {
        setProjects(data);
        return;
      }
      const projectAccess = user?.Access?.Resources?.find(
        (r) => r.ResourceID === ResourcesTypes.Enum.Projects
      )?.Properties?.ProjectIDs as string[];
      setProjects(data.filter((p) => projectAccess?.includes(p.ProjectID)));
    }
    if (errors) errors.forEach((err) => toast.error(err.message));
  };

  const handleCreateProject = async () => {
    if (!newProject.Name.trim()) {
      toast.error("Project name is required");
      return;
    }

    // Assuming you have a CreateProject API endpoint
    try {
      await API.PROJECT.CreateProject(newProject);
      toast.success("Project created successfully");
      fetchProjects();
      setNewProject({ Name: "", Description: "" });
      sheetCloseRef.current?.click();
    } catch (error) {
      console.log(error);
      toast.error("Failed to create project");
    }
  };

  const handleEditProject = async () => {
    if (!editingProject) return;

    try {
      await API.PROJECT.UpdateProject({
        ProjectID: editingProject.ProjectID,
        Name: editingProject.Name,
        Description: editingProject.Description,
      });
      toast.success("Project updated successfully");
      fetchProjects();
      editSheetCloseRef.current?.click();
    } catch (error) {
      console.log(error);
      toast.error("Failed to update project");
    }
  };

  const filteredProjects = projects.filter((project) =>
    project.Name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const paginatedProjects = filteredProjects.slice(
    (currentPage - 1) * projectsPerPage,
    currentPage * projectsPerPage
  );

  const totalPages = Math.ceil(filteredProjects.length / projectsPerPage);

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4 justify-between">
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search projects..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Tabs
            value={viewMode}
            onValueChange={(value) => setViewMode(value as "cards" | "table")}
            className="hidden sm:block"
          >
            <TabsList>
              <TabsTrigger value="cards">
                <LayoutGrid className="h-4 w-4 mr-2" />
                Cards
              </TabsTrigger>
              <TabsTrigger value="table">
                <TableIcon className="h-4 w-4 mr-2" />
                Table
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        <Sheet>
          <SheetTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Create Project
            </Button>
          </SheetTrigger>
          <SheetContent>
            <SheetHeader>
              <SheetTitle>Create New Project</SheetTitle>
              <SheetDescription>
                Add a new project to your workspace.
              </SheetDescription>
            </SheetHeader>
            <div className="space-y-4 p-4">
              <div className="space-y-2">
                <Label htmlFor="name">Project Name</Label>
                <Input
                  id="name"
                  value={newProject.Name}
                  onChange={(e) =>
                    setNewProject({ ...newProject, Name: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newProject.Description}
                  onChange={(e) =>
                    setNewProject({
                      ...newProject,
                      Description: e.target.value,
                    })
                  }
                />
              </div>
              <Button className="w-full" onClick={handleCreateProject}>
                Create Project
              </Button>
            </div>
            <SheetClose ref={sheetCloseRef} className="hidden" />
          </SheetContent>
        </Sheet>
      </div>

      <Tabs value={viewMode} className="w-full">
        <TabsContent value="cards" className="mt-0">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {paginatedProjects.map((project) => (
              <Sheet key={project.ProjectID}>
                <Card
                  className="cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => router.push(`/projects/${project.ProjectID}`)}
                >
                  <CardHeader className="flex justify-between items-center">
                    <CardTitle className="text-lg font-semibold line-clamp-1">
                      {project.Name}
                    </CardTitle>
                    <SheetTrigger asChild className="cursor-pointer">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingProject(project);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </SheetTrigger>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {project.Description || "No description available"}
                    </p>
                  </CardContent>
                  {/* <CardFooter className="flex justify-between items-center border-t">
                    <p className="text-xs text-muted-foreground">
                      Created {format(new Date(project.createdAt), "MMM d, yyyy")}
                    </p>
                    <Badge variant="secondary" className="text-xs">
                      ID: {project.ProjectID.slice(0, 8)}
                    </Badge>
                  </CardFooter> */}
                </Card>
                <SheetContent>
                  <SheetHeader>
                    <SheetTitle>Edit Project</SheetTitle>
                    <SheetDescription>
                      Make changes to your project.
                    </SheetDescription>
                  </SheetHeader>
                  <div className="space-y-4 p-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-name">Project Name</Label>
                      <Input
                        id="edit-name"
                        value={editingProject?.Name}
                        onChange={(e) =>
                          setEditingProject(
                            editingProject
                              ? { ...editingProject, Name: e.target.value }
                              : null
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-description">Description</Label>
                      <Textarea
                        id="edit-description"
                        value={editingProject?.Description}
                        onChange={(e) =>
                          setEditingProject(
                            editingProject
                              ? {
                                  ...editingProject,
                                  Description: e.target.value,
                                }
                              : null
                          )
                        }
                      />
                    </div>
                    <Button className="w-full" onClick={handleEditProject}>
                      Save Changes
                    </Button>
                  </div>
                  <SheetClose ref={editSheetCloseRef} className="hidden" />
                </SheetContent>
              </Sheet>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="table" className="mt-0">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="w-24">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedProjects.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-4">
                      No projects found
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedProjects.map((project) => (
                    <TableRow
                      key={project.ProjectID}
                      className="cursor-pointer"
                      onClick={() =>
                        router.push(`/projects/${project.ProjectID}`)
                      }
                    >
                      <TableCell className="font-medium">
                        {project.Name}
                      </TableCell>
                      <TableCell className="max-w-md truncate">
                        {project.Description || "No description available"}
                      </TableCell>
                      <TableCell>
                        <Sheet>
                          <SheetTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={(e) => {
                                e.stopPropagation();
                                setEditingProject(project);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </SheetTrigger>
                          <SheetContent>
                            <SheetHeader>
                              <SheetTitle>Edit Project</SheetTitle>
                              <SheetDescription>
                                Make changes to your project.
                              </SheetDescription>
                            </SheetHeader>
                            <div className="space-y-4 p-4">
                              <div className="space-y-2">
                                <Label htmlFor="edit-name-table">
                                  Project Name
                                </Label>
                                <Input
                                  id="edit-name-table"
                                  value={editingProject?.Name}
                                  onChange={(e) =>
                                    setEditingProject(
                                      editingProject
                                        ? {
                                            ...editingProject,
                                            Name: e.target.value,
                                          }
                                        : null
                                    )
                                  }
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="edit-description-table">
                                  Description
                                </Label>
                                <Textarea
                                  id="edit-description-table"
                                  value={editingProject?.Description}
                                  onChange={(e) =>
                                    setEditingProject(
                                      editingProject
                                        ? {
                                            ...editingProject,
                                            Description: e.target.value,
                                          }
                                        : null
                                    )
                                  }
                                />
                              </div>
                              <Button
                                className="w-full"
                                onClick={handleEditProject}
                              >
                                Save Changes
                              </Button>
                            </div>
                          </SheetContent>
                        </Sheet>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>

      {totalPages > 1 && (
        <Pagination className="mt-6">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                className={
                  currentPage === 1
                    ? "pointer-events-none opacity-50"
                    : "cursor-pointer"
                }
              />
            </PaginationItem>

            {/* First page */}
            {currentPage > 2 && (
              <PaginationItem>
                <PaginationLink onClick={() => setCurrentPage(1)}>
                  1
                </PaginationLink>
              </PaginationItem>
            )}

            {/* Ellipsis if needed */}
            {currentPage > 3 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Previous page if not first */}
            {currentPage > 1 && (
              <PaginationItem>
                <PaginationLink onClick={() => setCurrentPage(currentPage - 1)}>
                  {currentPage - 1}
                </PaginationLink>
              </PaginationItem>
            )}

            {/* Current page */}
            <PaginationItem>
              <PaginationLink
                isActive
                onClick={() => setCurrentPage(currentPage)}
              >
                {currentPage}
              </PaginationLink>
            </PaginationItem>

            {/* Next page if not last */}
            {currentPage < totalPages && (
              <PaginationItem>
                <PaginationLink onClick={() => setCurrentPage(currentPage + 1)}>
                  {currentPage + 1}
                </PaginationLink>
              </PaginationItem>
            )}

            {/* Ellipsis if needed */}
            {currentPage < totalPages - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Last page if not current or next */}
            {currentPage < totalPages - 1 && (
              <PaginationItem>
                <PaginationLink onClick={() => setCurrentPage(totalPages)}>
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            )}

            <PaginationItem>
              <PaginationNext
                onClick={() =>
                  setCurrentPage((p) => Math.min(totalPages, p + 1))
                }
                className={
                  currentPage === totalPages
                    ? "pointer-events-none opacity-50"
                    : "cursor-pointer"
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
