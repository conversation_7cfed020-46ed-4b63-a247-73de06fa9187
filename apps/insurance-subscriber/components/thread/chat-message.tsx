/* eslint-disable react/no-unknown-property */
"use client";

import React, { useRef, useState, useEffect, useCallback } from "react";
import { Avatar } from "@workspace/ui/components/avatar";
import { Card } from "@workspace/ui/components/card";
import { <PERSON><PERSON>, User, Copy, Volume2, Pause, Play, Square } from "lucide-react";
import { format } from "date-fns";
import { useTTS } from "@/contexts/tts-context";
import { toast } from "sonner";
import { cn } from "@workspace/ui/lib/utils";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";
import { PDFViewerSheet } from "../chat/pdf-renderer";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

interface ChatMessageProps {
  id: string;
  content: string;
  timestamp: string;
  isUser: boolean;
  status?:
    | "sending"
    | "sent"
    | "error"
    | "analyzing"
    | "researching"
    | "processing"
    | "completed"
    | "replying";
  isStreaming?: boolean;
  autoPlayTTS?: boolean;
  isExistingMessage?: boolean;
  citations?: {
    id: string;
    source_title: string;
    pdf_url: string | null;
    page: number;
    line_from: number | null;
    line_to: number | null;
    document_page_number: number;
  }[];
}

// Inline citation component with tooltip
const CitationNumber = ({
  number,
  citation,
  onClick,
}: {
  number: number;
  citation: NonNullable<ChatMessageProps["citations"]>[number];
  onClick: () => void;
}) => {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <span
            className="inline-flex items-center justify-center h-5 w-5 min-w-[1.25rem] mx-0.5 text-xs font-medium rounded-full bg-primary/20 text-primary hover:bg-primary/30 cursor-pointer align-baseline"
            onClick={onClick}
            style={{ display: "inline-flex", verticalAlign: "baseline" }}
          >
            {number}
          </span>
        </TooltipTrigger>
        <TooltipContent
          side="top"
          align="center"
          className="p-3 max-w-xs bg-popover border border-border shadow-md rounded-md"
        >
          <div className="flex flex-col gap-1.5 text-xs">
            <p className="font-semibold text-sm">
              Source Name: {citation.source_title}
            </p>
            <div className="flex items-center gap-1.5">
              <span className="font-medium">Page Number:</span> {citation.page}
            </div>
            <div className="flex items-center gap-1.5">
              <span className="font-medium">Line:</span> {citation.line_from} -{" "}
              {citation.line_to}
            </div>
            <div className="mt-1 text-primary text-[11px]">
              Click to view source document
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export function ChatMessage({
  id,
  content,
  timestamp,
  isUser,
  status = "sent",
  citations,
  isStreaming = false,
  autoPlayTTS = false,
  isExistingMessage = false,
}: ChatMessageProps) {
  // TTS context
  const {
    isPlaying,
    currentMessageId,
    progress,
    play,
    pause,
    resume,
    stop,
    autoRead,
  } = useTTS();

  // UI state
  const [showControls, setShowControls] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // TTS operation state
  const [isTTSBusy, setIsTTSBusy] = useState(false);
  const prevStreamingRef = useRef(isStreaming);
  const autoPlayedRef = useRef(false);

  // PDF viewer state
  const [pdfViewerContext, setPDFViewerContext] = useState({
    url: "",
    title: "",
    page: 1,
    document_page_number: 1,
    isOpen: false,
  });
  const [tableOfCitations, setTableOfCitations] = useState<
    {
      url: string;
      title: string;
      page: number;
      document_page_number: number;
    }[]
  >([]);

  // TTS control helpers
  const isThisMessagePlaying = currentMessageId === id && isPlaying;
  const isThisMessagePaused =
    currentMessageId === id && !isPlaying && currentMessageId !== null;

  // Handle streaming completion to trigger auto-read
  useEffect(() => {
    // Detect when streaming completes (was streaming before, not streaming now)
    const streamingJustCompleted = prevStreamingRef.current && !isStreaming;

    if (
      streamingJustCompleted &&
      !isUser &&
      !isExistingMessage &&
      autoRead &&
      autoPlayTTS
    ) {
      // Auto-play this message if:
      // 1. Streaming just completed
      // 2. It's an assistant message
      // 3. It's not an existing message
      // 4. Auto-read is enabled
      // 5. This specific message is allowed to auto-play

      // Only auto-play if not already played
      if (!autoPlayedRef.current && content && content !== "typing-indicator") {
        handleAutoPlay();
      }
    }

    // Update ref for next comparison
    prevStreamingRef.current = isStreaming;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isStreaming, isUser, isExistingMessage, autoRead, autoPlayTTS, content]);

  // Handle auto-play of message
  const handleAutoPlay = useCallback(() => {
    if (isTTSBusy || !content || content === "typing-indicator") return;

    setIsTTSBusy(true);

    try {
      // Process content to replace citation markers with readable text
      const processedContent = processContentWithCitations(content);

      // If another message is playing, stop it first
      if (currentMessageId && currentMessageId !== id) {
        stop();

        // Small delay to ensure cleanup before starting playback
        setTimeout(() => {
          play(id, processedContent);
          autoPlayedRef.current = true;
          setIsTTSBusy(false);
        }, 150);
      } else {
        // Play immediately if no other message is playing
        play(id, processedContent);
        autoPlayedRef.current = true;
        setIsTTSBusy(false);
      }
    } catch (error) {
      console.error("Error during auto-play:", error);
      setIsTTSBusy(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [content, currentMessageId, id, isTTSBusy, play, stop]);

  // Process content to replace citation markers with readable text
  const processContentWithCitations = useCallback(
    (text: string) => {
      if (!text || !citations?.length) return text;

      // Replace citation markers with readable text
      const citationRegex = /\[\[([^\]]+)\]\]/g;
      return text.replace(citationRegex, (match, citationId) => {
        const citation = citations.find((c) => c.id === citationId);
        if (citation) {
          return ` (Source: ${citation.source_title}, Page: ${citation.page}) `;
        }
        return "";
      });
    },
    [citations]
  );

  // Handle manual TTS control (play/pause/resume)
  const handleTTSControl = useCallback(() => {
    if (isTTSBusy || !content || content === "typing-indicator") return;

    setIsTTSBusy(true);

    try {
      // If this message is playing, pause it
      if (isThisMessagePlaying) {
        pause();
        setIsTTSBusy(false);
        return;
      }

      // If this message is paused, resume it
      if (isThisMessagePaused) {
        resume();
        setIsTTSBusy(false);
        return;
      }

      // Process content to replace citation markers with readable text
      const processedContent = processContentWithCitations(content);

      // Otherwise, start new playback
      // If another message is playing, stop it first
      if (currentMessageId && currentMessageId !== id) {
        stop();

        // Small delay to ensure cleanup
        setTimeout(() => {
          play(id, processedContent);
          setIsTTSBusy(false);
        }, 150);
      } else {
        // Play immediately if no other message is playing
        play(id, processedContent);
        setIsTTSBusy(false);
      }
    } catch (error) {
      console.error("Error handling TTS control:", error);
      setIsTTSBusy(false);
    }
  }, [
    content,
    currentMessageId,
    id,
    isThisMessagePaused,
    isThisMessagePlaying,
    isTTSBusy,
    pause,
    play,
    resume,
    stop,
    processContentWithCitations,
  ]);

  // Handle stop button click
  const handleStopTTS = useCallback(() => {
    if (isTTSBusy) return;
    stop();
  }, [isTTSBusy, stop]);

  // Clean up TTS when component unmounts
  useEffect(() => {
    return () => {
      if (currentMessageId === id) {
        stop();
      }
    };
  }, [currentMessageId, id, stop]);

  // Handle copy to clipboard
  const handleCopy = useCallback(() => {
    if (content && content !== "typing-indicator") {
      navigator.clipboard
        .writeText(content)
        .then(() => toast.success("Copied to clipboard"))
        .catch(() => toast.error("Failed to copy text"));
    }
  }, [content]);

  // Handle citation click to open PDF viewer
  const handleCitationClick = useCallback(
    (citation: NonNullable<ChatMessageProps["citations"]>[number]) => {
      if (!citation || !citation.pdf_url) return;

      setPDFViewerContext({
        url: citation.pdf_url,
        title: citation.source_title,
        page: citation.page,
        document_page_number: citation.document_page_number,
        isOpen: true,
      });

      setTableOfCitations([
        {
          url: citation.pdf_url,
          title: citation.source_title,
          page: citation.page,
          document_page_number: citation.document_page_number,
        },
      ]);
    },
    []
  );

  // Custom renderer for content with inline citations
  const MarkdownWithCitations = useCallback(() => {
    if (!content || content === "typing-indicator" || !citations?.length) {
      return (
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw, rehypeSanitize]}
        >
          {content || ""}
        </ReactMarkdown>
      );
    }

    // Create a map of unique citation IDs to sequential numbers
    const citationMap = new Map<string, number>();
    const uniqueCitationIds = Array.from(new Set(citations.map((c) => c.id)));
    uniqueCitationIds.forEach((id, index) => {
      if (id) {
        citationMap.set(id, index + 1);
      }
    });

    // Handle full markdown first, then inject citations
    const citationRegex = /\[\[([^\]]+)\]\]/g;

    // Replace citation markers with special placeholders that won't interfere with markdown
    const processedContent = content.replace(
      citationRegex,
      (match, citationId) => {
        return `\uE000${citationId}\uE001`;
      }
    );

    // Helper function to render text with citations
    const renderWithCitations = (
      children: React.ReactNode
    ): React.ReactNode => {
      if (!children || !citations?.length) return children;

      // Process each child
      return React.Children.map(children, (child) => {
        // If child is not a string, return it as is
        if (typeof child !== "string") return child;

        // Split by citation placeholders
        const parts = child.split(/\uE000([^\uE001]+)\uE001/);
        if (parts.length === 1) return child;

        // Build result with alternating text and citation components
        const result: React.ReactNode[] = [];
        parts.forEach((part, i) => {
          if (i % 2 === 0) {
            // Regular text
            if (part) result.push(part);
          } else {
            // Citation placeholder
            const citationId = part;
            const citation = citations.find((c) => c.id === citationId);

            if (citation) {
              const number = citationMap.get(citationId) || 0;
              result.push(
                <CitationNumber
                  key={`citation-${i}`}
                  number={number}
                  citation={citation}
                  onClick={() => handleCitationClick(citation)}
                />
              );
            } else {
              // If citation not found, show original marker
              result.push(`[[${citationId}]]`);
            }
          }
        });

        return result;
      });
    };

    // Render the content as markdown
    return (
      <div className="markdown-with-citations">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw, rehypeSanitize]}
          components={{
            // Custom renderer for paragraphs that might contain citation placeholders
            p: ({ children, ...props }) => {
              return <p {...props}>{renderWithCitations(children)}</p>;
            },
            // Custom renderer for list items that might contain citation placeholders
            li: ({ children, ...props }) => {
              return <li {...props}>{renderWithCitations(children)}</li>;
            },
            // Ensure other elements like headings also handle citations
            h1: ({ children, ...props }) => (
              <h1 {...props}>{renderWithCitations(children)}</h1>
            ),
            h2: ({ children, ...props }) => (
              <h2 {...props}>{renderWithCitations(children)}</h2>
            ),
            h3: ({ children, ...props }) => (
              <h3 {...props}>{renderWithCitations(children)}</h3>
            ),
            h4: ({ children, ...props }) => (
              <h4 {...props}>{renderWithCitations(children)}</h4>
            ),
            h5: ({ children, ...props }) => (
              <h5 {...props}>{renderWithCitations(children)}</h5>
            ),
            h6: ({ children, ...props }) => (
              <h6 {...props}>{renderWithCitations(children)}</h6>
            ),
            strong: ({ children, ...props }) => (
              <strong {...props}>{renderWithCitations(children)}</strong>
            ),
            em: ({ children, ...props }) => (
              <em {...props}>{renderWithCitations(children)}</em>
            ),
          }}
        >
          {processedContent}
        </ReactMarkdown>
      </div>
    );
  }, [content, citations, handleCitationClick]);

  return (
    <div className={`flex gap-3 ${isUser ? "flex-row-reverse" : ""} mt-4`}>
      <Avatar className="h-10 w-10 shrink-0 hidden md:block">
        {isUser ? <User className="h-6 w-6" /> : <Bot className="h-6 w-6" />}
      </Avatar>

      <div
        className={`md:max-w-[65%] max-w-[96%] ${isUser ? "text-right ml-auto" : ""}`}
      >
        <div className="text-xs text-muted-foreground mb-1 flex items-center gap-2">
          <div className={`flex items-center gap-1 ${isUser ? "ml-auto" : ""}`}>
            {isUser ? "You" : "Assistant"} •{" "}
            {format(new Date(timestamp), "h:mm a")}
            {status === "sending" && <span className="ml-2 ">Sending...</span>}
            {status === "analyzing" && (
              <span className="ml-2 text-primary ">Analyzing...</span>
            )}
            {status === "researching" && (
              <span className="ml-2 text-primary ">Researching...</span>
            )}
            {status === "processing" && (
              <span className="ml-2 text-primary ">Processing...</span>
            )}
            {status === "error" && (
              <span className="ml-2 text-destructive">Failed to send</span>
            )}
          </div>
        </div>

        <div
          className="relative group"
          onMouseEnter={() => setShowControls(true)}
          onMouseLeave={() => setShowControls(false)}
        >
          {/* Message card */}
          <Card
            className={`px-4 py-2 ${
              isUser ? "bg-primary/10" : "bg-muted"
            } rounded-sm relative`}
          >
            {/* Custom CSS for inline citations */}
            <style jsx global>{`
              .markdown-with-citations {
                display: block;
              }
              .markdown-with-citations ol {
                list-style-type: decimal;
                padding-left: 2em;
                margin: 1em 0;
              }
              .markdown-with-citations ol li {
                display: list-item;
                margin: 0.5em 0;
              }
              .markdown-with-citations ul {
                list-style-type: disc;
                padding-left: 2em;
                margin: 1em 0;
              }
              .markdown-with-citations ul li {
                display: list-item;
                margin: 0.5em 0;
              }
              .markdown-with-citations p {
                margin-bottom: 1em;
              }
              .markdown-with-citations p:last-child {
                margin-bottom: 0;
              }
              .prose-sm p {
                margin-bottom: 1em;
              }
              .prose-sm p:last-child {
                margin-bottom: 0;
              }
            `}</style>

            <div
              ref={contentRef}
              className="break-words prose-sm dark:prose-invert prose-headings:my-0 prose-p:my-0 max-w-none"
            >
              {content === "typing-indicator" ? (
                <div className="flex space-x-2 h-5 items-center">
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.2s]"></div>
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.4s]"></div>
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.6s]"></div>
                </div>
              ) : content ? (
                <MarkdownWithCitations />
              ) : isStreaming ? (
                <div className="flex space-x-2 h-5 items-center">
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.2s]"></div>
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.4s]"></div>
                  <div className="h-2 w-2 bg-accent-foreground rounded-full animate-bounce [animation-delay:0.6s]"></div>
                </div>
              ) : (
                ""
              )}
            </div>
          </Card>

          {/* Bottom controls (copy + TTS) */}
          <div className="flex justify-end items-center mt-2 gap-2">
            <div className="flex items-center gap-2">
              {/* Copy button */}
              <button
                onClick={handleCopy}
                className={cn(
                  "p-1.5 rounded-full hover:bg-muted/80 text-muted-foreground opacity-0 transition-opacity",
                  showControls && "opacity-100",
                  (!content || content === "typing-indicator") &&
                    "opacity-50 cursor-not-allowed"
                )}
                aria-label="Copy message"
                disabled={
                  isTTSBusy || !content || content === "typing-indicator"
                }
              >
                <Copy size={16} />
              </button>

              {/* TTS controls - only for assistant messages */}
              {!isUser && (
                <>
                  {/* Play/Pause/Resume button */}
                  <div className="flex gap-1.5">
                    <button
                      onClick={handleTTSControl}
                      className={cn(
                        "p-1.5 rounded-full bg-muted hover:bg-muted/80 text-foreground opacity-0 transition-opacity",
                        (showControls ||
                          isThisMessagePlaying ||
                          isThisMessagePaused) &&
                          "opacity-100",
                        (!content ||
                          content === "typing-indicator" ||
                          isTTSBusy) &&
                          "opacity-50 cursor-not-allowed"
                      )}
                      aria-label={
                        isThisMessagePlaying
                          ? "Pause"
                          : isThisMessagePaused
                            ? "Resume"
                            : "Play"
                      }
                      disabled={
                        isTTSBusy || !content || content === "typing-indicator"
                      }
                    >
                      {isThisMessagePlaying ? (
                        <Pause size={16} />
                      ) : isThisMessagePaused ? (
                        <Play size={16} />
                      ) : (
                        <Volume2 size={16} />
                      )}
                    </button>

                    {/* Stop button - only when message is active */}
                    {(isThisMessagePlaying || isThisMessagePaused) && (
                      <button
                        onClick={handleStopTTS}
                        className={cn(
                          "p-1.5 rounded-full bg-muted hover:bg-muted/80 text-foreground",
                          isTTSBusy && "opacity-50 cursor-not-allowed"
                        )}
                        aria-label="Stop"
                        disabled={isTTSBusy}
                      >
                        <Square size={16} />
                      </button>
                    )}
                  </div>

                  {/* Progress bar - only when message is active */}
                  {(isThisMessagePlaying || isThisMessagePaused) && (
                    <div className="flex-1 h-3 bg-muted-foreground/10 rounded-full overflow-hidden w-36">
                      <div
                        className="h-full bg-primary transition-all duration-100"
                        style={{ width: `${progress * 100}%` }}
                      />
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <PDFViewerSheet
        setPDFViewerContext={setPDFViewerContext}
        url={pdfViewerContext.url}
        title={pdfViewerContext.title}
        isOpen={pdfViewerContext.isOpen}
        tableOfCitations={tableOfCitations}
        onClose={() =>
          setPDFViewerContext({ ...pdfViewerContext, isOpen: false })
        }
        pageNumber={pdfViewerContext.page}
        documentPageNumber={pdfViewerContext.document_page_number}
      />
    </div>
  );
}
