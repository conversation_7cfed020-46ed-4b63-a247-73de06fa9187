import fitz
from langchain_community.document_loaders import PyPDFLoader
from langchain_unstructured import UnstructuredLoader
import os
import json
import unstructured_client
from unstructured_client.models import operations, shared
import asyncio

client = unstructured_client.UnstructuredClient(
    api_key_auth="",
)


def parse_pdf(file_path: str) -> list[str]:
    doc = fitz.open(file_path)
    text = [page.get_text() for page in doc]
    return text


def parse_pdf_langchain(file_path: str) -> list[str]:
    loader = PyPDFLoader(file_path)
    pages = []
    pages = loader.load()
    return pages


def parse_pdf_langchain_unstructured(file_path: str) -> list[str]:
    loader = UnstructuredLoader(
        file_path=file_path,
        api_key=os.getenv("UNSTRUCTURED_API_KEY"),
        partition_via_api=True,
    )
    pages = loader.load()
    return pages


async def parse_pdf_unstructured(filename, input_dir, output_dir) -> list[str]:
    req = operations.PartitionRequest(
        partition_parameters=shared.PartitionParameters(
            files=shared.Files(
                content=open(filename, "rb"),
                file_name=filename,
            ),
            strategy=shared.Strategy.VLM,
            vlm_model="gpt-4o",
            vlm_model_provider="openai",
            languages=["eng", "hin", "mar"],
            split_pdf_page=False,
            split_pdf_allow_failed=False,
            split_pdf_concurrency_level=15,
            combine_under_n_chars=200,
            extract_image_block_types=["Image", "Table"],
            starting_page_number=1,
            unique_element_ids=True,
            chunking_strategy="by_title",
            max_characters=1024,
            multipage_sections=True,
            coordinates=True,
        ),
    )

    try:
        res = await client.general.partition_async(request=req)
        element_dicts = [element for element in res.elements]
        json_elements = json.dumps(element_dicts, indent=2)

        # Create the output directory structure.
        relative_path = os.path.relpath(os.path.dirname(filename), input_dir)
        output_subdir = os.path.join(output_dir, relative_path)
        os.makedirs(output_subdir, exist_ok=True)

        # Write the output file.
        output_filename = os.path.join(
            output_subdir, os.path.basename(filename) + ".json"
        )
        with open(output_filename, "w") as file:
            file.write(json_elements)

    except Exception as e:
        print(f"Error processing {filename}: {e}")


async def process_files(input_directory, output_directory):
    tasks = []
    print(f"Processing files from {input_directory} to {output_directory}")
    for root, _, files in os.walk(input_directory):
        for file in files:
            if not file.endswith(".json"):
                print(f"{file}")
                full_path = os.path.join(root, file)
                tasks.append(
                    parse_pdf_unstructured(full_path, input_directory, output_directory)
                )

    await asyncio.gather(*tasks)


if __name__ == "__main__":
    input_dir = "agents/_dummy"
    output_dir = "agents/_dummy_output"

    asyncio.run(process_files(input_dir, output_dir))
