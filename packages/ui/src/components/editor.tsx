"use client";

import { forwardRef, useCallback, useEffect } from "react";

import Highlight from "@tiptap/extension-highlight";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import { Editor, EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Button } from "@workspace/ui/components/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { cn } from "@workspace/ui/lib/utils";
import {
  AlignCenter,
  AlignLeft,
  AlignRight,
  Bold,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Highlighter,
  IndentDecrease,
  IndentIncrease,
  Italic,
  Link2,
  List,
  ListOrdered,
  Pilcrow,
  Redo,
  Ruler,
  Strikethrough,
  UnderlineIcon,
  Undo,
  WrapText,
} from "lucide-react";

const Toolbar = ({ editor }: { editor: Editor | null }) => {
  const setLink = useCallback(() => {
    if (!editor) return;
    const previousUrl = editor.getAttributes("link").href;
    const url = window.prompt("URL", previousUrl);
    if (url === null) return;
    if (url === "") {
      editor.chain().focus().extendMarkRange("link").unsetLink().run();
      return;
    }
    editor.chain().focus().extendMarkRange("link").setLink({ href: url }).run();
  }, [editor]);

  if (!editor) return null;

  return (
    <div className="flex flex-wrap gap-1 items-center justify-between rounded-lg border-2 p-2">
      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().toggleBold().run()}
            onClick={() => editor.chain().focus().toggleBold().run()}
            variant={editor.isActive("bold") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <Bold size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Bold</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().toggleItalic().run()}
            onClick={() => editor.chain().focus().toggleItalic().run()}
            variant={editor.isActive("italic") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <Italic size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Italic</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().toggleStrike().run()}
            onClick={() => editor.chain().focus().toggleStrike().run()}
            variant={editor.isActive("strike") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <Strikethrough size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Strikethrough</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().toggleUnderline().run()}
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            variant={editor.isActive("underline") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <UnderlineIcon size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Underline</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().toggleHighlight().run()}
            onClick={() => editor.chain().focus().toggleHighlight().run()}
            variant={editor.isActive("highlight") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <Highlighter size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Highlight</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={setLink}
            variant={editor.isActive("link") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <Link2 size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Hyperlink</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().toggleCode().run()}
            onClick={() => editor.chain().focus().toggleCode().run()}
            variant={editor.isActive("code") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <Code size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Code</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().toggleCodeBlock().run()}
            onClick={() => editor.chain().focus().toggleCodeBlock().run()}
            variant={editor.isActive("codeBlock") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <Code size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Code Block</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              !editor.can().chain().focus().toggleHeading({ level: 1 }).run()
            }
            onClick={() =>
              editor.chain().focus().toggleHeading({ level: 1 }).run()
            }
            variant={
              editor.isActive("heading", { level: 1 }) ? "default" : "outline"
            }
            color="primary"
            size="icon"
          >
            <Heading1 size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Heading 1</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              !editor.can().chain().focus().toggleHeading({ level: 2 }).run()
            }
            onClick={() =>
              editor.chain().focus().toggleHeading({ level: 2 }).run()
            }
            variant={
              editor.isActive("heading", { level: 2 }) ? "default" : "outline"
            }
            color="primary"
            size="icon"
          >
            <Heading2 size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Heading 2</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              !editor.can().chain().focus().toggleHeading({ level: 3 }).run()
            }
            onClick={() =>
              editor.chain().focus().toggleHeading({ level: 3 }).run()
            }
            variant={
              editor.isActive("heading", { level: 3 }) ? "default" : "outline"
            }
            color="primary"
            size="icon"
          >
            <Heading3 size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Heading 3</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().setParagraph().run()}
            onClick={() => editor.chain().focus().setParagraph().run()}
            variant={editor.isActive("paragraph") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <Pilcrow size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Paragraph</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().setTextAlign("left").run()}
            onClick={() => editor.chain().focus().setTextAlign("left").run()}
            variant={
              editor.isActive({ textAlign: "left" }) ? "default" : "outline"
            }
            color="primary"
            size="icon"
          >
            <AlignLeft size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Align Left</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              !editor.can().chain().focus().setTextAlign("center").run()
            }
            onClick={() => editor.chain().focus().setTextAlign("center").run()}
            variant={
              editor.isActive({ textAlign: "center" }) ? "default" : "outline"
            }
            color="primary"
            size="icon"
          >
            <AlignCenter size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Align Center</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().setTextAlign("right").run()}
            onClick={() => editor.chain().focus().setTextAlign("right").run()}
            variant={
              editor.isActive({ textAlign: "right" }) ? "default" : "outline"
            }
            color="primary"
            size="icon"
          >
            <AlignRight size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Align Right</TooltipContent>
      </Tooltip>

      {/* <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              !editor.can().chain().focus().setTextAlign("justify").run()
            }
            onClick={() => editor.chain().focus().setTextAlign("justify").run()}
            variant={
              editor.isActive({ textAlign: "justify" }) ? "default" : "outline"
            }
            color="primary"
            size="icon"
          >
            <TextAlignJustify size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Align Justify</TooltipContent>
      </Tooltip> */}

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().toggleBulletList().run()}
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            variant={editor.isActive("bulletList") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <List size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Bullet List</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().toggleOrderedList().run()}
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            variant={editor.isActive("orderedList") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <ListOrdered size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Numbered List</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              !editor.can().chain().focus().liftListItem("listItem").run()
            }
            onClick={() =>
              editor.chain().focus().liftListItem("listItem").run()
            }
            variant={editor.isActive("bulletList") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <IndentDecrease size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Outdent</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              !editor.can().chain().focus().sinkListItem("listItem").run()
            }
            onClick={() =>
              editor.chain().focus().sinkListItem("listItem").run()
            }
            variant={editor.isActive("bulletList") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <IndentIncrease size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Indent</TooltipContent>
      </Tooltip>

      {/* <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().setImage().run()}
            onClick={() => editor.chain().focus().setImage().run()}
            variant={editor.isActive("image") ? "default" : "outline"}
            color="primary"
            size="icon"
          >
            <ImageIcon size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Insert Image</TooltipContent>
      </Tooltip> */}

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().setHardBreak().run()}
            onClick={() => editor.chain().focus().setHardBreak().run()}
            variant="outline"
            color="primary"
            size="icon"
          >
            <WrapText size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Insert Break Line</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().setHorizontalRule().run()}
            onClick={() => editor.chain().focus().setHorizontalRule().run()}
            variant="outline"
            color="primary"
            size="icon"
          >
            <Ruler size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Insert Horizontal Rule</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().undo().run()}
            onClick={() => editor.chain().focus().undo().run()}
            variant="outline"
            color="primary"
            size="icon"
          >
            <Undo size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Undo</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>
          <Button
            className="disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!editor.can().chain().focus().redo().run()}
            onClick={() => editor.chain().focus().redo().run()}
            variant="outline"
            color="primary"
            size="icon"
          >
            <Redo size={20} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Redo</TooltipContent>
      </Tooltip>
    </div>
  );
};

// Main Editor Component
interface AdvanceEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  className?: string;
  editorClassName?: string;
  viewOnly?: boolean;
}

const AdvanceEditor = forwardRef<HTMLDivElement, AdvanceEditorProps>(
  ({ content, onChange, className, editorClassName, viewOnly }, ref) => {
    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          bulletList: {
            keepMarks: true,
            keepAttributes: false,
          },
          orderedList: {
            keepMarks: true,
            keepAttributes: false,
          },
        }),
        Image,
        Underline,
        Highlight,
        Link.configure({ openOnClick: false }),
        TextAlign.configure({ types: ["heading", "paragraph"] }),
      ],
      content: content,
      editorProps: {
        attributes: {
          class: cn(
            "prose prose-sm prose-zinc max-w-none dark:prose-invert focus:outline-none [&_*]:my-2",
            editorClassName,
          ),
        },
      },
      editable: !viewOnly,
      parseOptions: { preserveWhitespace: "full" },
      onUpdate: ({ editor }) => {
        onChange?.(editor.getHTML());
      },
    });

    useEffect(() => {
      if (editor && content !== editor.getHTML()) {
        editor.commands.setContent(content || "");
      }
    }, [content, editor]);

    if (!editor) {
      return <div>Loading editor...</div>;
    }

    return (
      <div className={className} id="rich-text-editor">
        {!viewOnly && (
          <>
            <Toolbar editor={editor} />
          </>
        )}
        <EditorContent
          editor={editor}
          className={cn(
            "grid min-h-[160px] w-full rounded-lg bg-transparent px-4 py-2 text-sm placeholder:opacity-80 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50",
          )}
          ref={ref}
        />
      </div>
    );
  },
);

AdvanceEditor.displayName = "AdvanceEditor";

export default AdvanceEditor;
