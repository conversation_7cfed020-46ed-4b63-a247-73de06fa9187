import { MiddlewareConsumer, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatService } from './chat.service';
import { ChatController } from './chat.controller';
import { ThreadService } from './thread.service';
import { Thread, ThreadSchema } from './schemas/thread.schema';
import { ChatMessage, ChatMessageSchema } from './schemas/chat-message.schema';
import { SourceSchema } from '../embed/schemas/source.schema';
import { TokenCounterModule } from '../token-counter/token-counter.module';
import { SourceModel } from '../embed/schemas/source.schema';
import { IsCreditAvailableGuard } from '../../common/guards/is-credit-avail.guard';
import { PlanNSubscriptionService } from '../plan-n-subscription/plan-n-subscription.service';
import { PlanNSubscriptionModule } from '../plan-n-subscription/plan-n-subscription.module';
import { IndSourceModel, IndSourceSchema } from '../embed/schemas/ind-source.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Thread.name, schema: ThreadSchema },
      { name: ChatMessage.name, schema: ChatMessageSchema },
      { name: SourceModel.name, schema: SourceSchema },
      { name: IndSourceModel.name, schema: IndSourceSchema },
    ]),
    TokenCounterModule,
    PlanNSubscriptionModule,
  ],
  controllers: [ChatController],
  providers: [ChatService, ThreadService],
  exports: [ChatService, ThreadService],
})
export class ChatModule {}
