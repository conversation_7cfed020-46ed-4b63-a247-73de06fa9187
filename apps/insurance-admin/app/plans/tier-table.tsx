"use client";

import { API } from "@/apis/api";
import { TTierGroup } from "@/apis/plan-&-tier-group.api";
import { TTenant } from "@/apis/tenant.api";
import { TPlan } from "@/types/plan-subscription";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@workspace/ui/components/alert-dialog";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/sheet";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Textarea } from "@workspace/ui/components/textarea";
import { Copy, Edit, Plus, Search, Squirrel, Trash2 } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

const host = "agent.zuma.co.in";

export default function TierTable() {
  const [plans, setPlans] = useState<TPlan[]>([]);
  const [tierGroups, setTierGroups] = useState<TTierGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [tenant, setTenant] = useState<TTenant | null>(null);

  useEffect(() => {
    fetchTierGroups();
    fetchPlans();
    fetchTenant();
  }, []);

  const fetchTierGroups = async () => {
    setLoading(true);
    try {
      const { data, errors } = await API.PLAN_TIER.GetTierGroups();
      if (data) {
        setTierGroups(data);
      }
      if (errors) {
        toast.error(errors[0]?.message || "Failed to fetch tier groups");
      }
    } catch (error) {
      toast.error("An error occurred while fetching tier groups");
    } finally {
      setLoading(false);
    }
  };

  const fetchPlans = async () => {
    setLoading(true);
    try {
      const { data, errors } = await API.PLAN_TIER.GetPlans();
      if (data) {
        setPlans(data);
      }
      if (errors) {
        toast.error(errors[0]?.message || "Failed to fetch plans");
      }
    } catch (error) {
      toast.error("An error occurred while fetching plans");
    } finally {
      setLoading(false);
    }
  };

  const fetchTenant = async () => {
    setLoading(true);
    try {
      const { data, errors } = await API.TENANT.GetTenant();
      if (data) {
        setTenant(data);
      }
      if (errors) {
        toast.error(errors[0]?.message || "Failed to fetch tenant");
      }
    } catch (error) {
      toast.error("An error occurred while fetching tenant");
    } finally {
      setLoading(false);
    }
  };

  const filteredTierGroups = tierGroups.filter((tierGroup) => {
    const matchesSearch =
      searchQuery === "" ||
      tierGroup.Name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tierGroup.Description?.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  const [isTierSheetOpen, setIsTierSheetOpen] = useState(false);
  const [selectedTierGroup, setSelectedTierGroup] = useState<TTierGroup | null>(
    null
  );

  const ManageTierSheet = ({
    selectedTierGroup,
  }: {
    selectedTierGroup?: TTierGroup | null;
  }) => {
    const [tierGroup, setTierGroup] = useState<
      Omit<
        TTierGroup,
        "_id" | "TenantID" | "InviteCode" | "createdAt" | "updatedAt" | "Plans"
      >
    >({
      Name: selectedTierGroup?.Name || "",
      Description: selectedTierGroup?.Description || "",
      PlanIDs: selectedTierGroup?.PlanIDs || [],
    });

    const AddPlanDialog = () => {
      const [planIDs, setPlanIDs] = useState<string[]>([]);
      const [searchQuery, setSearchQuery] = useState("");

      const listPlans = useCallback(() => {
        return plans.filter(
          (plan) =>
            !tierGroup?.PlanIDs.includes(plan.PlanID) &&
            (searchQuery === "" ||
              plan.Name.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      }, [plans, tierGroup?.PlanIDs, searchQuery]);

      return (
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <div className="flex justify-between items-center gap-2">
              <p className="text-md font-semibold text-muted-foreground">
                Tier Plans
              </p>
              <Button variant="outline">
                <Plus className="h-4 w-4" />
                Add Plan
              </Button>
            </div>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Add Plan</AlertDialogTitle>
            </AlertDialogHeader>
            <Input
              placeholder="Search plans..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <div className="space-y-4 px-4 max-h-[400px] overflow-y-scroll">
              {listPlans().map((plan) => (
                <div
                  key={plan.PlanID}
                  className="flex items-center gap-2 justify-between"
                >
                  <div>
                    <Label>{plan.Name}</Label>
                    <p className="text-sm text-muted-foreground">
                      {plan.Description}
                    </p>
                  </div>
                  <Checkbox
                    checked={planIDs.includes(plan.PlanID)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setPlanIDs((prev) => [...prev, plan.PlanID]);
                      } else {
                        setPlanIDs((prev) =>
                          prev.filter((id) => id !== plan.PlanID)
                        );
                      }
                    }}
                  />
                </div>
              ))}
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <Button
                onClick={async () => {
                  setTierGroup({ ...tierGroup, PlanIDs: planIDs });
                }}
              >
                Save
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      );
    };

    return (
      <Sheet open={isTierSheetOpen} onOpenChange={setIsTierSheetOpen}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>
              {selectedTierGroup ? "Edit Tier Group" : "Add Tier Group"}
            </SheetTitle>
          </SheetHeader>
          <div className="space-y-4 px-4">
            <Input
              placeholder="Name"
              value={tierGroup.Name}
              onChange={(e) =>
                setTierGroup({ ...tierGroup, Name: e.target.value })
              }
            />
            <Textarea
              placeholder="Description"
              value={tierGroup.Description}
              onChange={(e) =>
                setTierGroup({ ...tierGroup, Description: e.target.value })
              }
            />
            <AddPlanDialog />
            {tierGroup.PlanIDs.length > 0 && (
              <div className="flex flex-col gap-2">
                {tierGroup.PlanIDs.map((planID) => (
                  <div
                    key={planID}
                    className="flex items-center gap-2 bg-muted p-2 rounded-md justify-between"
                  >
                    <p>{plans.find((plan) => plan.PlanID === planID)?.Name}</p>
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => {
                        setTierGroup({
                          ...tierGroup,
                          PlanIDs: tierGroup.PlanIDs.filter(
                            (id) => id !== planID
                          ),
                        });
                      }}
                    >
                      <Trash2 className="h-4 w-4" color="red" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
            {tierGroup.PlanIDs.length === 0 && (
              <div className="flex flex-col items-center justify-center mt-10">
                <Squirrel className="h-8 w-8 text-muted-foreground" />
                <p className="text-xl text-muted-foreground p-2 rounded-md">
                  No Plans Added
                </p>
              </div>
            )}
          </div>
          <SheetFooter>
            <Button
              onClick={async () => {
                console.log("tierGroup", tierGroup);
                const { data, errors } = selectedTierGroup
                  ? await API.PLAN_TIER.UpdateTierGroup({
                      ...tierGroup,
                      _id: selectedTierGroup._id,
                    })
                  : await API.PLAN_TIER.CreateTierGroup(tierGroup);
                if (errors) {
                  errors.forEach((error) => toast.error(error.message));
                }
                toast.success(data?.message);
                setIsTierSheetOpen(false);
                fetchTierGroups();
              }}
            >
              Save
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    );
  };

  return (
    <div className="space-y-6">
      {/* Search */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative w-full sm:w-72">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search plans..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button
          onClick={() => {
            setSelectedTierGroup(null);
            setIsTierSheetOpen(true);
          }}
        >
          <Plus className="mr-2 h-4 w-4" />
          Create Tier Group
        </Button>
        <ManageTierSheet selectedTierGroup={selectedTierGroup} />
      </div>

      {/* Plans Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Plans</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-10">
                  <div className="flex items-center justify-center">
                    <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredTierGroups.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-10">
                  {searchQuery
                    ? "No tier groups found matching the search"
                    : "No tier groups found"}
                </TableCell>
              </TableRow>
            ) : (
              filteredTierGroups.map((tierGroup) => (
                <TableRow key={tierGroup._id}>
                  <TableCell className="font-medium">
                    {tierGroup.Name}
                  </TableCell>
                  <TableCell className="flex flex-wrap gap-2">
                    {plans
                      .filter((plan) => tierGroup.PlanIDs.includes(plan.PlanID))
                      .map((plan) => (
                        <Badge key={plan.PlanID}>{plan.Name}</Badge>
                      ))}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          navigator.clipboard.writeText(
                            `https://${host}/sign-up?tier_group=${tierGroup.InviteCode}&domain=${tenant?.WorkSpaceDomain}`
                          );
                          toast.success("Invite link copied to clipboard");
                        }}
                      >
                        <Copy />
                        Invite Link
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSelectedTierGroup(tierGroup);
                          setIsTierSheetOpen(true);
                        }}
                      >
                        <Edit />
                        Edit
                      </Button>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="hover:text-error-foreground"
                          >
                            <Trash2 className="h-4 w-4" color="red" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent>
                          Are you sure you want to delete this tier group?
                          <div className="mt-2 space-x-2">
                            <Button
                              onClick={async () => {
                                const { errors } =
                                  await API.PLAN_TIER.DeleteTierGroup({
                                    _id: tierGroup._id,
                                  });
                                if (errors) {
                                  errors.forEach((error) =>
                                    toast.error(error.message)
                                  );
                                }
                                toast.success("Tier group deleted");
                                fetchTierGroups();
                              }}
                            >
                              Yes, delete
                            </Button>
                            <Button
                              onClick={() => {}}
                              variant="outline"
                              className="hover:text-muted-foreground"
                            >
                              Cancel
                            </Button>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
