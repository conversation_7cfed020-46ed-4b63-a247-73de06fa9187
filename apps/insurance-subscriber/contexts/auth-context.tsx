import {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
} from "react";
import { API } from "@/apis/api";
import { TUser } from "@/types/user";
import { usePathname, useRouter } from "next/navigation";
import { toast } from "sonner";
import { TTenant } from "@/apis/tenant.api";

interface AuthContextType {
  user: TUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signOut: () => Promise<void>;
  refetch: () => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  getTenant: () => Promise<void>;
  tenant: TTenant | null;
}

// Create a context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  signOut: async () => {},
  refetch: async () => {},
  signIn: async () => {},
  getTenant: async () => {},
  tenant: null,
});

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<TUser | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [tenant, setTenant] = useState<TTenant | null>(null);
  const router = useRouter();
  const pathname = usePathname();

  const fetchUser = async (): Promise<void> => {
    setIsLoading(true);
    try {
      // Try to get user from localStorage first for immediate UI update
      const storedUser = localStorage.getItem("user");
      if (storedUser) {
        try {
          setUser(JSON.parse(storedUser));
        } catch (error) {
          console.error("Failed to parse user from localStorage", error);
          localStorage.removeItem("user");
        }
      }

      // Then fetch fresh data from API
      const { data, errors } = await API.AUTH.GetCurrentUser();

      if (errors) {
        errors.forEach((error) => {
          if (error.message) toast.error(error.message);
        });
        setUser(null);
        localStorage.removeItem("user");
        return;
      }

      if (data) {
        setUser(data);
        localStorage.setItem("user", JSON.stringify(data));
      } else {
        setUser(null);
        localStorage.removeItem("user");
      }
    } catch (error) {
      console.error("Auth fetch failed:", error);
      setUser(null);
      localStorage.removeItem("user");
    } finally {
      setIsLoading(false);
    }
  };

  const getTenant = async () => {
    const { data, errors } = await API.TENANT.GetTenant();

    if (!errors && data) {
      // set localStorage
      localStorage.setItem("tenant", JSON.stringify(data));
      setTenant(data);
    }
  };

  useEffect(() => {
    if (!pathname.includes("sign-up")) {
      fetchUser();
    }
  }, []);

  useEffect(() => {
    if (!localStorage.getItem("tenant") && user) {
      getTenant();
    }
    if (localStorage.getItem("tenant")) {
      setTenant(JSON.parse(localStorage.getItem("tenant")!));
    }
  }, [user]);

  const signOut = async (): Promise<void> => {
    try {
      const { data, errors } = await API.AUTH.SignOut();

      if (data) {
        setUser(null);
        localStorage.removeItem("user");
        document.cookie =
          "_session=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC;";
        toast.success(data.message || "Successfully signed out");
        router.push("/sign-in");
      }

      if (errors) {
        throw new Error(errors[0]?.message || "Failed to sign out");
      }
    } catch (error) {
      console.error("Failed to sign out", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to sign out"
      );
    }
  };

  const signIn = async (email: string, password: string): Promise<void> => {
    try {
      const { data, errors } = await API.AUTH.SignIn({
        Email: email,
        Password: password,
      });

      if (data) {
        // set cookie
        document.cookie = `_session=${(data.ack as unknown as { token: string }).token}; path=/; expires=${new Date(Date.now() + 86400000).toUTCString()}`;
        toast.success(data.message || "Login successful");
        fetchUser();
        localStorage.setItem("user", JSON.stringify(data.ack));
        localStorage.removeItem("tenant");
        router.push("/");
      }

      if (errors) {
        const errorMessage = errors[0]?.message || "Authentication failed";
        toast.error(errorMessage);
      }
    } catch (err) {
      console.error("Login error:", err);
      const errorMessage = "An unexpected error occurred. Please try again.";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    signOut,
    refetch: fetchUser,
    signIn,
    getTenant,
    tenant,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);
