DERIEVE_SECTIONS_PROMPT = """
You are a helpful assistant that can derive topics/sections and hierarhical subtopics/subsections from a given text.

You will be given a object and you need to derive the topics/sections and hierarhical subtopics/subsections from the object.
The object is a list of dictionaries, each dictionary contains the following keys:
- page_content: The content of the page
- metadata: The metadata of the page

Pages can be from any kind on document, like a policy, a contract, a report, academic books, etc and from any industry, like insurance, banking, healthcare, academic etc. And can be in any language, like English, Hindi, Marathi, etc. Language will be provided in the input separately, so use and identify topics/sections based on it.

Your task is to derive the topics/sections and hierarhical subtopics/subsections from the page_contents. You can use the metadata if needed for understanding the linkages between the topics/sections across the pages and the hierarchy of the topics/sections and continuing the topics/sections across the pages.

First identify kind of document and industry from the page_contents and maintain the context of it for further processing.

Make sure all the topics/sections and hierarhical subtopics/subsections are covered from the provided source.
Do not miss any topics/sections or hierarhical subtopics/subsections.
Maintain the hierarchy of the topics/sections and hierarhical subtopics/subsections across the pages, if continued to next pages.
Read through all the documents in order and identify all headings and subheadings in the text (ignore activity prompts, callout boxes, or questions unless they are formatted as official headings).
Please provide a clear, hierarchical list of all the headings and subheadings as they appear in the text.
Try to indentify exact heading or subheading for topics/sections, and hierarhical subtopics/subsections from the provided pages.
Also get page number range for each topic and start page number for each subtopic.

DO NOT HALLUCINATE ANY TOPICS/SECTIONS OR SUBTOPICS/SUBSECTIONS OR PAGE NUMBERS. ONLY USE THE PROVIDED TEXT TO DERIVE THE TOPICS/SECTIONS AND SUBTOPICS/SUBSECTIONS. (VERY IMPORTANT, SHOULD BE STRICTLY BASED ON THE PROVIDED SOURCE)

The topics/sections and hierarhical subtopics/subsections should be in the following format:
[
    {{
        "section_name": "The name of the section",
        "section_description": "The description of the section in short under 200 characters",
        "subsections": [
            {{
                "subsection_name": "The name of the subsection (if any)",
                "page_number": "The start page number for the subsection",
            }}
        ],
        "page_numbers": {{  
            "start_page_number": "The start page number for the section",
            "end_page_number": "The end page number for the section",
        }}
    }}
]

# Inputs
Source Document Pages: {source}

Source Document Language: {language}

Source Document File Type: {file_type}
"""

SUMMARIZE_CHUNKS_PROMPT = """
You are a helpful assistant that can summarize and optimize a given text for RAG retrieval.

You will be given a chunk of the page and you need to summarize it.

The text is a chunk of a document, and can be from any kind of document, like a policy, a contract, a report, academic books, etc and from any industry, like insurance, banking, healthcare, academic etc. And can be in any language, like English, Hindi, Marathi, etc. Language will be provided in the input separately, so use and summarize based on it in same language.

Your task is to summarize the text and optimize it for RAG retrieval.
Here summarization do not mean to necessarily reduce the text, but to optimize it for RAG retrieval. So you SHOULD NOT lose or remove or omit any key information or details from the text. 
Focus on industry specific terms, concepts, and jargon normally used in conversation and important data related to that industry.

The output should be in the following format:
{{
    "summary": "The summary of the text optimized for RAG retrieval",
}}

# Inputs
Chunk Content: {chunk_content}

Chunk Language: {language}
"""
