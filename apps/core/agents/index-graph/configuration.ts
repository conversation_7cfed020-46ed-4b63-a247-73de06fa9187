import { Annotation } from '@langchain/langgraph';
import { BaseConfigurationAnnotation } from '../shared/configuration';
import { RunnableConfig } from '@langchain/core/runnables';
import { ensureBaseConfiguration } from '../shared/configuration';

// This file contains sample documents to index, based on the following LangChain and LangGraph documentation pages:
// - https://python.langchain.com/docs/concepts/
// - https://langchain-ai.github.io/langgraph/concepts/low_level/
const DEFAULT_DOCS_FILE = './science-textbook-ix.json';

/**
 * The configuration for the indexing process.
 */
export const IndexConfigurationAnnotation = Annotation.Root({
  ...BaseConfigurationAnnotation.spec,

  /**
   * PDF blob to process and index
   */
  pdfBlob: Annotation<Blob>,

  /**
   * Tenant ID for the document being indexed
   */
  tenantId: Annotation<string>,

  /**
   * Project ID for the document being indexed
   */
  projectId: Annotation<string>,

  /**
   * Original filename of the document
   */
  fileName: Annotation<string>,

  /**
   * Book ID for the document being indexed
   */
  sourceId: Annotation<string>,

  /**
   * Pages of content to index
   */
  pagesOfContent: Annotation<number[]>,

  /**
   * Page number offset for the document being indexed
   */
  pageNumberOffset: Annotation<number>,

  /**
   * Source name for the document being indexed
   */
  sourceName: Annotation<string>,
});

/**
 * Create an typeof IndexConfigurationAnnotation.State instance from a RunnableConfig object.
 *
 * @param config - The configuration object to use.
 * @returns An instance of typeof IndexConfigurationAnnotation.State with the specified configuration.
 */
export function ensureIndexConfiguration(config: RunnableConfig): typeof IndexConfigurationAnnotation.State {
  const configurable = (config?.configurable || {}) as Partial<typeof IndexConfigurationAnnotation.State>;
  const baseConfig = ensureBaseConfiguration(config);

  if (!configurable.pdfBlob) {
    throw new Error('PDF Blob is required for indexing');
  }

  if (!configurable.tenantId) {
    throw new Error('Tenant ID is required for indexing');
  }

  if (!configurable.projectId) {
    throw new Error('Project ID is required for indexing');
  }

  return {
    ...baseConfig,
    pdfBlob: configurable.pdfBlob,
    tenantId: configurable.tenantId,
    projectId: configurable.projectId,
    fileName: configurable.fileName || 'unknown.pdf',
    sourceId: configurable.sourceId,
    sourceName: configurable.sourceName,
    pagesOfContent: configurable.pagesOfContent,
    pageNumberOffset: configurable.pageNumberOffset,
  };
}
