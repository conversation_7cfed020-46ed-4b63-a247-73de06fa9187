import { Test, TestingModule } from '@nestjs/testing';
import { ChatService } from './chat.service';
import { getModelToken } from '@nestjs/mongoose';
import { ChatMessage } from './schemas/chat-message.schema';
import { Thread } from './schemas/thread.schema';
import { Model } from 'mongoose';
import { CreateChatDto } from './dto/create-chat.dto';
import { ChatResponseDto } from './dto/chat-response.dto';
import { ThreadService } from './thread.service';
import { NotFoundException, UnauthorizedException } from '@nestjs/common';
import { ChatHistoryDto } from './dto/chat-history.dto';

describe('ChatService', () => {
  let service: ChatService;
  let chatMessageModel: Model<ChatMessage>;
  let threadService: ThreadService;

  // Sample data for testing
  const mockUserID = 'user123';
  const mockTenantID = 'tenant123';
  const mockProjectID = 'project123';
  const mockThreadId = 'thread123';
  const mockMessageId = 'msg123';
  const mockMessage = 'Test message';
  const mockAssistantResponse = 'This is a test response';

  // Mock messages
  const mockUserMessage = {
    messageId: mockMessageId,
    threadId: mockThreadId,
    role: 'user',
    content: mockMessage,
    timestamp: new Date(),
    UserID: mockUserID,
    TenantID: mockTenantID,
    ProjectID: mockProjectID,
  };

  const mockAssistantMessage = {
    messageId: 'msg456',
    threadId: mockThreadId,
    role: 'assistant',
    content: mockAssistantResponse,
    timestamp: new Date(),
    UserID: mockUserID,
    TenantID: mockTenantID,
    ProjectID: mockProjectID,
  };

  const mockChatDto: CreateChatDto = {
    message: mockMessage,
    ProjectID: mockProjectID,
    ThreadID: mockThreadId,
  };

  const mockChatResponseDto: ChatResponseDto = {
    message: mockAssistantResponse,
    ThreadID: mockThreadId,
    isComplete: true,
  };

  beforeEach(async () => {
    // Create mock for ChatMessageModel
    const chatMessageModelMock = {
      new: jest.fn(),
      constructor: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      deleteMany: jest.fn(),
      exec: jest.fn(),
    };

    // Create mock for ThreadService
    const threadServiceMock = {
      validateAndGetThread: jest.fn(),
      createThread: jest.fn(),
      validateThreadOwnership: jest.fn(),
      updateThreadActivity: jest.fn(),
      deleteThread: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatService,
        {
          provide: getModelToken(ChatMessage.name),
          useValue: chatMessageModelMock,
        },
        {
          provide: ThreadService,
          useValue: threadServiceMock,
        },
      ],
    }).compile();

    service = module.get<ChatService>(ChatService);
    chatMessageModel = module.get<Model<ChatMessage>>(getModelToken(ChatMessage.name));
    threadService = module.get<ThreadService>(ThreadService);

    // Mock the retrieval graph function
    jest.spyOn<any, any>(service, 'getRetrievalGraph').mockResolvedValue({
      processQuery: jest.fn().mockResolvedValue({
        logic: 'Test logic',
        relevantDocs: ['doc1', 'doc2'],
      }),
    });

    // Mock the AI model
    jest.spyOn<any, any>(service, 'loadModel').mockResolvedValue({
      call: jest.fn().mockResolvedValue({ text: mockAssistantResponse }),
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new thread if threadId is not provided', async () => {
      const createChatDtoWithoutThread: CreateChatDto = {
        message: mockMessage,
        ProjectID: mockProjectID,
      };

      const newThreadId = 'newThread123';

      threadService.createThread = jest.fn().mockResolvedValue({
        threadId: newThreadId,
        ProjectID: mockProjectID,
        title: 'New Thread',
      });

      chatMessageModel.create = jest
        .fn()
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce({
          ...mockAssistantMessage,
          threadId: newThreadId,
        });

      const result = await service.create(mockUserID, mockTenantID, createChatDtoWithoutThread);

      expect(threadService.createThread).toHaveBeenCalledWith(
        mockUserID,
        mockTenantID,
        expect.objectContaining({
          ProjectID: mockProjectID,
        })
      );

      expect(chatMessageModel.create).toHaveBeenCalledTimes(2);
      expect(result.ThreadID).toBe(newThreadId);
    });

    it('should use existing thread if threadId is provided', async () => {
      threadService.validateAndGetThread = jest.fn().mockResolvedValue({
        threadId: mockThreadId,
        ProjectID: mockProjectID,
      });

      chatMessageModel.create = jest
        .fn()
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockAssistantMessage);

      const result = await service.create(mockUserID, mockTenantID, mockChatDto);

      expect(threadService.validateAndGetThread).toHaveBeenCalledWith(mockThreadId, mockUserID);
      expect(chatMessageModel.create).toHaveBeenCalledTimes(2);
      expect(result.ThreadID).toBe(mockThreadId);
      expect(result.message).toBe(mockAssistantResponse);
    });
  });

  describe('streamResponse', () => {
    it('should stream responses in chunks', async () => {
      const chunks = [];
      const messageHandler = jest.fn((chunk) => {
        chunks.push(chunk);
      });

      threadService.validateAndGetThread = jest.fn().mockResolvedValue({
        threadId: mockThreadId,
        ProjectID: mockProjectID,
      });

      chatMessageModel.create = jest
        .fn()
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockAssistantMessage);

      chatMessageModel.find = jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue([mockUserMessage, mockAssistantMessage]),
        }),
      });

      // Mock the streaming model
      jest.spyOn<any, any>(service, 'loadStreamingModel').mockResolvedValue({
        stream: jest.fn().mockImplementation(async function* () {
          yield { text: 'This ' };
          yield { text: 'is ' };
          yield { text: 'a ' };
          yield { text: 'test ' };
          yield { text: 'response' };
        }),
      });

      await service.streamResponse(mockUserID, mockTenantID, mockChatDto, messageHandler);

      expect(threadService.validateAndGetThread).toHaveBeenCalledWith(mockThreadId, mockUserID);
      expect(chatMessageModel.create).toHaveBeenCalledTimes(2);
      expect(messageHandler).toHaveBeenCalledTimes(6); // 5 text chunks + 1 initial
      expect(chunks[0].isComplete).toBe(false);
      expect(chunks[5].isComplete).toBe(true);
    });
  });

  describe('getChatHistory', () => {
    it('should return chat history for a thread', async () => {
      const messages = [mockUserMessage, mockAssistantMessage];

      threadService.validateAndGetThread = jest.fn().mockResolvedValue({
        threadId: mockThreadId,
        ProjectID: mockProjectID,
        title: 'Test Thread',
        userId: mockUserID,
        tenantId: mockTenantID,
      });

      chatMessageModel.find = jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(messages),
        }),
      });

      const result = await service.getChatHistory(mockThreadId, mockUserID);

      expect(threadService.validateAndGetThread).toHaveBeenCalledWith(mockThreadId, mockUserID);
      expect(chatMessageModel.find).toHaveBeenCalledWith({ threadId: mockThreadId });
      expect(result.messages.length).toBe(2);
      expect(result.ThreadID).toBe(mockThreadId);
    });

    it('should throw if thread does not exist', async () => {
      threadService.validateAndGetThread = jest.fn().mockRejectedValue(new NotFoundException('Thread not found'));

      await expect(service.getChatHistory(mockThreadId, mockUserID)).rejects.toThrow(NotFoundException);
    });
  });

  describe('clearChatHistory', () => {
    it('should delete all messages for a thread', async () => {
      threadService.validateAndGetThread = jest.fn().mockResolvedValue({
        threadId: mockThreadId,
      });

      chatMessageModel.deleteMany = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 2 }),
      });

      const result = await service.clearChatHistory(mockThreadId, mockUserID);

      expect(threadService.validateAndGetThread).toHaveBeenCalledWith(mockThreadId, mockUserID);
      expect(chatMessageModel.deleteMany).toHaveBeenCalledWith({ threadId: mockThreadId });
      expect(result).toEqual({ success: true });
    });
  });

  describe('deleteThread', () => {
    it('should delete thread and all associated messages', async () => {
      threadService.validateAndGetThread = jest.fn().mockResolvedValue({
        threadId: mockThreadId,
      });

      threadService.deleteThread = jest.fn().mockResolvedValue(true);

      chatMessageModel.deleteMany = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 2 }),
      });

      const result = await service.deleteThread(mockThreadId, mockUserID);

      expect(threadService.validateAndGetThread).toHaveBeenCalledWith(mockThreadId, mockUserID);
      expect(threadService.deleteThread).toHaveBeenCalledWith(mockThreadId);
      expect(chatMessageModel.deleteMany).toHaveBeenCalledWith({ threadId: mockThreadId });
      expect(result).toEqual({ success: true });
    });

    it('should throw if thread deletion fails', async () => {
      threadService.validateAndGetThread = jest.fn().mockResolvedValue({
        threadId: mockThreadId,
      });

      threadService.deleteThread = jest.fn().mockResolvedValue(false);

      await expect(service.deleteThread(mockThreadId, mockUserID)).rejects.toThrow();
    });
  });
});
