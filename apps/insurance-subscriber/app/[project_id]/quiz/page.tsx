"use client";
import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { useProjectContext } from "@/contexts/project-context";
import { API } from "@/apis/api";
import { TPopQuiz } from "@/apis/pop-quiz.api";
import { TProject } from "@/apis/subscription.api";
import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@workspace/ui/components/card";
import {
  Check,
  ShieldQuestion,
  Clock,
  Book,
  BarChart,
  Brain,
  BookText,
  BookOpenCheck,
  CircleHelp,
  RefreshCw,
} from "lucide-react";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import { useQuizStore } from "@/components/pop-quiz/pop-quiz-list";

export default function QuizPage() {
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const { selectedProject, projects, setSelectedProject } = useProjectContext();
  const {
    quizzes,
    isLoading: isQuizzesLoading,
    fetchQuizzes,
    triggerRefresh,
  } = useQuizStore();

  const [isProjectLoading, setIsProjectLoading] = useState(true);
  const [isTriggering, setIsTriggering] = useState(false);
  const [projectDetails, setProjectDetails] = useState<TProject | null>(null);
  const [questionCount, setQuestionCount] = useState<number>(5);

  // Greeting messages
  const greetings = [
    "Ready to test your knowledge?",
    "Let's see what you've learned!",
    "Time to challenge yourself!",
    "Put your knowledge to the test!",
    "Quiz time! How much do you remember?",
    "Ready for a brain workout?",
    "Let's flex those mental muscles!",
    "Pop quiz time - no pressure!",
    "Challenge accepted?",
    "Time to prove what you know!",
  ];

  const randomGreeting =
    greetings[Math.floor(Math.random() * greetings.length)];

  // Calculate statistics
  const pendingQuizzes = quizzes.filter((quiz) => {
    const userSubscription = quiz.Subscribers?.find(
      (sub) => sub.UserID === user?.UserID,
    );
    return (
      (quiz.FloatingSubscribers && !userSubscription) ||
      userSubscription?.Status === "Pending"
    );
  });

  const completedQuizzes = quizzes.filter((quiz) => {
    const userSubscription = quiz.Subscribers?.find(
      (sub) => sub.UserID === user?.UserID,
    );
    return userSubscription?.Status === "Submitted";
  });

  const pendingSelfQuizzes = pendingQuizzes.filter((quiz) => quiz.IsSelfQuiz);
  const pendingAdminQuizzes = pendingQuizzes.filter((quiz) => !quiz.IsSelfQuiz);
  const completedSelfQuizzes = completedQuizzes.filter(
    (quiz) => quiz.IsSelfQuiz,
  );
  const completedAdminQuizzes = completedQuizzes.filter(
    (quiz) => !quiz.IsSelfQuiz,
  );

  // Fetch project data and initialize everything when component mounts or project changes
  useEffect(() => {
    if (params.project_id) {
      fetchProjectDetails(params.project_id as string);
    }
  }, [params.project_id]);

  // When project selector changes, navigate to that project's quiz page
  useEffect(() => {
    if (selectedProject && selectedProject.id !== params.project_id) {
      router.push(`/${selectedProject.id}/quiz`);
    }
  }, [selectedProject]);

  const fetchProjectDetails = async (projectId: string) => {
    setIsProjectLoading(true);
    try {
      // Get projects to find tenant ID
      const projectsResponse = await API.SUBSCRIPTION.GetMyProjects();

      if (projectsResponse.errors) {
        toast.error("Failed to fetch project details");
        setIsProjectLoading(false);
        return;
      }

      // Find the current project in the response
      const projectDetail = projectsResponse.data?.find(
        (project: TProject) => project.ProjectID === projectId,
      );

      if (!projectDetail) {
        toast.error("Project not found");
        setIsProjectLoading(false);
        return;
      }

      setProjectDetails(projectDetail);

      // Set selected project in context if not already set to this project
      if (!selectedProject || selectedProject.id !== projectId) {
        const projectForContext = {
          id: projectDetail.ProjectID,
          name: projectDetail.Name,
          description: projectDetail.Description,
        };
        setSelectedProject(projectForContext);
      }

      // Now fetch quizzes using the store
      fetchQuizzes(projectId);
    } catch (error) {
      console.error("Error fetching project details:", error);
      toast.error("Failed to fetch project data");
    } finally {
      setIsProjectLoading(false);
    }
  };

  const handleTriggerNewQuiz = async () => {
    if (!selectedProject) {
      toast.error("Please select a project");
      return;
    }

    if (questionCount < 1 || questionCount > 20) {
      toast.error("Question count must be between 1 and 20");
      return;
    }

    setIsTriggering(true);
    try {
      // Use the proper params format for TriggerSelfQuiz API
      const result = await API.POP_QUIZ.TriggerSelfQuiz({
        ProjectID: selectedProject.id,
        NumberOfQuestions: questionCount,
      });

      if (result.errors) {
        result.errors.forEach((error) => {
          toast.error(error.message || "Failed to trigger quiz");
        });
        return;
      }

      // The API returns the new quiz info in the ack property
      const popQuizId = result.data?.PopQuizID;

      if (popQuizId) {
        toast.success("Quiz generated successfully!");

        // Trigger a refresh in the quiz store
        triggerRefresh();

        // Navigate to the new quiz
        router.push(`/${selectedProject.id}/quiz/${popQuizId}`);
      } else {
        toast.error("Failed to create quiz");
      }
    } catch (error) {
      console.error("Error triggering quiz:", error);
      toast.error("Failed to trigger quiz");
    } finally {
      setIsTriggering(false);
    }
  };

  const handleRefresh = () => {
    if (selectedProject?.id) {
      triggerRefresh();
    }
  };

  const isLoading = isProjectLoading || isQuizzesLoading;

  if (isLoading) {
    return (
      <div className="container mx-auto max-w-4xl py-8 space-y-6">
        <Skeleton className="h-10 w-1/3" />
        <Skeleton className="h-6 w-2/3" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <Skeleton className="h-10 w-full mt-4" />
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-4xl py-8 space-y-6">
      {/* Header with Project Selector */}
      <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold mb-1">
            Hello, <span className="text-primary">{user?.Name}</span>!
          </h1>
          <p className="text-lg text-muted-foreground">{randomGreeting}</p>
        </div>

        <div className="w-full md:w-64">
          <Label htmlFor="project-select" className="text-sm mb-1 block">
            Select Project
          </Label>
          <Select
            value={selectedProject?.id || ""}
            onValueChange={(value) => {
              const project = projects.find((p) => p.id === value);
              if (project) {
                setSelectedProject(project);
              }
            }}
          >
            <SelectTrigger id="project-select" className="w-full">
              <SelectValue placeholder="Select a project" />
            </SelectTrigger>
            <SelectContent>
              {projects.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Quiz Status Dashboard */}
      <Card className="border shadow-sm">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <div>
            <CardTitle>Quiz Dashboard</CardTitle>
            <CardDescription>
              Track your progress and quiz completion statistics
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            className="h-8 w-8 p-0"
            title="Refresh quizzes"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Pending Quizzes */}
            <div className="flex flex-col p-4 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Clock className="h-5 w-5 text-amber-500" />
                <h3 className="text-sm font-medium">Pending</h3>
              </div>
              <p className="text-3xl font-bold">{pendingQuizzes.length}</p>
              <div className="text-xs text-muted-foreground mt-1 space-y-1">
                <div className="flex justify-between">
                  <span>Self-Quizzes:</span>
                  <span className="font-medium">
                    {pendingSelfQuizzes.length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Admin Quizzes:</span>
                  <span className="font-medium">
                    {pendingAdminQuizzes.length}
                  </span>
                </div>
              </div>
            </div>

            {/* Completed Quizzes */}
            <div className="flex flex-col p-4 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Check className="h-5 w-5 text-green-500" />
                <h3 className="text-sm font-medium">Completed</h3>
              </div>
              <p className="text-3xl font-bold">{completedQuizzes.length}</p>
              <div className="text-xs text-muted-foreground mt-1 space-y-1">
                <div className="flex justify-between">
                  <span>Self-Quizzes:</span>
                  <span className="font-medium">
                    {completedSelfQuizzes.length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Admin Quizzes:</span>
                  <span className="font-medium">
                    {completedAdminQuizzes.length}
                  </span>
                </div>
              </div>
            </div>

            {/* Self-Triggered */}
            <div className="flex flex-col p-4 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <Brain className="h-5 w-5 text-indigo-500" />
                <h3 className="text-sm font-medium">Self Quizzes</h3>
              </div>
              <p className="text-3xl font-bold">
                {pendingSelfQuizzes.length + completedSelfQuizzes.length}
              </p>
              <div className="text-xs text-muted-foreground mt-1 space-y-1">
                <div className="flex justify-between">
                  <span>Pending:</span>
                  <span className="font-medium">
                    {pendingSelfQuizzes.length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Completed:</span>
                  <span className="font-medium">
                    {completedSelfQuizzes.length}
                  </span>
                </div>
              </div>
            </div>

            {/* Admin Quizzes */}
            <div className="flex flex-col p-4 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <BookOpenCheck className="h-5 w-5 text-blue-500" />
                <h3 className="text-sm font-medium">Admin Quizzes</h3>
              </div>
              <p className="text-3xl font-bold">
                {pendingAdminQuizzes.length + completedAdminQuizzes.length}
              </p>
              <div className="text-xs text-muted-foreground mt-1 space-y-1">
                <div className="flex justify-between">
                  <span>Pending:</span>
                  <span className="font-medium">
                    {pendingAdminQuizzes.length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Completed:</span>
                  <span className="font-medium">
                    {completedAdminQuizzes.length}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Create New Quiz Section */}
      <Card className="border shadow-sm bg-muted/10">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2">
            <CircleHelp className="h-5 w-5 text-primary" />
            Trigger New Self Quiz
          </CardTitle>
          <CardDescription>
            Test your knowledge by creating a new quiz with custom settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
              <div className="space-y-2">
                <Label htmlFor="question-count" className="text-sm font-medium">
                  Number of Questions
                </Label>
                <div className="flex items-center gap-4">
                  <Input
                    id="question-count"
                    type="number"
                    min={1}
                    max={20}
                    value={questionCount}
                    onChange={(e) =>
                      setQuestionCount(parseInt(e.target.value) || 5)
                    }
                    className="w-24"
                  />
                  <div className="flex-1 flex items-center gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() =>
                        setQuestionCount(Math.max(1, questionCount - 1))
                      }
                      disabled={questionCount <= 1}
                    >
                      <span>-</span>
                    </Button>
                    <div className="w-full bg-muted/40 h-2 rounded-full">
                      <div
                        className="bg-primary h-2 rounded-full transition-all"
                        style={{
                          width: `${Math.min(100, (questionCount / 20) * 100)}%`,
                        }}
                      ></div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() =>
                        setQuestionCount(Math.min(20, questionCount + 1))
                      }
                      disabled={questionCount >= 20}
                    >
                      <span>+</span>
                    </Button>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Choose between 1-20 questions for your quiz
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <div className="text-sm text-muted-foreground">
                  Ready to test your knowledge?
                </div>
                <Button
                  onClick={handleTriggerNewQuiz}
                  disabled={isTriggering || !selectedProject}
                  className="gap-2 h-10 w-full"
                  size="lg"
                >
                  {isTriggering ? (
                    <>
                      <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
                      Generating Quiz...
                    </>
                  ) : (
                    <>
                      Start New Quiz
                      <ShieldQuestion className="h-4 w-4" />
                    </>
                  )}
                </Button>
                <Button
                  disabled
                  className="gap-2 h-10 w-full"
                  size="lg"
                  variant="outline"
                >
                  <>
                    Practice Mistakes (Coming Soon)
                    <ShieldQuestion className="h-4 w-4" />
                  </>
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quiz List */}
      <Card className="border shadow-sm mt-6">
        <CardHeader className="pb-2">
          <CardTitle>Your Quizzes</CardTitle>
          <CardDescription>
            View and manage your pending and completed quizzes
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs defaultValue="pending" className="w-full">
            <div className="px-6 pt-2 border-b">
              <TabsList className="w-full grid grid-cols-2 mb-2">
                <TabsTrigger value="pending" className="relative">
                  Pending
                  {pendingQuizzes.length > 0 && ` (${pendingQuizzes.length})`}
                </TabsTrigger>
                <TabsTrigger value="completed">Completed</TabsTrigger>
              </TabsList>
            </div>

            {/* Pending Quizzes Tab */}
            <TabsContent value="pending" className="p-4 space-y-4">
              {pendingQuizzes.length === 0 ? (
                <div className="text-center py-6">
                  <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                  <h3 className="text-lg font-medium">No Pending Quizzes</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    You don't have any quizzes waiting for your response.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {pendingQuizzes.map((quiz) => (
                    <Card
                      key={quiz.PopQuizID}
                      className="cursor-pointer hover:border-primary transition-colors"
                      onClick={() =>
                        router.push(
                          `/${selectedProject?.id}/quiz/${quiz.PopQuizID}`,
                        )
                      }
                    >
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-base">
                            {quiz.Title}
                          </CardTitle>
                          <div
                            className={`px-2 py-1 rounded-full text-xs ${quiz.IsSelfQuiz ? "bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300" : "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300"}`}
                          >
                            {quiz.IsSelfQuiz ? "Self Quiz" : "Admin Quiz"}
                          </div>
                        </div>
                        <CardDescription className="line-clamp-1">
                          {quiz.Description ||
                            "Test your knowledge on this topic"}
                        </CardDescription>
                      </CardHeader>
                      <CardFooter className="pt-0 pb-3 text-xs text-muted-foreground">
                        <div className="flex justify-between w-full">
                          <span>{quiz.QuestionCount} questions</span>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>Pending Response</span>
                          </div>
                        </div>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            {/* Completed Quizzes Tab */}
            <TabsContent value="completed" className="p-4 space-y-4">
              {completedQuizzes.length === 0 ? (
                <div className="text-center py-6">
                  <BookText className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                  <h3 className="text-lg font-medium">No Completed Quizzes</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    You haven't completed any quizzes yet.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {completedQuizzes.map((quiz) => (
                    <Card
                      key={quiz.PopQuizID}
                      className="cursor-pointer hover:border-primary transition-colors"
                      onClick={() =>
                        router.push(
                          `/${selectedProject?.id}/quiz/${quiz.PopQuizID}`,
                        )
                      }
                    >
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-base">
                            {quiz.Title}
                          </CardTitle>
                          <div
                            className={`px-2 py-1 rounded-full text-xs ${quiz.IsSelfQuiz ? "bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300" : "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300"}`}
                          >
                            {quiz.IsSelfQuiz ? "Self Quiz" : "Admin Quiz"}
                          </div>
                        </div>
                        <CardDescription className="line-clamp-1">
                          {quiz.Description || "Completed quiz"}
                        </CardDescription>
                      </CardHeader>
                      <CardFooter className="pt-0 pb-3 text-xs text-muted-foreground">
                        <div className="flex justify-between w-full">
                          <span>{quiz.QuestionCount} questions</span>
                          <div className="flex items-center gap-1">
                            <Check className="h-3 w-3 text-green-500" />
                            <span>Completed</span>
                          </div>
                        </div>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
