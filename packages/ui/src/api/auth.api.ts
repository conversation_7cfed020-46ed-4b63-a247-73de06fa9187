import { TryCatch } from "../lib/try-catch.js";
import { KY, TAckResponse, TErrorResponse } from "./_.index.js";

export type TGender = "Male" | "Female" | "Other";
export type TUserType = "Admin" | "Subscriber";
export type PartialUserAttrs = {
  Name: string;
  PhoneNumber: string;
  Gender: TGender;
  DateOfBirth: string;
  Address: string;
  ProfilePicture: string;
  ZoneInfo: string;
  Locale: string;
  GivenName: string;
  FamilyName: string;
  MiddleName: string;
  Website: string;
  UserID: string;
  UserType: TUserType;
  OAuth?: {
    Google: {
      Connected: boolean;
      UserID: string;
    };
  };
};

export type TUser = {
  Email: string;
  TenantID: string;
} & Partial<PartialUserAttrs>;

const GetCurrentUser = async () => {
  return await TryCatch<TUser, TErrorResponse>(KY.get("auth/current").json());
};

type TSignInParams = {
  Email: string;
  Password: string;
};
const SignIn = async (params: TSignInParams) => {
  return await TryCatch<TAckResponse<TUser>, TErrorResponse>(
    KY.post("auth/sign-in?UserType=Admin,Employee", { json: params }).json()
  );
};

type TUpdateDetailsParams = {
  Email?: string;
  Password?: string;
  FirstName?: string;
  LastName?: string;
  ProfilePicture?: string;
};
const UpdateDetails = async (params: TUpdateDetailsParams) => {
  return await TryCatch<TAckResponse<TUser>, TErrorResponse>(
    KY.put("auth/details", { json: params }).json()
  );
};

const SignOut = async () => {
  // delete cookie
  document.cookie = "_session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/";
  return await TryCatch<TAckResponse<void>, TErrorResponse>(
    KY.delete("auth/sign-out").json()
  );
};

export const AUTH = {
  GetCurrentUser,
  SignIn,
  UpdateDetails,
  SignOut,
};
