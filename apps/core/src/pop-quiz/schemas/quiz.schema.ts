import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import GenerateID from '../../../common/lib/generate-id';
import mongoose from 'mongoose';
import { PopQuizStatus, SubscriberStatus, TPopQuizStatus } from '../entities/pop-quiz.entity';
import { TSubscriberStatus } from '../entities/pop-quiz.entity';
import { PopQuizQuestionModel } from './question.schema';

@Schema({
  _id: null,
})
class Subscriber {
  @Prop({
    type: String,
    required: true,
  })
  UserID: string;

  @Prop({
    type: String,
    enum: SubscriberStatus.options,
    default: SubscriberStatus.enum.Pending,
  })
  Status: TSubscriberStatus;
}

@Schema({
  collection: 'pop_quiz',
  timestamps: true,
  versionKey: false,
})
export class PopQuizModel {
  @Prop({
    type: String,
    required: true,
    trim: true,
  })
  Title: string;

  @Prop({
    type: String,
  })
  Description?: string;

  @Prop({
    type: String,
    enum: PopQuizStatus.options,
    default: PopQuizStatus.enum.Draft,
  })
  Status: TPopQuizStatus;

  @Prop({
    type: [String],
    default: [],
  })
  Questions: string[];

  @Prop({
    type: Number,
    default: 0,
  })
  QuestionCount: number;

  @Prop({
    type: String,
    default: () => GenerateID.Generate16Hex('pop_quiz_'),
  })
  PopQuizID: string;

  @Prop({
    type: mongoose.Schema.Types.Mixed,
    default: {},
  })
  Scopes: Record<string, string>;

  @Prop({
    type: String,
    required: true,
  })
  ProjectID: string;

  @Prop({
    type: String,
    required: true,
  })
  TenantID: string;

  @Prop({
    type: [Subscriber],
    default: [],
  })
  Subscribers: Subscriber[];

  @Prop({
    type: Boolean,
    default: false,
  })
  FloatingSubscribers: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  IsSelfQuiz: boolean;
}

export const PopQuizSchema = SchemaFactory.createForClass(PopQuizModel);

PopQuizSchema.virtual('QuestionDetails', {
  ref: PopQuizQuestionModel.name,
  localField: 'Questions',
  foreignField: 'QuestionID',
});
