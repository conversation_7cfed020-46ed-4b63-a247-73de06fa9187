import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateTokenCounterDto } from './dto/create-token-counter.dto';
import { TokenCounterModel } from './schemas/token-counter.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AppTypeEnum } from '../core/user/entities/user.entity';
import { ProjectModel } from '../project/schemas/project.schema';
import { User } from '../core/user/schemas/user.model';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class TokenCounterService {
  constructor(
    @InjectModel(TokenCounterModel.name) private readonly tokenCounterModel: Model<TokenCounterModel>,
    @InjectModel(ProjectModel.name) private readonly projectModel: Model<ProjectModel>,
    @InjectModel(User.name) private readonly userModel: Model<User>,
    private readonly eventEmitter: EventEmitter2
  ) {}

  async create(createTokenCounterDto: CreateTokenCounterDto) {
    const { ProjectID, TenantID, ResourceIDs, ResourceType, TokenCount, UserID } = createTokenCounterDto;
    const TokenCounter = await this.tokenCounterModel.create({
      ProjectID,
      TenantID,
      TokenCount: {
        InputTokens: TokenCount?.PromptTokens,
        OutputTokens: TokenCount?.CompletionTokens,
        TotalTokens: TokenCount?.TotalTokens,
        LLMInvocations: TokenCount?.LLMInvocations?.map((invocation) => ({
          TokenCount: {
            InputTokens: invocation?.tokenUsage?.promptTokens,
            OutputTokens: invocation?.tokenUsage?.completionTokens,
            TotalTokens: invocation?.tokenUsage?.totalTokens,
          },
          Model: invocation?.model,
          Node: invocation?.node,
          Usage: {
            InputTokens: invocation?.usage?.prompt_tokens,
            OutputTokens: invocation?.usage?.completion_tokens,
            TotalTokens: invocation?.usage?.total_tokens,
            InputTokenDetails: {
              AudioTokens: invocation?.usage?.prompt_tokens_details?.audio_tokens,
              CachedTokens: invocation?.usage?.prompt_tokens_details?.cached_tokens,
            },
            OutputTokenDetails: {
              AudioTokens: invocation?.usage?.completion_tokens_details?.audio_tokens,
              ReasoningTokens: invocation?.usage?.completion_tokens_details?.reasoning_tokens,
              AcceptedPredictionTokens: invocation?.usage?.completion_tokens_details?.accepted_prediction_tokens,
              RejectedPredictionTokens: invocation?.usage?.completion_tokens_details?.rejected_prediction_tokens,
            },
            SystemFingerprint: invocation?.system_fingerprint,
          },
        })),
      },
      ResourceType,
      ResourceIDs,
      UserID,
    });

    // Emit Event
    this.eventEmitter.emit('token-counter.created', TokenCounter);
    return TokenCounter;
  }

  async getProjectsOverviewForTenant(TenantID: string, AppType: AppTypeEnum) {
    const tokenCounter = await this.tokenCounterModel.aggregate([
      {
        $match: {
          TenantID,
        },
      },
      {
        $lookup: {
          from: 'projects',
          localField: 'ProjectID',
          foreignField: 'ProjectID',
          as: 'project',
        },
      },
      {
        $unwind: {
          path: '$project',
        },
      },
      {
        $match: {
          'project.AppType': AppType,
        },
      },
      {
        $group: {
          _id: {
            ProjectID: '$ProjectID',
            ResourceType: '$ResourceType',
          },
          TotalTokens: {
            $sum: '$TokenCount.TotalTokens',
          },
          InteractionCount: {
            $sum: 1,
          },
          Project: {
            $first: '$project',
          },
        },
      },
      {
        $group: {
          _id: '$_id.ProjectID',
          Resources: {
            $push: {
              ResourceType: '$_id.ResourceType',
              TotalTokens: '$TotalTokens',
              InteractionCount: '$InteractionCount',
            },
          },
          Project: {
            $first: '$Project',
          },
        },
      },
      {
        $project: {
          _id: 0,
          ProjectID: '$_id',
          Resources: 1,
          Project: 1,
        },
      },
    ]);

    const totalCounts = tokenCounter.reduce(
      (acc, curr) => {
        acc.TotalTokens += curr.Resources.reduce((acc, curr) => acc + curr.TotalTokens, 0);
        acc.InteractionCount += curr.Resources.reduce((acc, curr) => acc + curr.InteractionCount, 0);
        return acc;
      },
      { TotalTokens: 0, InteractionCount: 0 }
    );

    return { TotalCounts: totalCounts, Projects: tokenCounter };
  }

  async getUsersOverviewForProject(TenantID: string, AppType: AppTypeEnum, ProjectID: string) {
    const project = await this.projectModel.findOne({ TenantID, ProjectID });
    if (!project) {
      throw new NotFoundException('Project not found');
    }
    if (project.AppType !== AppType) {
      throw new BadRequestException('Project type does not match');
    }

    const tokenCounter = await this.tokenCounterModel.aggregate([
      {
        $match: {
          TenantID,
          ProjectID,
        },
      },
      {
        $group: {
          _id: {
            UserID: '$UserID',
            ResourceType: '$ResourceType',
          },
          TotalTokens: { $sum: '$TokenCount.TotalTokens' },
          InteractionCount: { $sum: 1 },
        },
      },
      {
        $group: {
          _id: '$_id.UserID',
          Resources: {
            $push: {
              ResourceType: '$_id.ResourceType',
              TotalTokens: '$TotalTokens',
              InteractionCount: '$InteractionCount',
            },
          },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: 'UserID',
          as: 'User',
          pipeline: [
            {
              $project: {
                Email: 1,
                UserType: 1,
                Name: 1,
                FamilyName: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$User',
        },
      },
      {
        $project: {
          _id: 0,
          UserID: '$_id',
          Resources: 1,
          User: 1,
        },
      },
    ]);

    const totalCounts = tokenCounter.reduce(
      (acc, curr) => {
        acc.TotalTokens += curr.Resources.reduce((acc, curr) => acc + curr.TotalTokens, 0);
        acc.InteractionCount += curr.Resources.reduce((acc, curr) => acc + curr.InteractionCount, 0);
        return acc;
      },
      { TotalTokens: 0, InteractionCount: 0 }
    );

    return { TotalCounts: totalCounts, Users: tokenCounter, Project: project };
  }

  async getUserStatsForProject(TenantID: string, AppType: AppTypeEnum, ProjectID: string, UserID: string) {
    const project = await this.projectModel.findOne({ TenantID, ProjectID });
    if (!project) {
      throw new NotFoundException('Project not found');
    }
    if (project.AppType !== AppType) {
      throw new BadRequestException('Project type does not match');
    }
    const user = await this.userModel.findOne({ TenantID, UserID }).select('UserID Email Name FamilyName AppType');
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.AppType !== AppType) {
      throw new BadRequestException('User type does not match');
    }

    const tokenCounter = await this.tokenCounterModel.find({ TenantID, ProjectID, UserID });

    return {
      User: user,
      Project: project,
      TokenCounter: tokenCounter,
    };
  }
}
