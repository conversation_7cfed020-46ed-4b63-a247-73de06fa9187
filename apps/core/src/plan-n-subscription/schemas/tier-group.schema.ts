import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import GenerateID from '../../../common/lib/generate-id';
import { PlanModel } from './plan.schema';

@Schema({
  timestamps: true,
  collection: 'tier_groups',
  versionKey: false,
})
export class TierGroupModel {
  @Prop({
    type: String,
    required: true,
  })
  Name: string;

  @Prop({
    type: String,
  })
  Description?: string;

  @Prop({
    type: Array,
    default: [],
  })
  PlanIDs: string[];

  @Prop({
    type: String,
    default: () =>
      GenerateID.GenerateShortId({
        length: 7,
      }),
    unique: true,
  })
  InviteCode: string;

  @Prop({
    type: String,
    required: true,
  })
  TenantID: string;

  @Prop({
    type: Boolean,
    default: false,
  })
  ListAsPublic: boolean;
}

export const TierGroupSchema = SchemaFactory.createForClass(TierGroupModel);

TierGroupSchema.virtual('Plans', {
  ref: PlanModel.name,
  localField: 'PlanIDs',
  foreignField: 'PlanID',
  justOne: false,
});
