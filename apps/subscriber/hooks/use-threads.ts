import { useState, useEffect, useCallback, useRef } from "react";
import { API } from "@/apis/api";
import { ThreadDto } from "@/apis/chat.api";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth-context";

interface UseThreadsReturn {
  threads: ThreadDto[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useThreads(projectId?: string): UseThreadsReturn {
  const [threads, setThreads] = useState<ThreadDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isMounted = useRef(true);
  const { isAuthenticated } = useAuth();

  // Implement refetch function with useCallback to maintain reference stability
  const fetchThreads = useCallback(async (): Promise<void> => {
    if (!isMounted.current || !isAuthenticated) return;

    console.log("Fetching threads in useThreads hook");
    setIsLoading(true);
    setError(null);

    try {
      const { data, errors } = await API.CHAT.GetThreads();

      if (!isMounted.current) return;

      if (errors) {
        const errorMessage = errors[0]?.message || "Failed to fetch threads";
        setError(errorMessage);
        toast.error(errorMessage);
        return;
      }

      if (data) {
        const threadsArray = data.threads || [];
        setThreads(threadsArray);
      } else {
        setThreads([]);
      }
    } catch (err) {
      if (!isMounted.current) return;

      const errorMessage =
        "An unexpected error occurred while fetching threads";
      setError(errorMessage);
      toast.error(errorMessage);
      console.error("Threads fetch error:", err);
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId, isAuthenticated]);

  // Initial setup and cleanup
  useEffect(() => {
    isMounted.current = true;
    fetchThreads();

    return () => {
      isMounted.current = false;
    };
  }, [fetchThreads]);

  // Explicit handling of authentication state changes
  useEffect(() => {
    if (isAuthenticated && isMounted.current) {
      console.log("Auth state changed in useThreads, fetching threads");
      fetchThreads();
    }
  }, [isAuthenticated, fetchThreads]);

  return {
    threads,
    isLoading,
    error,
    refetch: fetchThreads,
  };
}
