import { DeleteObjectCommand, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { omit } from 'radash';
import GenerateID from '../../common/lib/generate-id';
import { CreateTenantDto, UpdateTenantDto } from './dto/tenant.service.dto';
import { TenantModel } from './schemas/tenant.schema';

@Injectable()
export class TenantService {
  private s3Client: S3Client;
  private s3Bucket: string;
  private path = 'academic-lm/tenant';
  constructor(
    @InjectModel(TenantModel.name)
    private readonly TenantModel: Model<TenantModel>,
    private readonly ConfigService: ConfigService
  ) {
    this.s3Bucket = `https://${this.ConfigService.get('S3_NXTCAMPUS_PLATFORM')}.s3.${this.ConfigService.get('AWS_REGION')}.amazonaws.com`;
    this.s3Client = new S3Client({
      region: this.ConfigService.get('AWS_REGION'),
      credentials: {
        accessKeyId: this.ConfigService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.ConfigService.get('AWS_SECRET_ACCESS_KEY'),
      },
    });
  }

  async HandleLogo(Logo: string, Key: string) {
    if (!Logo?.includes('base64')) {
      return {
        Location: Logo,
        Key,
      };
    }

    const Bucket = this.ConfigService.get('S3_NXTCAMPUS_PLATFORM');
    const upload = this.s3Client.send(
      new PutObjectCommand({
        Bucket,
        Key,
        Body: Buffer.from(Logo.split(',')[1], 'base64'),
        ContentType: Logo.split(';')[0].split(':')[1],
        ACL: 'public-read',
      })
    );

    return {
      Location: `${this.s3Bucket}/${Key}`,
      Key,
    };
  }

  async HandlePictures(Pictures: string[], path: string): Promise<string[]> {
    const Bucket = this.ConfigService.get('S3_NXTCAMPUS_PLATFORM');

    const Base64Pictures = Pictures?.filter((pic) => pic.includes('base64')) || [];
    const PictureURLs = Pictures?.filter((pic) => !pic.includes('base64')) || [];

    for (const pic of Base64Pictures) {
      const Key = `${path}/${GenerateID.Generate16Hex('pic_')}`;
      const upload = this.s3Client.send(
        new PutObjectCommand({
          Bucket,
          Key,
          Body: Buffer.from(pic.split(',')[1], 'base64'),
          ContentType: pic.split(';')[0].split(':')[1],
          ACL: 'public-read',
        })
      );
      await upload;
      PictureURLs.push(`${this.s3Bucket}/${Key}`);
    }

    return PictureURLs;
  }

  async CreateTenant(CreateTenantDto: CreateTenantDto) {
    const WorkSpaceDomain = CreateTenantDto?.WorkSpaceDomain;
    const findTenant = await this.TenantModel.findOne({ WorkSpaceDomain });
    if (findTenant) {
      throw new BadRequestException('Workspace domain already exists');
    }
    const TenantID = GenerateID.Generate16Hex('tnt_');
    const Logo = await this.HandleLogo(CreateTenantDto?.Logo, `${this.path}/${TenantID}/assets/logo`);
    const Pictures = await this.HandlePictures(CreateTenantDto?.Pictures, `${this.path}/${TenantID}/assets/pictures`);

    return await this.TenantModel.create({
      ...CreateTenantDto,
      TenantID,
      AdminUserID: CreateTenantDto.AdminUserID,
      Logo: Logo.Location,
      Pictures,
    });
  }

  async GetTenant(TenantID: string) {
    return await this.TenantModel.findOne({ TenantID });
  }

  async UpdateTenant(TenantID: string, UpdateTenantDto: UpdateTenantDto) {
    const tenant = await this.TenantModel.findOne({ TenantID });

    const Logo = await this.HandleLogo(UpdateTenantDto?.Logo, `${this.path}/${TenantID}/assets/logo`);
    const Pictures = await this.HandlePictures(UpdateTenantDto?.Pictures, `${this.path}/${TenantID}/assets/pictures`);

    const RemovedPictures = tenant?.Pictures?.filter((pic) => !Pictures.includes(pic)) || [];

    for (const pic of RemovedPictures) {
      const key = pic.replace(this.s3Bucket + '/', '');
      const deleteObject = new DeleteObjectCommand({
        Bucket: this.ConfigService.get('S3_NXTCAMPUS_PLATFORM'),
        Key: key,
      });
      await this.s3Client.send(deleteObject);
    }

    return await this.TenantModel.updateOne(
      { TenantID },
      {
        $set: {
          ...omit(UpdateTenantDto, ['Logo', 'Pictures', 'AdminUserID', 'WorkSpaceDomain', 'TenantID']),
          ...(Logo ? { Logo: Logo.Location + '?time=' + Date.now() } : {}),
          ...(Pictures ? { Pictures: Pictures.map((pic) => pic + '?time=' + Date.now()) } : {}),
        },
      }
    );
  }

  async GetTenantByWorkspaceDomain(WorkSpaceDomain: string) {
    return await this.TenantModel.findOne({ WorkSpaceDomain }).select('-AdminUserID');
  }
}
