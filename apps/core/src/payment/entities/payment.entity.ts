import { z } from 'zod';

export const OrderStatus = z.enum([
  'Pending', // Payment Created
  'Success', // Payment Success
  'Failed', // Payment Failed
  'Cancelled',
  'Refunded',
  'Expired',
  'WaitingForPGResponse',
]);
export const SettlementStatus = z.enum(['Pending', 'Success', 'Failed', 'Cancelled', 'Refunded', 'Expired', 'NA']);

export const PaymentGatewayProviders = z.enum([
  'RAZORPAY',
  'IDBI',
  'PAYTM',
  'ATOM',
  'PAYU',
  'SBI',
  'CASHFREE',
  'PAYPAL',
]);

export type TOrderStatus = z.infer<typeof OrderStatus>;
export type TSettlementStatus = z.infer<typeof SettlementStatus>;
export type TPaymentGatewayProviders = z.infer<typeof PaymentGatewayProviders>;

export const OrderAttrs = z.object({
  Amount: z.number(), // In Paisa
  OrderID: z.string(),
  Timestamp: z.date(), // UTC
  Status: OrderStatus,
  Settlement: z
    .object({
      Status: SettlementStatus,
      Timestamp: z.date().optional(),
      ETA: z.date().optional(),
    })
    .default({
      Status: 'NA',
    }),
  Gateway: PaymentGatewayProviders,
  IdempotentKey: z.string().optional(),
  // PG Fields
  GatewaySignatures: z.any(),
  VerificationSignatures: z.any(),
  OrderVerified: z.boolean().default(false),
  OrderVerifiedAt: z.date().optional(),
  // Optional fields
  Surcharge: z.number().optional(),
  SurchargePercentage: z.number().optional(),
  MerchantBearingCharge: z.number().optional(), // infinite surcharge loop
  RedirectURL: z.string().optional(),
  UserDetails: z
    .object({
      Email: z.string(),
      Mobile: z.string(),
      UserID: z.string(),
    })
    .optional(),
  Extras: z.record(z.string()).optional(), // UDFs
  ScopeParameters: z.record(z.string()).optional(),
  ErrorMessage: z.string().optional(),
  Scope: z.string(),
});

export type OrderAttrs = z.infer<typeof OrderAttrs>;
