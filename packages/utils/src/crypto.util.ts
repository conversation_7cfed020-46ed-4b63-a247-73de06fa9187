import { createCipheriv, createDecipheriv, scryptSync } from 'crypto';

type TParam = {
  secret: string;
  algorithm: string;
  data?: object;
};

class CryptoUtils {
  generateSignature<params extends TParam>(params: params) {
    const { secret, algorithm, data } = params;
    const key = scryptSync(secret, 'salt', 32);
    const iv = Buffer.alloc(16, 0);
    const cipher = createCipheriv(algorithm, key, iv);
    const encrypted = Buffer.concat([cipher.update(JSON.stringify(data)), cipher.final()]);
    const value = encrypted.toString('hex');
    return value;
  }

  verifySignature<params extends TParam>(params: params, value: string) {
    const { secret, algorithm } = params;
    const key = scryptSync(secret, 'salt', 32);
    const iv = Buffer.alloc(16, 0);
    const decipher = createDecipheriv(algorithm, key, iv);
    const decrypted = Buffer.concat([decipher.update(value, 'hex'), decipher.final()]);
    return decrypted.toString();
  }
}

export default CryptoUtils;
