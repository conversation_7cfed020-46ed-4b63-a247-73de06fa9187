import * as crypto from 'crypto';

export type TRazorpayCreateOrderResponse = {
  id: string;
  entity: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  offers: any[];
  status: string;
  attempts: number;
  notes: any;
  created_at: number;
};

export type TRazorpayGetOrderDetailsResponse = {
  id: string;
  entity: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  offers: any[];
  status: string;
  attempts: number;
  notes: any;
  created_at: number;
};

export class Razorpay {
  private readonly RAZORPAY_KEY_ID: string;
  private readonly RAZORPAY_SECRET_KEY: string;

  constructor({ RAZORPAY_KEY_ID, RAZORPAY_SECRET_KEY }: { RAZORPAY_KEY_ID: string; RAZORPAY_SECRET_KEY: string }) {
    this.RAZORPAY_KEY_ID = RAZORPAY_KEY_ID;
    this.RAZORPAY_SECRET_KEY = RAZORPAY_SECRET_KEY;
  }

  async RazorpayAxios(method: string, endpoint: string, data?: object) {
    const url = `https://api.razorpay.com/v1/${endpoint}`;
    const headers = {
      Authorization: `Basic ${Buffer.from(`${this.RAZORPAY_KEY_ID}:${this.RAZORPAY_SECRET_KEY}`).toString('base64')}`,
    };

    const response = await fetch(url, { method, headers, body: data ? JSON.stringify(data) : undefined });
    return response.json();
  }

  async CreateOrder(params: {
    amount: number;
    currency?: string;
    receipt?: string;
  }): Promise<TRazorpayCreateOrderResponse> {
    const payload = {
      amount: params.amount,
      currency: params.currency || 'INR',
      receipt: params.receipt || `rcpt_${Date.now()}`,
    };
    const response = await this.RazorpayAxios('POST', '/orders', payload);
    return response;
  }

  async GetOrderDetails(orderId: string): Promise<TRazorpayGetOrderDetailsResponse> {
    const response = await this.RazorpayAxios('GET', `/orders/${orderId}`);
    return response;
  }

  VerifySignature(orderId: string, paymentId: string, signature: string): boolean {
    const body = `${orderId}|${paymentId}`;
    const expectedSignature = crypto.createHmac('sha256', this.RAZORPAY_SECRET_KEY).update(body).digest('hex');

    return expectedSignature === signature;
  }
}
