export enum DocumentStatus {
  UPLOADING = "uploading",
  UPLOADED = "uploaded",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
}

export type TProject = {
  Name: string;
  Description: string;
  ProjectID: string;
  TenantID: string;
  createdAt: string;
  updatedAt: string;
};

export type TSource = {
  _id: string;
  SourceID: string;
  SourceName: string;
  Sections: {
    SectionID: string;
    SectionName: string;
    Description: string;
    SubSections: {
      SubSectionName: string;
      PageNumber: number;
      DocumentPageNumber: number;
    }[];
    PageNumber: {
      From: number;
      To: number;
    };
    DocumentPageNumber: {
      From: number;
      To: number;
    };
  }[];
  Status: DocumentStatus;
  TenantID: string;
  ProjectID: string;
  AWSKey: string;
  ErrorMessage: string | null;
  PageNumberOffset: number;
  createdAt: string;
  updatedAt: string;
  SourceSummary: {
    Title: string;
    Summary: string;
    MainTopics: string[];
    SectionHighlights: [
      {
        SectionName: string;
        Highlight: string;
      },
    ];
  };
};

export type TProjectDetails = TProject & {
  Sources: TSource[];
};
