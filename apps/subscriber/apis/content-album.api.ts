import { TryCatch } from "@/lib/try-catch";
import { KY, TErrorResponse } from "./_.index";

export interface ContentGroup {
  ContentGroupID: string;
  Title: string;
  Description?: string;
  SortOrder?: number;
}

export interface Content {
  ContentID: string;
  Meta: {
    Title: string;
    Description?: string;
    Transcript?: string; // Plain Text
    Subtitles?: {
      Language: string;
      URL: string;
    }[]; // SRT
    Attachments?: string[]; // URL
  };
  S3: {
    Location: string;
    Key: string;
    Type: string;
    FileSize: number;
    Expiration: string;
    AccessUrl: string;
  };
  CDN: {
    URL: string;
  };
  HLS?: {
    Resolutions: {
      Resolution: string;
      URL: string;
    }[];
  };
  Duration: number;
  SortOrder?: number;
  ContentGroupID?: string;
}

export interface TContentAlbum {
  ContentAlbumID: string;
  Name: string;
  Description?: string;
  CoverImage: string;

  TenantID: string;
  ProjectID: string;

  ContentGroups: ContentGroup[];
  Contents: Content[];

  ContentCount: number;
  ContentDuration: number;

  createdAt: string;
  updatedAt: string;
}

const GetContentAlbums = async (Params: { ProjectID: string }) => {
  return await TryCatch<TContentAlbum[], TErrorResponse>(
    KY.get(`subscription/project/${Params.ProjectID}/content-albums`).json(),
  );
};

const GetContentAlbum = async (Params: {
  ContentAlbumID: string;
  ProjectID: string;
}) => {
  return await TryCatch<TContentAlbum, TErrorResponse>(
    KY.get(
      `subscription/project/${Params.ProjectID}/content-album/${Params.ContentAlbumID}`,
    ).json(),
  );
};

const GetContent = async (Params: {
  ContentAlbumID: string;
  ContentID: string;
  ProjectID: string;
}) => {
  return await TryCatch<Content, TErrorResponse>(
    KY.get(
      `subscription/project/${Params.ProjectID}/content-album/${Params.ContentAlbumID}/content/${Params.ContentID}`,
    ).json(),
  );
};

export const CONTENT_ALBUM = {
  GetContentAlbums,
  GetContentAlbum,
  GetContent,
};
