import { trim } from 'radash';

export const TimeBasedQuery = (params: { CreatedAt?: string; UpdatedAt?: string; Raw?: boolean }) => {
  if (!params?.CreatedAt && !params?.UpdatedAt) {
    return {};
  }
  // trim the dates
  params.CreatedAt = params?.CreatedAt ?? trim(params?.CreatedAt ?? '');
  params.UpdatedAt = params?.UpdatedAt ?? trim(params?.UpdatedAt ?? '');

  const createdAtFrom = params?.CreatedAt?.split(',').at(0);
  const createdAtTo = params?.CreatedAt?.split(',').at(1);

  const updatedAtFrom = params?.UpdatedAt?.split(',').at(0);
  const updatedAtTo = params?.UpdatedAt?.split(',').at(1);

  if (params?.Raw) {
    return { $gte: new Date(createdAtFrom || updatedAtFrom), $lte: new Date(createdAtTo || updatedAtTo) };
  }
  return {
    createdAt: { $gte: new Date(createdAtFrom), $lte: new Date(createdAtTo) },
    updatedAt: { $gte: new Date(updatedAtFrom), $lte: new Date(updatedAtTo) },
  };
};
