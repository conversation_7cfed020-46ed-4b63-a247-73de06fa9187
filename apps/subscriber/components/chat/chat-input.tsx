import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Textarea } from "@workspace/ui/components/textarea";
import { cn } from "@workspace/ui/lib/utils";
import { Check, ChevronDown, Dot, Mic, SendHorizontal, X } from "lucide-react";
import { useEffect, useState } from "react";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import { AutoReadToggle } from "../message-actions/AutoReadToggle";

export interface ChatInputProps {
  placeholder?: string;
  isDisabled?: boolean;
  onSubmit: (message: string) => void;
  onMessageChange?: (message: string) => void;
}

export default function ChatInput({
  placeholder,
  isDisabled,
  onSubmit,
  onMessageChange,
}: ChatInputProps) {
  const [message, setMessage] = useState<string>("");
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
    finalTranscript,
  } = useSpeechRecognition();

  if (!browserSupportsSpeechRecognition) {
    return <span>Browser doesn't support speech recognition.</span>;
  }

  const [selectedLanguage, setSelectedLanguage] = useState("en-US");

  const language = {
    "en-US": "English",
    "mr-IN": "Marathi",
    "hi-IN": "Hindi",
  };

  const handleStopListening = () => {
    (SpeechRecognition as any).stopListening();
    resetTranscript();
    setMessage("");
  };

  const handleEscKey = (e: KeyboardEvent) => {
    if (e.key === "Escape") {
      handleStopListening();
    }
    if (e.key === "Enter" && !e.shiftKey) {
      onSubmit(message);
      handleStopListening();
      setMessage("");
    }
  };

  useEffect(() => {
    document.addEventListener("keydown", handleEscKey);
    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [handleEscKey, handleStopListening, onSubmit]);

  // on Transcribe
  useEffect(() => {
    if (listening) {
      setMessage(finalTranscript);
      onMessageChange?.(finalTranscript);
    }
  }, [finalTranscript, listening]);

  const STT = () => {
    return (
      <div
        className={cn(
          "flex items-center w-full",
          listening && "gap-8 justify-between"
        )}
      >
        {!listening && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <ChevronDown className="size-4" />{" "}
                {language[selectedLanguage as keyof typeof language]}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[200px]">
              <DropdownMenuItem
                onClick={() => {
                  handleStopListening();
                  setSelectedLanguage("en-US");
                }}
              >
                {language["en-US"]}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  handleStopListening();
                  setSelectedLanguage("hi-IN");
                }}
              >
                {language["hi-IN"]}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  handleStopListening();
                  setSelectedLanguage("mr-IN");
                }}
              >
                {language["mr-IN"]}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        {listening && (
          <div
            onClick={() => {
              handleStopListening();
            }}
            className="cursor-pointer text-white rounded-full p-2 bg-red-500"
          >
            <X className="size-4" />
          </div>
        )}
        {listening && (
          <div className="flex items-center">
            <Dot className="size-8 animate-pulse text-red-500" />
            <span className="text-xs text-muted-foreground">
              <p className="text-sm">
                Listening...{" "}
                <span className="text-xs">(Press ESC to stop)</span>
              </p>
            </span>
          </div>
        )}
        <div
          onClick={() => {
            if (listening) {
              handleStopListening();
              resetTranscript();
            } else {
              (SpeechRecognition as any).startListening({
                continuous: true,
                interimResults: true,
                language: selectedLanguage,
              });
            }
          }}
          className={cn(
            "bg-primary/30 text-primary-foreground rounded-full p-2 cursor-pointer",
            listening && "bg-primary text-primary-foreground"
          )}
        >
          {listening ? (
            <Check className="size-4" />
          ) : (
            <Mic className="size-4" />
          )}
        </div>
      </div>
    );
  };

  const onSubmitHandler = () => {
    onSubmit(message);
    setMessage("");
  };

  return (
    <div className="bg-accent rounded-2xl border-gray-600 shadow-2xl relative w-full hover:border-gray-400">
      <div className="pointer-events-none absolute inset-0 opacity-0 transition duration-200 ease-in-out bg-gray-900/20 group-hover:opacity-100" />
      <form
        onSubmit={(e) => {
          e.preventDefault();
          onSubmitHandler();
        }}
        className="flex items-center gap-2 px-2 py-2 border-none flex-col w-full"
      >
        <Textarea
          placeholder={placeholder ?? "Type your message..."}
          value={message}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
            setMessage(e.target.value);
            onMessageChange?.(e.target.value);
          }}
          className="flex-1 rounded-2xl px-4 py-2 border-none focus:outline-none focus-visible:ring-0 shadow-none lg:bg-foreground/10"
          onKeyDown={(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
            // Send message on Enter (without Shift)
            if (e.key === "Enter" && !e.shiftKey) {
              e.preventDefault();
              if (message?.trim() && !isDisabled) {
                onSubmitHandler();
                setMessage("");
              }
            }
            // Allow Shift+Enter for new line (default behavior)
          }}
        />
        <div className="flex justify-between items-center w-full gap-4 px-2">
          {!listening && <AutoReadToggle />}

          <div className={cn("flex items-center gap-4", listening && "w-full")}>
            <STT />

            {!listening && (
              <Button
                type="submit"
                size="icon"
                className="rounded-full cursor-pointer shadow-none size-8"
                disabled={!message.trim()}
                onClick={() => onSubmitHandler()}
              >
                <SendHorizontal />
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}
