import _, { max } from 'lodash';
import { ObjectId } from 'mongodb';

// General Functions
export const Arrayify = (value: unknown | unknown[]): unknown[] => (_.isArray(value) ? value : [value]) as unknown[];

// Array Functions
export const ArrayUnique = (value: unknown[]) => _.uniq(value);
export const ArrayFilter = (value: unknown[]) => _.filter(value);
export const ArrayMap = (value: unknown[]) => _.map(value);
export const ArraySort = (value: unknown[]) => _.sortBy(value);
export const ArrayReverse = (value: unknown[]) => _.reverse(value);
export const ArrayChunk = (value: unknown[], size: number) => _.chunk(value, size);
export const ArrayFlatten = (value: unknown[]) => _.flatten(value);
export const ArrayDifference = (value: unknown[], compare: unknown[]) => _.difference(value, compare);
export const ArrayIntersection = (value: unknown[], compare: unknown[]) => _.intersection(value, compare);
export const ArrayUnion = (value: unknown[], compare: unknown[]) => _.union(value, compare);
export const ArrayMerge = (value: unknown[], compare: unknown[]) => _.concat(value, compare);
export const ArrayRemove = (value: unknown[], compare: unknown[]) => _.pull(value, compare);
export const ArrayRemoveAll = (value: unknown[], compare: unknown[]) => _.pullAll(value, compare);
export const ArrayRemoveAt = (value: unknown[], index: number) => _.pullAt(value, index);
export const ArrayRemoveAtAll = (value: unknown[], index: number[]) => _.pullAt(value, index);
export const ArrayRemoveByIndex = (value: unknown[], index: number) => _.remove(value, (v, i) => i === index);
export const ArrayRemoveByValue = (value: unknown[], compare: unknown) => _.remove(value, (v) => v === compare);
export const ArrayRemoveByValues = (value: unknown[], compare: unknown[]) =>
  _.remove(value, (v) => compare.includes(v));
export const ArrayRemoveByCondition = (value: unknown[], condition: (v: unknown) => boolean) =>
  _.remove(value, condition);
export const ArrayRemoveEmpty = (value: unknown[]) => _.compact(value);
export const ArrayRemoveDuplicates = (value: unknown[]) => _.uniq(value);
export const ArrayRemoveNull = (value: unknown[]) => _.compact(value);
export const ArrayRemoveUndefined = (value: unknown[]) => _.compact(value);
export const ArrayRemoveFalsy = (value: unknown[]) => _.compact(value);
export const ArrayRemoveTruthy = (value: unknown[]) => _.compact(value);
export const ArrayRemoveZero = (value: number[]) => _.compact(value);
export const ArrayRemoveEmptyString = (value: string[]) => _.compact(value);
export const ArrayRemoveEmptyObject = (value: Record<string, unknown>[]) => _.compact(value);
export const ValueInArray = (value: unknown, array: unknown[]) => _.includes(array, value);
export const ArrayToObject = (value: Record<string, unknown>[]) =>
  value.reduce((acc, current) => {
    // Merge the current object into the accumulator
    return Object.assign(acc, current);
  }, {});

// MongoDB Functions
export const ToObjectId = (value: string | string[]) => Arrayify(value).map((id: string) => new ObjectId(id));

export const ArrayToObjectId = (value: string | string[]) => Arrayify(value).map((id: string) => new ObjectId(id));
export const ObjectIdToString = (value: ObjectId | ObjectId[] | unknown) =>
  Arrayify(value).map((id: ObjectId) => id.toHexString());
export const ObjectIdToStringArray = (value: ObjectId | ObjectId[]) =>
  Arrayify(value).map((id: ObjectId) => id.toHexString());
export const MissingNumbers = (arr: number[]): number[] => {
  const missingNumbers = [];
  for (let i = 1; i <= (max(arr) as number); i++) {
    if (!arr.includes(i)) {
      missingNumbers.push(i);
    }
  }
  return missingNumbers;
};

// Object Functions

export const Objectify = (value: Record<string, unknown> | unknown) => (_.isObject(value) ? value : {});
export const MergeObjects = (objects: Record<string, unknown>[]) => _.merge({}, ...objects);
export const MergeObject = (object: Record<string, unknown>) => _.merge({}, object);
export const ObjectKeys = (object: Record<string, unknown>) => Object.keys(object);
export const ObjectValues = (object: Record<string, unknown>) => Object.values(object);
export const ObjectEntries = (object: Record<string, unknown>) => Object.entries(object);
export const RenameKeys = (object: Record<string, unknown>, keysMap: Record<string, string>) =>
  Object?.keys(object).reduce((acc, key) => {
    const newKey = keysMap[key] || key;
    return { ...acc, [newKey]: object[key] };
  }, {});
// compare object a and b
export const IsEqual = (a: Record<string, unknown>, b: Record<string, unknown>) => _.isEqual(a, b);
export const IsNotEqual = (a: Record<string, unknown>, b: Record<string, unknown>) => !_.isEqual(a, b);
export const IsValid = (value: any) => value !== null && value !== undefined && value !== '';

// String
export const Title = (str: string | string[]) => {
  return Arrayify(str)?.map((s: string) =>
    s
      .toLowerCase()
      .replace(/_/g, ' ')
      .replace(/\b\w/g, (l) => l.toUpperCase())
  );
};

// URL
export const IsURL = (url: string) => {
  return url?.toString()?.includes('http://') || url?.toString().includes('https://');
};

// Boolean
export const IsTrue = (value: any) =>
  value === true ||
  value === 'true' ||
  value === 1 ||
  value === '1' ||
  value === 'yes' ||
  value === 'y' ||
  value === 'on' ||
  value === 'enable' ||
  value === 'enabled';

export const IsNot = (value: any) => !IsTrue(value);

// Map UTILS
export const ArrayToMap = <T>(array: T[], key: string) => {
  const map = new Map<string, T>([]);
  array.forEach((item) => {
    if (item[key]) map.set(item[key] as unknown as string, item);
  });

  return map;
};

// Design Patterns
export const FallBackValue = (value: any, fallback: any) => (IsValid(value) ? value : fallback);
export const FallBackFunction = (value: any, fallback: Function) => (IsValid(value) ? value : fallback);
export const Flatten = (value: any) => _.flat(value);

export const FlattenObject = (
  obj: Record<string, any>,
  parentKey = '',
  result: Record<string, any> = {}
): Record<string, any> => {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const fullKey = parentKey ? `${parentKey}.${key}` : key;
      const value = obj[key];

      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        // Recursively flatten the object
        FlattenObject(value, fullKey, result);
      } else {
        // Assign the value if it's not an object
        result[fullKey] = value;
      }
    }
  }
  return result;
};

// Query
export const SafeQuery = (value: Record<string, unknown>, err_cb: () => void = () => {}) => {
  const flattenValues = Flatten(value);

  const keyLength = Object.keys(flattenValues as Record<string, unknown>).length;
  const safeValues = Object.values(flattenValues as Record<string, unknown>).filter(
    (v) => v !== null && v !== undefined
  );

  if (keyLength === safeValues.length) {
    return value;
  }

  err_cb();
};

export const ParseStrBoolean = (value: string | boolean) => {
  if (
    value === 'true' ||
    value === '1' ||
    value === 'yes' ||
    value === 'y' ||
    value === 'on' ||
    value === 'enable' ||
    value === 'enabled' ||
    value === true
  ) {
    return true;
  }
  return false;
};

export const FindDifferenceOfObjects = (obj1: Record<string, unknown>, obj2: Record<string, unknown>) => {
  const difference: Record<string, unknown> = {};
  const changes: {
    Key: string;
    From: string | number | boolean;
    To: string | number | boolean;
  }[] = [];
  Object.keys(obj1).forEach((key) => {
    if (obj1[key] !== obj2[key]) {
      difference[key] = obj1[key];
      changes.push({
        Key: key,
        From: obj1[key] as string | number | boolean,
        To: obj2[key] as string | number | boolean,
      });
    }
  });
  return { difference, isDiff: Object.keys(difference).length > 0, changes };
};

export const ReplaceWithValues = (str: string, values: Record<string, unknown>) => {
  let newStr = str;
  Object.entries(values).forEach(([key, value]) => {
    console.log(key, value);
    newStr = newStr.replace(new RegExp(`\\b${key}\\b`, 'g'), value.toString());
  });
  return newStr;
};
