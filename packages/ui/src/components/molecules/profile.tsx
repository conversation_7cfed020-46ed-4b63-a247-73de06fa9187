/**
 * Profile is Reusable molecule component that can be used to display or update user profile information
 * @returns JSX Element
 */
import { useState } from "react";
import { useUser } from "@workspace/ui/hooks/use-auth";
import { AUTH } from "../../api/auth.api.js";
import { Avatar, AvatarFallback, AvatarImage } from "../avatar.js";
import { Button } from "../button.js";
import { Input } from "../input.js";
import { Label } from "../label.js";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../tabs.js";
import { Alert, AlertDescription, AlertTitle } from "../alert.js";
import { AlertCircle } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../card.js";
import { toast } from "sonner";

export function ProfileBase() {
  const { user } = useUser();

  if (!user) return null;

  const initials = (user.GivenName?.[0] || "") + (user.FamilyName?.[0] || "");
  const displayName =
    user.GivenName && user.FamilyName
      ? `${user.GivenName} ${user.FamilyName}`
      : user.Name || user.Email;

  return (
    <div className="flex flex-col items-center space-y-4 p-4">
      <Avatar className="h-24 w-24">
        <AvatarImage src={user.ProfilePicture || ""} alt={displayName} />
        <AvatarFallback>
          {initials || user.Email?.[0]?.toUpperCase() || "U"}
        </AvatarFallback>
      </Avatar>

      <div className="text-center">
        <h3 className="text-lg font-medium">{displayName}</h3>
        <p className="text-sm text-muted-foreground">{user.Email}</p>
        {user.PhoneNumber && (
          <p className="text-sm text-muted-foreground">{user.PhoneNumber}</p>
        )}
      </div>

      {user.OAuth?.Google?.Connected && (
        <Alert variant="default" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Google Account Connected</AlertTitle>
          <AlertDescription>
            Your profile is linked to your Google account.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

export function ProfileModel() {
  const { user } = useUser();
  const [isLoading, setIsLoading] = useState(false);

  if (!user) return null;

  const [formData, setFormData] = useState({
    Email: user.Email || "",
    Password: "",
    FirstName: user.GivenName || "",
    LastName: user.FamilyName || "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const updateData = {
        Email: formData.Email !== user.Email ? formData.Email : undefined,
        Password: formData.Password || undefined,
        FirstName:
          formData.FirstName !== user.GivenName
            ? formData.FirstName
            : undefined,
        LastName:
          formData.LastName !== user.FamilyName ? formData.LastName : undefined,
      };

      // Only send fields that have changed
      const filteredData = Object.fromEntries(
        Object.entries(updateData).filter(([_, v]) => v !== undefined)
      );

      if (Object.keys(filteredData).length === 0) {
        toast.info("No changes to save");
        setIsLoading(false);
        return;
      }

      const { data, errors } = await AUTH.UpdateDetails(filteredData);

      if (errors) {
        errors.forEach((error) => toast.error(error.message));
      }

      if (data) {
        toast.success("Profile updated successfully");
        // Reset password field after successful update
        setFormData((prev) => ({ ...prev, Password: "" }));
      }
    } catch (error) {
      toast.error("Failed to update profile");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
        <CardDescription>View and update your profile details</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="view" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="view">View Profile</TabsTrigger>
            <TabsTrigger value="edit">Edit Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="view">
            <div className="space-y-6">
              <ProfileBase />

              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Email</Label>
                  <div className="col-span-3">{user.Email}</div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Full Name</Label>
                  <div className="col-span-3">
                    {user.GivenName && user.FamilyName
                      ? `${user.GivenName} ${user.FamilyName}`
                      : user.Name || "Not provided"}
                  </div>
                </div>
                {user.Gender && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label className="text-right">Gender</Label>
                    <div className="col-span-3">{user.Gender}</div>
                  </div>
                )}
                {user.PhoneNumber && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label className="text-right">Phone</Label>
                    <div className="col-span-3">{user.PhoneNumber}</div>
                  </div>
                )}
                {user.Website && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label className="text-right">Website</Label>
                    <div className="col-span-3">{user.Website}</div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="edit">
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                {user.OAuth?.Google?.Connected && (
                  <Alert className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Google Account Connected</AlertTitle>
                    <AlertDescription>
                      Profile picture cannot be updated as your account is
                      linked to Google.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex justify-center mb-4">
                  <Avatar className="h-24 w-24">
                    <AvatarImage
                      src={user.ProfilePicture || ""}
                      alt={user.Email}
                    />
                    <AvatarFallback>
                      {(user.GivenName?.[0] || "") +
                        (user.FamilyName?.[0] || "") ||
                        user.Email?.[0]?.toUpperCase() ||
                        "U"}
                    </AvatarFallback>
                  </Avatar>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">
                    Email
                  </Label>
                  <Input
                    id="email"
                    name="Email"
                    value={formData.Email}
                    onChange={handleChange}
                    className="col-span-3"
                    disabled={user.OAuth?.Google?.Connected}
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="firstName" className="text-right">
                    First Name
                  </Label>
                  <Input
                    id="firstName"
                    name="FirstName"
                    value={formData.FirstName}
                    onChange={handleChange}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="lastName" className="text-right">
                    Last Name
                  </Label>
                  <Input
                    id="lastName"
                    name="LastName"
                    value={formData.LastName}
                    onChange={handleChange}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="password" className="text-right">
                    New Password
                  </Label>
                  <Input
                    id="password"
                    name="Password"
                    type="password"
                    value={formData.Password}
                    onChange={handleChange}
                    placeholder="Leave blank to keep current password"
                    className="col-span-3"
                    disabled={user.OAuth?.Google?.Connected}
                  />
                </div>
              </div>

              <CardFooter className="flex justify-end">
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Saving..." : "Save Changes"}
                </Button>
              </CardFooter>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
