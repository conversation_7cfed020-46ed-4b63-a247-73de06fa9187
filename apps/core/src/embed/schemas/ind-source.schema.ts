import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export enum DocumentStatus {
  UPLOADING = 'uploading',
  UPLOADED = 'uploaded',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

@Schema({ collection: 'ind_source', timestamps: true, versionKey: false })
export class IndSourceModel {
  @Prop({ required: true })
  SourceID: string;

  @Prop({ required: true })
  SourceName: string;

  @Prop({ required: true, default: [] })
  Sections: {
    SectionID: string;
    SectionName: string;
    Description?: string;
    SubSections?: {
      SubSectionName: string;
      PageNumber: number;
      DocumentPageNumber: number;
    }[];
    PageNumber: {
      From: number;
      To: number;
    };
    DocumentPageNumber: {
      From: number;
      To: number;
    };
  }[];

  @Prop({
    type: String,
    enum: Object.values(DocumentStatus),
    default: DocumentStatus.UPLOADING,
  })
  Status: DocumentStatus;

  @Prop({ type: String, required: true })
  TenantID: string;

  @Prop({ type: String, required: true })
  ProjectID: string;

  @Prop({ type: String, required: true })
  AWSKey: string;

  @Prop()
  ErrorMessage?: string;

  @Prop({ type: Object })
  SourceSummary?: {
    Title: string;
    Summary: string;
    MainTopics: string[];
    SectionHighlights: {
      SectionName: string;
      Highlight: string;
    }[];
  };

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const IndSourceSchema = SchemaFactory.createForClass(IndSourceModel);

// Add indexes for better query performance
IndSourceSchema.index({ SourceID: 1 }, { unique: true });
IndSourceSchema.index({ TenantID: 1, ProjectID: 1 });
IndSourceSchema.index({ status: 1 });
