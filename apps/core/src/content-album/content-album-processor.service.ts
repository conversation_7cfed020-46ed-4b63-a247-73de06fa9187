import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ContentAlbumModel } from './schemas/content-album.schema';
import { IndSourceModel } from '../embed/schemas/ind-source.schema';
import { LectureService } from '../lecture/lecture.service';
import { AgeGroup, TeachingStyle } from '../lecture/dto/create-lecture.dto';
import { AppTypeEnum } from '../core/user/entities/user.entity';
import GenerateID from '../../common/lib/generate-id';
import { SourceModel } from '../embed/schemas/source.schema';

interface GenerateContentAlbumEvent {
  ContentAlbumID: string;
  TenantID: string;
  ProjectID: string;
  SourceID: string;
  Name?: string;
  Description?: string;
  CoverImageLocation?: string;
  UserID: string;
  AppType: AppTypeEnum;
}

@Injectable()
export class ContentAlbumProcessorService {
  constructor(
    @InjectModel(ContentAlbumModel.name)
    private readonly ContentAlbumModel: Model<ContentAlbumModel>,
    @InjectModel(SourceModel.name)
    private readonly SourceModel: Model<SourceModel>,
    @InjectModel(IndSourceModel.name)
    private readonly IndSourceModel: Model<IndSourceModel>,
    private readonly lectureService: LectureService
  ) {}

  @OnEvent('content-album.generate')
  async handleGenerateContentAlbum(event: GenerateContentAlbumEvent) {
    const { ContentAlbumID, TenantID, ProjectID, SourceID, Name, Description, CoverImageLocation, UserID, AppType } =
      event;

    if (AppType === AppTypeEnum.Industry) {
      const source = await this.IndSourceModel.findOne({ SourceID, TenantID, ProjectID });
      if (!source) {
        throw new Error('Source not found');
      }

      console.log('Start generating lectures');
      const lectures = await Promise.all(
        source.Sections.map(async (section) => {
          return this.lectureService.create(
            {
              TenantID,
              ProjectID,
              SourceID,
              ChapterID: section.SectionID,
              TeachingStyle: TeachingStyle.TECHNICAL,
              AgeGroup: AgeGroup.ADULT,
              CreativityLevel: 0.7,
            },
            UserID,
            AppType
          );
        })
      );
      console.log('Lectures generated');

      const SectionMap = source.Sections.reduce((acc, section, index) => {
        acc[section.SectionID] = {
          Title: section.SectionName,
          Description: section.Description,
          SortOrder: index + 1,
        };
        return acc;
      }, {});

      const ContentGroupMap: Record<string, { ContentGroupID: string }> = {};

      const ContentGroups = lectures.map((lecture) => {
        const section = SectionMap[lecture.data.ChapterID];
        const contentGroupID = GenerateID.Generate24Hex('cont_grp_');
        lecture.data.Lectures.forEach((_lecture) => {
          ContentGroupMap[_lecture.ContentID] = {
            ContentGroupID: contentGroupID,
          };
        });
        return {
          ContentGroupID: contentGroupID,
          Title: section.Title,
          Description: section.Description,
          SortOrder: section.SortOrder,
        };
      });

      const Contents = lectures
        .map((lecture) => {
          const lectureContents = lecture.data.Lectures.map((lecture, index) => {
            return {
              ContentID: lecture.ContentID,
              ContentGroupID: ContentGroupMap[lecture.ContentID].ContentGroupID,
              Meta: {
                Title: lecture.ContentTitle,
                Description: lecture.Description,
                Transcript: lecture.FormattedTranscript,
              },
              SortOrder: index + 1,
            };
          });
          return lectureContents;
        })
        .flat();

      const contentAlbum = await this.ContentAlbumModel.create({
        ContentAlbumID,
        Name: Name ?? source.SourceName,
        Description: 'Generate with Super Embed',
        CoverImage: CoverImageLocation,
        TenantID,
        ProjectID,
        ContentCount: Contents.length,
        ContentDuration: 0,
        ContentGroups,
        Contents,
      });

      return contentAlbum;
    } else {
      const source = await this.SourceModel.findOne({ SourceID, TenantID, ProjectID });
      if (!source) {
        throw new Error('Source not found');
      }

      console.log('Start generating lectures');
      const lectures = await Promise.all(
        source.Chapters.map(async (chapter) => {
          return this.lectureService.create(
            {
              TenantID,
              ProjectID,
              SourceID,
              ChapterID: chapter.ChapterID,
              TeachingStyle: TeachingStyle.TECHNICAL,
              AgeGroup: AgeGroup.ADULT,
              CreativityLevel: 0.7,
            },
            UserID,
            AppType
          );
        })
      );
      console.log('Lectures generated');

      const ChapterMap = source.Chapters.reduce(
        (acc, chapter, index) => {
          acc[chapter.ChapterID] = {
            Title: chapter.ChapterName,
            Description: chapter.Summary ?? '',
            SortOrder: chapter.ChapterNumber ?? index + 1,
          };
          return acc;
        },
        {} as Record<string, { Title: string; Description: string; SortOrder: number }>
      );

      const ContentGroupMap: Record<string, { ContentGroupID: string }> = {};

      const ContentGroups = lectures.map((lecture) => {
        const chapter = ChapterMap[lecture.data.ChapterID];
        const contentGroupID = GenerateID.Generate24Hex('cont_grp_');
        lecture.data.Lectures.forEach((_lecture) => {
          ContentGroupMap[_lecture.ContentID] = {
            ContentGroupID: contentGroupID,
          };
        });
        return {
          ContentGroupID: contentGroupID,
          Title: chapter.Title,
          Description: chapter.Description,
          SortOrder: chapter.SortOrder,
        };
      });

      const Contents = lectures
        .map((lecture) => {
          const lectureContents = lecture.data.Lectures.map((lecture, index) => {
            return {
              ContentID: lecture.ContentID,
              ContentGroupID: ContentGroupMap[lecture.ContentID].ContentGroupID,
              Meta: {
                Title: lecture.ContentTitle,
                Description: lecture.Description,
                Transcript: lecture.FormattedTranscript,
              },
              SortOrder: index + 1,
            };
          });
          return lectureContents;
        })
        .flat();

      const contentAlbum = await this.ContentAlbumModel.create({
        ContentAlbumID,
        Name: Name ?? source.SourceName,
        Description: 'Generate with Super Embed',
        CoverImage: CoverImageLocation,
        TenantID,
        ProjectID,
        ContentCount: Contents.length,
        ContentDuration: 0,
        ContentGroups,
        Contents,
      });

      return contentAlbum;
    }
  }
}
