import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TTSController } from './tts.controller';
import { TTSService } from './tts.service';
import { TTSModel, TTSSchema } from './schema/tts.schema';
import { EventEmitterModule } from '@nestjs/event-emitter';

@Module({
  imports: [EventEmitterModule.forRoot(), MongooseModule.forFeature([{ name: TTSModel.name, schema: TTSSchema }])],
  controllers: [TTSController],
  providers: [TTSService],
  exports: [TTSService],
})
export class TTSModule {}
