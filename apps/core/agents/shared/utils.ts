import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { Document } from '@langchain/core/documents';
import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { <PERSON><PERSON><PERSON><PERSON>ere } from '@langchain/cohere';
import { AIMessage } from '@langchain/core/messages';

export function formatDoc(doc: Document): string {
  const metadata = doc.metadata || {};
  const meta = Object.entries(metadata)
    .map(([k, v]) => ` ${k}=${v}`)
    .join('');
  const metaStr = meta ? ` ${meta}` : '';

  return `<document${metaStr}>\n${doc.pageContent}\n</document>`;
}

export function formatDocs(docs?: Document[]): string {
  /**Format a list of documents as XML. */
  if (!docs || docs.length === 0) {
    return '<documents></documents>';
  }
  const formatted = docs.map(formatDoc).join('\n');
  return `<documents>\n${formatted}\n</documents>`;
}

/**
 * Load a chat model from a fully specified name.
 * @param fullySpecifiedName - String in the format 'provider/model' or 'provider/account/provider/model'.
 * @returns A Promise that resolves to a BaseChatModel instance.
 */
export async function loadChatModel(fullySpecifiedName: string): Promise<BaseChatModel> {
  const index = fullySpecifiedName.indexOf('/');
  if (index === -1) {
    throw new Error("Invalid format. Use 'provider/model' (e.g., 'openai/gpt-4o-mini').");
  }

  const provider = fullySpecifiedName.slice(0, index);
  const model = fullySpecifiedName.slice(index + 1);

  switch (provider) {
    case 'openai':
      return new ChatOpenAI({
        modelName: model,
        openAIApiKey: process.env.OPENAI_API_KEY,
        temperature: 0,
        topP: 0,
        frequencyPenalty: 2,
        presencePenalty: 2,
        streaming: true,
      });
    case 'anthropic':
      return new ChatAnthropic({
        modelName: model,
        anthropicApiKey: process.env.ANTHROPIC_API_KEY,
        temperature: 0,
        topP: 0,
        streaming: true,
      });
    case 'cohere':
      return new ChatCohere({
        model: model,
        apiKey: process.env.COHERE_API_KEY,
        temperature: 0,
        streaming: true,
      });
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
}

export type LLMOutput = {
  generations: {
    text: string;
    message: {
      lc: number;
      type: string;
      id: string[];
      kwargs: {
        content: string;
        additional_kwargs: Record<string, unknown>;
        response_metadata: {
          tokenUsage: {
            promptTokens: number;
            completionTokens: number;
            totalTokens: number;
          };
          finish_reason: string;
          model_name: string;
          usage: {
            prompt_tokens: number;
            completion_tokens: number;
            total_tokens: number;
            prompt_tokens_details: {
              cached_tokens: number;
              audio_tokens: number;
            };
            completion_tokens_details: {
              reasoning_tokens: number;
              audio_tokens: number;
              accepted_prediction_tokens: number;
              rejected_prediction_tokens: number;
            };
          };
          system_fingerprint: string;
        };
        id: string;
        tool_calls: [];
        invalid_tool_calls: [];
        usage_metadata: {
          output_tokens: number;
          input_tokens: number;
          total_tokens: number;
          input_token_details: {
            audio: number;
            cache_read: number;
          };
          output_token_details: {
            audio: number;
            reasoning: number;
          };
        };
      };
    };
    generationInfo: {
      finish_reason: string;
    };
  }[][];
  llmOutput: {
    tokenUsage: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    };
  };
};

export type LLMInvocation = {
  id: string;
  node: string;
  tokenUsage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    prompt_tokens_details: {
      cached_tokens: number;
      audio_tokens: number;
    };
    completion_tokens_details: {
      reasoning_tokens: number;
      audio_tokens: number;
      accepted_prediction_tokens: number;
      rejected_prediction_tokens: number;
    };
  };
  system_fingerprint: string;
};

export function calculateLLMInvocations(
  output: LLMOutput,
  node: string
): {
  PromptTokens: number;
  CompletionTokens: number;
  TotalTokens: number;
  LLMInvocations: LLMInvocation[];
} {
  const llmInvocations = output?.generations
    ?.map((generation) => {
      return generation?.map((g) => {
        const { message } = g;
        if (!message) return null;
        const { kwargs } = JSON.parse(JSON.stringify(message));
        if (!kwargs) return null;
        return {
          id: kwargs?.id,
          tokenUsage: kwargs?.response_metadata?.tokenUsage,
          model: kwargs?.response_metadata?.model_name,
          usage: kwargs?.response_metadata?.usage,
          system_fingerprint: kwargs?.response_metadata?.system_fingerprint,
          node,
        };
      });
    })
    ?.flat()
    .filter((g) => g !== null);

  const TokenCount = {
    PromptTokens: output?.llmOutput?.tokenUsage?.promptTokens ?? 0,
    CompletionTokens: output?.llmOutput?.tokenUsage?.completionTokens ?? 0,
    TotalTokens: output?.llmOutput?.tokenUsage?.totalTokens ?? 0,
    LLMInvocations: llmInvocations ?? [],
  };

  return TokenCount;
}
