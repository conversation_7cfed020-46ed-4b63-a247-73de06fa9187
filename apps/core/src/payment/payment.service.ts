import { BadGatewayException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ICreateOrder, IVerifyOrder } from './dto/order.controller.dto';
import { OrderStatus } from './entities/payment.entity';
import { OrderModel } from './schemas/order.schema';
import { Razorpay } from './sdk/razarpay.sdk';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class PaymentService {
  private readonly Razorpay: Razorpay;

  constructor(
    @InjectModel(OrderModel.name)
    private readonly OrderModel: Model<OrderModel>,
    private readonly ConfigService: ConfigService,
    private readonly EventEmitter: EventEmitter2
  ) {
    this.Razorpay = new Razorpay({
      RAZORPAY_KEY_ID: this.ConfigService.get('RAZORPAY_KEY_ID'),
      RAZORPAY_SECRET_KEY: this.ConfigService.get('RAZORPAY_SECRET_KEY'),
    });
  }

  InvalidOrder = async (OrderID: string) => {
    await this.OrderModel.updateOne({ OrderID }, { Status: OrderStatus.enum.Failed });
  };

  ValidateIdempotentKey = async (idempotentKey: string) => {
    const getOrders = await this.OrderModel.find({
      IdempotentKey: idempotentKey,
    }).lean();

    const statuses = getOrders.map((order) => order.Status);

    const orderSuccess = statuses.includes(OrderStatus.enum.Success);

    const orderPending = statuses.includes(OrderStatus.enum.Pending);

    if (orderPending && orderSuccess) {
      const pendingOrders = getOrders.filter((order) => order.Status === OrderStatus.enum.Pending);
      pendingOrders.forEach((order) => {
        this.ReValidateOrder(order.OrderID);
      });
      console.log(`Multiple orders found for idempotent key ${idempotentKey}, Self Healing Initiated`);
    }

    if (orderSuccess) {
      await this.ReValidateOrder(getOrders.find((order) => order.Status === OrderStatus.enum.Success).OrderID);
      throw new Error('Order already been paid.');
    }

    if (orderPending) {
      const pendingOrder = getOrders.find((order) => order.Status === OrderStatus.enum.Pending);

      return pendingOrder;
    }

    return null;
  };

  async CreateOrder(Params: ICreateOrder) {
    if (Params.IdempotentKey) {
      const idempotentOrder = await this.ValidateIdempotentKey(Params.IdempotentKey);

      if (idempotentOrder) {
        // Validate Order Created Time, Order created time should be less than 10 minutes
        const orderCreatedTime = idempotentOrder?.Timestamp;
        const validOrderCreatedTime =
          orderCreatedTime && new Date().getTime() - orderCreatedTime.getTime() < 10 * 60 * 1000;

        const revalidatedOrder = await this.ReValidateOrder(idempotentOrder.OrderID);

        console.log('revalidatedOrder', revalidatedOrder);

        // if order is already paid
        if (revalidatedOrder.order.Status === OrderStatus.enum.Success) {
          return revalidatedOrder.order;
        }

        // if order is already pending
        if (validOrderCreatedTime && revalidatedOrder.order.Status === OrderStatus.enum.Pending) {
          return idempotentOrder;
        }
      }
    }

    // Create Order on PG
    const createOrderResponse = await this.Razorpay.CreateOrder({
      amount: Params.Amount,
      currency: 'INR',
    });

    // Save Order
    const order = await this.OrderModel.create({
      ...Params,
      OrderID: createOrderResponse.id,
      Status: OrderStatus.enum.Pending,
      GatewaySignatures: createOrderResponse,
    });

    // Return Order
    return order;
  }

  async GetOrderDetails(OrderID: string) {
    const order = await this.OrderModel.findOne({ OrderID });
    if (!order) {
      throw new Error('Order not found');
    }
    return order;
  }

  async ReValidateOrder(OrderID: string) {
    const order = await this.OrderModel.findOne({ OrderID });
    if (!order) {
      throw new Error('Order not found');
    }

    const razorpayOrder = await this.Razorpay.GetOrderDetails(order.OrderID);

    if (order.Status === OrderStatus.enum.Pending && razorpayOrder.status === 'paid') {
      await this.OrderModel.updateOne(
        { OrderID },
        { Status: OrderStatus.enum.Success, OrderVerifiedAt: new Date(), OrderVerified: true }
      );
    }

    if (order.Status === OrderStatus.enum.Pending && razorpayOrder.status === 'attempted') {
      await this.OrderModel.updateOne(
        { OrderID },
        { Status: OrderStatus.enum.Failed, OrderVerifiedAt: new Date(), OrderVerified: true }
      );
    }

    if (
      order.Status === OrderStatus.enum.Pending &&
      razorpayOrder.status === 'created' &&
      order.Timestamp.getTime() - new Date().getTime() > 10 * 60 * 1000
    ) {
      await this.OrderModel.updateOne({ OrderID }, { Status: OrderStatus.enum.Expired });
    }

    this.EventEmitter.emit('payment.order.captured', order);

    return {
      order,
      PGResponse: razorpayOrder,
    };
  }

  async VerifyOrder(Params: IVerifyOrder) {
    const order = await this.OrderModel.findOne({ OrderID: Params.OrderID });
    if (!order) {
      throw new Error('Order not found');
    }

    const razorpayOrder = await this.Razorpay.VerifySignature(order.OrderID, Params.PaymentID, Params.Signature);

    if (!razorpayOrder) {
      order.Status = OrderStatus.enum.Failed;
      await order.save();
      this.EventEmitter.emit('payment.order.failed', order);

      throw new BadGatewayException('Order verification failed');
    }

    order.Status = OrderStatus.enum.Success;
    await order.save();

    // emit event
    this.EventEmitter.emit('payment.order.captured', order);

    return order;
  }
}
