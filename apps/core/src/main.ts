import { INestApplication, Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { json } from 'body-parser';
import * as cookieParser from 'cookie-parser';
import { ErrorInterceptor } from '../common/interceptors/error.interceptor';
import { AppModule } from './app.module';

class App {
  static app: INestApplication;
  static config: ConfigService;

  static async init() {
    await App.bootstrap();
    App.initMiddleware();
    App.Interceptors();
    App.connections();
    App.vroom();
  }

  static initMiddleware() {
    this.app.use(
      json({
        limit: '50mb',
      })
    );

    this.app.use((req, _, next) => {
      // set x-cookie as cookie
      if (req.headers['x-cookie']) {
        //copy all cookies from x-cookie to cookie
        req.headers.cookie = req.headers['x-cookie'];
        delete req.headers['x-cookie'];
      }
      next();
    });
    this.app.use(cookieParser());
    this.app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
      })
    );
    Logger.log('::: Middleware initialized :::');
  }

  static async Interceptors() {
    this.app.useGlobalInterceptors(new ErrorInterceptor());
    Logger.log('::: Interceptors initialized :::');
  }

  static async connections() {}

  static async bootstrap() {
    App.app = await NestFactory.create(AppModule);

    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://academic-lm-sda-admin.vercel.app',
      'https://academic-lm-sda-admin.vercel.app/',
      'https://academic-lm-sda-subscriber.vercel.app',
      'https://academic-lm-sda-subscriber.vercel.app/',
      'https://acadlm.nextcampus.xyz',
      'https://acadlm.nextcampus.xyz/',
    ];
    // enable cors
    App.app.enableCors({
      // origin: allowedOrigins,
      // credentials: true,
    });

    this.config = this.app.get(ConfigService);

    console.log('::: Bootstrap completed :::');
    console.log('::: Config loaded :::');
  }

  static vroom() {
    const PORT = this.config.get<number>('PORT');
    App.app.listen(this.config.get<number>('PORT'));
    Logger.log(`::: Server started on port ${PORT} :::`);
  }
}

App.init();
