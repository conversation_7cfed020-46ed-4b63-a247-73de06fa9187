export class ContentGroup {
  ContentGroupID: string;
  Title: string;
  Description?: string;
  SortOrder?: number;
}

export class HLS {
  Resolutions: {
    Resolution: string;
    URL: string;
  }[];
}

export class S3 {
  Location: string;
  Key: string;
  Type: string;
  FileSize: number; // Size In KB
  Expiration: Date;
  AccessUrl: string;
  FileName: string;
}

export class Content {
  ContentGroupID?: string; // For Content Group
  ContentID: string;
  Meta: {
    Title: string;
    Description?: string;
    Transcript?: string; // Plain Text
    Subtitles?: {
      Language: string;
      Content: string;
    }[]; // SRT
    Attachments?: string[]; // URL
  };
  S3: S3;

  CDN?: {
    URL: string;
  };

  HLS?: HLS;

  SortOrder?: number;

  Duration: number;

  Scopes?: Record<string, string | boolean | number>;
}

export class TContent extends Content {}

export class ContentAlbum {
  ContentAlbumID: string;
  Name: string;
  CoverImage: string;

  TenantID: string;
  ProjectID: string;

  ContentGroups: ContentGroup[];
  Contents: Content[];

  Scopes?: Record<string, string | boolean | number>;

  ContentCount: number;
  ContentDuration: number;
}
