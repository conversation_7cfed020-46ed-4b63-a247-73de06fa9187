from dataclasses import dataclass, field
from agents.embedding_agent.state import EmbeddingState


@dataclass(kw_only=True)
class PageMetadata:
    source_name: str
    page_number: int


@dataclass(kw_only=True)
class Page:
    page_content: str
    metadata: PageMetadata


@dataclass(kw_only=True)
class PageNumbers:
    start_page_number: int
    end_page_number: int


@dataclass(kw_only=True)
class Subsection:
    subsection_name: str
    page_number: int


@dataclass(kw_only=True)
class Section:
    section_id: str
    section_name: str
    section_description: str
    subsections: list[Subsection]
    page_numbers: PageNumbers


@dataclass(kw_only=True)
class Chunk:
    chunk_id: str
    chunk_number: int
    chunk_content: str
    metadata: PageMetadata


@dataclass(kw_only=True)
class EnhancedChunks(Chunk):
    sections: list[Section] = field(default_factory=list)


@dataclass(kw_only=True)
class SummarizedChunks(EnhancedChunks):
    summarized_content: str


@dataclass(kw_only=True)
class InputState(EmbeddingState):
    pass


@dataclass(kw_only=True)
class OutputState:
    sections: list[Section] = field(default_factory=list)
    summarized_chunks: list[SummarizedChunks] = field(default_factory=list)


@dataclass(kw_only=True)
class State(EmbeddingState):
    pages: list[Page] = field(default_factory=list)
    sections: list[Section] = field(default_factory=list)
    chunks: list[Chunk] = field(default_factory=list)
    enhanced_chunks: list[EnhancedChunks] = field(default_factory=list)
    summarized_chunks: list[SummarizedChunks] = field(default_factory=list)


# LLM Structured Outputs
@dataclass(kw_only=True)
class SectionsOutput:
    sections: list[Section]


@dataclass(kw_only=True)
class SummarizedChunksOutput:
    summary: str
