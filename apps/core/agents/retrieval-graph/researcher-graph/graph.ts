/**
 * Researcher graph used in the conversational retrieval system as a subgraph.
 * This module defines the core structure and functionality of the researcher graph,
 * which is responsible for generating search queries and retrieving relevant documents.
 */

import { StateGraph, END, START, Send } from '@langchain/langgraph';
import { z } from 'zod';

import { RunnableConfig } from '@langchain/core/runnables';
import { ensureAgentConfiguration } from '../configuration';
import { QueryStateAnnotation, ResearcherStateAnnotation } from './state';
import { loadChatModel } from '../../shared/utils';
import { makeRetriever } from '../../shared/retrieval';
import { type MessageContent } from '@langchain/core/messages';

/**
 * Safely extracts text from various message content formats
 */
function extractTextFromContent(content: any): string {
  if (typeof content === 'string') {
    return content;
  }

  if (Array.isArray(content)) {
    // Try to find a text content object
    for (const item of content) {
      if (item && typeof item === 'object' && item.type === 'text' && typeof item.text === 'string') {
        return item.text;
      }
    }
  }

  // Fallback: convert to string or return empty
  return String(content || '');
}

async function generateQueries(
  state: typeof ResearcherStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof ResearcherStateAnnotation.Update> {
  const Response = z.object({
    queries: z.array(z.string()),
  });

  const configuration = ensureAgentConfiguration(config);
  const model = (await loadChatModel(configuration.queryModel)).withStructuredOutput(Response);

  // Extract text from question
  const question = extractTextFromContent(state.question);

  const messages: { role: string; content: string }[] = [
    { role: 'system', content: configuration.generateQueriesSystemPrompt },
    { role: 'human', content: question },
  ];
  const response = await model.invoke(messages);
  return { queries: response.queries };
}

async function retrieveDocuments(
  state: typeof QueryStateAnnotation.State,
  config: RunnableConfig
): Promise<typeof ResearcherStateAnnotation.Update> {
  const vectorStore = await makeRetriever(config);

  // Extract text from query
  const query = extractTextFromContent(state.query);

  // Add tenant and project filtering to the configuration
  const configWithFilters = {
    ...config,
    configurable: {
      ...(config?.configurable || {}),
      searchKwargs: {
        ...(config?.configurable?.searchKwargs || {}),
        filter: {
          ...(config?.configurable?.searchKwargs?.filter || {}),
          tenantId: state.tenantId,
          projectId: state.projectId,
        },
      },
    },
  };

  const response = await vectorStore.similaritySearchWithScore(query, 4, {
    tenantId: state.tenantId,
    projectId: state.projectId,
  });
  const filteredResponse = response.filter(
    (doc) =>
      doc[0].metadata.tenantId === state.tenantId && doc[0].metadata.projectId === state.projectId && doc[1] >= 0.3
  );
  console.log(
    `Query: ${query} Retrieved ${response.length} documents and filtered to ${filteredResponse.length}, documents: ${JSON.stringify(
      response
    )}`
  );
  return { documents: filteredResponse };
}

function retrieveInParallel(state: typeof ResearcherStateAnnotation.State): Send[] {
  return state.queries.map(
    (query: string) =>
      new Send('retrieveDocuments', {
        query,
        tenantId: state.tenantId,
        projectId: state.projectId,
      })
  );
}

// Define the graph
const builder = new StateGraph({
  stateSchema: ResearcherStateAnnotation,
})
  .addNode('generateQueries', generateQueries)
  .addNode('retrieveDocuments', retrieveDocuments)
  .addEdge(START, 'generateQueries')
  .addConditionalEdges('generateQueries', retrieveInParallel, ['retrieveDocuments'])
  .addEdge('retrieveDocuments', END);

// Compile into a graph object that you can invoke and deploy.
export const graph = builder.compile().withConfig({ runName: 'ResearcherGraph' });
