import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import GenerateID from '../../../common/lib/generate-id';

@Schema({ collection: 'lecture', timestamps: true, versionKey: false })
export class Lecture extends Document {
  @Prop({ required: true, unique: true, index: true, default: () => GenerateID.Generate16Hex('lct_') })
  LectureID: string;

  @Prop({ required: true })
  TenantID: string;

  @Prop({ required: true })
  ProjectID: string;

  @Prop({ required: true })
  SourceID: string;

  @Prop({ required: true })
  ChapterID: string;

  @Prop({ required: true })
  LectureTitle: string;

  @Prop({ required: true })
  LectureDescription: string;

  @Prop({ required: true })
  LectureSummary: string;

  @Prop({ required: true })
  TeachingStyle: string;

  @Prop({ required: true })
  AgeGroup: string;

  @Prop({ required: true })
  CreativityLevel: string;

  @Prop({ required: true })
  LectureScript: string;

  @Prop({ required: false })
  SSMLScript: string;

  @Prop({ required: true })
  TeachingPlan: string;

  @Prop({ required: false })
  Lectures: {
    ContentID: string;
    ContentTitle: string;
    Description: string;
    Transcript: string;
    FormattedTranscript: string;
  }[];
}

export const LectureSchema = SchemaFactory.createForClass(Lecture);
