import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import GenerateID from '../../../common/lib/generate-id';

@Schema({ collection: 'chat_messages', timestamps: true, versionKey: false })
export class ChatMessage {
  @Prop({
    required: true,
    default: () => GenerateID.Generate16Hex('msg_'),
  })
  MessageId: string;

  @Prop({ required: true })
  ThreadID: string;

  @Prop({ required: true, enum: ['user', 'assistant', 'system'] })
  role: 'user' | 'assistant' | 'system';

  @Prop({ required: true })
  content: string;

  @Prop({ required: true })
  timestamp: Date;

  @Prop({ required: true })
  UserID: string;

  @Prop({ required: true })
  TenantID: string;

  @Prop({ required: true })
  ProjectID: string;

  @Prop({ required: false })
  citations: {
    id: string;
    source_title: string;
    pdf_url: string | null;
    page: number;
    line_from: number | null;
    line_to: number | null;
  }[];

  @Prop({ required: false })
  output_language: string;
}

export type ChatMessageDocument = ChatMessage & Document;
export const ChatMessageSchema = SchemaFactory.createForClass(ChatMessage);
