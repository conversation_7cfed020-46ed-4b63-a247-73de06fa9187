import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ collection: 'ind_chunks', timestamps: true, versionKey: false })
export class IndChunkModel {
  @Prop({ required: true })
  ChunkID: string;

  @Prop({ required: true })
  SourceID: string;

  @Prop({ required: true, default: [] })
  Sections: {
    SectionID: string;
    SectionName: string;
    SubSections: {
      SubSectionName: string;
    }[];
  }[];

  @Prop({ type: Number, required: true })
  ChunkNumber: number;

  @Prop({ type: Number, required: true })
  PageNumber: number;

  @Prop({ type: Number, required: true })
  DocumentPageNumber: number;

  @Prop({ type: String })
  Summary?: string;

  @Prop({ type: String, required: true })
  Content: string;

  @Prop({ type: String, required: true })
  TenantID: string;

  @Prop({ type: String, required: true })
  ProjectID: string;
}

export const IndChunkSchema = SchemaFactory.createForClass(IndChunkModel);

// Add indexes for better query performance
IndChunkSchema.index({ SourceID: 1 });
IndChunkSchema.index({ ChunkID: 1 }, { unique: true });
IndChunkSchema.index({ TenantID: 1, ProjectID: 1 });
