import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Res,
  UseGuards,
  HttpStatus,
  HttpException,
  Query,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { ChatService } from './chat.service';
import { CreateChatDto } from './dto/create-chat.dto';
import { UpdateChatDto } from './dto/update-chat.dto';
import { ChatResponseDto } from './dto/chat-response.dto';
import { ChatHistoryDto } from './dto/chat-history.dto';
import { Response } from 'express';
import { ThreadService } from './thread.service';
import { CreateThreadDto } from './dto/thread.dto';
import { AuthGuard } from '../../common/guards/auth.guard';
import { User } from '../../common/decorators/core.decorators';
import { TUser } from '../core/user/entities/user.entity';
import { IsCreditAvailableGuard } from '../../common/guards/is-credit-avail.guard';
import { PlanNSubscriptionService } from '../plan-n-subscription/plan-n-subscription.service';

@Controller('chat')
@UseGuards(AuthGuard) // Apply authentication to all chat endpoints
export class ChatController {
  constructor(
    private readonly chatService: ChatService,
    private readonly threadService: ThreadService,
    private readonly PlanNSub: PlanNSubscriptionService
  ) {}

  @Post('send')
  async create(@Body() createChatDto: CreateChatDto, @Req() req) {
    try {
      const userId = req.user.UserID;
      const tenantId = req.user.TenantID;

      if (!userId || !tenantId) {
        throw new UnauthorizedException('Invalid authentication credentials');
      }

      return await this.chatService.create(userId, tenantId, createChatDto);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'An error occurred while sending the message',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('stream')
  async streamMessage(
    @Body() createChatDto: CreateChatDto,
    @Req() req,
    @Res() res: Response,
    @User() user: TUser,
    @Query('SubscriptionID') SubID: string
  ) {
    if (SubID) {
      const checkLimit = await this.PlanNSub.IsCreditLimitAvailable({
        UserID: user.UserID,
        SubscriptionID: SubID,
      });

      if (checkLimit.error) {
        throw new HttpException('Credit limit exceeded', HttpStatus.FORBIDDEN);
      }
    }

    // Set up SSE
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders();

    const userId = req.user.UserID;
    const tenantId = req.user.TenantID;

    if (!userId || !tenantId) {
      const errorMessage = {
        message: 'Error: Invalid authentication credentials',
        isComplete: true,
        chunkIndex: -1,
      };
      res.write(`data: ${JSON.stringify(errorMessage)}\n\n`);
      res.end();
      return;
    }

    // Create a message handler function
    const messageHandler = (chunk) => {
      res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      if (chunk.isComplete) {
        res.end();
      }
    };

    // Stream the response
    try {
      await this.chatService.streamResponse(userId, tenantId, createChatDto, messageHandler, user.AppType);
    } catch (error) {
      // If there's an error, send it as an event and end the stream
      const errorMessage = {
        message: `Error: ${error.message || 'An unexpected error occurred'}`,
        isComplete: true,
        chunkIndex: -1,
      };
      res.write(`data: ${JSON.stringify(errorMessage)}\n\n`);
      res.end();
    }
  }

  @Get('history/:ThreadID')
  async getChatHistory(@Param('ThreadID') ThreadID: string, @Req() req) {
    try {
      const userId = req.user.UserID;

      if (!userId) {
        throw new UnauthorizedException('Invalid authentication credentials');
      }

      if (!ThreadID) {
        throw new BadRequestException('Thread ID is required');
      }

      return await this.chatService.getChatHistory(ThreadID, userId);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'An error occurred while retrieving chat history',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete('history/:ThreadID')
  async clearChatHistory(@Param('ThreadID') ThreadID: string, @Req() req) {
    try {
      const userId = req.user.UserID;

      if (!userId) {
        throw new UnauthorizedException('Invalid authentication credentials');
      }

      if (!ThreadID) {
        throw new BadRequestException('Thread ID is required');
      }

      return await this.chatService.clearChatHistory(ThreadID, userId);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'An error occurred while clearing chat history',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('threads')
  async getThreads(@Req() req, @Query('ProjectID') ProjectID?: string) {
    try {
      const userId = req.user.UserID;

      if (!userId) {
        throw new UnauthorizedException('Invalid authentication credentials');
      }

      if (ProjectID) {
        return await this.threadService.getThreadsByProject(userId, ProjectID);
      }
      return await this.threadService.getThreads(userId);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'An error occurred while retrieving threads',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('threads')
  async createThread(@Body() createThreadDto: CreateThreadDto, @Req() req) {
    try {
      const userId = req.user.UserID;
      const tenantId = req.user.TenantID;

      if (!userId || !tenantId) {
        throw new UnauthorizedException('Invalid authentication credentials');
      }

      if (!createThreadDto.ProjectID) {
        throw new BadRequestException('Project ID is required');
      }

      return await this.threadService.createThread(userId, tenantId, createThreadDto);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'An error occurred while creating thread',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('threads/:ThreadID')
  async getThread(@Param('ThreadID') ThreadID: string, @Req() req) {
    try {
      const userId = req.user.UserID;

      if (!userId) {
        throw new UnauthorizedException('Invalid authentication credentials');
      }

      if (!ThreadID) {
        throw new BadRequestException('Thread ID is required');
      }

      // Use validateAndGetThread to ensure ownership and get thread data in one call
      return await this.threadService.validateAndGetThread(ThreadID, userId);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'An error occurred while retrieving thread',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Patch('threads/:ThreadID')
  async updateThread(@Param('ThreadID') ThreadID: string, @Body() updateData: { title: string }, @Req() req) {
    try {
      const userId = req.user.UserID;

      if (!userId) {
        throw new UnauthorizedException('Invalid authentication credentials');
      }

      if (!ThreadID) {
        throw new BadRequestException('Thread ID is required');
      }

      if (!updateData.title) {
        throw new BadRequestException('Thread title is required');
      }

      const result = await this.threadService.updateThreadTitle(ThreadID, userId, updateData.title);

      if (!result) {
        throw new HttpException('Failed to update thread title', HttpStatus.BAD_REQUEST);
      }

      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'An error occurred while updating thread',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete('threads/:ThreadID')
  async deleteThread(@Param('ThreadID') ThreadID: string, @Req() req) {
    try {
      const userId = req.user.UserID;

      if (!userId) {
        throw new UnauthorizedException('Invalid authentication credentials');
      }

      if (!ThreadID) {
        throw new BadRequestException('Thread ID is required');
      }

      return await this.chatService.deleteThread(ThreadID, userId);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'An error occurred while deleting thread',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get()
  findAll() {
    return this.chatService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.chatService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateChatDto: UpdateChatDto) {
    return this.chatService.update(+id, updateChatDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.chatService.remove(+id);
  }
}
