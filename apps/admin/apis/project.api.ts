import { TryCatch } from "@/lib/try-catch";
import { TAckResponse, TErrorResponse, KY } from "./_.index";
import { TProject, TProjectDetails } from "@/types/project";

export type TCreateProjectParams = {
  Name: string;
  Description: string;
};

export type TUpdateProjectParams = TCreateProjectParams & {
  ProjectID: string;
};

const CreateProject = async (params: TCreateProjectParams) => {
  return await TryCatch<TAckResponse<TProject>, TErrorResponse>(
    KY.post("project", { json: params }).json()
  );
};

const UpdateProject = async (params: TUpdateProjectParams) => {
  return await TryCatch<TAckResponse<TProject>, TErrorResponse>(
    KY.patch(`project/${params.ProjectID}`, { json: params }).json()
  );
};

const ListProjects = async () => {
  return await TryCatch<TProject[], TErrorResponse>(KY.get("project").json());
};

const DeleteProject = async (params: TUpdateProjectParams) => {
  return await TryCatch<TAckResponse<TProject>, TErrorResponse>(
    KY.delete(`project/${params.ProjectID}`).json()
  );
};

const GetProject = async (params: { ProjectID: string }) => {
  return await TryCatch<TProject, TErrorResponse>(
    KY.get(`project/${params.ProjectID}`).json()
  );
};

const GetProjectDetails = async (params: { ProjectID: string }) => {
  return await TryCatch<TProjectDetails, TErrorResponse>(
    KY.get(`project/${params.ProjectID}/details`).json()
  );
};

const AddSource = async (body: {
  ProjectID: string;
  TenantID: string;
  tableOfContentPages: number[];
  pageNumberOffset: number;
  sourceName: string;
  file: File;
  sourceType: string;
  language: string;
}) => {
  const formData = new FormData();
  formData.append("ProjectID", body.ProjectID);
  formData.append("TenantID", body.TenantID);
  formData.append("sourceName", body.sourceName);
  formData.append("pageNumberOffset", String(body.pageNumberOffset));
  formData.append("file", body.file);
  formData.append("sourceType", body.sourceType);
  formData.append("language", body.language);
  formData.append("version", "v2");
  body.tableOfContentPages.forEach((page, idx) => {
    formData.append(`tableOfContentPages[${idx}]`, String(page));
  });
  return await TryCatch<TAckResponse<TProject>, TErrorResponse>(
    KY.post(`embed`, { body: formData }).json()
  );
};

const DeleteSource = async (params: {
  ProjectID: string;
  TenantID: string;
  SourceID: string;
}) => {
  return await TryCatch<
    TAckResponse<{
      success: boolean;
      message: string;
      sourceId: string;
      tenantId: string;
      projectId: string;
    }>,
    TErrorResponse
  >(
    KY.delete(`embed/${params.SourceID}`, {
      searchParams: {
        tenantId: params.TenantID,
        projectId: params.ProjectID,
      },
      timeout: 100000,
    }).json()
  );
};

export const PROJECT = {
  CreateProject,
  ListProjects,
  UpdateProject,
  DeleteProject,
  GetProject,
  GetProjectDetails,
  AddSource,
  DeleteSource,
};
