"use client";
import { API } from "@/apis/api";
import { TContentAlbum } from "@/apis/content-album.api";
import { useProjectContext } from "@/contexts/project-context";
import { Button } from "@workspace/ui/components/button";
import {
  Box<PERSON>,
  Clapperboard,
  Clock8,
  LibraryBig,
  ScanEye,
  Upload,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const ContentLibrary = () => {
  const [contentAlbums, setContentAlbums] = useState<TContentAlbum[]>([]);
  const { selectedProject } = useProjectContext();
  const router = useRouter();

  const getContentAlbums = async () => {
    const { data, errors } = await API.CONTENT_ALBUM.GetContentAlbums({
      ProjectID: selectedProject?.id!,
    });

    if (!errors && data.length > 0) {
      setContentAlbums(data);
    }
  };

  useEffect(() => {
    if (!selectedProject) return;
    getContentAlbums();
  }, [selectedProject]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 w-full gap-4">
      {contentAlbums.map((album) => (
        <div
          className="cursor-pointer rounded-lg bg-accent flex flex-col gap-3 hover:shadow-lg pb-2"
          key={album.ContentAlbumID}
          onClick={() =>
            router.push(`/${album.ProjectID}/content/${album.ContentAlbumID}`)
          }
        >
          <div className="flex items-center gap-2">
            <img
              src={album.CoverImage}
              alt={album.Name}
              width={"100%"}
              height={"100%"}
              className="w-full h-full object-cover rounded-lg max-h-40 overflow-hidden"
            />
          </div>
          <div className="px-2">
            <h2 className="text-lg font-semibold">{album.Name}</h2>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {album.Description ?? "..."}
            </p>
            <div className="py-2 flex flex-wrap">
              {/* if  updated is less than 24 hours ago, show updated else show createdAt */}
              {new Date(album.updatedAt).getTime() - new Date().getTime() <
                24 * 60 * 60 * 1000 && (
                <span className="text-xs text-white px-2 py-1 rounded-sm bg-accent flex items-center gap-1 max-w-fit">
                  <Upload size={12} />
                  Content Updated
                </span>
              )}
              {new Date(album.updatedAt).getTime() - new Date().getTime() >=
                24 * 60 * 60 * 1000 && (
                <span className="text-xs text-white px-2 py-1 rounded-sm bg-green-500 flex items-center gap-1 max-w-fit">
                  <Upload size={12} />
                  Recently Added
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center gap-3 text-sm px-2">
            <span className="flex items-center gap-1">
              <Clock8 size={16} />
              <span className="text-xs text-muted-foreground  ">
                {Math.floor((album.ContentDuration ?? 0) / 60 / 60) > 0
                  ? Math.floor((album.ContentDuration ?? 0) / 60 / 60) +
                    " hours"
                  : Math.floor((album.ContentDuration ?? 0) / 60) + " minutes"}
              </span>
            </span>
            <span className="flex items-center gap-1">
              <Clapperboard size={16} />
              <span className="text-xs text-muted-foreground">
                {album.ContentCount ?? 0} contents
              </span>
            </span>
            <span className="flex items-center gap-1">
              <Boxes size={16} />
              <span className="text-xs text-muted-foreground">
                {album.ContentGroups.length} modules
              </span>
            </span>
          </div>

          <div className="px-2 w-full mt-1">
            <Button
              variant="outline"
              className="w-full cursor-pointer"
              onClick={() =>
                router.push(
                  `/${album.ProjectID}/content/${album.ContentAlbumID}`,
                )
              }
            >
              <ScanEye size={16} />
              View
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default function ContentPage() {
  return (
    <div className="flex w-full">
      <div className="flex flex-col w-full gap-4">
        {/* <div className="flex items-center gap-2">
          <LibraryBig size={24} />
          <h1 className="text-2xl font-semibold">Content Library</h1>
        </div> */}

        <div className="block md:hidden">
          <ContentLibrary />
        </div>
        <div className="flex-col justify-center items-center gap-2 h-[calc(100vh-100px)] hidden md:flex">
          <LibraryBig size={48} strokeWidth={1} />
          <h1 className="text-2xl font-semibold">
            Select Library to Load Content
          </h1>
        </div>
      </div>
    </div>
  );
}
