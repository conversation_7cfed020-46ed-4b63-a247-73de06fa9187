import { z } from 'zod';

export const ImageType = ['image/jpg', 'image/png', 'image/jpeg', 'image/tiff', 'image/tif'] as const;
export const BinaryType = ['application/pdf'] as const;
export const TextType = ['text/plain', 'text/csv'] as const;
export const SupportedMediaTypes = [...ImageType, ...BinaryType, ...TextType]; // Union of all media types
export const MediaAcronym = SupportedMediaTypes?.map((mediaType) => mediaType?.split('/')?.slice(-1)?.[0]);

// Enums
export const EBinaryTypes = z.enum([...BinaryType]);
export const EImageTypes = z.enum([...ImageType]);
export const ETextTypes = z.enum([...TextType]);
