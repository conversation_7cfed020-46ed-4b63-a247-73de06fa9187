import { Global, MiddlewareConsumer, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ProjectService } from './project.service';
import { ProjectController } from './project.controller';
import { ProjectModel, ProjectSchema } from './schemas/project.schema';
import { PlanNSubscriptionModule } from '../plan-n-subscription/plan-n-subscription.module';
import { SourceModel, SourceSchema } from '../embed/schemas/source.schema';
import { IndSourceModel } from '../embed/schemas/ind-source.schema';
import { IndSourceSchema } from '../embed/schemas/ind-source.schema';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ProjectModel.name, schema: ProjectSchema },
      { name: SourceModel.name, schema: SourceSchema },
      { name: IndSourceModel.name, schema: IndSourceSchema },
    ]),
    PlanNSubscriptionModule,
  ],
  controllers: [ProjectController],
  providers: [ProjectService],
  exports: [ProjectService],
})
export class ProjectModule {}
