"use client";
import { API } from "@/apis/api";
import { TSubscriptionStatus } from "@/apis/subscription.api";
import {
  SubscriptionStatus,
  TPlan,
  TSubscription,
} from "@/types/plan-subscription";
import { TUser } from "@/types/user";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Calendar } from "@workspace/ui/components/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Label } from "@workspace/ui/components/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Separator } from "@workspace/ui/components/separator";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON>it<PERSON>,
  She<PERSON><PERSON>rigger,
} from "@workspace/ui/components/sheet";
import { Switch } from "@workspace/ui/components/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { cn } from "@workspace/ui/lib/utils";
import { format } from "date-fns";
import {
  Building2,
  Cake,
  CalendarIcon,
  ChevronLeftIcon,
  Info,
  Mail,
  MapPin,
  Phone,
  Plus,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const statusColorMap = {
  [SubscriptionStatus.Active]: "bg-green-500",
  [SubscriptionStatus.Inactive]: "bg-gray-500",
  [SubscriptionStatus.Cancelled]: "bg-red-500",
  [SubscriptionStatus.Paused]: "bg-yellow-500",
  [SubscriptionStatus.Pending]: "bg-blue-500",
};

export default function UserPage() {
  const [subscriptions, setSubscriptions] = useState<TSubscription[]>([]);
  const [user, setUser] = useState<TUser | null>(null);
  const [plans, setPlans] = useState<TPlan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<TPlan | null>(null);
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<string>("");
  const [isSuperUser, setIsSuperUser] = useState<boolean>(false);
  const [status, setStatus] = useState<SubscriptionStatus>(
    SubscriptionStatus.Active
  );

  const userID = useParams<{ userID: string }>().userID;

  const DatePicker = ({
    date,
    setDate,
  }: {
    date: Date | null;
    setDate: (date: Date) => void;
  }) => {
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn(
              "w-[280px] justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon />
            {date ? format(date, "PPP") : <span>Pick a date</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={date as Date}
            onSelect={(date) => {
              if (date) {
                setDate(date);
              }
            }}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    );
  };

  const fetchSubscriptions = async () => {
    const { data, errors } = await API.SUBSCRIPTION.ListSubscriptions({
      UserIDs: [userID || ""],
    });
    if (data) {
      setSubscriptions(data);
    }
    if (errors) {
      toast.error(errors[0]?.message || "Something went wrong");
    }
  };

  const fetchPlans = async () => {
    const { data, errors } = await API.SUBSCRIPTION.ListPlans();
    if (data) {
      setPlans(data);
    }
    if (errors) {
      toast.error(errors[0]?.message || "Something went wrong");
    }
  };

  const fetchUser = async () => {
    const { data, errors } = await API.USER.GetUser({ UserID: userID || "" });
    if (data) {
      setUser(data);
    }
    if (errors) {
      toast.error(errors[0]?.message || "Something went wrong");
    }
  };

  useEffect(() => {
    fetchSubscriptions();
    fetchPlans();
    fetchUser();
  }, [userID]);

  if (!user) return null;

  const router = useRouter();
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={() => router.back()}>
          <ChevronLeftIcon />
        </Button>
        <h1 className="text-sm">Go Back To Users</h1>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={user.ProfilePicture} />
                <AvatarFallback>
                  {user.Name?.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-2xl">{user.Name}</CardTitle>
                <CardDescription>{user.Email}</CardDescription>
              </div>
            </div>
            <Badge
              variant={user.UserType === "Admin" ? "default" : "secondary"}
            >
              {user.UserType}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{user.Email}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span>{user.PhoneNumber || "Not provided"}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                <span>
                  {user?.DateOfBirth
                    ? format(new Date(user.DateOfBirth), "PPP")
                    : "Not provided"}
                </span>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{user.Address || "Not provided"}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Cake className="h-4 w-4 text-muted-foreground" />
                <span>
                  {user?.DateOfBirth
                    ? format(new Date(user.DateOfBirth), "PPP")
                    : "Not provided"}
                </span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Building2 className="h-4 w-4 text-muted-foreground" />
                <span>User ID: {user.UserID}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Subscriptions</CardTitle>
            <Sheet>
              <SheetTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Subscription
                </Button>
              </SheetTrigger>
              <SheetContent className="w-[400px] sm:w-[540px] px-4 py-6">
                <SheetHeader>
                  <SheetTitle>Add New Subscription</SheetTitle>
                  <SheetDescription>
                    Create a new subscription for {user.Name}
                  </SheetDescription>
                </SheetHeader>
                <Separator className="my-2" />
                <form className="space-y-4">
                  <div className="space-y-2">
                    <Label>Select Plan</Label>
                    <Select
                      onValueChange={(value) =>
                        setSelectedPlan(
                          plans.find((p) => p.PlanID === value) || null
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a plan" />
                      </SelectTrigger>
                      <SelectContent>
                        {plans
                          .filter(
                            (plan) =>
                              plan.PlanID !==
                              subscriptions.find(
                                (s) => s.Plan.PlanID === plan.PlanID
                              )?.Plan.PlanID
                          )
                          .map((plan) => (
                            <SelectItem key={plan.PlanID} value={plan.PlanID}>
                              {plan.Name} - {plan.Tier}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedPlan && (
                    <div className="space-y-2">
                      <div className="rounded-lg border p-4">
                        <h4 className="font-medium">{selectedPlan.Name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {selectedPlan.Description}
                        </p>
                        <div className="mt-2 flex flex-col items-center justify-between">
                          {selectedPlan.PricingStructures.map((pricing) => (
                            <div
                              key={pricing.Type}
                              className="flex w-full gap-2 justify-between"
                            >
                              <h5 className="text-sm text-muted-foreground font-medium">
                                {pricing.Type}
                              </h5>
                              <p className="text-sm font-semibold">
                                {new Intl.NumberFormat("en-US", {
                                  style: "currency",
                                  currency: selectedPlan.Currency,
                                }).format(pricing.Price / 100)}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {
                    <div className="bg-muted p-4 rounded-md flex items-center justify-between">
                      <Label>
                        Super User{" "}
                        <span className="text-xs text-muted-foreground">
                          (No Capping & No Expiry)
                        </span>
                      </Label>
                      <Switch
                        checked={isSuperUser}
                        onCheckedChange={setIsSuperUser}
                      />
                    </div>
                  }

                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <Label>Variant</Label>
                      <Select
                        onValueChange={(value) => setSelectedVariant(value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a variant" />
                        </SelectTrigger>
                        <SelectContent>
                          {selectedPlan?.PricingStructures.map((pricing) => (
                            <SelectItem key={pricing.Type} value={pricing.Type}>
                              {pricing.Type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Status</Label>
                      <Select
                        defaultValue={status}
                        onValueChange={(value) =>
                          setStatus(value as SubscriptionStatus)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a status" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(SubscriptionStatus).map((status) => (
                            <SelectItem key={status} value={status}>
                              {status}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Start Date</Label>
                    <DatePicker date={startDate} setDate={setStartDate} />
                  </div>

                  <div className="space-y-2">
                    <Label>End Date (Optional)</Label>
                    <DatePicker date={endDate} setDate={setEndDate} />
                  </div>

                  <Button
                    type="submit"
                    className="w-full my-auto"
                    onClick={async (e: React.FormEvent) => {
                      e.preventDefault();
                      if (!selectedPlan) {
                        toast.error("Please select a plan");
                        return;
                      }

                      if (!selectedVariant) {
                        toast.error("Please select a variant");
                        return;
                      }

                      const { data, errors } =
                        await API.SUBSCRIPTION.CreateSubscription({
                          PlanID: selectedPlan.PlanID || "",
                          UserID: user.UserID || "",
                          Status: status as TSubscriptionStatus,
                          StartDate: startDate.toISOString(),
                          EndDate: endDate?.toISOString() || null,
                          Variant: selectedVariant,
                          SuperUser: isSuperUser,
                          Price: selectedPlan.PricingStructures.find(
                            (ps) => ps.Type === selectedVariant
                          )?.Price,
                        });

                      if (data) {
                        toast.success("Subscription created successfully");
                        fetchSubscriptions();
                        // Close sheet
                      }

                      if (errors) {
                        toast.error(
                          errors[0]?.message || "Failed to create subscription"
                        );
                      }
                    }}
                  >
                    Create Subscription
                  </Button>
                </form>
              </SheetContent>
            </Sheet>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Plan</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Price</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {subscriptions.map((subscription) => (
                <TableRow key={subscription.PlanID}>
                  <TableCell>
                    <div className="font-medium">{subscription.Plan.Name}</div>
                    <div className="text-sm text-muted-foreground">
                      {subscription.Plan.Tier}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={`${
                        statusColorMap[subscription.Status]
                      } bg-opacity-10 border-0`}
                    >
                      <div
                        className={`h-2 w-2 rounded-full ${
                          statusColorMap[subscription.Status]
                        } mr-2`}
                      />
                      {subscription.Status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {subscription?.StartDate
                      ? format(new Date(subscription.StartDate), "PP")
                      : "No start date"}
                  </TableCell>
                  <TableCell>
                    {subscription?.EndDate
                      ? format(new Date(subscription.EndDate), "PP")
                      : "No end date"}
                  </TableCell>
                  <TableCell>
                    {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: subscription.Plan.Currency,
                    }).format(subscription.Price / 100)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
