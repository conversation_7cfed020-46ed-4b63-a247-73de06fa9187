import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import GenerateID from '../../../common/lib/generate-id';
import { PopQuizQuestionModel } from './question.schema';
import { PopQuizModel } from './quiz.schema';
import { User } from '../../core/user/schemas/user.model';

@Schema({
  _id: null,
})
export class QuestionResponse {
  @Prop({
    type: String,
    required: true,
  })
  QuestionID: string;

  @Prop({
    type: Boolean,
    required: true,
  })
  IsCorrect: boolean;

  @Prop({
    type: [String],
    required: true,
  })
  OptionIDs?: string[];
}

@Schema({
  _id: null,
})
export class Result {
  @Prop({
    type: Number,
    required: true,
  })
  CorrectAnswers: number;

  @Prop({
    type: Number,
    required: true,
  })
  IncorrectAnswers: number;

  @Prop({
    type: Number,
    required: true,
  })
  TotalAnswers: number;

  @Prop({
    type: Number,
    required: true,
  })
  Score: number;
}

@Schema({
  collection: 'pop_quiz_responses',
  timestamps: true,
  versionKey: false,
})
export class PopQuizResponseModel {
  @Prop({
    type: String,
    required: true,
  })
  PopQuizID: string;

  @Prop({
    type: String,
    required: true,
  })
  UserID: string;

  @Prop({
    type: String,
    required: true,
    default: () => GenerateID.Generate16Hex('pop_res_'),
  })
  ResponseID: string;

  @Prop({
    type: [QuestionResponse],
    required: true,
  })
  Questions: QuestionResponse[];

  @Prop({
    type: Result,
    required: true,
  })
  Result: Result;

  @Prop({
    required: true,
    type: String,
  })
  TenantID: string;
}

export const PopQuizResponseSchema = SchemaFactory.createForClass(PopQuizResponseModel);

PopQuizResponseSchema.index({ ResponseID: 1 }, { unique: true });
PopQuizResponseSchema.index({ ResponseID: 1, SourceID: 1, TenantID: 1 }, { unique: true });

PopQuizResponseSchema.virtual('PopQuiz', {
  ref: PopQuizModel.name,
  localField: 'PopQuizID',
  foreignField: 'PopQuizID',
  justOne: true,
});

PopQuizResponseSchema.virtual('QuestionDetails', {
  ref: PopQuizQuestionModel.name,
  localField: 'Questions.QuestionID',
  foreignField: 'QuestionID',
});

PopQuizResponseSchema.virtual('User', {
  ref: User.name,
  localField: 'UserID',
  foreignField: 'UserID',
  justOne: true,
});
