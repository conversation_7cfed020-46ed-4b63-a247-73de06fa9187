"use client";
import { useState, useRef } from "react";
import { Upload } from "lucide-react";

interface ImageUploadProps extends React.HTMLAttributes<HTMLDivElement> {
  onUpload: (base64: string) => void;
  currentImage?: string;
}

export function ImageUpload({
  onUpload,
  currentImage,
  className,
  ...props
}: ImageUploadProps) {
  const [preview, setPreview] = useState<string | null>(currentImage || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onloadend = () => {
      const base64 = reader.result as string;
      setPreview(base64);
      onUpload(base64);
    };
    reader.readAsDataURL(file);
  };

  return (
    <div
      className={`relative border-2 border-dashed rounded-lg flex items-center justify-center cursor-pointer hover:border-primary/50 transition-colors ${className}`}
      onClick={() => fileInputRef.current?.click()}
      {...props}
    >
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/*"
        onChange={handleFileChange}
      />

      {preview ? (
        <img
          src={preview}
          alt="Preview"
          className="w-full h-full object-cover rounded-lg"
        />
      ) : (
        <div className="flex flex-col items-center justify-center p-4 text-muted-foreground">
          <Upload className="w-8 h-8 mb-2" />
          <span className="text-sm">Upload Image</span>
        </div>
      )}
    </div>
  );
}
