"use client";

import { useState, useRef, useEffect } from "react";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { Card } from "@workspace/ui/components/card";

interface Message {
  id: number;
  sender: string;
  content: string;
  time: string;
  isUser: boolean;
}
interface ChatScreenProps {
  threadId?: number;
  threadName?: string;
}

export default function ChatScreen({
  threadName = "General Discussion",
}: ChatScreenProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();

    if (!inputValue.trim()) return;

    const newMessage = {
      id: messages.length + 1,
      sender: "You",
      content: inputValue,
      time: new Date().toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      }),
      isUser: true,
    };

    setMessages([...messages, newMessage]);
    setInputValue("");
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  return (
    <div className="flex flex-col h-full">
      {/* Thread title */}
      <div className="mb-4">
        <h2 className="text-xl font-semibold">{threadName}</h2>
      </div>
      {/* Messages area */}
      <ScrollArea className="flex-1 pr-4">
        <div className="space-y-4 pb-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.isUser ? "justify-end" : "justify-start"}`}
            >
              <div
                className={`flex max-w-[80%] ${message.isUser ? "flex-row-reverse" : "flex-row"} items-start gap-2`}
              >
                {!message.isUser && (
                  <Avatar className="w-8 h-8">
                    <AvatarImage src="" />
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                )}

                <div
                  className={`
                    px-4 py-2 rounded-lg 
                    ${
                      message.isUser
                        ? "bg-primary text-primary-foreground rounded-tr-none"
                        : "bg-muted rounded-tl-none"
                    }
                  `}
                >
                  <div className="flex justify-between items-baseline gap-4 mb-1">
                    <span className="font-medium text-sm">
                      {message.sender}
                    </span>
                    <span className="text-xs opacity-70">{message.time}</span>
                  </div>
                  <p>{message.content}</p>
                </div>

                {message.isUser && (
                  <Avatar className="w-8 h-8">
                    <AvatarImage src="" />
                    <AvatarFallback>You</AvatarFallback>
                  </Avatar>
                )}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      {/* Message input */}
      <Card className="mt-4 p-2">
        <form onSubmit={handleSendMessage} className="flex gap-2">
          <Input
            placeholder="Type your message..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            className="flex-1"
          />
          <Button type="submit" disabled={!inputValue.trim()}>
            Send
          </Button>
        </form>
      </Card>
    </div>
  );
}
