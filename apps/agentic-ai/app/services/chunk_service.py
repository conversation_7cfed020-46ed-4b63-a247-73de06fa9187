from typing import List
from app.db.mongodb import db
from app.models.chunk import Chunk


class ChunkService:
    def __init__(self):
        self.collection = db["ind_chunks"]
        self._create_indexes()

    async def _create_indexes(self):
        """Create indexes for better query performance"""
        await self.collection.create_index("SourceID")
        await self.collection.create_index("ChunkID", unique=True)
        await self.collection.create_index([("TenantID", 1), ("ProjectID", 1)])

    async def create_chunks(self, chunks: List[Chunk]) -> List[Chunk]:
        """Create multiple chunks"""
        chunk_dicts = [chunk.model_dump() for chunk in chunks]
        await self.collection.insert_many(chunk_dicts)
        return chunks

    async def get_chunks_by_source(self, source_id: str) -> List[Chunk]:
        """Get all chunks for a source"""
        cursor = self.collection.find({"SourceID": source_id})
        chunks = await cursor.to_list(length=None)
        return [Chunk(**chunk) for chunk in chunks]
