import { Annotation, START, END, StateGraph } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';
import { PROMPTS } from './prompts';
import { calculateLLMInvocations, LLMInvocation, LLMOutput } from '../shared/utils';

const OutputSchema = z.object({
  response: z.array(
    z.object({
      question: z.string(),
      options: z.array(
        z.object({
          title: z.string(),
          isCorrect: z.boolean(),
        })
      ),
      answer: z.object({
        title: z.string(),
        explnation: z.string(),
        citations: z.array(
          z.object({
            id: z.string(),
            source_title: z.string(),
            source_id: z.string().nullable(),
            page_number: z.number(),
            document_page_number: z.number(),
          })
        ),
      }),
      difficulty: z.enum(['easy', 'medium', 'hard']),
    })
  ),
});

const InputStateAnnotation = Annotation.Root({
  ChapterName: Annotation<string>(),
  ChapterNumber: Annotation<number>(),
  ChapterSummary: Annotation<string>(),
  ChapterTopics: Annotation<
    {
      Topic: string;
      PageNumber: number;
      SubTopics?: string[];
    }[]
  >(),
  SourceName: Annotation<string>(),
  SourceID: Annotation<string>(),
  Chunks: Annotation<
    {
      ChunkNumber: number;
      PageNumber: number;
      DocumentPageNumber: number;
      Summary: string;
      Topic: string;
      SubTopic: string;
      Content: string;
      ChunkID: string;
    }[]
  >(),
});

const AgentStateAnnotation = Annotation.Root({
  ...InputStateAnnotation.spec,
  questions: Annotation<z.infer<(typeof OutputSchema.shape)['response']>>(),
  TokenCount: Annotation<{
    PromptTokens: number;
    CompletionTokens: number;
    TotalTokens: number;
    LLMInvocations: LLMInvocation[];
  }>({
    default: () => ({
      PromptTokens: 0,
      CompletionTokens: 0,
      TotalTokens: 0,
      LLMInvocations: [],
    }),
    reducer: (acc, curr) => ({
      PromptTokens: acc.PromptTokens + curr.PromptTokens,
      CompletionTokens: acc.CompletionTokens + curr.CompletionTokens,
      TotalTokens: acc.TotalTokens + curr.TotalTokens,
      LLMInvocations: [...acc.LLMInvocations, ...curr.LLMInvocations],
    }),
  }),
});

async function getAllChunksForChapter(
  state: typeof AgentStateAnnotation.State
): Promise<typeof AgentStateAnnotation.Update> {
  return state;
}

async function generateQuizes(state: typeof AgentStateAnnotation.State): Promise<typeof AgentStateAnnotation.Update> {
  let { TokenCount } = state;
  const model = new ChatOpenAI({
    model: 'gpt-4.1-mini',
    temperature: 0.2,
    callbacks: [
      {
        handleLLMEnd(output: LLMOutput) {
          const { PromptTokens, CompletionTokens, TotalTokens, LLMInvocations } = calculateLLMInvocations(
            output,
            'generate_quizes'
          );
          TokenCount = {
            PromptTokens,
            CompletionTokens,
            TotalTokens,
            LLMInvocations,
          };
        },
      },
    ],
  });

  const messages = [
    {
      role: 'system',
      content: PROMPTS.QUIZ_GENERATOR,
    },
    {
      role: 'user',
      content: JSON.stringify(state),
    },
  ];

  const response = await model.withStructuredOutput(OutputSchema).invoke(messages);
  return {
    questions: response.response,
    TokenCount,
  };
}

const builder = new StateGraph(AgentStateAnnotation)
  .addNode('get_all_chunks_for_chapter', getAllChunksForChapter)
  .addNode('generate_quizes', generateQuizes)
  .addEdge(START, 'get_all_chunks_for_chapter')
  .addEdge('get_all_chunks_for_chapter', 'generate_quizes')
  .addEdge('generate_quizes', END);

export const graph = builder.compile();
