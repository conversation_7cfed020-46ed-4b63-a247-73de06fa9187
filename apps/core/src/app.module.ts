import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import * as path from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ChatModule } from './chat/chat.module';
import { ContentAlbumModule } from './content-album/content-album.module';
import { AuthModule } from './core/auth/auth.module';
import { ConfigModule } from './core/config/config.module';
import { DatabaseModule } from './core/database/database.module';
import { DatabaseService } from './core/database/database.service';
import { UserModule } from './core/user/user.module';
import { EmbedModule } from './embed/embed.module';
import { LectureModule } from './lecture/lecture.module';
import { PaymentModule } from './payment/payment.module';
import { PlanNSubscriptionModule } from './plan-n-subscription/plan-n-subscription.module';
import { PopQuizModule } from './pop-quiz/pop-quiz.module';
import { ProjectModule } from './project/project.module';
import { TenantModule } from './tenant/tenant.module';
import { TestModule } from './test/test.module';
import { TokenCounterModule } from './token-counter/token-counter.module';
import { TTSModule } from './tts/tts.module';
import { CacheModule } from '@nestjs/cache-manager';
@Module({
  imports: [
    ConfigModule,
    CacheModule.register({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      inject: [DatabaseService],
      useFactory: (databaseService: DatabaseService) =>
        databaseService.createMongooseOptions({
          sslPemPath: path.join(__dirname, `../../certs/X509.pem`),
        }),
      imports: [DatabaseModule],
    }),
    TenantModule,
    ProjectModule,
    UserModule,
    AuthModule,
    PlanNSubscriptionModule,
    EmbedModule,
    ChatModule,
    TestModule,
    LectureModule,
    TTSModule,
    PopQuizModule,
    ContentAlbumModule,
    TokenCounterModule,
    PaymentModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
