import { TryCatch } from "@/lib/try-catch";
import { KY, TAckResponse, TErrorResponse } from "./_.index";
import { CapType, TPlan } from "./plan-&-tier.api";

export interface TProject {
  _id: string;
  Name: string;
  Description: string;
  TenantID: string;
  ProjectID: string;
  createdAt: string;
  updatedAt: string;
}

export interface TAudioBook {
  _id: string;
  Name: string;
  Summary: string;
  AudioConfig: {
    SpeechRate: number;
    Pitch: number;
    Gender: "MALE" | "FEMALE";
    VoiceModel: string;
    Language: string;
    SampleRateHertz: number;
    VolumeGainDecibels: number;
  };
  SpeechText: string;
  CharacterCount: number;
  Duration: number;
  Provider: "Google";
  Status: "Completed" | "Processing" | "Failed";
  Scopes: {
    ProjectID: string;
  };
  TenantID: string;
  CreditUsage: {
    Cost: number;
    Currency: string;
    Credits: number | null;
    PerCharacter: number;
  };
  createdAt: string;
  updatedAt: string;
  Event: {
    StartedAt: string;
    FinishedAt: string;
  };
  S3: {
    Location: string;
    Key: string;
    AudioFormat: "MP3" | "WAV" | "AAC";
    FileSize: number;
    Expiration: string;
    AccessUrl: string;
  };
}

export interface TAudioBookMeta
  extends Pick<
    TAudioBook,
    | "_id"
    | "Name"
    | "Summary"
    | "CharacterCount"
    | "Duration"
    | "Provider"
    | "Status"
    | "createdAt"
    | "updatedAt"
  > {}

export enum SubscriptionStatus {
  Active = "Active",
  Inactive = "Inactive",
  Cancelled = "Cancelled",
  Paused = "Paused",
  Pending = "Pending", // Queued for confirmation
}

export enum RenewalStatus {
  Paid = "Paid",
  Failed = "Failed",
  Pending = "Pending",
  AwaitingConfirmation = "AwaitingConfirmation",
}

export interface TRenewalLog {
  _id: string;
  FromDate: string;
  ToDate: string;
  OrderID: string;
  Price: number;
  Status: RenewalStatus;
  createdAt: string;
  updatedAt: string;
}

export interface Credit {
  UserID: string;
  Type: "Subscription" | "TopUp";
  CapType: CapType;
  SubscriptionID: string;
  RenewalID?: string;
  Credit: number;
  RemainingCredit: number;
  ExpiryDate: Date | null; // Matches subscription end date for renewal
  LastSyncDate: Date; // sync with token usage
}

export interface TSubscription {
  _id: string;
  UserID: string;
  TenantID: string;
  Price: number;
  Status: SubscriptionStatus;
  PlanID: string;
  AutoRenew: boolean;
  RenewalLogs: TRenewalLog[];
  Plan: TPlan;
  StartDate: string;
  EndDate: string;
  createdAt: string;
  updatedAt: string;
  Variant: string;
  VariantDuration?: number;
  Credits: Credit[];
}

export interface TOrder {
  OrderID: string;
  Amount: number;
  Currency: string;
  Status: string;
}

const GetMyProjects = async () => {
  return await TryCatch<TProject[], TErrorResponse>(
    KY.get("subscription/me/projects").json()
  );
};

const GetMySubscriptions = async () => {
  return await TryCatch<TSubscription[], TErrorResponse>(
    KY.get("subscription/me/subs").json()
  );
};

const GetAudioBooks = async (ProjectID: string) => {
  return await TryCatch<TAudioBookMeta[], TErrorResponse>(
    KY.get(
      `subscription/project/${ProjectID}/audio-books?Status=Completed`
    ).json()
  );
};

const GetAudioBook = async (AudioBookID: string, ProjectID: string) => {
  return await TryCatch<TAudioBook, TErrorResponse>(
    KY.get(`subscription/project/${ProjectID}/audio-book/${AudioBookID}`).json()
  );
};

const PayForSubscription = async (SubscriptionID: string) => {
  return await TryCatch<TAckResponse<TOrder>, TErrorResponse>(
    KY.post(`subscription/payment/${SubscriptionID}`).json()
  );
};

const UpgradeSubscription = async (Params: {
  SubscriptionID: string;
  Variant: string;
  ForceCancel: boolean;
  PlanID: string;
}) => {
  return await TryCatch<TAckResponse<TOrder>, TErrorResponse>(
    KY.put(`subscription/upgrade`, {
      json: Params,
    }).json()
  );
};

const VerifyPayment = async (Params: {
  OrderID: string;
  PaymentID: string;
  Signature: string;
}) => {
  return await TryCatch<TAckResponse<TOrder>, TErrorResponse>(
    KY.post(`payment/order/verify`, { json: Params }).json()
  );
};

export const SUBSCRIPTION = {
  GetMyProjects,
  GetMySubscriptions,
  GetAudioBooks,
  GetAudioBook,
  PayForSubscription,
  UpgradeSubscription,
  VerifyPayment,
};
