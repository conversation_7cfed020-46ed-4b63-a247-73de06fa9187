/* eslint-disable @next/next/no-img-element */
"use client";
import { API_BASE_URL } from "@/apis/_.index";
import { API } from "@/apis/api";
import { TTenant } from "@/apis/tenant.api";
import { ThemeToggle } from "@/components/theme-toggle";
import Orb from "@/components/ui/orb";
import { useAuth } from "@/contexts/auth-context";
import { useIsMobile } from "@/hooks/use-is-mobile";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@workspace/ui/components/alert";
import { AuroraText } from "@workspace/ui/components/aurora-text";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Marquee } from "@workspace/ui/components/marquee";
import { cn } from "@workspace/ui/lib/utils";
import { Eye, EyeOff, LogIn, Terminal, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { ReactNode, Suspense, useEffect, useState } from "react";
import { toast } from "sonner";

const SignIn = () => {
  // get #[domain] from url
  const [tenant, setTenant] = useState<TTenant | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const ssoError = useSearchParams().get("error");
  const token = useSearchParams().get("token");

  const getTenant = async (subdomain: string) => {
    const { data, errors } = await API.TENANT.GetTenantByDomain(subdomain);
    if (data) {
      setTenant(data);
    }
    if (errors) {
      console.log(errors);
    }
  };

  useEffect(() => {
    const domain = window.location.href.split("#")[1];
    if (!domain) return;
    getTenant(domain);
  }, []);
  const isMobile = useIsMobile();

  const images = tenant?.Pictures?.length
    ? tenant?.Pictures
    : [
        "https://6512psv07j.ufs.sh/f/etAgn58tC72WVYHZ78EIKl5BN3twmjURQXgrJCGiDf68MFod",
        "https://6512psv07j.ufs.sh/f/etAgn58tC72Wu3ViWRZDyrqU1g0KHdCTiczGOAnlMWeShjR7",
        "https://6512psv07j.ufs.sh/f/etAgn58tC72WMxEbkDA40SWRJBZ9ODz6klTxwFHsgKGadc78",
      ];

  const PlatformIcons = {
    LinkedIn: (h: number, w: number) => (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="#0077B5"
        width={w}
        height={h}
      >
        <path d="M20 2H4C2.89 2 2 2.9 2 4v16c0 1.1.89 2 2 2h16c1.11 0 2-.9 2-2V4c0-1.1-.89-2-2-2zM8.27 18H5.56V9h2.71v9zm-1.34-10.21a1.55 1.55 0 11-.01-3.1 1.55 1.55 0 01.01 3.1zm11.58 10.21h-2.71v-4.29c0-1.08-.02-2.47-1.51-2.47s-1.74 1.18-1.74 2.4V18h-2.71V9h2.61v1.23h.04c.36-.68 1.23-1.4 2.52-1.4 2.7 0 3.2 1.77 3.2 4.06V18z M9.75 15.02V8.98l5.5 3.02-5.5 3.02z" />
      </svg>
    ),
    Twitter: (h: number, w: number) => <X className="h-8 w-8" size={w} />,
    Instagram: (h: number, w: number) => (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="#E4405F"
        width={w}
        height={h}
      >
        <path d="M7.75 2h8.5A5.75 5.75 0 0122 7.75v8.5A5.75 5.75 0 0116.25 22h-8.5A5.75 5.75 0 012 16.25v-8.5A5.75 5.75 0 017.75 2zm8.5 2h-8.5A3.75 3.75 0 004 7.75v8.5A3.75 3.75 0 007.75 20h8.5A3.75 3.75 0 0020 16.25v-8.5A3.75 3.75 0 0016.25 4zm-4.25 5.25a3.75 3.75 0 110 7.5 3.75 3.75 0 010-7.5zm0 2a1.75 1.75 0 100 3.5 1.75 1.75 0 000-3.5zm4.5-3.5a1 1 0 110 2 1 1 0 010-2z" />
      </svg>
    ),
    YouTube: (h: number, w: number) => (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="#FF0000"
        width={w}
        height={h}
      >
        <path d="M21.8 8.01s-.2-1.42-.8-2.04a3.36 3.36 0 00-2.42-.83C16.92 5 12 5 12 5s-4.92 0-6.56.14a3.36 3.36 0 00-2.42.83C2.4 6.59 2.2 8.01 2.2 8.01S2 9.86 2 11.7v.6c0 1.84.2 3.69.2 3.69s.2 1.42.8 2.04a3.36 3.36 0 002.42.83c1.64.14 6.56.14 6.56.14s4.92 0 6.56-.14a3.36 3.36 0 002.42-.83c.6-.62.8-2.04.8-2.04s.2-1.84.2-3.69v-.6c0-1.84-.2-3.69-.2-3.69zM9.75 15.02V8.98l5.5 3.02-5.5 3.02z" />
      </svg>
    ),
    Discord: (h: number, w: number) => (
      <svg
        width={w}
        height={h}
        viewBox="0 -28.5 256 256"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="xMidYMid"
      >
        <g>
          <path
            d="M216.856339,16.5966031 C200.285002,8.84328665 182.566144,3.2084988 164.041564,0 C161.766523,4.11318106 159.108624,9.64549908 157.276099,14.0464379 C137.583995,11.0849896 118.072967,11.0849896 98.7430163,14.0464379 C96.9108417,9.64549908 94.1925838,4.11318106 91.8971895,0 C73.3526068,3.2084988 55.6133949,8.86399117 39.0420583,16.6376612 C5.61752293,67.146514 -3.4433191,116.400813 1.08711069,164.955721 C23.2560196,181.510915 44.7403634,191.567697 65.8621325,198.148576 C71.0772151,190.971126 75.7283628,183.341335 79.7352139,175.300261 C72.104019,172.400575 64.7949724,168.822202 57.8887866,164.667963 C59.7209612,163.310589 61.5131304,161.891452 63.2445898,160.431257 C105.36741,180.133187 151.134928,180.133187 192.754523,160.431257 C194.506336,161.891452 196.298154,163.310589 198.110326,164.667963 C191.183787,168.842556 183.854737,172.420929 176.223542,175.320965 C180.230393,183.341335 184.861538,190.991831 190.096624,198.16893 C211.238746,191.588051 232.743023,181.531619 254.911949,164.955721 C260.227747,108.668201 245.831087,59.8662432 216.856339,16.5966031 Z M85.4738752,135.09489 C72.8290281,135.09489 62.4592217,123.290155 62.4592217,108.914901 C62.4592217,94.5396472 72.607595,82.7145587 85.4738752,82.7145587 C98.3405064,82.7145587 108.709962,94.5189427 108.488529,108.914901 C108.508531,123.290155 98.3405064,135.09489 85.4738752,135.09489 Z M170.525237,135.09489 C157.88039,135.09489 147.510584,123.290155 147.510584,108.914901 C147.510584,94.5396472 157.658606,82.7145587 170.525237,82.7145587 C183.391518,82.7145587 193.761324,94.5189427 193.539891,108.914901 C193.539891,123.290155 183.391518,135.09489 170.525237,135.09489 Z"
            fill="#5865F2"
            fill-rule="nonzero"
          ></path>
        </g>
      </svg>
    ),
    Facebook: (h: number, w: number) => (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="#1877F2"
        width={w}
        height={h}
      >
        <path d="M22 12.07C22 6.48 17.52 2 12 2S2 6.48 2 12.07c0 5 3.66 9.13 8.44 9.88v-6.98h-2.54v-2.9h2.54v-2.2c0-2.51 1.49-3.9 3.77-3.9 1.09 0 2.23.2 2.23.2v2.43h-1.26c-1.24 0-1.63.77-1.63 1.56v1.9h2.78l-.44 2.9h-2.34v6.98c4.78-.75 8.44-4.88 8.44-9.88z" />
      </svg>
    ),
    Other: (h: number, w: number) => <></>,
  };

  function LoginForm({ className, ...props }: React.ComponentProps<"div">) {
    const router = useRouter();
    const [error, setError] = useState<string | null>(null);
    const { isAuthenticated, refetch: refetchAuth } = useAuth();
    const [showPassword, setShowPassword] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      setIsLoading(true);
      setError(null);

      const Email = (e.target as HTMLFormElement).email.value;
      const Password = (e.target as HTMLFormElement).password.value;

      try {
        const { data, errors } = await API.AUTH.SignIn({
          Email,
          Password,
        });

        if (data) {
          // set cookie
          document.cookie = `_session=${(data.ack as unknown as { token: string }).token}; path=/; expires=${new Date(Date.now() + 86400000).toUTCString()}`;
          toast.success(data.message || "Login successful");
          refetchAuth();
          localStorage.setItem("user", JSON.stringify(data.ack));
          router.push("/");
        }

        if (errors) {
          const errorMessage = errors[0]?.message || "Authentication failed";
          setError(errorMessage);
          toast.error(errorMessage);
        }
      } catch (err) {
        console.error("Login error:", err);
        const errorMessage = "An unexpected error occurred. Please try again.";
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    const ssoLogin = async () => {
      // set cookie
      document.cookie = `_session=${token}; path=/; expires=${new Date(Date.now() + 86400000).toUTCString()}`;
      toast.success("Login successful");
      localStorage.setItem("user", JSON.stringify({}));
      router.push("/");
    };

    useEffect(() => {
      if (isAuthenticated) {
        router.push("/");
      }
    }, [isAuthenticated, router]);

    useEffect(() => {
      if (token) {
        ssoLogin();
      }
    }, [token]);

    return (
      <div className="mx-auto w-full flex flex-col space-y-4 max-w-lg">
        <div className="w-full">
          <div
            className={cn(
              "flex gap-2 flex-col items-center",
              tenant?.Personalization?.HeaderPlacing
            )}
          >
            <div>
              {tenant?.Personalization?.ShowTenantLogo &&
              tenant?.Logo?.length ? (
                <img
                  src={tenant?.Logo}
                  alt={tenant?.Name}
                  className="object-contain h-full max-w-[220px] max-h-[100px]"
                />
              ) : (
                <div className="w-20 h-20 justify-center items-center flex">
                  <Orb
                    hoverIntensity={0}
                    rotateOnHover={false}
                    hue={20}
                    forceHoverState={true}
                  />
                </div>
              )}
            </div>
            {tenant?.Personalization?.ShowTenantName && (
              <AuroraText className="text-2xl font-bold">
                {tenant?.Name}
              </AuroraText>
            )}
            {!tenant && (
              <AuroraText className="text-2xl font-bold">
                Zuma Industries
              </AuroraText>
            )}
            {tenant?.Personalization?.ShowWebsiteURL && (
              <Badge
                variant="outline"
                className="cursor-pointer mt-2 "
                onClick={() => window.open(tenant?.WebsiteURL, "_blank")}
              >
                {new URL(tenant?.WebsiteURL || "").hostname}
              </Badge>
            )}
            {tenant?.Personalization?.ShowSocialCards && (
              <div className="flex gap-2 items-center">
                {tenant?.SocialCards?.map((card, i) => (
                  <Link href={card?.URL} key={i}>
                    {
                      PlatformIcons?.[
                        card?.Platform as keyof typeof PlatformIcons
                      ]?.(28, 28) as ReactNode
                    }
                  </Link>
                ))}
              </div>
            )}
          </div>
          {ssoError && (
            <Alert variant="destructive" className="mt-6">
              <Terminal />
              <AlertTitle>Login Failed</AlertTitle>
              <AlertDescription>{ssoError}</AlertDescription>
            </Alert>
          )}
          <div className="flex flex-col gap-1 mt-10">
            <h1 className="text-xl md:text-2xl font-bold tracking-tight text-foreground">
              Login
            </h1>
            <p className="text-sm">
              {tenant?.Personalization?.ShowTagline && tenant?.Tagline}
            </p>
          </div>
        </div>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => {
            const redirectUrl = window.location.href.split("?")[0];
            window.location.href = `${API_BASE_URL}/auth/google?RedirectURL=${redirectUrl}`;
          }}
          size="lg"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path
              d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
              fill="currentColor"
            />
          </svg>
          Login with Google
        </Button>
        <span className="text-md text-muted-foreground mx-auto">OR</span>
        <form className="max-w-full w-full space-y-6" onSubmit={handleSubmit}>
          <div className="flex flex-col gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
                disabled={isLoading}
              />
            </div>
            <div className="relative space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                required
                disabled={isLoading}
                className="pr-10"
                placeholder="Enter your password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-2 top-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                tabIndex={-1}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>

          <Button type="submit" className="w-full h-11 text-base">
            <LogIn className="mr-2 h-4 w-4" />
            {isLoading ? (
              <>
                <span className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-t-2 border-current"></span>
                Logging in...
              </>
            ) : (
              "Login"
            )}
          </Button>
        </form>
      </div>
    );
  }

  if (isLoading) return null;

  return (
    <div className="flex min-h-screen flex-col w-full">
      <div className="absolute top-4 right-4 z-50 bg-background/70 backdrop-blur-md p-1 rounded-full">
        <ThemeToggle />
      </div>
      <main className="flex relative items-center h-screen justify-center">
        <div
          className={`absolute top-0 left-0 w-full h-screen flex justify-center overflow-hidden`}
        >
          <div
            className="flex flex-row items-center gap-4"
            style={{
              transform:
                "translateX(-50px) translateY(0px) translateZ(-100px) rotateX(20deg) rotateY(-10deg) rotateZ(20deg)",
            }}
          >
            <Marquee vertical className="[--duration:60s]">
              {images.reverse().map((image) => (
                <figure
                  key={image}
                  className="rounded-lg overflow-hidden max-h-[400px]"
                >
                  <Image
                    src={image}
                    alt=""
                    className="w-full h-full object-cover"
                    unoptimized
                    width={100}
                    height={100}
                  />
                </figure>
              ))}
            </Marquee>
            <Marquee vertical className="[--duration:100s]">
              {images.map((image) => (
                <figure
                  key={image}
                  className="rounded-lg overflow-hidden max-h-[400px]"
                >
                  <Image
                    src={image}
                    alt=""
                    className="w-full h-full object-cover"
                    width={100}
                    height={100}
                    unoptimized
                  />
                </figure>
              ))}
            </Marquee>
          </div>
          <div className="pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r from-zinc-500/20 dark:from-zinc-800/60"></div>
          <div className="pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l from-zinc-500/20 dark:from-zinc-800"></div>
        </div>
        <div className="max-w-xl bg-background/90 dark:bg-background/70 backdrop-blur-lg w-full p-6 sm:p-8 space-y-6 shadow-xl sm:rounded-lg flex-1 flex flex-col justify-center">
          <LoginForm />
        </div>
      </main>
    </div>
  );
};

export default function SignInPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
        </div>
      }
    >
      <SignIn />
    </Suspense>
  );
}
