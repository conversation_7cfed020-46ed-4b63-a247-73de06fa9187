import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common';
import { LectureService } from './lecture.service';
import { CreateLectureDto } from './dto/create-lecture.dto';
import { UpdateLectureDto } from './dto/update-lecture.dto';
import { AuthAdminGuard } from '../../common/guards/auth.guard';
import { TUser } from '../core/user/entities/user.entity';
import { User } from '../../common/decorators/core.decorators';

@UseGuards(AuthAdminGuard)
@Controller('lecture')
export class LectureController {
  constructor(private readonly lectureService: LectureService) {}

  @Post('generate')
  create(@Body() createLectureDto: CreateLectureDto, @User() User: TUser) {
    return this.lectureService.create(createLectureDto, User.UserID, User.AppType);
  }

  @Get()
  findAll(@Query() query: { TenantID: string; ProjectID: string; SourceID: string; ChapterID?: string }) {
    return this.lectureService.findAll(query.TenantID, query.ProjectID, query.SourceID, query.ChapterID);
  }

  @Get(':LectureID')
  findOne(@Param('LectureID') LectureID: string) {
    return this.lectureService.findOne(LectureID);
  }

  @Patch(':LectureID')
  update(@Param('LectureID') LectureID: string, @Body() updateLectureDto: UpdateLectureDto) {
    return this.lectureService.update(LectureID, updateLectureDto);
  }

  @Delete(':LectureID')
  remove(@Param('LectureID') LectureID: string) {
    return this.lectureService.remove(LectureID);
  }

  @Post('tts/:LectureID')
  generateTTS(
    @Param('LectureID') LectureID: string,
    @Body() body: { TenantID: string; ProjectID: string; SourceID: string }
  ) {
    return this.lectureService.generateTTS(LectureID, body.TenantID, body.ProjectID, body.SourceID);
  }
}
