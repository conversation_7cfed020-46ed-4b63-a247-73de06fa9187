import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { ElevenLabsClient } from 'elevenlabs';
import { z } from 'zod';
import TTSBlueprint, {
  IGenerateCostEstimationParams,
  IListVoicesResponse,
  ISupportedVoicesResponse,
  ISynthesizeSpeechParams,
} from './_.blueprint';
import { CostEstimationType, ISynthesizeSpeechResponse } from './google.tts';

const SupportVoices: ISupportedVoicesResponse[] = [
  {
    Name: 'Daniel',
    LanguageCode: 'en-US',
    Accent: 'British',
    Gender: 'Male',
    VoiceModel: 'onwK4e9ZLuTAKqWW03F9',
    PricePerCharacter: null, // 16usd per 1m characters
  },
  {
    Name: 'Shanaya',
    LanguageCode: 'en-IN',
    Accent: 'Indian',
    Gender: 'Female',
    VoiceModel: 'tzoR7arDwmW2nN2tuFJy',
    PricePerCharacter: null, // 16usd per 1m characters
  },
];

const FoundationModel = z.enum(['eleven_multilingual_v2', 'eleven_flash_v2_5']);
export type TFoundationModel = z.infer<typeof FoundationModel>;
export default class ElevenLabsTTSAdapter
  implements
    TTSBlueprint<
      IListVoicesResponse[],
      object,
      ISynthesizeSpeechResponse,
      ISynthesizeSpeechParams,
      CostEstimationType,
      Omit<IGenerateCostEstimationParams, 'Voice'>
    >
{
  private Client: ElevenLabsClient;
  private readonly MAXIMUM_CHARACTER_LENGTH = 3000;

  /**
   * Pricing as Per Creator Tier (APR 2025) : https://elevenlabs.io/pricing#pricing-table
   */
  private FoundationModelConstraint = {
    eleven_multilingual_v2: {
      CharacterLimit: 30000, // 10k
      PerCharacter: 0.0003, // 0.30 usd per 1k characters
    },
    eleven_flash_v2_5: {
      CharacterLimit: 400000, // 40K
      PerCharacter: 0.00015, // 0.15 usd per 1k characters
    },
  };
  constructor(private readonly ConfigService: ConfigService) {
    this.Client = new ElevenLabsClient({
      apiKey: this.ConfigService.get('ELEVEN_LABS'),
    });
  }

  async ListVoices(params: unknown) {
    return SupportVoices;
  }

  ChunkTextIntoParagraphs(Text: string, FoundationModel: TFoundationModel) {
    // logical chunking
    const Chunks: string[] = [];
    let CurrentChunk = '';
    const Paragraphs = Text.split('\n\n');
    for (const Paragraph of Paragraphs) {
      if (CurrentChunk.length + Paragraph.length > this.FoundationModelConstraint[FoundationModel].CharacterLimit) {
        Chunks.push(CurrentChunk);
        CurrentChunk = Paragraph;
      } else {
        CurrentChunk += Paragraph;
      }
    }
    if (CurrentChunk.trim().length > 0) {
      Chunks.push(CurrentChunk);
    }
    return Chunks;
  }

  async SpeechToAudio(
    Params: {
      Text: string;
      VoiceID: string;
      PreviousText?: string;
      NextText?: string;
      FoundationModel?: TFoundationModel;
    } & Omit<ISynthesizeSpeechParams, 'Voice' | 'LanguageCode' | 'Encoding'>
  ) {
    const { Text, VoiceID, PreviousText, NextText, SpeechRate, VolumeGainDb, Pitch } = Params;
    const response = await axios.post(
      `https://api.elevenlabs.io/v1/text-to-speech/${VoiceID}/stream`,
      {
        text: Text,
        model_id: Params.FoundationModel ?? FoundationModel.Enum.eleven_flash_v2_5,
        previous_text: PreviousText ?? null,
        next_text: NextText ?? null,
        voice_settings: {
          stability: 0.5, // Optional: controls variation
          similarity_boost: 0.75, // Optional: controls voice consistency
          style: 0.5, // Optional: used with "Eleven Monolingual v1"
          use_speaker_boost: true, // Optional: improves output quality
          speed: SpeechRate, // NEW: 1.0 is normal, <1 is slower, >1 is faster
          pitch: Pitch, // NEW: -1.0 (lower) to +1.0 (higher)
        },
      },
      {
        headers: {
          'xi-api-key': this.ConfigService.get('ELEVEN_LABS'),
        },
        responseType: 'arraybuffer',
      }
    );

    return Buffer.from(response.data);
  }

  async SynthesizeSpeech(params: ISynthesizeSpeechParams & { FoundationModel?: TFoundationModel }) {
    console.log(params);
    const EnableAudioStitching = params.Text.length > this.MAXIMUM_CHARACTER_LENGTH;
    const Chunks = EnableAudioStitching
      ? this.ChunkTextIntoParagraphs(params.Text, params.FoundationModel ?? FoundationModel.Enum.eleven_flash_v2_5)
      : [params.Text];

    console.log(`🔊 [ElevenLabsTTS] Chunk size: ${Chunks.length}`);
    const AudioBuffers: Buffer[] = [];
    for (const Chunk of Chunks) {
      const AudioBuffer = await this.SpeechToAudio({
        Text: Chunk,
        VoiceID: params.Voice,
        PreviousText: Chunks[Chunks.indexOf(Chunk) - 1],
        NextText: Chunks[Chunks.indexOf(Chunk) + 1],
        SpeechRate: params.SpeechRate,
        VolumeGainDb: params.VolumeGainDb,
        Pitch: params.Pitch,
        FoundationModel: params.FoundationModel,
      });
      AudioBuffers.push(AudioBuffer);
    }
    const AudioContent = Buffer.concat(AudioBuffers);
    return {
      AudioContent,
      Cost: await this.GenerateCostEstimation({
        Text: params.Text,
        FoundationModel: params.FoundationModel,
      }),
    };
  }

  async GenerateCostEstimation(
    params: Omit<IGenerateCostEstimationParams, 'Voice' | 'ModelName'> & { FoundationModel?: TFoundationModel }
  ): Promise<CostEstimationType> {
    const CharacterCount = params.Text.length;
    const Price =
      this.FoundationModelConstraint[params.FoundationModel ?? FoundationModel.Enum.eleven_flash_v2_5].PerCharacter *
      CharacterCount;
    console.log(`🔊 [ElevenLabsTTS] Generated cost estimation: ${Price} for ${CharacterCount} characters`);
    return {
      Cost: Price,
      Currency: 'USD',
      PerCharacter: this.FoundationModelConstraint[params.FoundationModel].PerCharacter,
    };
  }
}
