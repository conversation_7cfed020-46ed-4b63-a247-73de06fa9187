import { IsEnum, IsOptional, MinLength } from 'class-validator';
import { TUser, TUserType, AppTypeEnum } from '../entities/user.entity';

export class CreateUserDto extends TUser {
  @MinLength(6, {
    message: 'Password must be at least 6 characters long',
  })
  Password: string;

  @IsEnum(['Admin', 'Subscriber', 'Employee'], {
    message: 'UserType must be one of Admin or Subscriber or Employee',
  })
  UserType: TUserType;

  @IsOptional()
  @IsEnum([AppTypeEnum.Industry, AppTypeEnum.Academic], {
    message: 'AppType must be one of Industry or Academic',
  })
  AppType?: AppTypeEnum;

  @IsOptional()
  OAuth?: {
    Google: {
      Connected: boolean;
      UserID: string;
    };
  };
}
