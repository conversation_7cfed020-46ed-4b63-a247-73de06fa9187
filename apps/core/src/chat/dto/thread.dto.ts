import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateThreadDto {
  @IsNotEmpty()
  @IsString()
  ProjectID: string;

  @IsOptional()
  @IsString()
  title?: string;
}

export class ThreadDto {
  ThreadID: string;
  ProjectID: string;
  title: string;
  userId: string;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
}

export class ThreadListDto {
  threads: ThreadDto[];
  count: number;
}
