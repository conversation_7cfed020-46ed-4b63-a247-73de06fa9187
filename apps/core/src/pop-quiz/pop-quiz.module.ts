import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PopQuiz<PERSON>ontroller, QuestionController, ResponseController } from './pop-quiz.controller';
import { PopQuizService } from './pop-quiz.service';
import { PopQuizModel, PopQuizSchema } from './schemas/quiz.schema';
import { PopQuizQuestionModel, PopQuizQuestionSchema } from './schemas/question.schema';
import { PopQuizResponseModel, PopQuizResponseSchema } from './schemas/response.schema';
import { SourceModel, SourceSchema } from '../embed/schemas/source.schema';
import { Chunk, ChunkSchema } from '../embed/schemas/chunk.schema';
import { QuestionService } from './qustion.service';
import { ResponseService } from './response.service';
import { PlanNSubscriptionModule } from '../plan-n-subscription/plan-n-subscription.module';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ProjectModule } from '../project/project.module';
import { TokenCounterModule } from '../token-counter/token-counter.module';
import { IndChunkModel, IndChunkSchema } from '../embed/schemas/ind-chunk.schema';
import { IndSourceModel, IndSourceSchema } from '../embed/schemas/ind-source.schema';

@Module({
  imports: [
    EventEmitter2,
    PlanNSubscriptionModule,
    ProjectModule,
    TokenCounterModule,
    MongooseModule.forFeature([
      {
        name: PopQuizModel.name,
        schema: PopQuizSchema,
      },
      {
        name: PopQuizResponseModel.name,
        schema: PopQuizResponseSchema,
      },
      {
        name: PopQuizQuestionModel.name,
        schema: PopQuizQuestionSchema,
      },
      {
        name: SourceModel.name,
        schema: SourceSchema,
      },
      {
        name: Chunk.name,
        schema: ChunkSchema,
      },
      {
        name: IndChunkModel.name,
        schema: IndChunkSchema,
      },
      {
        name: IndSourceModel.name,
        schema: IndSourceSchema,
      },
    ]),
  ],
  controllers: [PopQuizController, QuestionController, ResponseController],
  providers: [PopQuizService, QuestionService, ResponseService],
  exports: [PopQuizService, QuestionService, ResponseService],
})
export class PopQuizModule {}
