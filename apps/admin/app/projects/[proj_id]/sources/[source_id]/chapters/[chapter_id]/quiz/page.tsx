"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Link from "next/link";
import { TProjectDetails, TSource } from "@/types/project";
import { API } from "@/apis/api";
import {
  ChevronLeft,
  AlertCircle,
  FileText,
  Loader2,
  PlusCircle,
  Check,
  ChevronDown,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@workspace/ui/components/breadcrumb";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Button } from "@workspace/ui/components/button";
import { toast } from "sonner";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@workspace/ui/components/accordion";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  QUIZ,
  TQuestion,
  TPopQuizListResponse,
  TGenerateQuestionsResponse,
  TPopQuiz,
  TQuizResponse,
} from "@/apis/quiz.api";

export default function ChapterQuizPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.proj_id as string;
  const sourceId = params.source_id as string;
  const chapterId = params.chapter_id as string;

  // Add this style tag to ensure our styles take precedence
  const styles = {
    questionNumber: {
      width: "24px",
      height: "24px",
      backgroundColor: "#000",
      color: "white",
      borderRadius: "50%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      fontSize: "0.875rem",
      flexShrink: 0,
      marginRight: "0.5rem",
      marginTop: "2px",
    },
    questionGap: {
      gap: "0.75rem",
    },
    optionCorrect: {
      borderColor: "#10b981",
      backgroundColor: "#f0fdf4",
    },
    optionIncorrect: {
      borderColor: "#e5e7eb",
      backgroundColor: "transparent",
    },
  };

  // Format citations by replacing [ChunkID] with actual source and page
  const formatCitations = (text: string, citations: any[] = []) => {
    if (!text || !citations || !citations.length) return text;

    let formattedText = text;

    // Create a map of citation IDs to their details for efficient lookup
    const citationMap = new Map();
    citations.forEach((citation) => {
      if (citation.ID) {
        citationMap.set(citation.ID, citation);
      }
    });

    // Replace each [ChunkID] with source and page info
    const regex = /\[([\w-]+)\]/g;
    formattedText = formattedText.replace(regex, (match, chunkId) => {
      const citation = citationMap.get(chunkId);
      if (citation) {
        return `[${citation.SourceTitle}, Page ${citation.PageNumber}]`;
      }
      return match; // Keep original if citation not found
    });

    return formattedText;
  };

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [project, setProject] = useState<TProjectDetails | null>(null);
  const [source, setSource] = useState<TSource | null>(null);
  const [chapter, setChapter] = useState<any | null>(null);
  const [questions, setQuestions] = useState<TQuestion[]>([]);
  const [quizzes, setQuizzes] = useState<TPopQuizListResponse[]>([]);
  const [quizResponses, setQuizResponses] = useState<
    Record<string, TQuizResponse[]>
  >({});
  const [activeQuizId, setActiveQuizId] = useState<string | null>(null);
  const [loadingQuestions, setLoadingQuestions] = useState(false);
  const [loadingQuizzes, setLoadingQuizzes] = useState(false);
  const [loadingResponses, setLoadingResponses] = useState(false);
  const [generatingQuestions, setGeneratingQuestions] = useState(false);
  const [showCreateQuizDialog, setShowCreateQuizDialog] = useState(false);
  const [newQuiz, setNewQuiz] = useState({
    title: "",
    description: "",
    questionCount: 5,
  });
  const [creatingQuiz, setCreatingQuiz] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const projectResponse = await API.PROJECT.GetProjectDetails({
          ProjectID: projectId,
        });

        if (projectResponse.data) {
          setProject(projectResponse.data);
          const foundSource = projectResponse.data.Sources?.find(
            (s) => s.SourceID === sourceId
          );

          if (foundSource) {
            setSource(foundSource);
            const foundChapter = foundSource.Chapters?.find(
              (c) => c.ChapterID === chapterId
            );

            if (foundChapter) {
              setChapter(foundChapter);

              // Only fetch questions and quizzes after we have the source data
              await Promise.all([
                fetchQuestionsWithSource(foundSource),
                fetchQuizzesWithSource(foundSource),
              ]);
            } else {
              setError("Chapter not found");
            }
          } else {
            setError("Source not found");
          }
        }
      } catch (error) {
        console.error("Failed to fetch data:", error);
        setError("An error occurred while fetching data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId, sourceId, chapterId]);

  // Separate function to fetch questions with source data provided directly
  const fetchQuestionsWithSource = async (sourceData: TSource) => {
    setLoadingQuestions(true);
    try {
      console.log("Fetching questions for chapter:", chapterId);
      const response = await QUIZ.ListQuestions({
        ProjectID: projectId,
        SourceIDs: [sourceId],
        ChapterID: chapterId,
      });

      console.log("Question response in component:", response);

      // Handle multiple possible response structures
      if (response && response.data) {
        let questionsData: TQuestion[] = [];

        if (Array.isArray(response.data)) {
          questionsData = response.data;
        } else if (response.data.ack && Array.isArray(response.data.ack)) {
          questionsData = response.data.ack;
        }

        console.log("Final questions data:", questionsData);
        setQuestions(questionsData);
      } else {
        console.log("No questions data found in response");
        setQuestions([]);
      }
    } catch (error) {
      console.error("Failed to fetch questions:", error);
      toast.error("Failed to fetch questions");
      setQuestions([]);
    } finally {
      setLoadingQuestions(false);
    }
  };

  // Use this wrapper for the normal fetch questions function
  const fetchQuestions = async () => {
    if (!source) return;
    await fetchQuestionsWithSource(source);
  };

  // Separate function to fetch quizzes with source data provided directly
  const fetchQuizzesWithSource = async (sourceData: TSource) => {
    setLoadingQuizzes(true);
    try {
      console.log(
        "Fetching quizzes for project:",
        projectId,
        "tenant:",
        sourceData.TenantID
      );
      const response = await QUIZ.ListQuizes({
        ProjectID: projectId,
        TenantID: sourceData.TenantID,
      });

      console.log("Raw quizzes response in component:", response);

      // Handle multiple possible response structures
      if (response && response.data) {
        let quizzesData: any[] = [];

        if (Array.isArray(response.data)) {
          console.log(
            "Response.data is an array with",
            response.data.length,
            "items"
          );
          quizzesData = response.data;
        } else if (response.data.ack && Array.isArray(response.data.ack)) {
          console.log(
            "Response.data.ack is an array with",
            response.data.ack.length,
            "items"
          );
          quizzesData = response.data.ack;
        } else {
          console.log(
            "Response data structure:",
            JSON.stringify(response.data, null, 2)
          );
          // Try to extract any array from the response
          for (const key in response.data) {
            if (Array.isArray((response.data as any)[key])) {
              console.log(
                "Found array in response.data." + key,
                (response.data as any)[key].length,
                "items"
              );
              quizzesData = (response.data as any)[key];
              break;
            }
          }
        }

        console.log(
          "Quizzes data before filtering:",
          JSON.stringify(quizzesData.slice(0, 1), null, 2)
        );

        // Filter quizzes to only include those related to the current chapter
        const filteredQuizzes = quizzesData.filter((quiz) => {
          // Check if Scopes directly contains the ChapterID
          if (quiz.Scopes && quiz.Scopes.ChapterID === chapterId) {
            return true;
          }

          // Check in QuestionDetails if it exists
          if (
            quiz.QuestionDetails &&
            Array.isArray(quiz.QuestionDetails) &&
            quiz.QuestionDetails.length > 0
          ) {
            const hasMatchingQuestion = quiz.QuestionDetails.some(
              (q: any) => q?.Scopes?.ChapterID === chapterId
            );
            if (hasMatchingQuestion) {
              return true;
            }
          }

          // Check in Questions array if they're objects with Scopes
          if (
            quiz.Questions &&
            Array.isArray(quiz.Questions) &&
            quiz.Questions.length > 0 &&
            typeof quiz.Questions[0] === "object"
          ) {
            const hasMatchingQuestion = quiz.Questions.some(
              (q: any) => q?.Scopes?.ChapterID === chapterId
            );
            if (hasMatchingQuestion) {
              return true;
            }
          }

          return false;
        });

        console.log(
          `Filtered to ${filteredQuizzes.length} quizzes for chapter ${chapterId}`
        );
        setQuizzes(filteredQuizzes);
      } else {
        console.log("No quizzes data found in response");
        setQuizzes([]);
      }
    } catch (error) {
      console.error("Failed to fetch quizzes:", error);
      toast.error("Failed to fetch quizzes");
      setQuizzes([]);
    } finally {
      setLoadingQuizzes(false);
    }
  };

  // Use this wrapper for the normal fetch quizzes function
  const fetchQuizzes = async () => {
    if (!source) return;
    await fetchQuizzesWithSource(source);
  };

  const handleGenerateQuestions = async () => {
    if (!source) return;

    setGeneratingQuestions(true);
    try {
      toast.info("Generating questions... This may take a minute or more.");
      const response = await QUIZ.GenerateQuestions({
        ProjectID: projectId,
        SourceID: sourceId,
        ID: chapterId,
        NumberOfQuestions: 20,
      });

      console.log("Generate questions response in component:", response);

      // Consider any non-error response as successful
      if (response && response.data) {
        toast.success("Questions generated successfully");
        // Wait a moment to give the backend time to process before fetching
        setTimeout(async () => {
          await fetchQuestionsWithSource(source);
        }, 1000);
      } else if (response && response.errors) {
        toast.error(`Error: ${response.errors[0]?.message || "Unknown error"}`);
      }
    } catch (error) {
      console.error("Failed to generate questions:", error);
      toast.error("Failed to generate questions");
    } finally {
      setGeneratingQuestions(false);
    }
  };

  const handleCreateQuiz = async () => {
    if (!source || !questions.length) return;

    if (!newQuiz.title.trim()) {
      toast.error("Title is required");
      return;
    }

    const maxQuestionCount = questions.length;
    if (newQuiz.questionCount > maxQuestionCount) {
      toast.error(`Question count cannot be more than ${maxQuestionCount}`);
      return;
    }

    setCreatingQuiz(true);
    try {
      const response = await QUIZ.GenerateQuizes({
        Title: newQuiz.title,
        Description: newQuiz.description,
        Status: "Active",
        Questions: [],
        QuestionCount: newQuiz.questionCount,
        Scopes: {
          ChapterID: chapterId,
        },
        ProjectID: projectId,
        Subscribers: [],
        FloatingSubscribers: true,
      });

      console.log("Create quiz response in component:", response);

      // Consider any non-error response as successful
      if (response && response.data) {
        toast.success("Quiz created successfully");
        setShowCreateQuizDialog(false);
        setNewQuiz({
          title: "",
          description: "",
          questionCount: 5,
        });
        // Wait a moment to give the backend time to process before fetching
        setTimeout(async () => {
          await fetchQuizzesWithSource(source);
        }, 1000);
      } else if (response && response.errors) {
        toast.error(`Error: ${response.errors[0]?.message || "Unknown error"}`);
      }
    } catch (error) {
      console.error("Failed to create quiz:", error);
      toast.error("Failed to create quiz");
    } finally {
      setCreatingQuiz(false);
    }
  };

  // Update the fetchQuizResponses function
  const fetchQuizResponses = async (quizId: string) => {
    if (!source || !quizId) return;

    setLoadingResponses(true);
    setActiveQuizId(quizId);

    try {
      console.log("Fetching responses for quiz:", quizId);
      const response = await QUIZ.GetResponsesForQuiz(quizId);

      console.log("Quiz responses:", response);

      if (response && response.data) {
        let responsesData: TQuizResponse[] = [];

        if (Array.isArray(response.data)) {
          responsesData = response.data;
        } else if (response.data.ack && Array.isArray(response.data.ack)) {
          responsesData = response.data.ack;
        }

        setQuizResponses((prev) => ({
          ...prev,
          [quizId]: responsesData,
        }));
      } else {
        console.log("No response data found");
        setQuizResponses((prev) => ({
          ...prev,
          [quizId]: [],
        }));
      }
    } catch (error) {
      console.error("Failed to fetch quiz responses:", error);
      toast.error("Failed to fetch quiz responses");
    } finally {
      setLoadingResponses(false);
    }
  };

  if (loading) {
    return (
      <div className="w-full h-full p-6 md:p-10 space-y-6">
        <div className="border rounded-lg bg-muted/30 p-6">
          <Skeleton className="h-8 w-1/3 mb-4" />
          <Skeleton className="h-4 w-2/3 mb-2" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-1/4" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !project || !source || !chapter) {
    return (
      <div className="w-full h-full py-6 space-y-6">
        <div className="border rounded-lg bg-muted/30 p-6">
          <h2 className="text-2xl font-bold mb-4">Error</h2>
          <p className="text-muted-foreground">
            {error || "Resource not found"}
          </p>
          <Link
            href={`/projects/${projectId}/sources/${sourceId}`}
            className="mt-4 inline-flex items-center text-primary hover:underline"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to Source
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full p-6 md:p-10 space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/projects">Projects</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/projects/${projectId}`}>
              {project.Name}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/projects/${projectId}/sources/${sourceId}`}>
              {source.SourceName}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>
              Chapter {chapter.ChapterNumber}: {chapter.ChapterName} - Quiz
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Chapter Meta */}
      <div className="border rounded-lg bg-muted/30 p-6 mb-6">
        <h2 className="text-2xl font-bold mb-1 flex items-center gap-2">
          <FileText className="w-6 h-6 text-muted-foreground" />
          Chapter {chapter.ChapterNumber}: {chapter.ChapterName}
        </h2>
        <div className="flex flex-wrap gap-4 text-xs text-muted-foreground mb-2">
          <span>
            <b>Project:</b> {project.Name}
          </span>
          <span>
            <b>Source:</b> {source.SourceName}
          </span>
          <span>
            <b>Chapter ID:</b> {chapter.ChapterID}
          </span>
          <span>
            <b>Pages:</b> {chapter.PageNumber?.From ?? "N/A"} -{" "}
            {chapter.PageNumber?.To ?? "N/A"}
          </span>
        </div>
        {chapter.Summary && (
          <div className="mt-2">
            <p className="text-sm text-muted-foreground">{chapter.Summary}</p>
          </div>
        )}
      </div>

      {/* Questions Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold">Questions</h3>
          <Button
            onClick={handleGenerateQuestions}
            disabled={
              generatingQuestions || loadingQuestions || questions.length > 0
            }
          >
            {generatingQuestions ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : questions.length > 0 ? (
              "Questions Already Generated"
            ) : (
              <>
                <PlusCircle className="mr-2 h-4 w-4" />
                Generate Questions
              </>
            )}
          </Button>
        </div>

        {loadingQuestions ? (
          <div className="space-y-2">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        ) : questions.length === 0 ? (
          <div className="border rounded-lg p-6 text-center text-muted-foreground">
            No questions available for this chapter. Click "Generate Questions"
            to create some.
          </div>
        ) : (
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="questions-list">
              <AccordionTrigger className="px-4 py-2 bg-muted/30 hover:bg-muted/50 rounded-t-md">
                <span className="text-base font-medium">
                  View All Questions ({questions.length})
                </span>
              </AccordionTrigger>
              <AccordionContent className="pt-4 space-y-6 px-1">
                {questions.map((question, idx) => (
                  <div
                    key={question.QuestionID}
                    className="border rounded-lg shadow-sm overflow-hidden"
                  >
                    <div className="bg-muted/20 p-4 border-b">
                      <div className="flex justify-between items-start">
                        <h4 className="text-base font-medium mb-2 flex items-center">
                          <span style={styles.questionNumber}>{idx + 1}</span>
                          {question.QuestionText}
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="outline" className="ml-2">
                            {question.Type === "SINGLE_CHOICE"
                              ? "Single Choice"
                              : question.Type}
                          </Badge>
                          <Badge variant="outline" className="ml-2">
                            {question.Difficulty === "easy"
                              ? "Easy"
                              : question.Difficulty === "medium"
                                ? "Medium"
                                : question.Difficulty === "hard"
                                  ? "Hard"
                                  : "Unknown"}
                          </Badge>
                        </div>
                      </div>

                      {question.Tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {question.Tags.map((tag, tidx) => (
                            <Badge
                              key={tidx}
                              variant="secondary"
                              className="text-xs"
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>

                    <div className="p-4">
                      <div className="grid grid-cols-1 gap-3 mb-4">
                        {question.Options.map((option) => (
                          <div
                            key={option.OptionID}
                            className="p-3 rounded-md border flex items-center"
                            style={{
                              ...(option.IsCorrect
                                ? styles.optionCorrect
                                : styles.optionIncorrect),
                              gap: "0.75rem",
                            }}
                          >
                            <div
                              className={`w-5 h-5 rounded-full flex items-center justify-center ${
                                option.IsCorrect
                                  ? "bg-green-500 text-white"
                                  : "border border-muted-foreground/30"
                              }`}
                            >
                              {option.IsCorrect && (
                                <Check className="h-3 w-3" />
                              )}
                            </div>
                            <div className="flex-1">
                              <p
                                className={`text-sm ${option.IsCorrect ? "font-medium" : ""}`}
                              >
                                {option.Title}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="border-t pt-4 mt-2">
                        <h5 className="text-sm font-medium mb-2">
                          Answer Explanation:
                        </h5>
                        <p className="text-sm text-muted-foreground whitespace-pre-line">
                          {formatCitations(
                            question.Answer.Explnation,
                            question.Answer.Citations
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        )}
      </div>

      {/* Pop Quizzes Section */}
      <div className="space-y-4 mt-8">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-xl font-semibold">Pop Quizzes</h3>
          <Dialog
            open={showCreateQuizDialog}
            onOpenChange={setShowCreateQuizDialog}
          >
            <DialogTrigger asChild>
              <Button
                disabled={questions.length === 0}
                className="bg-black hover:bg-gray-800"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Create New Quiz
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Quiz</DialogTitle>
                <DialogDescription>
                  Create a new quiz from the available questions for this
                  chapter.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Quiz Title</Label>
                  <Input
                    id="title"
                    value={newQuiz.title}
                    onChange={(e) =>
                      setNewQuiz({ ...newQuiz, title: e.target.value })
                    }
                    placeholder="Enter quiz title"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={newQuiz.description}
                    onChange={(e) =>
                      setNewQuiz({ ...newQuiz, description: e.target.value })
                    }
                    placeholder="Enter quiz description"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="questionCount">
                    Number of Questions (max: {questions.length})
                  </Label>
                  <Input
                    id="questionCount"
                    type="number"
                    min={1}
                    max={questions.length}
                    value={newQuiz.questionCount}
                    onChange={(e) =>
                      setNewQuiz({
                        ...newQuiz,
                        questionCount: Math.min(
                          Math.max(1, parseInt(e.target.value) || 1),
                          questions.length
                        ),
                      })
                    }
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowCreateQuizDialog(false)}
                  disabled={creatingQuiz}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateQuiz}
                  disabled={creatingQuiz || !newQuiz.title.trim()}
                >
                  {creatingQuiz ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Quiz"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {loadingQuizzes ? (
          <div className="space-y-2">
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        ) : quizzes.length === 0 ? (
          <div className="border rounded-lg p-6 text-center text-muted-foreground">
            No quizzes available for this chapter. Click "Create New Quiz" to
            create one.
          </div>
        ) : (
          <div className="space-y-2">
            {quizzes.map((quiz, index) => {
              const quizId = quiz.PopQuizID;
              return (
                <Accordion
                  key={quizId || `quiz-${index}`}
                  type="single"
                  collapsible
                  className="w-full border rounded-lg shadow-sm overflow-hidden bg-card"
                >
                  <AccordionItem value="item-1" className="border-0">
                    <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-muted/10 w-full">
                      <div className="flex justify-between items-center w-full">
                        <div>
                          <h4 className="text-base font-medium text-left">
                            {quiz.Title}
                          </h4>
                          <p className="text-sm text-muted-foreground mt-0.5 text-left">
                            {quiz.Description || "Test your knowledge"}
                          </p>
                          <div className="flex flex-wrap gap-2 mt-1 text-xs text-muted-foreground">
                            <span>
                              Created:{" "}
                              {quiz.createdAt
                                ? formatDistanceToNow(
                                    new Date(quiz.createdAt),
                                    { addSuffix: true }
                                  )
                                : "Recently"}
                            </span>
                            <span>•</span>
                            <span>
                              Questions:{" "}
                              {quiz.QuestionCount ||
                                quiz.Questions?.length ||
                                0}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              quiz.Status === "Active" ? "default" : "secondary"
                            }
                            className="capitalize"
                          >
                            {quiz.Status}
                          </Badge>
                        </div>
                      </div>
                    </AccordionTrigger>

                    <AccordionContent className="pb-0 border-t">
                      <Tabs
                        defaultValue="questions"
                        className="w-full"
                        onValueChange={(value) => {
                          if (value === "responses" && quizId) {
                            fetchQuizResponses(quizId);
                          }
                        }}
                      >
                        <div className="px-4 py-2 border-b">
                          <TabsList className="mb-0 w-auto">
                            <TabsTrigger value="questions" className="text-sm">
                              Questions
                            </TabsTrigger>
                            <TabsTrigger
                              value="responses"
                              className="text-sm"
                              onClick={() => {
                                if (quizId) {
                                  fetchQuizResponses(quizId);
                                }
                              }}
                            >
                              Responses
                            </TabsTrigger>
                          </TabsList>
                        </div>

                        <div className="px-4 py-3">
                          <TabsContent
                            value="questions"
                            className="mt-0 space-y-5"
                          >
                            {quiz.QuestionDetails &&
                            quiz.QuestionDetails.length > 0 ? (
                              <div className="space-y-4">
                                {quiz.QuestionDetails.map((question, idx) => (
                                  <Accordion
                                    key={
                                      question.QuestionID || `question-${idx}`
                                    }
                                    type="single"
                                    collapsible
                                    className="border rounded-lg overflow-hidden bg-card"
                                  >
                                    <AccordionItem
                                      value="item-1"
                                      className="border-0"
                                    >
                                      <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-muted/10">
                                        <div
                                          className="flex w-full"
                                          style={styles.questionGap}
                                        >
                                          <div style={styles.questionNumber}>
                                            {idx + 1}
                                          </div>
                                          <div className="flex-1 text-left flex justify-between items-center">
                                            <h4 className="text-base font-medium line-clamp-1">
                                              {question.QuestionText}
                                            </h4>
                                            <div className="flex items-center gap-2 ml-2">
                                              <Badge
                                                variant="outline"
                                                className="text-xs"
                                              >
                                                {question.Type ===
                                                "SINGLE_CHOICE"
                                                  ? "Single Choice"
                                                  : question.Type}
                                              </Badge>
                                              <Badge
                                                variant="outline"
                                                className="text-xs"
                                              >
                                                {question.Difficulty === "easy"
                                                  ? "Easy"
                                                  : question.Difficulty ===
                                                      "medium"
                                                    ? "Medium"
                                                    : question.Difficulty ===
                                                        "hard"
                                                      ? "Hard"
                                                      : "Unknown"}
                                              </Badge>
                                            </div>
                                          </div>
                                        </div>
                                      </AccordionTrigger>
                                      <AccordionContent className="px-4 pt-2 pb-4 border-t">
                                        <div className="grid grid-cols-1 gap-2 mb-4 mt-3">
                                          {question.Options.map((option) => (
                                            <div
                                              key={option.OptionID}
                                              className="p-3 rounded-md border flex items-center"
                                              style={{
                                                ...(option.IsCorrect
                                                  ? styles.optionCorrect
                                                  : styles.optionIncorrect),
                                                gap: "0.75rem",
                                              }}
                                            >
                                              <div
                                                className={`w-5 h-5 rounded-full flex items-center justify-center ${
                                                  option.IsCorrect
                                                    ? "bg-green-500 text-white"
                                                    : "border border-muted-foreground/30"
                                                }`}
                                              >
                                                {option.IsCorrect && (
                                                  <Check className="h-3 w-3" />
                                                )}
                                              </div>
                                              <div className="flex-1">
                                                <p
                                                  className={`text-sm ${option.IsCorrect ? "font-medium" : ""}`}
                                                >
                                                  {option.Title}
                                                </p>
                                              </div>
                                            </div>
                                          ))}
                                        </div>

                                        <div className="border-t pt-4 mt-2">
                                          <h5 className="text-sm font-medium mb-2">
                                            Answer Explanation:
                                          </h5>
                                          <p className="text-sm text-muted-foreground whitespace-pre-line">
                                            {formatCitations(
                                              question.Answer?.Explnation,
                                              question.Answer?.Citations
                                            )}
                                          </p>
                                        </div>
                                      </AccordionContent>
                                    </AccordionItem>
                                  </Accordion>
                                ))}
                              </div>
                            ) : (
                              <div className="border rounded-lg p-6 text-center text-muted-foreground">
                                No questions available for this quiz.
                              </div>
                            )}
                          </TabsContent>

                          <TabsContent value="responses" className="mt-0">
                            {loadingResponses && activeQuizId === quizId ? (
                              <div className="flex justify-center py-8">
                                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                              </div>
                            ) : quizResponses[quizId] &&
                              quizResponses[quizId].length > 0 ? (
                              <div className="border rounded-lg overflow-hidden">
                                <Table>
                                  <TableHeader className="bg-muted/20">
                                    <TableRow>
                                      <TableHead>Name</TableHead>
                                      <TableHead>Email</TableHead>
                                      <TableHead>Gender</TableHead>
                                      <TableHead>Score</TableHead>
                                      <TableHead className="text-right">
                                        Correct
                                      </TableHead>
                                      <TableHead className="text-right">
                                        Incorrect
                                      </TableHead>
                                      <TableHead className="text-right">
                                        Total
                                      </TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {quizResponses[quizId].map((response) => (
                                      <TableRow key={response.ResponseID}>
                                        <TableCell className="font-medium">
                                          {response.User?.Name || "Anonymous"}
                                        </TableCell>
                                        <TableCell>
                                          {response.User?.Email || "N/A"}
                                        </TableCell>
                                        <TableCell>
                                          {response.User?.Gender || "N/A"}
                                        </TableCell>
                                        <TableCell>
                                          <Badge
                                            variant="outline"
                                            className="font-medium bg-background"
                                          >
                                            {Math.round(
                                              response.Result?.Score || 0
                                            )}
                                            %
                                          </Badge>
                                        </TableCell>
                                        <TableCell className="text-right text-green-600 font-medium">
                                          {response.Result?.CorrectAnswers || 0}
                                        </TableCell>
                                        <TableCell className="text-right text-red-600 font-medium">
                                          {response.Result?.IncorrectAnswers ||
                                            0}
                                        </TableCell>
                                        <TableCell className="text-right font-medium">
                                          {response.Result?.TotalAnswers || 0}
                                        </TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </div>
                            ) : (
                              <div className="border rounded-lg p-6 text-center text-muted-foreground">
                                No responses available for this quiz yet.
                              </div>
                            )}
                          </TabsContent>
                        </div>
                      </Tabs>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
