export class CreateCreditUsageDto {
  UserID: string;
  Type: 'Subscription' | 'TopUp';
  CapType: 'Token' | 'Message' | 'Interaction';
  SubscriptionID: string;
  RenewalID?: string;
  Credit: number;
  RemainingCredit: number;
  ExpiryDate: Date | null; // Matches subscription end date for renewal
  LastSyncDate: Date; // sync with token usage
}

export class UpdateCreditUsageDto {
  CreditUsageID: string;
  Credit: number;
  RemainingCredit: number;
  ExpiryDate: Date | null; // Matches subscription end date for renewal
  LastSyncDate: Date; // sync with token usage
}

export class ListCreditUsageDto {
  TenantID: string;
  UserID: string;
}
