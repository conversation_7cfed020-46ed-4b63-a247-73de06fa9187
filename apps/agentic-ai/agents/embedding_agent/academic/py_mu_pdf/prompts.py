DERIVE_CONTENTS_PROMPT = """
You are a highly specialized AI Agent designed to extract table of contents (TOC) information from text representations of PDF pages. Your sole task is to analyze the provided text, identify chapter titles, chapter numbers, and corresponding page numbers, and output the data in a structured JSON format. **DO NOT make up information. If the data is not explicitly available in the input text, the corresponding field should be set to null.**

**Input:**

You will be provided with a text string representing the content of a PDF page or a series of PDF pages in {language} language. This text will typically contain the table of contents for a book, document, or report. You will also be given the total number of pages in the document. If the documents are empty or do not contain a table of contents, return an empty JSON array.

Your sole responsibility is to **accurately extract** chapter-wise structured data from a raw textual TOC. The input text may contain TOC formatted as a list, table, or in free form. It may include numerals in local scripts (e.g., १, २, ३, ४, ५, ६, ७, ८, ९, ० for 1, 2, 3, 4, 5, 6, 7, 8, 9, 0). You must correctly identify and normalize all such data.

The text will be in {language} language.
Table of content can be in any format. It can be list, table, etc, make sure you extract the information correctly.

**Output:**

Your output MUST be a JSON array of JSON objects. Each object in the array represents a single entry from the table of contents (e.g., a chapter, section, or topic). Each object should have the following structure:

```json
[
  {{
    "chapter_number": 1,
    "chapter_name": "Chapter Title 1",
    "page_from": 10,
    "page_to": 25
  }}
]
```

Example:
Input:
पहली इकाई\nदूसरी इकाई\n* अनुक्रमणिका *\nक्र.\nपाठ का नाम\nविधा \nरचनाकार\nपृष्\u200dठ\n  १. भारत महिमा\nकविता\nजयशंकर प्रसाद\n१-२\n

Output:
[
  {{
    "chapter_number": 1,
    "chapter_name": "भारत महिमा",
    "page_from": 1,
    "page_to": 2
  }},
]

**IMPORTANT RULES**:
- Never guess or hallucinate missing values.
- If a field is missing or unclear in the text, return `null` for that field.
- Normalize all digits from any script (e.g., "५४-५६" → 54 to 56).
- Remove any newline noise or formatting artifacts.
- Do not include duplicate entries.

Documents: {documents}
Total Pages: {total_pages}
Language: {language}
"""

DERIVE_TABLE_OF_CONTENTS_PROMPT = """
I will provide you with an object containing array of documents in 'docs' which are from chapter in a textbook. Each doc has the following structure:

Your task is to:

Read through all the documents in order.
Identify all headings and subheadings in the text (ignore activity prompts, callout boxes, or questions unless they are formatted as official headings).
Please provide a clear, hierarchical list of all the headings and subheadings as they appear in the text.

For each main heading, create an object with:
topic: the main heading text,
pageNumber: the page number where the heading first appears,
subTopics: an array of all subheadings (direct children) under that main heading.
Return the result as a single object in the following format:
{{
  "topics": [
    {{
      "chapterName": "chapter name",
      "topic": "topic 1", 
      "pageNumber": 10,
      "subTopics": ["sub topic 1", "sub topic 2"]
    }}
  ]
}}

Here is the chapter data:
{chapter}

Book Language: {language}
"""

SUMMARIZE_CHUNKS_PROMPT = """
You are generating optimized summaries for a Retrieval Augmented Generation (RAG) system that processes academic textbooks. Your goal is to create a summary that PRESERVES ALL IMPORTANT INFORMATION.

## Task:
1. Create a detailed information-dense summary of the text (2-3 lines minimum, but can be longer if needed to capture all important concepts and details) in same language optimized for same language of book.
2. Identify the main topic that best categorizes this content.  
3. Identify a more specific subtopic that helps in precise retrieval.
4. Use as much as terminology and definitions from the original text.

## Important Instructions:
- This is for academic content optimization, not data removal.
- Include ALL key concepts, terms, definitions, and relationships.
- Preserve numerical data, facts, and specific examples.
- The summary should be detailed enough that the RAG system can accurately retrieve this chunk when answering specific queries.
- Do not add any information not present in the original text.
- Do not omit any significant information from the original text.
- Focus on factual content, not stylistic elements.
- Generate Summary in same language of book: {language}

## Text to Summarize:
{page_content}

## Topics and Subtopics In Chapter:
{topics_in_chapter}

Book Language: {language}

Respond in JSON format with the following structure:
{{"summary": "detailed information-preserving summary", "topic": "main topic", "subtopic": "more specific subtopic"}}
"""

GENERATE_CHAPTER_SUMMARIES_PROMPT = """
You are optimizing academic textbook content for a Retrieval Augmented Generation (RAG) system. Your task is to create a comprehensive chapter summary that preserves all essential information.

## Chapter: "{chapter_name}"

## Task:
Create a comprehensive, information-dense summary of this chapter (approx 10 lines) based on the following chunk summaries. This summary will be used by a RAG system to understand and retrieve the overall content of this chapter.

## Important Instructions:
- This is for information optimization, not reduction.
- Preserve ALL key concepts, theories, principles, and relationships.
- Include important terminology, definitions, and academic frameworks.
- Maintain all numerical data, facts, examples, and case studies.
- Identify and list the main topics covered in this chapter.
- The summary should be detailed enough that the RAG system can accurately determine if this chapter contains relevant information.
- Do not add information not present in the provided summaries.
- Focus on academic content and factual information.
- Organize information in a logical and structured manner.
- Generate Summary in same language of book: {language}

## Chunk Summaries:
{chunk_summaries}

Book Language: {language}

Respond in JSON format with:
{{
  "chapter_id": "{chapter_id}",
  "chapter_name": "{chapter_name}",
  "summary": "comprehensive 10 line summary that preserves all important information",
  "topics": ["topic 1", "topic 2", "topic 3"]
}}
"""

GENERATE_BOOK_SUMMARY_PROMPT = """
You are optimizing academic textbook content for a Retrieval Augmented Generation (RAG) system. Your task is to create a comprehensive book summary that preserves all essential information.

## Book Title: "{source_name}"

## Task:
Create a detailed, information-rich summary of this entire textbook (approximately 20 lines) based on the following chapter summaries. This summary will be used by a RAG system to understand the overall content and structure of the book.

## Important Instructions:
- This is for information optimization, not reduction - preserve ALL important content.
- Highlight the most important theories, concepts, principles, and methodologies.
- Identify the progression of topics and how chapters build upon each other.
- Extract and list the main topics that span across multiple chapters.
- For each chapter, identify its most significant contribution to the overall book.
- The summary should be detailed enough for the RAG system to understand the complete scope of the book.
- Structure the summary in a way that shows relationships between different parts of the book.
- Do not add information not present in the provided chapter summaries.
- Focus on academic content, factual information, and educational value.
- Generate Summary in same language of book: {language}

## Chapter Summaries:
{chapter_summaries_text}

Book Language: {language}

Respond in JSON format with:
{{
  "title": "{source_name}",
  "summary": "comprehensive 20-line summary that preserves all key information",
  "main_topics": ["core topic 1", "core topic 2", "core topic 3"],
  "chapter_highlights": [
    {{"chapter_name": "chapter name", "highlight": "most significant contribution/concept from this chapter"}}
  ]
}}
"""
