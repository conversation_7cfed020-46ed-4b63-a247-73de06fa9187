"use client";

import React, { useCallback } from "react";
import { Volume2, VolumeX } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { useAutoRead } from "../../contexts/auto-read-context";
import { Switch } from "@workspace/ui/components/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

export function AutoReadToggle() {
  const { isAutoReadEnabled, toggleAutoRead } = useAutoRead();

  const handleToggle = useCallback(() => {
    try {
      toggleAutoRead();
    } catch (error) {
      console.error("Failed to toggle auto-read:", error);
    }
  }, [toggleAutoRead]);

  const iconClasses = cn(
    "transition-colors",
    isAutoReadEnabled ? "text-primary" : "text-muted-foreground"
  );

  const textClasses = cn(
    "text-xs font-medium transition-colors hidden md:block",
    isAutoReadEnabled ? "text-primary" : "text-muted-foreground"
  );

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-2 px-3 py-1.5 rounded-full border border-border bg-background/50 hover:bg-background/80 transition-colors">
            <div className={iconClasses}>
              {isAutoReadEnabled ? (
                <Volume2 size={16} />
              ) : (
                <VolumeX size={16} />
              )}
            </div>

            <Switch
              checked={isAutoReadEnabled}
              onCheckedChange={handleToggle}
              className="scale-75"
              aria-label="Toggle auto-read"
            />

            <span className={textClasses}>Auto Read</span>
          </div>
        </TooltipTrigger>
        <TooltipContent side="top">
          <p>
            {isAutoReadEnabled
              ? "Auto-read new messages (enabled)"
              : "Auto-read new messages (disabled)"}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
