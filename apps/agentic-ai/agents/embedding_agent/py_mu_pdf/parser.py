import fitz


def parse_pdf(file_content: bytes) -> list[dict]:
    doc = fitz.open(stream=file_content, filetype="pdf")
    pages: list[dict] = [
        {
            "page_content": page.get_text(),
            "metadata": {
                "file_name": "Mediclaim Policy.pdf",
                "file_path": "agents/sample_data/Mediclaim Policy.pdf",
                "page_number": page.number + 1,
            },
        }
        for page in doc
    ]
    return pages


if __name__ == "__main__":
    with open("agents/sample_data/Mediclaim Policy.pdf", "rb") as file:
        file_content = file.read()
        pages = parse_pdf(file_content)
        print(pages)
